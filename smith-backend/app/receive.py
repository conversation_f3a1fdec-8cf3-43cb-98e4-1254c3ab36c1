import logging
from datetime import datetime, timezone
from typing import Any, cast

import jsonschema_rs
import orjson
from fastapi import HTTPException, Request
from lc_config.tracing import trace_async_function_call
from starlette.requests import ClientDisconnect

logger = logging.getLogger(__name__)


def parse_with_schema(data: bytearray | bytes, schema: jsonschema_rs.JSONSchema) -> Any:
    try:
        parsed = orjson.loads(data)
    except orjson.JSONDecodeError as e:
        logger.info(
            f"Invalid JSON: {e.msg} at line:{e.lineno} col:{e.colno} pos:{e.pos}"
        )
        raise HTTPException(
            status_code=422,
            detail="Request body is not valid JSON",
        )

    try:
        schema.validate(parsed)
    except jsonschema_rs.ValidationError as e:
        logger.info(f"Invalid schema: {e.schema_path[1:]}: {e.message}")
        raise HTTPException(
            status_code=422,
            detail=f"{e.schema_path[1:]}: {e.message}",
        )
    except ValueError as e:
        logger.info(f"Value Error: {str(e)}")
        raise HTTPException(
            status_code=422,
            detail=str(e),
        )

    return parsed


async def consume_body(request: Request) -> bytearray:
    """Consumes an incoming request efficiently"""
    return await trace_async_function_call(
        name="consume",
        service="receive",
        resource="consume",
        func=_consume_body,
        args=(request,),
        kwargs={},
    )


async def _consume_body(request: Request) -> bytearray:
    raw = bytearray()
    try:
        async for chunk in request.stream():
            raw += chunk
    except ClientDisconnect:
        raise HTTPException(status_code=499, detail="Client disconnected")

    return raw


########################################
#           PARSING UTILITIES          #
########################################


def convert_timestamp_to_isostring(ts: str | int | float | None) -> str | None:
    if ts is None:
        return None

    if isinstance(ts, int) or isinstance(ts, float):
        try:
            ts = datetime.fromtimestamp(ts, tz=timezone.utc).isoformat()
        except ValueError:
            try:
                ts = datetime.fromtimestamp(
                    cast(int, ts) / 1000, tz=timezone.utc
                ).isoformat()
            except Exception:
                ts = None
        except Exception:
            ts = None

    if isinstance(ts, str):
        try:
            ts = datetime.fromisoformat(ts).isoformat()
        except Exception:
            ts = None

    return ts
