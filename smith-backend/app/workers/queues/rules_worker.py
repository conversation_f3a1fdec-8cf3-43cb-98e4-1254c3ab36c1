# ruff: noqa: E402
import logging
from typing import Any, Dict

import structlog
from lc_config.settings import shared_settings
from lc_database.queue.connection import AsyncRedisRetry, get_sharded_settings
from lc_database.redis import create_async_queue, set_saq_log_level

from app.models.datasets.run_over_dataset import (
    create_dataset_runs_manager as create_dataset_runs_manager_source,
)
from app.models.runs.rules_apply import apply_run_rule as apply_run_rule_source
from app.workers.utils import shutdown_callback, startup_callback, trace_job

# set SAQ logging to WARNING
set_saq_log_level(logging.WARNING)

logger = structlog.getLogger(__name__)

# ========================================== Run Rules Queue ========================================
# This queue is for any Run Rules jobs or crons.
# ===================================================================================================


@trace_job
async def apply_run_rule(
    ctx: Dict[Any, Any],
    *args,
    **kwargs,
):
    await apply_run_rule_source(*args, **kwargs)


@trace_job
async def apply_dataset_manager(
    ctx: Dict[Any, Any],
    *args,
    **kwargs,
):
    await create_dataset_runs_manager_source(*args, **kwargs)


settings = {
    "queue": create_async_queue(
        shared_settings.RUN_RULES_QUEUE,
        AsyncRedisRetry.from_url(shared_settings.REDIS_DATABASE_URI),
    ),
    "functions": [
        apply_run_rule,
        apply_dataset_manager,
    ],
    "startup": startup_callback,
    "shutdown": shutdown_callback,
    "concurrency": shared_settings.MAX_ASYNC_JOBS_PER_WORKER,
    "cron_jobs": [],
}


def sharded_settings() -> dict:
    return get_sharded_settings(shared_settings.RUN_RULES_QUEUE, settings)
