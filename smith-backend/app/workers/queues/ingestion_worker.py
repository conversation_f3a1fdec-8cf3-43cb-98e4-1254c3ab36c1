# ruff: noqa: E402

import logging

import structlog
from lc_config.settings import shared_settings
from lc_database.queue.connection import AsyncRedisRetry, get_sharded_settings
from lc_database.redis import create_async_queue, set_saq_log_level

from app.workers.jobs.ingestion import persist_batched_runs, persist_run_or_parent
from app.workers.utils import shutdown_callback, startup_callback

logger = structlog.getLogger(__name__)

# ========================================== Ingestion Queue ========================================
# This queue is for any Ingestion specific jobs.
# ===================================================================================================

# set SAQ logging to WARNING
set_saq_log_level(logging.WARNING)

settings = {
    "queue": create_async_queue(
        shared_settings.INGESTION_QUEUE,
        AsyncRedisRetry.from_url(shared_settings.REDIS_DATABASE_URI),
    ),
    "functions": [
        persist_batched_runs,
        persist_run_or_parent,
    ],
    "startup": startup_callback,
    "shutdown": shutdown_callback,
    "concurrency": shared_settings.MAX_ASYNC_JOBS_PER_WORKER,
    "cron_jobs": [],
}


def sharded_settings() -> dict:
    return get_sharded_settings(shared_settings.INGESTION_QUEUE, settings)
