# ruff: noqa: E402
import logging

import structlog
from lc_config.settings import shared_settings
from lc_database.queue.connection import AsyncRedisRetry, get_sharded_settings
from lc_database.redis import create_async_queue, set_saq_log_level
from saq import CronJob

from app.workers.jobs.export import run_bulk_export_partition, sync_served_dataset
from app.workers.utils import shutdown_callback, startup_callback

# set SAQ logging to WARNING
set_saq_log_level(logging.WARNING)

logger = structlog.getLogger(__name__)

# ========================================== Export Queue ============================================
# This queue is for heavy data export type jobs. This queue uses more resources in general
# such as CPU / and Memory. Typically it will have less concurrency and more heavyweight jobs.
# ===================================================================================================


cron_jobs: list[CronJob] = []

settings = {
    "queue": create_async_queue(
        shared_settings.EXPORT_QUEUE,
        AsyncRedisRetry.from_url(shared_settings.REDIS_DATABASE_URI),
    ),
    "functions": [
        run_bulk_export_partition,
        sync_served_dataset,
    ],
    "startup": startup_callback,
    "shutdown": shutdown_callback,
    "concurrency": shared_settings.MAX_ASYNC_JOBS_PER_WORKER,
    "cron_jobs": cron_jobs,
}


def sharded_settings() -> dict:
    return get_sharded_settings(shared_settings.EXPORT_QUEUE, settings)
