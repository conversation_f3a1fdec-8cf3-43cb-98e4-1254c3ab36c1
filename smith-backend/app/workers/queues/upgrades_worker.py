# ruff: noqa: E402
import logging

import structlog
from lc_config.settings import shared_settings
from lc_database.queue.connection import AsyncRedisRetry, get_sharded_settings
from lc_database.redis import create_async_queue, set_saq_log_level
from saq import CronJob

from app.workers.jobs.upgrades import (
    schedule_upgrade_batched_ch_traces,
    upgrade_batched_ch_traces,
    upgrade_batched_runs,
)
from app.workers.utils import shutdown_callback, startup_callback

logger = structlog.getLogger(__name__)

# ========================================== Upgrades Queue ============================================
# This queue is for TTL trace upgrades.
# ===================================================================================================

# set SAQ logging to WARNING
set_saq_log_level(logging.WARNING)

cron_jobs: list[CronJob] = []

settings = {
    "queue": create_async_queue(
        shared_settings.UPGRADES_QUEUE,
        AsyncRedisRetry.from_url(shared_settings.REDIS_DATABASE_URI),
    ),
    "functions": [
        upgrade_batched_runs,
        upgrade_batched_ch_traces,
        schedule_upgrade_batched_ch_traces,
    ],
    "startup": startup_callback,
    "shutdown": shutdown_callback,
    "concurrency": shared_settings.MAX_ASYNC_JOBS_PER_WORKER,
    "cron_jobs": cron_jobs,
}


def sharded_settings() -> dict:
    return get_sharded_settings(shared_settings.UPGRADES_QUEUE, settings)
