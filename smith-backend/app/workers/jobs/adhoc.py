# ruff: noqa: E402
import asyncio
import hashlib
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import Any, DefaultDict, Dict, Literal, Set
from uuid import UUID, uuid5

import orjson
import structlog
from lc_config.settings import shared_settings as settings
from lc_database import redis
from lc_database.clickhouse import (
    RELATED_MATERIALIZED_VIEWS,
    ClickhouseClient,
    clickhouse_client,
)
from lc_database.database import asyncpg_conn
from lc_database.s3_client import (
    IO_TYPES,
    S3_SINGLE_REGION_BUCKETS,
    BaseCloudStorageDeletionManager,
)

from app.api.auth.schemas import BaseAuthInfo, parse_auth_info
from app.crud import (
    delete_runs_ch,
    delete_runs_trace_id_ch,
    delete_tracer_sessions_ch,
    evict_tenant_from_tracer_sessions_cache,
)
from app.models.alerts.match import aggregate_and_check_alert_rule
from app.models.billing.usagereporting import nodes, seats, traces
from app.models.bulk_exports import jobs as bulk_export_jobs
from app.models.charts import jobs as charts_jobs
from app.models.datasets.indexed.index import sync_served_datasets_cron
from app.models.datasets.run_over_dataset import (
    execute_playground_rules_serializable,
    fetch_and_execute_examples_serializable,
)
from app.models.identities import crud as identities_crud
from app.models.organizations import (
    metronome_cache as org_metronome_cache,
)
from app.models.organizations import (
    onboarding_emails as onboarding_emails_jobs,
)
from app.models.organizations import (
    self_hosted as self_hosted_customers,
)
from app.models.runs.rules_apply import (
    cron_schedule_apply_rules as cron_schedule_apply_rules_source,
)
from app.prompt_optimization import optimize
from app.utils import arun_in_executor, load_json
from app.workers.utils import trace_job

logger = structlog.getLogger(__name__)

DELETE_RETRY_DELAY = 600
DELETE_RETRIES = 6
DELETE_TIMEOUT = 1200


@trace_job
async def enable_clickhouse_merge_setting_job(ctx: Dict[Any, Any]) -> None:
    """Enable force merge settings - this is to ensure TTL processing."""
    await update_clickhouse_merge_setting_job(ctx, action="enable")


@trace_job
async def disable_clickhouse_merge_setting_job(ctx: Dict[Any, Any]) -> None:
    """Disable force merge settings."""
    await update_clickhouse_merge_setting_job(ctx, action="disable")


async def update_clickhouse_merge_setting_job(
    ctx: Dict[Any, Any], action: Literal["enable", "disable"]
) -> None:
    """
    Update force merge settings and TTL drop only parts.
    This is to enforce more aggressive TTL during lighter periods.
    """

    if not settings.ENABLE_CLICKHOUSE_MERGE_SETTINGS_CRON:
        await logger.ainfo(
            "Skipping update_clickhouse_merge_setting_job - ENABLE_CLICKHOUSE_MERGE_SETTINGS_CRON is False",
        )
        return

    tables = ["runs"] + RELATED_MATERIALIZED_VIEWS["runs"]

    # merge settings forces more aggressive merges
    merge_setting_value = 300 if action == "enable" else 0
    # if set to 1 it means TTL is only enforced when processing part
    only_drop_parts_value = 0 if action == "enable" else 1

    async with clickhouse_client(ClickhouseClient.INTERNAL_ANALYTICS_SLOW) as ch:
        for table in tables:
            if table == "runs":
                await ch.execute(
                    "update_merge_settings",
                    f"ALTER TABLE {table} MODIFY SETTING ttl_only_drop_parts={only_drop_parts_value}",
                )
                await logger.ainfo(
                    "Updated ttl_only_drop_parts setting",
                    action=action,
                    setting_value=only_drop_parts_value,
                    table=table,
                )
            else:
                await ch.execute(
                    "update_merge_settings",
                    f"ALTER TABLE {table} MODIFY SETTING min_age_to_force_merge_seconds={merge_setting_value}",
                )
                await logger.ainfo(
                    "Updated min_age_to_force_merge_seconds setting",
                    action=action,
                    setting_value=merge_setting_value,
                    table=table,
                )

    async with clickhouse_client(ClickhouseClient.INGESTION_SLOW) as ch:
        # Log counts for runs table
        runs_count = await ch.fetchrow(
            "update_merge_settings_runs_count",
            """
            SELECT count(1) as count FROM runs
            WHERE toDateTime(assumeNotNull(trace_first_received_at) + toIntervalSecond(assumeNotNull(ttl_seconds))) < NOW()
            AND ttl_seconds IS NOT NULL
            AND trace_first_received_at IS NOT NULL
            """,
        )
        await logger.ainfo(
            "Runs table TTL count", count=runs_count["count"], table="runs"
        )

        # Log counts for other materialized views
        for table in RELATED_MATERIALIZED_VIEWS["runs"]:
            if "kv" in table or "hourly" in table:
                # skip kv tables as they are expensive to calculate
                continue
            count = await ch.fetchrow(
                "update_merge_settings_mv_table_count",
                """
                SELECT count(1) as count FROM {table}
                WHERE greatest(
                    toDateTime(assumeNotNull(trace_first_received_at) + toIntervalSecond(assumeNotNull(trace_ttl_seconds))),
                    toDateTime(assumeNotNull(trace_first_received_at) + toIntervalDay(30))
                ) < NOW()
                AND trace_first_received_at IS NOT NULL
                AND trace_ttl_seconds IS NOT NULL
                """,
                params={"table": table},
            )
            await logger.ainfo(f"{table} TTL count", count=count["count"], table=table)


@trace_job
async def cron_schedule_apply_rules(
    ctx: Dict[Any, Any],
    *args,
    **kwargs,
):
    await cron_schedule_apply_rules_source(*args, **kwargs)


@trace_job
async def background_delete_tracer_sessions(
    ctx: Dict[Any, Any], auth_dict: dict[str, Any], tracer_session_ids: list[str]
) -> None:
    # dual-write: lock, leave on original
    async with redis.aredis_pool() as aredis:
        auth: BaseAuthInfo = await arun_in_executor(parse_auth_info, auth_dict)
        delete_key = (
            uuid5(
                auth.tenant_id,
                orjson.dumps(sorted(tracer_session_ids)).decode("utf-8"),
            ),
        )
        inflight_key = f"delete_tracer_sessions:{str(delete_key)}"
        lock = aredis.lock(inflight_key, timeout=300, blocking=False)
        if not await lock.acquire():
            await logger.awarning(
                f"Failed to acquire lock {inflight_key}, already running, exiting",
                session_ids=tracer_session_ids,
            )
            return
        try:
            logger.info(
                "Deleting tracer sessions",
                session_ids=tracer_session_ids,
            )
            if settings.FF_DISABLE_TRACER_SESSION_DELETES:
                # Instead of deleting, re-queue the job to run again in 30 minutes
                async with redis.async_queue(settings.ADHOC_QUEUE) as queue:
                    await queue.enqueue(
                        "background_delete_tracer_sessions",
                        auth_dict=auth_dict,
                        tracer_session_ids=tracer_session_ids,
                        scheduled=(
                            datetime.now(timezone.utc)
                            + timedelta(
                                minutes=settings.FF_REENQUEUE_TRACER_SESSION_DELAY
                            )
                        ).timestamp(),
                        # Use the same retry settings as the original job
                        retry_delay=DELETE_RETRY_DELAY,
                        timeout=DELETE_TIMEOUT,
                        retries=DELETE_RETRIES,
                        retry_backoff=True,
                    )

                await logger.ainfo(
                    "Re-queued delete job for tracer sessions",
                    session_ids=tracer_session_ids,
                    next_run_time=(
                        datetime.now(timezone.utc)
                        + timedelta(minutes=settings.FF_REENQUEUE_TRACER_SESSION_DELAY)
                    ).isoformat(),
                )
            elif settings.FF_TRACER_SESSION_DELETE_BATCHING:
                # Create a new sorted set for session deletes
                sorted_set_key = settings.TRACER_SESSION_DELETE_SORTED_SET_KEY
                current_time = datetime.now(timezone.utc).timestamp()
                sorted_set_data = {
                    "tenant_id": auth_dict["tenant_id"],
                    "session_ids": tracer_session_ids,
                }
                await aredis.zadd(
                    sorted_set_key,
                    {orjson.dumps(sorted_set_data).decode(): current_time},
                )
                # evict tenant since deletes are batched
                await evict_tenant_from_tracer_sessions_cache(auth_dict["tenant_id"])

                await logger.ainfo(
                    "Added delete job data to sorted set",
                    sorted_set_key=sorted_set_key,
                    session_ids=tracer_session_ids,
                )
            else:
                await delete_tracer_sessions_ch(
                    [UUID(auth.tenant_id.hex)],
                    [UUID(session_id) for session_id in tracer_session_ids],
                )
        finally:
            try:
                await lock.release()
            except Exception as e:
                logger.info(f"Error releasing lock {inflight_key}: {str(e)}")


@trace_job
async def process_batched_tracer_session_deletes(ctx: Dict[Any, Any]) -> None:
    async with redis.aredis_pool() as aredis:
        sorted_set_key = settings.TRACER_SESSION_DELETE_SORTED_SET_KEY

        # Get a batch of items from the sorted set
        batch = await aredis.zrange(
            sorted_set_key,
            0,
            settings.TRACER_SESSION_DELETE_BATCH_SIZE - 1,
            withscores=True,
        )

        if not batch:
            await logger.ainfo("No pending tracer session deletes found")
            return

        batch_items = []
        session_ids: Set[str] = set()
        tenant_ids: Set[str] = set()
        for item, _ in batch:
            data = orjson.loads(item)
            try:
                if (
                    len(session_ids)
                    + len(data["session_ids"])
                    + len(tenant_ids)
                    + len(data["tenant_id"])
                    > settings.TRACER_SESSION_DELETE_MAX_QUERY_SIZE
                ):
                    logger.info("Reached query max size limit, stopping processing")
                    break
                tenant_id = data["tenant_id"]
                session_ids.update(data["session_ids"])
                tenant_ids.add(tenant_id)
                batch_items.append(item)
            except Exception as e:
                logger.error(
                    "Error processing batch item",
                    error=e,
                    data=data,
                )

        if batch_items:

            async def delete_feedbacks_postgres():
                async with asyncpg_conn() as db:
                    await db.execute(
                        """
                        DELETE FROM feedbacks
                        WHERE session_id = ANY($1::uuid[])
                        AND tenant_id = ANY($2::uuid[])
                        """,
                        [UUID(session_id) for session_id in session_ids],
                        [UUID(tenant_id) for tenant_id in tenant_ids],
                    )

            delete_tasks = [
                delete_tracer_sessions_ch(
                    [UUID(tenant_id) for tenant_id in tenant_ids],
                    [UUID(session_id) for session_id in session_ids],
                ),
                delete_feedbacks_postgres(),
            ]

            await asyncio.gather(*delete_tasks)

            # Remove processed items from the sorted set
            await aredis.zrem(sorted_set_key, *batch_items)

        await logger.ainfo(
            f"Processed and deleted {len(batch_items)} batched tracer session delete requests"
        )


def _parse_offsets(fragment: str) -> list[tuple[int, int]]:
    """Parse a "start-end" or comma‑separated list of such ranges."""
    ranges: list[tuple[int, int]] = []
    for part in filter(None, fragment.split(",")):
        try:
            start_str, end_str = part.split("-", 1)
            ranges.append((int(start_str), int(end_str)))
        except ValueError:
            return []
    return ranges


def _extract_base_and_offsets(url: str) -> tuple[str, list[tuple[int, int]]]:
    if not url:
        return "", []
    if "#" not in url:
        return url, []
    base, fragment = url.split("#", 1)
    return base, _parse_offsets(fragment)


async def get_s3_url_offsets(
    *,
    tenant_ids: list[UUID],
    session_ids: list[UUID],
    trace_ids: list[UUID],
) -> dict[str, list[tuple[int, int]]]:
    params = {
        "tenant_ids": list(tenant_ids),
        "session_ids": list(session_ids),
        "trace_ids": list(trace_ids),
    }

    sql = """
        SELECT inputs_s3_urls, outputs_s3_urls, s3_urls
        FROM runs
        PREWHERE tenant_id IN {tenant_ids}
          AND session_id IN {session_ids}
          AND (id, is_root, start_time) IN (
            SELECT id, is_root, start_time
            FROM runs_trace_id
            WHERE tenant_id IN {tenant_ids}
              AND session_id IN {session_ids}
              AND trace_id IN {trace_ids}
          )
    """

    mapping: DefaultDict[str, list[tuple[int, int]]] = defaultdict(list)

    async with clickhouse_client(ClickhouseClient.USER_HEAVY_WORKLOADS_SLOW) as ch:
        rows = await ch.fetch("get_s3_url_offsets", sql, params=params)

    for row in rows:
        for field in ("inputs_s3_urls", "outputs_s3_urls", "s3_urls"):
            raw = row.get(field)
            if not raw:
                continue
            try:
                urls_dict = load_json(raw) or {}
            except Exception:
                continue

            for url in urls_dict.values():
                base, ranges = _extract_base_and_offsets(url)
                if not base:
                    continue
                if not ranges and base not in mapping:
                    mapping[base] = []
                for rng in ranges:
                    if rng not in mapping[base]:
                        mapping[base].append(rng)

    return dict(mapping)


async def _query_traces_by_metadata_batch(
    tenant_metadata_map: dict[str, list[dict[str, str]]],
) -> dict[str, tuple[list[str], list[str]]]:
    """Query ClickHouse for trace‑ and session‑ids matching metadata, batched per tenant."""
    results: dict[str, tuple[list[str], list[str]]] = {}

    async with clickhouse_client(ClickhouseClient.USER_HEAVY_WORKLOADS_SLOW) as client:
        for tenant_id, metadata_list in tenant_metadata_map.items():
            if not metadata_list:
                results[tenant_id] = ([], [])
                continue

            kv_pairs: list[tuple[str, str]] = []
            for md in metadata_list:
                kv_pairs.extend((k, f'"{v}"') for k, v in md.items())

            query = """
            SELECT
                   r.trace_id,
                   r.session_id
            FROM runs AS r
            ANY INNER JOIN (
                SELECT run_id
                FROM runs_metadata_kv
                WHERE tenant_id = {tenant_id}
                  AND (key, value) IN {kv}
            ) AS kv ON r.id = kv.run_id
            WHERE r.tenant_id = {tenant_id}
            """

            try:
                rows = await client.fetch(
                    name="query_traces_by_metadata_batch",
                    query=query,
                    params={"tenant_id": tenant_id, "kv": kv_pairs},
                    query_meta={"tenant_id": tenant_id},
                )
                if rows:
                    results[str(tenant_id)] = (
                        [str(row["trace_id"]) for row in rows],
                        [str(row["session_id"]) for row in rows if row["session_id"]],
                    )
                else:
                    results[str(tenant_id)] = ([], [])
            except Exception as e:
                logger.warning(f"Failed query for tenant {tenant_id}: {e}")
                results[str(tenant_id)] = ([], [])

    await logger.ainfo(
        "Retrieved traces by metadata",
        num_tenants=len(results),
        total_trace_ids=sum(len(trace_ids) for trace_ids, _ in results.values()),
        total_session_ids=sum(len(session_ids) for _, session_ids in results.values()),
    )

    return results


async def requeue_remaining_tenant_results(
    tenant_results: dict[str, tuple[list[str], list[str]]],
) -> None:
    """Requeue tenant results that exceed the query size limit.

    Args:
        tenant_results: Dictionary mapping tenant IDs to tuples of (trace_ids, session_ids)
    """
    now_ts = datetime.now(timezone.utc).timestamp()
    sorted_set_key = settings.RUN_DELETE_SORTED_SET_KEY
    limit = settings.RUN_DELETE_MAX_QUERY_SIZE

    async with redis.aredis_pool() as aredis:
        for tenant_id, (trace_ids, session_ids) in tenant_results.items():
            for i in range(0, len(trace_ids), limit):
                chunk = trace_ids[i : i + limit]

                payload = {
                    "tenant_id": tenant_id,
                    "session_ids": session_ids,
                    "trace_ids": chunk,
                }

                await aredis.zadd(
                    sorted_set_key,
                    {orjson.dumps(payload).decode(): now_ts},
                )


@trace_job
async def process_batched_trace_deletes(ctx: Dict[Any, Any]) -> None:
    """
    Pop batched run‑delete requests off a Redis sorted‑set and:
      1. delete matching ClickHouse rows (runs + feedbacks + mv)
      2. Deletes s3 objects for every run in the batch
    """
    async with redis.aredis_pool() as aredis:
        sorted_set_key = settings.RUN_DELETE_SORTED_SET_KEY
        batch = await aredis.zrange(
            sorted_set_key,
            0,
            settings.RUN_DELETE_BATCH_SIZE - 1,
            withscores=True,
        )

        if not batch:
            await logger.ainfo("No pending run deletes found")
            return

        tenant_ids: Set[str] = set()
        session_ids: Set[str] = set()
        trace_ids: Set[str] = set()

        batch_items: list[str] = []

        # Process metadata deletions in batches by tenant
        tenant_metadata_map: dict[str, list[dict[str, str]]] = {}

        for raw_item, _score in batch:
            data = orjson.loads(raw_item)
            t_id: str = data["tenant_id"]

            if data.get("deletion_type") == "metadata":
                metadata = data["metadata"]
                if t_id not in tenant_metadata_map:
                    tenant_metadata_map[t_id] = []
                tenant_metadata_map[t_id].append(metadata)
            else:
                s_id: str = data.get("session_id")
                s_ids: list[str] = data.get("session_ids", [])
                r_ids: list[str] = data["trace_ids"]
                if not s_id and not s_ids:
                    logger.warning(
                        "No session_id or session_ids found in data", data=data
                    )
                    continue

                tenant_ids.add(t_id)
                if s_id:
                    session_ids.add(s_id)
                if s_ids:
                    session_ids.update(s_ids)
                trace_ids.update(r_ids)

            batch_items.append(raw_item)

            if len(trace_ids) >= settings.RUN_DELETE_MAX_QUERY_SIZE:
                logger.info("Reached query max size limit, stopping processing")
                break

        if tenant_metadata_map:
            try:
                tenant_results = await _query_traces_by_metadata_batch(
                    tenant_metadata_map
                )

                total_trace_ids = sum(
                    len(trace_ids) for trace_ids, _ in tenant_results.values()
                )
                if total_trace_ids >= settings.RUN_DELETE_MAX_QUERY_SIZE:
                    current_count = 0
                    tenant_results_to_delete = {}
                    tenant_results_to_requeue = {}

                    for tenant_id, (
                        trace_ids_list,
                        session_ids_list,
                    ) in tenant_results.items():
                        if (
                            current_count + len(trace_ids_list)
                            <= settings.RUN_DELETE_MAX_QUERY_SIZE
                        ):
                            # Include entire tenant in current batch
                            tenant_results_to_delete[tenant_id] = (
                                trace_ids_list,
                                session_ids_list,
                            )
                            current_count += len(trace_ids_list)
                        elif current_count < settings.RUN_DELETE_MAX_QUERY_SIZE:
                            remaining_capacity = (
                                settings.RUN_DELETE_MAX_QUERY_SIZE - current_count
                            )

                            # Take first N trace IDs
                            trace_ids_to_process = trace_ids_list[:remaining_capacity]
                            session_ids_to_process = session_ids_list[
                                :remaining_capacity
                            ]

                            # Remaining trace IDs go to requeue
                            trace_ids_to_requeue = trace_ids_list[remaining_capacity:]
                            session_ids_to_requeue = session_ids_list[
                                remaining_capacity:
                            ]

                            if trace_ids_to_process:
                                tenant_results_to_delete[tenant_id] = (
                                    trace_ids_to_process,
                                    session_ids_to_process,
                                )
                                current_count += len(trace_ids_to_process)

                            if trace_ids_to_requeue:
                                tenant_results_to_requeue[tenant_id] = (
                                    trace_ids_to_requeue,
                                    session_ids_to_requeue,
                                )
                        else:
                            tenant_results_to_requeue[tenant_id] = (
                                trace_ids_list,
                                session_ids_list,
                            )

                    if tenant_results_to_requeue:
                        await requeue_remaining_tenant_results(
                            tenant_results_to_requeue
                        )

                    tenant_results = tenant_results_to_delete

                for t_id, (
                    metadata_trace_ids,
                    metadata_session_ids,
                ) in tenant_results.items():
                    if metadata_trace_ids:
                        tenant_ids.add(t_id)
                        session_ids.update(metadata_session_ids)
                        trace_ids.update(metadata_trace_ids)
            except Exception as e:
                await logger.aerror(
                    "Failed to query traces for batched metadata deletion",
                    error=str(e),
                )
                raise

        if not trace_ids:
            await logger.ainfo("No traces to delete, skipping")
        else:
            tenant_uuids = [UUID(t) for t in tenant_ids]
            session_uuids = [UUID(s) for s in session_ids]
            trace_uuids = [UUID(r) for r in trace_ids]

            s3_url_offsets = await get_s3_url_offsets(
                tenant_ids=tenant_uuids,
                session_ids=session_uuids,
                trace_ids=trace_uuids,
            )

            if s3_url_offsets:
                async with redis.async_queue(settings.ADHOC_QUEUE) as queue:
                    await queue.enqueue(
                        "zero_out_or_delete_s3_objects",
                        scheduled=(
                            datetime.now(timezone.utc)
                            + timedelta(seconds=settings.RUN_DELETE_S3_ASSETS_DELAY_SEC)
                        ).timestamp(),
                        paths_to_ranges=s3_url_offsets,
                        retry_delay=DELETE_RETRY_DELAY,
                        timeout=DELETE_TIMEOUT,
                        retries=DELETE_RETRIES,
                        retry_backoff=True,
                    )

            async def delete_feedbacks_postgres():
                async with asyncpg_conn() as db:
                    await db.execute(
                        """
                        DELETE FROM feedbacks
                        WHERE run_id = ANY($1::uuid[])
                        AND tenant_id = ANY($2::uuid[])
                        AND session_id = ANY($3::uuid[])
                        """,
                        trace_uuids,
                        tenant_uuids,
                        session_uuids,
                    )

            delete_tasks = [
                delete_runs_ch(
                    tenant_uuids,
                    session_uuids,
                    trace_uuids,
                ),
                delete_feedbacks_postgres(),
            ]

            await asyncio.gather(*delete_tasks)

        if batch_items:
            await aredis.zrem(sorted_set_key, *batch_items)

        await logger.ainfo(
            "Processed trace delete requests",
            items=len(batch_items),
            traces=len(trace_ids),
            tenants=len(tenant_ids),
        )

        #  Re-enqueue if more work remains
        remaining = await aredis.zcard(sorted_set_key)
        if remaining >= settings.RUN_DELETE_MIN_REMAINING_RUNS_REQUEUE:
            async with redis.async_queue(settings.ADHOC_QUEUE) as queue:
                await queue.enqueue(
                    "process_batched_trace_deletes",
                    scheduled=(
                        datetime.now(timezone.utc)
                        + timedelta(seconds=settings.RUN_DELETE_REENQUEUE_DELAY_SEC)
                    ).timestamp(),
                    retry_delay=DELETE_RETRY_DELAY,
                    timeout=settings.RUN_DELETE_JOB_TIMEOUT_SEC,
                    retries=DELETE_RETRIES,
                    retry_backoff=True,
                )

        #  Schedule join-table cleanup for the next day
        async with redis.async_queue(settings.ADHOC_QUEUE) as queue:
            await queue.enqueue(
                "background_cleanup_runs_trace_id",
                tenant_ids=list(tenant_ids),
                session_ids=list(session_ids),
                trace_ids=list(trace_ids),
                scheduled=(
                    datetime.now(timezone.utc)
                    + timedelta(seconds=settings.RUN_TRACE_ID_CLEANUP_DELAY_SEC)
                ).timestamp(),
                key=f"cleanup_runs_trace_id:{hashlib.sha256(','.join(sorted(trace_ids)).encode('utf-8')).hexdigest()}",
                retry_delay=DELETE_RETRY_DELAY,
                timeout=DELETE_TIMEOUT,
                retries=DELETE_RETRIES,
                retry_backoff=True,
            )


@trace_job
async def background_cleanup_runs_trace_id(
    ctx: Dict[Any, Any],
    tenant_ids: list[str],
    session_ids: list[str],
    trace_ids: list[str],
) -> None:
    await delete_runs_trace_id_ch(
        [UUID(t) for t in tenant_ids],
        [UUID(s) for s in session_ids],
        [UUID(r) for r in trace_ids],
    )


@trace_job
async def background_delete_stored_assets(
    ctx: Dict[Any, Any], auth_dict: dict[str, Any], session_id: list[str]
) -> None:
    auth: BaseAuthInfo = await arun_in_executor(parse_auth_info, auth_dict)
    tenant_id_bytes = str(auth.tenant_id).encode("utf-8")
    session_id_bytes = str(session_id).encode("utf-8")
    hashed_tenant_id = hashlib.sha256(tenant_id_bytes).hexdigest()
    hashed_session_id = hashlib.sha256(session_id_bytes).hexdigest()

    deletion_tasks = []
    deletion_manager = BaseCloudStorageDeletionManager.get_instance()

    for bucket in ["", *S3_SINGLE_REGION_BUCKETS]:
        for root_prefix in ["", *settings.S3_TRACE_TIER_PREFIX_MAP.values()]:
            for object_prefix in IO_TYPES:
                # use hashed session id for obfuscation
                object_path = f"{object_prefix}/{hashed_tenant_id}/{hashed_session_id}"

                deletion_tasks.append(
                    deletion_manager.delete_storage_prefix(
                        f"{bucket + '/' if bucket else ''}{root_prefix + '/' if root_prefix else ''}{object_path}",
                        bucket,
                    )
                )

    await asyncio.gather(*deletion_tasks)


@trace_job
async def process_all_traces_trace_transactions(ctx: Dict[Any, Any]):
    await traces.AllTracesTraceProcessor.process_trace_transactions()


@trace_job
async def process_longlived_traces_trace_transactions(ctx: Dict[Any, Any]):
    await traces.LonglivedTracesTraceProcessor.process_trace_transactions()


@trace_job
async def report_trace_usage(ctx: Dict[Any, Any]):
    if settings.PHONE_HOME_ENABLED:
        await traces.SelfHostedTraceTransactionReporter.report_usage()
    else:
        await traces.TraceTransactionReporter.report_usage()


@trace_job
async def retry_trace_reporting_errors(ctx: Dict[Any, Any]):
    if settings.PHONE_HOME_ENABLED:
        await (
            traces.SelfHostedTraceTransactionReporter.retry_report_failed_transactions()
        )
    else:
        await traces.TraceTransactionReporter.retry_report_failed_transactions()


@trace_job
async def report_nodes_usage(ctx: Dict[Any, Any]):
    nodes.NodesTransactionReporter.clear_dedup_keys()
    await nodes.NodesTransactionReporter.report_usage()


@trace_job
async def retry_nodes_reporting_errors(ctx: Dict[Any, Any]):
    nodes.NodesTransactionReporter.clear_dedup_keys()
    await nodes.NodesTransactionReporter.retry_report_failed_transactions()


@trace_job
async def report_metronome_seat_usage(ctx: Dict[Any, Any]):
    await seats.SeatTransactionReporter.report_usage()


@trace_job
async def retry_metronome_seat_errors(ctx: Dict[Any, Any]):
    await seats.SeatTransactionReporter.retry_report_failed_transactions()


@trace_job
async def cron_record_seat_heartbeats(ctx: Dict[Any, Any]):
    await seats.SeatTransactionReporter.record_heartbeats()


@trace_job
async def record_seat_heartbeats(ctx: Dict[Any, Any], org_id: str):
    await identities_crud.record_seat_count_heartbeat(UUID(org_id))


@trace_job
async def record_seat_heartbeats_batch(ctx: Dict[Any, Any], org_ids: list[str]):
    await identities_crud.record_seat_count_heartbeats_batch(
        [UUID(org_id) for org_id in org_ids]
    )


@trace_job
async def update_metronome_cache_for_org(ctx: Dict[Any, Any], org_id: str):
    await org_metronome_cache.update(org_id)


@trace_job
async def sync_served_dataset_cron(
    ctx: Dict[Any, Any],
):
    await sync_served_datasets_cron()


@trace_job
async def fetch_and_execute_examples(
    ctx: Dict[Any, Any],
    *args,
    **kwargs,
):
    await fetch_and_execute_examples_serializable(*args, **kwargs)


@trace_job
async def execute_playground_rules(
    ctx: Dict[Any, Any],
    *args,
    **kwargs,
):
    await execute_playground_rules_serializable(*args, **kwargs)


@trace_job
async def setup_self_hosted_customer(
    ctx: Dict[Any, Any],
    customer_id: str,
):
    await self_hosted_customers.setup_self_hosted_customer(UUID(customer_id))


@trace_job
async def add_metronome_customers_for_self_hosted_customer_cron(
    ctx: Dict[Any, Any],
):
    await self_hosted_customers.add_metronome_customers_for_self_hosted_customers()


@trace_job
async def cron_schedule_bulk_exports(ctx: Dict[Any, Any]):
    await bulk_export_jobs.cron_schedule_bulk_exports()


@trace_job
async def cron_schedule_onboarding_emails(ctx: Dict[Any, Any]):
    await onboarding_emails_jobs.cron_schedule_onboarding_emails()


@trace_job
async def prompt_optimization(ctx: Dict[Any, Any], *args, **kwargs):
    await optimize.perform_optimization(*args, **kwargs)


@trace_job
async def cron_sync_org_charts(ctx: Dict[Any, Any]):
    await charts_jobs.sync_org_charts()


@trace_job
async def aggregate_and_check_alert_rule_worker(
    ctx: Dict[Any, Any],
    tenant_id: str,
    session_id: str,
    alert_rule_id: str,
    minute_key: str,
):
    await aggregate_and_check_alert_rule(
        tenant_id, session_id, alert_rule_id, minute_key
    )


@trace_job
async def prefetch_prebuilt_dashboards(ctx: Dict[Any, Any]):
    current_time = datetime.now(tz=timezone.utc)
    await charts_jobs.prefetch_prebuilt_dashboards_job(current_time)
