from __future__ import annotations

import asyncio
import re
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

import asyncpg
from fastapi import HTTPException
from lc_database.database import asyncpg_conn, asyncpg_pool, kwargs_to_pgpos

from app.api.auth import AuthInfo
from app.api.auth.schemas import Permissions
from app.hub import schema
from app.hub.api.pagination import PaginationQueryParams
from app.hub.utils import convert_to_runnable_sequence
from app.retry import retry_asyncpg

_repo_base_select_sql = """
    SELECT
    hr.id,
    hr.tenant_id,
    hr.is_public,
    hr.repo_handle,
    hr.description,
    hr.created_at,
    hr.updated_at,
    hr.tags,
    hr.readme,
    hr.is_archived,
    hr.original_repo_id,
    hr.upstream_repo_id,
    t.tenant_handle AS owner,
    CONCAT(
        CASE WHEN t.tenant_handle IS NOT NULL THEN
            CONCAT(t.tenant_handle, '/')
        ELSE
            ''
        END,
        hr.repo_handle
    ) AS full_name
    FROM hub_repos hr
    JOIN tenants t ON t.id = hr.tenant_id
"""

# todo: optimize commit download count
_repo_fields_sql = """
    hr.id,
    hr.tenant_id,
    hr.is_public, 
    hr.repo_handle,
    hr.description,
    hr.created_at,
    hr.updated_at,
    hr.tags,
    hr.readme,
    hr.is_archived,
    hr.original_repo_id,
    hr.upstream_repo_id,
    (
        SELECT COUNT(*) FROM hub_repo_likes 
        WHERE hub_repo_likes.repo_id = hr.id
    ) AS num_likes,
    (
        SELECT COUNT(*) FROM hub_commit_downloads 
        WHERE hub_commit_downloads.commit_id IN (
            SELECT id FROM hub_commits WHERE hub_commits.repo_id = hr.id
        )
        AND hub_commit_downloads.download_type = 'api'
    ) AS num_downloads,
    (
        SELECT COUNT(*) FROM hub_commit_downloads 
        WHERE hub_commit_downloads.commit_id IN (
            SELECT id FROM hub_commits WHERE hub_commits.repo_id = hr.id
        )
        AND hub_commit_downloads.download_type = 'web'
    ) AS num_views,
    (
        SELECT 
            tenant_handle
        FROM tenants 
        WHERE tenants.id = hr.tenant_id
    ) AS owner,
    (
        SELECT
            CONCAT(
                CASE WHEN tenant_handle IS NOT NULL THEN 
                    CONCAT(tenant_handle, '/')
                ELSE
                    ''
                END,
                hr.repo_handle
            )
        FROM tenants
        WHERE tenants.id = hr.tenant_id
    ) AS full_name,
    (
        SELECT commit_hash FROM hub_commits
        WHERE hub_commits.repo_id = hr.id
        ORDER BY created_at DESC
        LIMIT 1
    ) AS last_commit_hash,
    (
        SELECT COUNT(*) FROM hub_commits
        WHERE hub_commits.repo_id = hr.id
    ) AS num_commits,
    (
        SELECT CONCAT(
            CASE WHEN original_owner.tenant_handle IS NOT NULL THEN
                original_owner.tenant_handle
            ELSE
                '_null'         
            END,
            '/',
            original_repo.repo_handle
        ) FROM hub_repos original_repo
        INNER JOIN tenants original_owner ON original_owner.id = original_repo.tenant_id
        WHERE original_repo.id = hr.original_repo_id
    ) AS original_repo_full_name,
    (
        SELECT CONCAT(
            CASE WHEN upstream_owner.tenant_handle IS NOT NULL THEN
                CONCAT(upstream_owner.tenant_handle, '/')
            ELSE
                ''   
            END,
            upstream_repo.repo_handle
        ) FROM hub_repos upstream_repo
        INNER JOIN tenants upstream_owner ON upstream_owner.id = upstream_repo.tenant_id
        WHERE upstream_repo.id = hr.upstream_repo_id
    ) AS upstream_repo_full_name
"""

_repo_liked_by_auth_user_sql = """,
    (
        SELECT EXISTS(
            SELECT 1 FROM hub_repo_likes 
            WHERE hub_repo_likes.repo_id = hr.id 
            AND hub_repo_likes.liked_by = $tenant_id
        )
    ) AS liked_by_auth_user
"""

_repo_fts_expr_sql = """
(
    to_tsvector('english', coalesce(repo_handle, '')) ||
    to_tsvector('english', coalesce(tenants.tenant_handle, '')) ||
    to_tsvector('english', coalesce(description, '')) ||
    to_tsvector('english', coalesce(readme, '[]')) ||
    jsonb_to_tsvector('english', coalesce(tags, '[]'), '["string"]')
)
"""


def _build_comment(suboperation: str):
    return f"/*application='hub',action='repos.{suboperation}'*/"


def _build_repo_filter_and_where(
    filter_params: schema.ReposFilterQueryParams,
    use_exact_search_for_prompts: bool = False,
) -> tuple[list[str], Dict[str, Any]]:
    sql_kwargs: Dict[str, Any] = {}
    and_wheres = []

    if filter_params.tenant_id is not None:
        sql_kwargs["tenant_id"] = filter_params.tenant_id
        and_wheres.append("tenants.id = $tenant_id")

    if filter_params.tenant_handle is not None and filter_params.tenant_id is None:
        sql_kwargs["tenant_handle"] = filter_params.tenant_handle
        and_wheres.append("tenants.tenant_handle = $tenant_handle")

    if filter_params.tags is not None:
        sql_kwargs["tags"] = filter_params.tags
        and_wheres.append("hr.tags @> $tags")

    if filter_params.has_commits:
        and_wheres.append(
            """EXISTS(
                SELECT 1 FROM hub_commits
                WHERE hub_commits.repo_id = hr.id
            )"""
        )

    if filter_params.tag_value_id:
        sql_kwargs["tag_value_id"] = filter_params.tag_value_id
        and_wheres.append(
            """
            hr.id IN (
                SELECT t.resource_id
                FROM taggings t
                JOIN tag_values tv ON t.tag_value_id = tv.id
                JOIN tag_keys tk ON tv.tag_key_id = tk.id
                WHERE t.resource_type = 'prompt'
                AND t.tag_value_id = ANY($tag_value_id::uuid[])
                AND tk.tenant_id = $tenant_id
                GROUP BY t.resource_id, t.resource_type
                HAVING COUNT(*) = (
                    SELECT COUNT(*) FROM unnest($tag_value_id::uuid[])
                )
            )
            """
        )

    if filter_params.query:
        if use_exact_search_for_prompts:
            sql_kwargs["query"] = filter_params.query
            and_wheres.append(
                """(
                repo_handle ILIKE '%' || $query || '%' OR
                tenants.tenant_handle ILIKE $query || '%' OR
                description ILIKE '%' || $query || '%'
                )"""
            )
        else:
            if re.match(r"^[a-z][a-z0-9-]*/[a-z][a-z0-9-_]*$", filter_params.query):
                # it's a full name, so filter tenant handle, search by repo name
                owner, repo = filter_params.query.split("/")

                # dont override an existing tenant_handle filter
                sql_kwargs["tenant_handle"] = sql_kwargs.get("tenant_handle", owner)
                and_wheres.append("tenants.tenant_handle = $tenant_handle")

                # only search repo name
                sql_kwargs["query"] = repo
                and_wheres.append(
                    "(to_tsvector('english', coalesce(repo_handle, '')) @@ plainto_tsquery('english', $query))"
                )
            else:
                sql_kwargs["query"] = " & ".join(
                    [term + ":*" for term in filter_params.query.split()]
                )
                and_wheres.append(
                    f"""(
                        {_repo_fts_expr_sql} @@ to_tsquery('english', $query)
                    )"""
                )

    if filter_params.is_archived == "true":
        and_wheres.append("is_archived = TRUE")
    elif filter_params.is_archived == "false" or filter_params.is_archived is None:
        and_wheres.append("is_archived = FALSE")
    # else it's allow, so no filter

    if filter_params.is_public == "true":
        and_wheres.append("is_public = TRUE")
    elif filter_params.is_public == "false":
        and_wheres.append("is_public = FALSE")
    # else no filter on public

    if filter_params.upstream_repo_handle:
        sql_kwargs["upstream_repo_handle"] = filter_params.upstream_repo_handle
        upstream_repo_owner_and = ""
        if filter_params.upstream_repo_owner:
            sql_kwargs["upstream_repo_owner"] = filter_params.upstream_repo_owner
            upstream_repo_owner_and = (
                "AND upstream_tenant.tenant_handle = $upstream_repo_owner"
            )
        else:
            upstream_repo_owner_and = "AND upstream_tenant.id = $tenant_id"

        and_wheres.append(
            f"""
            upstream_repo_id = (
                SELECT upstream_repo.id FROM hub_repos upstream_repo
                INNER JOIN tenants upstream_tenant ON upstream_repo.tenant_id = upstream_tenant.id
                WHERE upstream_repo.repo_handle=$upstream_repo_handle 
                {upstream_repo_owner_and}
            )
            """
        )

    return and_wheres, sql_kwargs


def _build_repo_sort(
    sort_params: schema.ReposSortQueryParams,
) -> str:
    order_by_field = "num_likes"
    order_by_direction = "DESC"
    if sort_params.sort_field is not None:
        if (
            sort_params.sort_field == "num_likes"
            and sort_params.sort_direction == "desc"
        ):
            order_by_field = "num_likes"
            order_by_direction = "DESC"
        elif (
            sort_params.sort_field == "num_downloads"
            and sort_params.sort_direction == "desc"
        ):
            order_by_field = "num_downloads"
            order_by_direction = "DESC"
        elif (
            sort_params.sort_field == "num_views"
            and sort_params.sort_direction == "desc"
        ):
            order_by_field = "num_views"
            order_by_direction = "DESC"
        elif (
            sort_params.sort_field == "updated_at"
            and sort_params.sort_direction == "desc"
        ):
            order_by_field = "hr.updated_at"
            order_by_direction = "DESC"
        elif (
            sort_params.sort_field == "relevance"
            and sort_params.sort_direction == "desc"
        ):
            order_by_field = (
                f"ts_rank({_repo_fts_expr_sql}, plainto_tsquery('english', $query))"
            )
            order_by_direction = "DESC"
        else:
            raise HTTPException(
                status_code=400,
                detail="Invalid sort field / direction combination",
            )

    return f"{order_by_field} {order_by_direction}, hr.created_at {order_by_direction}"


@retry_asyncpg
async def get_repos_public(
    db: asyncpg.Connection,
    *,
    pagination_params: PaginationQueryParams = PaginationQueryParams(),
    filter_params: schema.ReposFilterQueryParams = schema.ReposFilterQueryParams(),
    sort_params: schema.ReposSortQueryParams = schema.ReposSortQueryParams(),
) -> tuple[List[schema.RepoWithLookups], int]:
    """Get a list of repos."""

    and_where, where_kwargs = _build_repo_filter_and_where(filter_params)
    and_where.append("is_public = TRUE")
    where_sql = " AND ".join(and_where)

    order_sql = _build_repo_sort(sort_params)

    sql_template = f"""SELECT 
            {_repo_fields_sql}
            FROM hub_repos hr
            INNER JOIN tenants ON tenants.id = hr.tenant_id
            WHERE {where_sql} 
            ORDER BY {order_sql}
            LIMIT $limit OFFSET $offset"""

    sql_kwargs = {
        **where_kwargs,
        "limit": pagination_params.limit,
        "offset": pagination_params.offset,
    }

    sql_query = kwargs_to_pgpos(sql_template, sql_kwargs)

    sql_template_total = f"""
        SELECT COUNT(*) FROM hub_repos hr
        INNER JOIN tenants ON tenants.id = hr.tenant_id 
        WHERE {where_sql}"""
    sql_query_total = kwargs_to_pgpos(sql_template_total, sql_kwargs)

    async with asyncpg_pool() as pool:
        repos, total = await asyncio.gather(
            db.fetch(
                sql_query.sql + _build_comment("get_repos.repos"), *sql_query.args
            ),
            pool.fetchval(
                sql_query_total.sql + _build_comment("get_repos.total"),
                *sql_query_total.args,
            ),
        )
    return [schema.RepoWithLookups(**repo) for repo in repos], total


@retry_asyncpg
async def get_repos(
    db: asyncpg.Connection,
    auth: AuthInfo,
    *,
    pagination_params: PaginationQueryParams = PaginationQueryParams(),
    filter_params: schema.ReposFilterQueryParams = schema.ReposFilterQueryParams(),
    sort_params: schema.ReposSortQueryParams = schema.ReposSortQueryParams(),
    with_latest_manifest: bool = False,
) -> tuple[List[schema.RepoWithLookups], int]:
    """Get a list of repos."""
    and_where, where_kwargs = _build_repo_filter_and_where(
        filter_params,
        auth.tenant_config.organization_config.use_exact_search_for_prompts,
    )
    and_where.append("(is_public = TRUE OR tenant_id = $tenant_id)")
    where_kwargs["tenant_id"] = auth.tenant_id
    where_sql = " AND ".join(and_where)

    order_sql = _build_repo_sort(sort_params)

    sql_template = f"""SELECT 
            {_repo_fields_sql}{_repo_liked_by_auth_user_sql}
            FROM hub_repos hr
            INNER JOIN tenants ON tenants.id = hr.tenant_id
            WHERE {where_sql} 
            ORDER BY {order_sql}
            LIMIT $limit OFFSET $offset"""

    sql_kwargs = {
        **where_kwargs,
        "limit": pagination_params.limit,
        "offset": pagination_params.offset,
    }

    sql_query = kwargs_to_pgpos(sql_template, sql_kwargs)

    sql_template_total = f"""SELECT COUNT(*) FROM hub_repos hr INNER JOIN tenants ON tenants.id = hr.tenant_id WHERE {where_sql}"""
    sql_query_total = kwargs_to_pgpos(sql_template_total, sql_kwargs)

    async with asyncpg_pool() as pool:
        repos, total = await asyncio.gather(
            db.fetch(
                sql_query.sql + _build_comment("get_repos.repos"), *sql_query.args
            ),
            pool.fetchval(
                sql_query_total.sql + _build_comment("get_repos.total"),
                *sql_query_total.args,
            ),
        )
    repos_with_lookups = [schema.RepoWithLookups(**repo) for repo in repos]
    if not with_latest_manifest:
        return repos_with_lookups, total

    repos_with_manifests = []

    from app.hub.crud import commits

    for repo in repos_with_lookups:
        repo_dict = dict(repo)
        if repo.num_commits > 0:
            commit, examples = await commits._get_commit_manifest(db, repo, None, True)

            repo_dict["latest_commit_manifest"] = {
                "commit_hash": commit.commit_hash,
                "manifest": convert_to_runnable_sequence(commit.manifest),
                "examples": examples,
            }

        repos_with_manifests.append(schema.RepoWithLookups(**repo_dict))

    return repos_with_manifests, total


@retry_asyncpg
async def get_repo_base_public(
    db: asyncpg.Connection,
    repo: str,
    owner: str,
) -> schema.RepoBaseInfo:
    sql_template = f"""
        {_repo_base_select_sql}
        WHERE hr.repo_handle = $repo_handle
        AND hr.tenant_id = (SELECT id FROM tenants WHERE tenant_handle = $owner)
        AND hr.is_public = TRUE
    """

    sql_query = kwargs_to_pgpos(
        sql_template,
        {
            "repo_handle": repo,
            "owner": owner,
        },
    )

    row = await db.fetchrow(
        sql_query.sql + _build_comment("get_repo_base"), *sql_query.args
    )
    if row is None:
        raise HTTPException(status_code=404, detail=f"Repo {owner}/{repo} not found")

    return schema.RepoBaseInfo(**row)


@retry_asyncpg
async def get_repo_base(
    db: asyncpg.Connection,
    auth: AuthInfo,
    repo: str,
    owner: Optional[str] = None,
) -> schema.RepoBaseInfo:
    if owner is None or owner == auth.tenant_handle:
        tenant_clause = "hr.tenant_id = $tenant_id"
    else:
        tenant_clause = "hr.tenant_id = (SELECT id FROM tenants WHERE tenant_handle = $owner) AND (hr.is_public = TRUE OR hr.tenant_id = $tenant_id)"

    sql_template = f"""
        {_repo_base_select_sql}
        WHERE hr.repo_handle = $repo_handle
        AND {tenant_clause}
    """

    sql_query = kwargs_to_pgpos(
        sql_template, {"repo_handle": repo, "owner": owner, "tenant_id": auth.tenant_id}
    )

    row = await db.fetchrow(
        sql_query.sql + _build_comment("get_repo_base"), *sql_query.args
    )
    if row is None:
        raise HTTPException(status_code=404, detail=f"Repo {owner}/{repo} not found")

    return schema.RepoBaseInfo(**row)


# use get_repo_base_public instead
# this makes unnecessary calculations unless you need them, including:
# - num_likes
# - num_downloads
# - num_views
@retry_asyncpg
async def get_repo_by_full_name_public(
    db: asyncpg.Connection, owner: str, repo: str
) -> schema.RepoWithLookups:
    """Get a repo."""
    sql_kwargs: Dict[str, Any] = {
        "tenant_handle": owner,
        "repo_handle": repo,
    }

    sql_template = f"""SELECT {_repo_fields_sql}
        FROM hub_repos hr WHERE tenant_id = (
            SELECT id FROM tenants WHERE tenant_handle = $tenant_handle
        ) AND repo_handle = $repo_handle 
        AND is_public = TRUE"""

    sql_query = kwargs_to_pgpos(sql_template, sql_kwargs)

    row = await db.fetchrow(
        sql_query.sql + _build_comment("get_repo_by_full_name_public"), *sql_query.args
    )
    if row is None:
        raise HTTPException(status_code=404, detail=f"Repo {owner}/{repo} not found")
    return schema.RepoWithLookups(**row)


# use get_repo_base instead
# this makes unnecessary calculations unless you need them, including:
# - num_likes
# - num_downloads
# - num_views
@retry_asyncpg
async def get_repo_by_full_name(
    db: asyncpg.Connection, auth: AuthInfo, repo: str, owner: Optional[str] = None
) -> schema.RepoWithLookups:
    """Get a repo."""
    sql_kwargs: Dict[str, Any] = {
        "tenant_handle": owner,
        "repo_handle": repo,
        "tenant_id": auth.tenant_id,
    }

    if owner is None:
        sql_template = f"""
            SELECT {_repo_fields_sql}{_repo_liked_by_auth_user_sql}
            FROM hub_repos hr
            WHERE tenant_id = $tenant_id
            AND repo_handle = $repo_handle
        """
    else:
        sql_template = f"""
            SELECT {_repo_fields_sql}{_repo_liked_by_auth_user_sql}
            FROM hub_repos hr
            WHERE tenant_id = (
                SELECT id FROM tenants WHERE tenant_handle = $tenant_handle
            )
            AND repo_handle = $repo_handle
            AND (is_public = TRUE OR tenant_id = $tenant_id)
        """

    sql_query = kwargs_to_pgpos(sql_template, sql_kwargs)

    row = await db.fetchrow(
        sql_query.sql + _build_comment("get_repo_by_full_name"), *sql_query.args
    )
    if row is None:
        raise HTTPException(status_code=404, detail=f"Repo {owner}/{repo} not found")
    return schema.RepoWithLookups(**row)


@retry_asyncpg
async def fork_repo(
    auth: AuthInfo,
    repo: str,
    body: schema.ForkRepoRequest,
    owner: Optional[str] = None,
) -> schema.RepoWithLookups:
    fields_sql = _repo_fields_sql.replace("hub_commits", "forked_commits")
    repo_auth_str = (
        "hub_repos.tenant_id = $new_tenant_id"
        if owner is None or owner == auth.tenant_handle
        else "is_public = TRUE AND tenants.tenant_handle = $old_tenant_handle"
    )
    sql, args = kwargs_to_pgpos(
        f"""
        WITH old_repo AS (
            SELECT hub_repos.id, hub_repos.readme, hub_repos.original_repo_id, hub_repos.tags 
            FROM hub_repos
            INNER JOIN tenants ON tenants.id = hub_repos.tenant_id
            WHERE hub_repos.repo_handle = $old_repo_handle
            AND {repo_auth_str}
        ),
        old_commits AS (
            SELECT
                id,
                commit_hash,
                manifest,
                manifest_sha,
                parent_id,
                created_at,
                updated_at,
                example_run_ids,
                original_commit_id,
                ls_user_id,
                gen_random_uuid() AS new_id
            FROM hub_commits
            WHERE repo_id = (SELECT id FROM old_repo)
        ),
        forked_repo AS (
            INSERT INTO hub_repos (
                tenant_id, repo_handle, description, is_public, tags, readme, upstream_repo_id, original_repo_id
            )
            SELECT
                $new_tenant_id,
                $new_repo_handle,
                $description,
                $is_public,
                CASE
                    WHEN $tags::jsonb = '[]'::jsonb OR $tags IS NULL
                    THEN old_repo.tags 
                    ELSE $tags 
                END,
                $readme,
                old_repo.id,
                CASE WHEN old_repo.original_repo_id IS NULL THEN old_repo.id ELSE old_repo.original_repo_id END
            FROM old_repo
            ON CONFLICT DO NOTHING
            RETURNING *
        ),
        forked_commits AS (
            INSERT INTO hub_commits (
                id,
                parent_id,
                repo_id,
                manifest,
                manifest_sha,
                commit_hash,
                created_at,
                updated_at,
                example_run_ids,
                original_commit_id,
                upstream_commit_id,
                ls_user_id
            )
            SELECT
                oc.new_id,
                (SELECT new_id FROM old_commits WHERE old_commits.id = oc.parent_id),
                (SELECT id FROM forked_repo),
                oc.manifest,
                oc.manifest_sha,
                oc.commit_hash,
                oc.created_at,
                oc.updated_at,
                oc.example_run_ids,
                CASE
                    WHEN oc.original_commit_id IS NULL THEN oc.id
                    ELSE oc.original_commit_id
                END,
                oc.id,
                oc.ls_user_id
            FROM old_commits oc
            WHERE EXISTS (SELECT id FROM forked_repo)
            RETURNING *
        )
        SELECT {fields_sql}, false AS liked_by_auth_user 
        FROM forked_repo hr
        """,
        {
            "new_tenant_id": auth.tenant_id,
            "new_repo_handle": body.repo_handle,
            "description": body.description,
            "readme": body.readme,
            "is_public": body.is_public,
            "tags": body.tags or [],
            "old_tenant_handle": owner,
            "old_repo_handle": repo,
        },
    )
    async with asyncpg_conn() as db:
        newrepo = await db.fetchrow(sql + _build_comment("fork_repo"), *args)
        if newrepo is None:
            sql, args = kwargs_to_pgpos(
                f"""SELECT 1 FROM hub_repos
                INNER JOIN tenants ON tenants.id = hub_repos.tenant_id
                AND repo_handle = $old_repo_handle
                AND {repo_auth_str}""",
                {
                    "old_tenant_handle": owner,
                    "old_repo_handle": repo,
                    "new_tenant_id": auth.tenant_id,
                },
            )
            old_repo = await db.fetchrow(
                sql,
                *args,
            )
            if old_repo is None:
                raise HTTPException(
                    status_code=404,
                    detail="Could not find repo to fork",
                )
            new_repo = await db.fetchrow(
                """SELECT 1 FROM hub_repos
                WHERE tenant_id = $1 AND repo_handle = $2""",
                auth.tenant_id,
                body.repo_handle,
            )
            if new_repo is not None:
                raise HTTPException(
                    status_code=409,
                    detail="Repo not forked because a repo with handle "
                    f"{body.repo_handle} already exists in owner.",
                )

            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while forking the repo. Please try again.",
            )
    return schema.RepoWithLookups(**newrepo)


@retry_asyncpg
async def create_repo(
    db: asyncpg.Connection,
    auth: AuthInfo,
    repo: schema.CreateRepoRequest,
    *,
    repo_id: UUID | None = None,
) -> schema.RepoWithLookups:
    """Create a repo."""
    if auth.public_sharing_disabled and repo.is_public:
        raise HTTPException(
            status_code=403,
            detail="Sharing resources is disabled for this organization.",
        )

    # additionally check DB to avoid caching
    public_sharing_disabled = await db.fetchval(
        "SELECT public_sharing_disabled FROM organizations WHERE id = $1",
        auth.organization_id,
    )
    if public_sharing_disabled is True and repo.is_public:
        raise HTTPException(
            status_code=403,
            detail="Sharing resources is disabled for this organization.",
        )
    created_repo = await db.fetchrow(
        f"""
        with new_repo as (
            INSERT INTO hub_repos (
                id, tenant_id, repo_handle, description, is_public, tags, readme
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7
            ) 
            ON CONFLICT (tenant_id, repo_handle) DO NOTHING
            RETURNING *
        )
        select {_repo_fields_sql}, false as liked_by_auth_user from new_repo hr"""
        + _build_comment("create_repo"),
        repo_id or uuid4(),
        auth.tenant_id,
        repo.repo_handle,
        repo.description,
        repo.is_public,
        repo.tags or [],
        repo.readme,
    )
    if created_repo is None:
        raise HTTPException(
            status_code=409,
            detail="Prompt not created because a prompt with handle "
            f"{repo.repo_handle} already exists in owner.",
        )
    return schema.RepoWithLookups(**created_repo)


@retry_asyncpg
async def update_repo(
    db: asyncpg.Connection,
    auth: AuthInfo,
    repo: str,
    fields: schema.UpdateRepoRequest,
    owner: Optional[str],
) -> schema.RepoWithLookups:
    """Update a repo."""
    # verify I am owner of the repo
    repo_obj = await get_repo_base(db=db, auth=auth, repo=repo, owner=owner)
    if repo_obj.tenant_id != auth.tenant_id:
        raise HTTPException(
            status_code=403,
            detail="You are not the owner of this repo.",
        )
    if fields.is_public is not None:
        if Permissions.PROMPTS_SHARE.value not in auth.identity_permissions:
            raise HTTPException(
                status_code=403,
                detail="You must have the repos:share permission to share/unshare repo.",
            )
        if auth.public_sharing_disabled and fields.is_public:
            raise HTTPException(
                status_code=403,
                detail="Sharing resources is disabled for this organization.",
            )
        # additionally check DB to avoid caching
        public_sharing_disabled = await db.fetchval(
            "SELECT public_sharing_disabled FROM organizations WHERE id = $1",
            auth.organization_id,
        )
        if public_sharing_disabled is True and fields.is_public:
            raise HTTPException(
                status_code=403,
                detail="Sharing resources is disabled for this organization.",
            )
    # only update the fields that are passed in
    updates: List[str] = []
    update_vals: Dict[str, Union[str, UUID, list[str], bool]] = {
        "id": repo_obj.id,
        "tenant_id": repo_obj.tenant_id,
    }
    # if fields.repo_handle is not None:
    #     updates.append("repo_handle = $repo_handle")
    #     update_vals["repo_handle"] = fields.repo_handle
    if fields.description is not None:
        updates.append("description = $description")
        update_vals["description"] = fields.description
    if fields.readme is not None:
        updates.append("readme = $readme")
        update_vals["readme"] = fields.readme
    if fields.tags is not None:
        updates.append("tags = $tags")
        update_vals["tags"] = fields.tags
    if fields.is_public is not None:
        updates.append("is_public = $is_public")
        update_vals["is_public"] = fields.is_public
    if fields.is_archived is not None:
        updates.append("is_archived = $is_archived")
        update_vals["is_archived"] = fields.is_archived

    if len(updates) == 0:
        raise HTTPException(
            status_code=400,
            detail="No fields to update",
        )

    update_str = ", ".join(updates)

    sql_template = f"""
        with updated_repo as (
            UPDATE hub_repos SET {update_str} 
            WHERE id = $id AND tenant_id = $tenant_id 
            RETURNING *
        )
        select {_repo_fields_sql}{_repo_liked_by_auth_user_sql} from updated_repo hr"""

    sql = kwargs_to_pgpos(sql_template, update_vals)
    updated_repo = await db.fetchrow(sql.sql + _build_comment("update_repo"), *sql.args)

    if updated_repo is None:
        # Crazy race condition case if a user deletes a
        # repo between get above and update
        raise HTTPException(status_code=404, detail=f"Repo {id} not found")
    return schema.RepoWithLookups(**updated_repo)


@retry_asyncpg
async def get_all_repo_tags_public(
    db: asyncpg.Connection,
    *,
    pagination_params: PaginationQueryParams = PaginationQueryParams(),
    filter_params: schema.ReposFilterQueryParams = schema.ReposFilterQueryParams(),
) -> List[schema.TagCount]:
    and_where, where_kwargs = _build_repo_filter_and_where(filter_params)
    and_where.append("is_public = TRUE")
    where_sql = " AND ".join(and_where)

    sql_kwargs: Dict[str, Any] = {
        "limit": pagination_params.limit,
        "offset": pagination_params.offset,
        **where_kwargs,
    }

    sql_template = f"""
        SELECT 
            jsonb_array_elements_text(tags) AS tag,
            COUNT(*) as count
        FROM hub_repos hr
        INNER JOIN tenants ON tenants.id = hr.tenant_id
        WHERE {where_sql}
        GROUP BY tag
        ORDER BY count DESC, tag ASC
        LIMIT $limit OFFSET $offset"""

    sql_query = kwargs_to_pgpos(sql_template, sql_kwargs)

    tags = await db.fetch(
        sql_query.sql + _build_comment("get_all_repo_tags_public"), *sql_query.args
    )
    return [schema.TagCount(**tag) for tag in tags]


@retry_asyncpg
async def get_all_repo_tags(
    db: asyncpg.Connection,
    auth: AuthInfo,
    *,
    pagination_params: PaginationQueryParams = PaginationQueryParams(),
    filter_params: schema.ReposFilterQueryParams = schema.ReposFilterQueryParams(),
) -> List[schema.TagCount]:
    and_where, where_kwargs = _build_repo_filter_and_where(
        filter_params,
        auth.tenant_config.organization_config.use_exact_search_for_prompts,
    )
    and_where.append("(is_public = TRUE OR tenant_id = $tenant_id)")
    where_kwargs["tenant_id"] = auth.tenant_id
    where_sql = " AND ".join(and_where)

    sql_kwargs: Dict[str, Any] = {
        "limit": pagination_params.limit,
        "offset": pagination_params.offset,
        **where_kwargs,
    }

    sql_template = f"""
        SELECT 
            jsonb_array_elements_text(tags) AS tag,
            COUNT(*) as count
        FROM hub_repos hr
        INNER JOIN tenants ON tenants.id = hr.tenant_id
        WHERE {where_sql}
        GROUP BY tag
        ORDER BY count DESC, tag ASC
        LIMIT $limit OFFSET $offset"""

    sql_query = kwargs_to_pgpos(sql_template, sql_kwargs)

    tags = await db.fetch(
        sql_query.sql + _build_comment("get_all_repo_tags"), *sql_query.args
    )
    return [schema.TagCount(**tag) for tag in tags]


@retry_asyncpg
async def delete_repo(
    db: asyncpg.Connection, auth: AuthInfo, repo: str, owner: Optional[str]
) -> None:
    """Delete a repo."""
    repo_obj = await get_repo_by_full_name(db, auth, repo, owner)
    if repo_obj.tenant_id != auth.tenant_id:
        raise HTTPException(
            status_code=403,
            detail="You are not the owner of this repo.",
        )
    await db.execute(
        """
        DELETE FROM hub_repos
        WHERE id = $1 AND tenant_id = $2
        """,
        repo_obj.id,
        auth.tenant_id,
    )

    return None
