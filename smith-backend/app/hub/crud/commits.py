from __future__ import annotations

import asyncio
import hashlib
import logging
from base64 import b64encode
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

import asyncpg
import orjson
from fastapi import HTTPException
from lc_config.tenant_config import TenantConfig
from lc_database.database import asyncpg_pool, json_dumps
from lc_logging.trace import set_tag, wrap
from pydantic import BaseModel, field_validator

from app import config
from app.api.auth import AuthInfo, AuthInfoRequiredHandle
from app.hub import schema
from app.hub.api.pagination import PaginationQueryParams
from app.hub.crud import repos
from app.hub.repo_versioning import get_repo_cache_version, increment_repo_cache_version
from app.hub.utils import (
    convert_to_prompt_playground,
    convert_to_runnable_sequence,
    core_rewrite_lookup,
    get_prompt_manifest,
    is_runnable_sequence,
    is_valid_runnable_sequence,
    tag_lookup,
    tag_vals,
)
from app.memoize import redis_cache
from app.models.prompt_webhooks.webhook_applier import apply_prompt_webhooks
from app.models.runs.fetch_ch import fetch_runs
from app.retry import retry_asyncpg
from app.schemas import BodyParamsForRunSchema, PromptWebhookPayload, RunSelect

logger = logging.getLogger(__name__)


@wrap(
    service="hub_cache",
    resource="get_repo_version_key",
    name="cache.get_repo_version_key",
)
async def _get_repo_version_key(*args, **kwargs) -> str:
    """Generate a version key based on actual repo cache version for cache invalidation."""
    # Extract owner and repo from function arguments
    owner = kwargs.get("owner")
    repo = kwargs.get("repo")

    if not owner or not repo:
        # Handle the actual function signatures:
        # get_commits(db, auth, owner, repo, *, pagination_params=...)
        # get_commits_public(db, owner, repo, *, pagination_params=...)

        if len(args) >= 4:  # get_commits: db, auth, owner, repo
            owner = args[2] if isinstance(args[2], (str, type(None))) else owner
            repo = args[3] if isinstance(args[3], str) else repo
        elif len(args) >= 3:  # get_commits_public: db, owner, repo
            owner = args[1] if isinstance(args[1], str) else owner
            repo = args[2] if isinstance(args[2], str) else repo

    if owner and repo:
        try:
            # Get the actual repo cache version from Redis
            version = await get_repo_cache_version(owner, repo)
            version_key = f"repo_v={version}"

            # Add span attributes for observability - supports both Datadog and OTEL
            set_tag("cache.repo_owner", owner)
            set_tag("cache.repo_name", repo)
            set_tag("cache.version_key", version_key)

            return version_key
        except Exception as e:
            # Log error but don't break caching - return fallback version
            logger.warning(
                "Failed to generate version key for %s/%s: %s", owner, repo, str(e)
            )
            return "repo_v=1"

    # Log when we can't extract repo info - this helps debug cache key issues
    logger.warning(
        "Could not extract owner/repo from cache function args. Args: %s, Kwargs: %s",
        [type(arg).__name__ for arg in args],
        list(kwargs.keys()),
    )
    return "repo_v=1"  # Return fallback instead of empty string


_parent_commit_hash_sql = """
    (
        SELECT commit_hash FROM hub_commits AS parent_commits
        WHERE parent_commits.id = hub_commits.parent_id
    ) AS parent_commit_hash
"""

_commit_lookups_sql = f"""
    (
        SELECT COUNT(*) FROM hub_commit_downloads
        WHERE hub_commit_downloads.commit_id = hub_commits.id
            AND hub_commit_downloads.download_type = 'api'
    ) AS num_downloads,
    (
        SELECT COUNT(*) FROM hub_commit_downloads
        WHERE hub_commit_downloads.commit_id = hub_commits.id
            AND hub_commit_downloads.download_type = 'web'
    ) AS num_views,
    {_parent_commit_hash_sql}
"""

_get_commits_sql = f"""SELECT *, {_commit_lookups_sql}
FROM hub_commits
WHERE repo_id = $1
ORDER BY created_at DESC
LIMIT $2
OFFSET $3
"""

_get_commits_with_author_sql = f"""SELECT hub_commits.*, {_commit_lookups_sql}, u.full_name
FROM hub_commits
LEFT JOIN users u ON u.ls_user_id = hub_commits.ls_user_id
WHERE repo_id = $1
ORDER BY created_at DESC
LIMIT $2
OFFSET $3
"""


@retry_asyncpg
async def _get_commits_and_total(
    db: asyncpg.Connection,
    repo_obj: schema.RepoBaseInfo,
    pagination_params: PaginationQueryParams,
    show_commit_authors: bool,
) -> tuple[List[schema.CommitWithLookups], int]:
    # get commits
    async with asyncpg_pool() as pool:
        commits, total = await asyncio.gather(
            db.fetch(
                _get_commits_with_author_sql
                if show_commit_authors
                else _get_commits_sql,
                repo_obj.id,
                pagination_params.limit,
                pagination_params.offset,
            ),
            pool.fetchval(
                """SELECT COUNT(*) FROM hub_commits WHERE repo_id = $1
                /*application='hub',action='commits.get_commits.total'*/""",
                repo_obj.id,
            ),
        )
    if commits is None:
        raise HTTPException(status_code=500, detail="Error while getting commits")
    dict_commits = [dict(commit) for commit in commits]
    for commit in dict_commits:
        commit["manifest"] = orjson.loads(commit["manifest"])
    return [schema.CommitWithLookups(**commit) for commit in dict_commits], total


@retry_asyncpg
@redis_cache(
    ttl=config.settings.HUB_COMMIT_LIST_CACHE_TTL_SECONDS,
    enabled=lambda: config.settings.CACHE_HUB_COMMITS,
    exclude_kwargs=["db"],
    version_func=_get_repo_version_key,
)
async def get_commits_public(
    db: asyncpg.Connection,
    owner: str,
    repo: str,
    *,
    pagination_params: PaginationQueryParams = PaginationQueryParams(),
) -> tuple[List[schema.CommitWithLookups], int]:
    """Get a list of commits."""
    # get repo, confirming access granted to repo
    repo_obj = await repos.get_repo_base_public(db=db, owner=owner, repo=repo)
    return await _get_commits_and_total(
        db, repo_obj, pagination_params, show_commit_authors=False
    )


@redis_cache(
    ttl=config.settings.HUB_COMMIT_LIST_CACHE_TTL_SECONDS,
    enabled=lambda: config.settings.CACHE_HUB_COMMITS,
    exclude_kwargs=["db", "auth"],
    version_func=_get_repo_version_key,
)
async def get_commits(
    db: asyncpg.Connection,
    auth: AuthInfo,
    owner: Optional[str],
    repo: str,
    *,
    pagination_params: PaginationQueryParams = PaginationQueryParams(),
) -> tuple[List[schema.CommitWithLookups], int]:
    """Get a list of commits."""
    # get repo, confirming access granted to repo
    repo_obj = await repos.get_repo_base(db=db, auth=auth, repo=repo, owner=owner)
    return await _get_commits_and_total(
        db,
        repo_obj,
        pagination_params,
        show_commit_authors=repo_obj.tenant_id == auth.tenant_id,
    )


@retry_asyncpg
async def _get_commit_manifest(
    db: asyncpg.Connection,
    repo_obj: schema.RepoBaseInfo,
    commit_hash_or_tag: str | None,
    get_examples: bool = False,
):
    if commit_hash_or_tag is None:
        commit = await db.fetchrow(
            f"""
            SELECT *, {_parent_commit_hash_sql} FROM hub_commits 
            WHERE repo_id = $1 
            ORDER BY created_at DESC LIMIT 1
            /*application='hub',action='commits.get_commit_manifest.commit'*/""",
            repo_obj.id,
        )
    elif len(commit_hash_or_tag) < 8:
        # only check for tag
        commit = await db.fetchrow(
            f"""
            SELECT hub_commits.*, {_parent_commit_hash_sql}
            FROM hub_commits
            LEFT JOIN hub_repo_tags t ON hub_commits.repo_id = t.repo_id AND hub_commits.id = t.commit_id
            WHERE hub_commits.repo_id = $1 AND t.tag_name = $2
            ORDER BY t.tag_name IS NOT NULL DESC, hub_commits.created_at DESC
            LIMIT 1
            /*application='hub',action='commits.get_commit_manifest.commit'*/
            """,
            repo_obj.id,
            commit_hash_or_tag,
        )
    else:
        # prioritize commit hash over tag but check for both
        commit = await db.fetchrow(
            f"""
            SELECT hub_commits.*, {_parent_commit_hash_sql}
            FROM hub_commits
            LEFT JOIN hub_repo_tags t ON hub_commits.repo_id = t.repo_id AND hub_commits.id = t.commit_id
            WHERE hub_commits.repo_id = $1 AND (hub_commits.commit_hash ^@ $2 OR t.tag_name = $2)
            ORDER BY 
                (hub_commits.commit_hash ^@ $2) DESC,  -- prioritize commit hash matches
                t.tag_name IS NOT NULL DESC,           -- then tags
                hub_commits.created_at DESC            -- then most recent (should theoretically never be needed)
            LIMIT 1
            /*application='hub',action='commits.get_commit_manifest.commit'*/
            """,
            repo_obj.id,
            commit_hash_or_tag,
        )

    if commit is None:
        raise HTTPException(
            status_code=404,
            detail=f"Commit {repo_obj.owner}/{repo_obj.repo_handle}/{commit_hash_or_tag}"
            " not found",
        )

    # confirm sha hash matches
    recompute_sha = _generate_commit_hash(
        commit.manifest_sha,
        commit.parent_commit_hash,
        commit.example_run_ids,
    )
    if recompute_sha != commit.commit_hash:
        raise HTTPException(
            status_code=500,
            detail=f"Commit hash does not match manifest hash: {recompute_sha} != "
            f"{commit.commit_hash}",
        )

    examples = []
    if (
        get_examples
        and commit.example_run_ids is not None
        and all(isinstance(e, str) for e in commit.example_run_ids)
    ):
        examples = await fetch_example_runs(
            AuthInfo(
                tenant_id=UUID(repo_obj.tenant_id.hex),
                tenant_config=TenantConfig(),
                organization_is_personal=False,
            ),
            [UUID(e) for e in commit.example_run_ids],
            [
                RunSelect.id,
                RunSelect.start_time,
                RunSelect.inputs,
                RunSelect.outputs,
                RunSelect.session_id,
            ],
        )

        examples = [
            schema.RepoExampleResponse(
                id=e["id"],
                start_time=e["start_time"],
                inputs=e["inputs"],
                outputs=e["outputs"],
                session_id=e["session_id"],
            )
            for e in examples
        ]

    commit = dict(commit)
    commit["manifest"] = orjson.loads(commit["manifest"])

    return schema.Commit(**commit), examples


@retry_asyncpg
async def get_commit_manifest_public(
    db: asyncpg.Connection,
    owner: str,
    repo: str,
    commit_hash_or_tag: str | None,
    get_examples: bool,
    is_view: bool,
    include_model: Optional[bool] = False,
) -> schema.CommitManifestResponse:
    """Get a commit."""
    # get repo, confirming public has access
    repo_obj = await repos.get_repo_base_public(db=db, repo=repo, owner=owner)

    commit, examples = await _get_commit_manifest(
        db, repo_obj, commit_hash_or_tag, get_examples
    )
    manifest = commit.manifest

    if include_model:
        manifest = convert_to_runnable_sequence(manifest)
    if not include_model:
        manifest = get_prompt_manifest(manifest)

    # increment commit download
    await db.execute(
        """INSERT INTO hub_commit_downloads 
            (commit_id, downloaded_by, download_type) 
        VALUES ($1, NULL, $2)
        /*application='hub',action='commits.get_commit_manifest.downloaded_by'*/""",
        commit.id,
        "web" if is_view else "api",
    )

    return schema.CommitManifestResponse(
        commit_hash=commit.commit_hash, manifest=manifest, examples=examples
    )


@retry_asyncpg
async def get_commit_manifest(
    db: asyncpg.Connection,
    auth: AuthInfo,
    owner: Optional[str],
    repo: str,
    commit_hash_or_tag: str | None,
    get_examples: bool,
    is_view: bool,
    include_model: Optional[bool] = False,
) -> schema.CommitManifestResponse:
    """Get a commit."""
    repo_obj = await repos.get_repo_base(db=db, auth=auth, repo=repo, owner=owner)

    commit, examples = await _get_commit_manifest(
        db, repo_obj, commit_hash_or_tag, get_examples
    )

    manifest = commit.manifest

    if include_model:
        manifest = convert_to_runnable_sequence(commit.manifest)
    else:
        manifest = get_prompt_manifest(commit.manifest)

    # increment commit download
    downloaded_by = auth.tenant_id
    await db.execute(
        """INSERT INTO hub_commit_downloads 
            (commit_id, downloaded_by, download_type) 
        VALUES ($1, $2, $3)
        /*application='hub',action='commits.get_commit_manifest.downloaded_by'*/""",
        commit.id,
        downloaded_by,
        "web" if is_view else "api",
    )

    return schema.CommitManifestResponse(
        commit_hash=commit.commit_hash, manifest=manifest, examples=examples
    )


class CreateCommitRequest(BaseModel):
    """All database fields for commits."""

    owner: Optional[str] = None
    repo: str
    parent_commit: Optional[str] = None
    manifest: Dict
    example_run_ids: list[UUID]
    skip_webhooks: bool | list[UUID] = False

    # validate parent_commit is None or 8-64 characters long
    @field_validator("parent_commit")
    @classmethod
    def parent_commit_length(cls, v):
        if v is not None and (len(v) < 8 or len(v) > 64):
            raise ValueError("Parent commit must be None or 8-64 characters long")
        return v


def _generate_commit_hash(
    manifest_sha: bytes,
    parent_commit: Optional[str],
    example_run_ids: list[str],
):
    """
    Generates the commit string.

    Steps:
    1. if the parent commit is None, use an empty string instead
    2. given a byte array of the manifest hash, convert it to a base64 string,
            encoded as utf-8
    3. concatenate the (manifest hash base64 string, ":", parent commit string)
    4. return the hex digest of the sha256 hash of the concatenated string (will be
            64 characters long)
    """
    # In sql:
    """
    encode(
      digest(
          concat(
              encode(
                  digest(
                      (select manifest from run_manifests where id=manifest_id)::text,
                      'sha256'
                  ), 
                  'base64')::text,
                  ':', 
                  (
                      select commit_hash from hub_commits parent 
                      where parent.id=hub_commits.parent_id
                  )
              ), 
              'sha256'
          ), 
      'hex'
    )
    """
    parent_commit_str = parent_commit if parent_commit is not None else ""
    manifest_sha_str = b64encode(manifest_sha).decode("utf-8")
    if example_run_ids is None or len(example_run_ids) == 0:
        pre_hash = f"{manifest_sha_str}:{parent_commit_str}"
    else:
        pre_hash = f"{manifest_sha_str}:{parent_commit_str}:{':'.join(example_run_ids)}"

    # return sha256 hash of pre_hash
    return hashlib.sha256(pre_hash.encode("utf-8")).hexdigest()


def _commits_match(ground_truth: Optional[str], abbreviated: Optional[str]) -> bool:
    """
    Checks if the full 64-character `ground_truth` hash matches the `abbreviated` one.

    For example:
    - ground_truth = e938f36c97bea27b0635f4c3088ca530e938f36c97bea27b0635f4c3088ca530
    - abbreviated = e938f36c
    Matches
    """

    assert ground_truth is None or len(ground_truth) == 64, (
        "full hashes must be None or 64 characters long"
    )
    assert abbreviated is None or len(abbreviated) >= 8 and len(abbreviated) <= 64, (
        "short hashes must be None or 8-64 characters long"
    )

    if ground_truth is None:
        return abbreviated is None
    if abbreviated is None:  # ground truth must not be none
        return False

    return ground_truth.startswith(abbreviated)


def _validate_loadable(value: Union[List[Any], Dict[str, Any]]) -> None:
    # logic from langchain.load.load.Reviver.__call__

    # handle list case
    if isinstance(value, list):
        for v in value:
            _validate_loadable(v)

    # don't validate non-dicts
    if not isinstance(value, dict):
        return

    # not implemented block
    if (
        value.get("lc", None) == 1
        and value.get("type", None) == "not_implemented"
        and value.get("id", None) is not None
    ):
        raise HTTPException(
            status_code=400,
            detail="Trying to load an object that doesn't implement "
            f"serialization: {value}",
        )

    if (
        value.get("lc", None) == 1
        and value.get("type", None) == "constructor"
        and value.get("id", None) is not None
    ):
        [*namespace, _] = value["id"]
        if namespace[0] not in [
            "langchain",
            "langchain_core",
            "langsmith",
            "langchain_anthropic",
            "langchain_groq",
            "langchain_google_genai",
            "langchain_aws",
            "langchain_openai",
            "langchain_google_vertexai",
            "langchain_mistralai",
            "langchain_fireworks",
            "langchain_xai",
            "langchain_deepseek",
        ]:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid namespace: {value}",
            )
        if len(namespace) == 1:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid namespace: {value}",
            )

        # assume subclass is serializable within langchain library
        # TODO: check that assumption

        kwargs = value.get("kwargs", dict())
        for v in kwargs.values():
            _validate_loadable(v)
        return

    # handle other dict case
    elif isinstance(value, dict):
        for v in value.values():
            _validate_loadable(v)


def _get_object_tag(auth: AuthInfoRequiredHandle, v: Dict[str, Any]) -> str:
    """
    Gets the type tag for the object,
    or throws a ValueError if it's an unsupported type
    """
    type_arr = get_prompt_manifest(v).get("id")
    if not isinstance(type_arr, list):
        raise ValueError("Manifest must have an id field")

    _validate_loadable(v)

    key = tuple(type_arr)
    tag = tag_lookup.get(key)
    if tag is None:
        raise ValueError(f"Manifest type {'.'.join(key)} is not supported")
    return tag


def _rewrite_ids(value: Dict[str, Any]) -> Dict[str, Any]:
    """To support older versions of LangChain, we need to rewrite namespaces to before core migration.

    Should remove this when most LangChain users are beyond v0.0.341"""

    if isinstance(value, list):
        return [_rewrite_ids(v) for v in value]

    if not isinstance(value, dict):
        return value

    if (
        value.get("lc", None) == 1
        and value.get("type", None) == "constructor"
        and value.get("id", None) is not None
    ):
        value_id = value["id"]
        if tuple(value_id) in core_rewrite_lookup:
            value["id"] = core_rewrite_lookup[tuple(value_id)]

        kwargs = value.get("kwargs", None)
        if kwargs is not None:
            value["kwargs"] = {k: _rewrite_ids(v) for k, v in kwargs.items()}

        return value

    return {k: _rewrite_ids(v) for k, v in value.items()}


async def fetch_example_runs(
    auth: AuthInfoRequiredHandle, ids: List[UUID], select: list[RunSelect]
):
    """Fetch example runs for a commit."""
    if not ids:
        return []

    result = await fetch_runs(
        auth,
        BodyParamsForRunSchema(id=ids, select=select),
    )
    return result["runs"]


@retry_asyncpg
async def create_commit(
    db: asyncpg.Connection, auth: AuthInfoRequiredHandle, req: CreateCommitRequest
) -> schema.CommitWithLookups:
    """Create a commit."""

    # get repo, only confirms read access
    repo_obj = await repos.get_repo_base(
        db=db, auth=auth, repo=req.repo, owner=req.owner
    )

    # confirm acting as owner with auth (write access)
    if auth.tenant_id != repo_obj.tenant_id:
        raise HTTPException(
            status_code=403,
            detail="You do not have permission to create a commit on this repo",
        )

    _rewrite_ids(req.manifest)

    manifest = req.manifest
    # convert to prompt playground if we don't support runnables
    # but validate that it's only a prompt and model
    if is_runnable_sequence(manifest) and is_valid_runnable_sequence(manifest):
        manifest = convert_to_prompt_playground(manifest)

    # Make sure it's a supported type and get tag
    try:
        tag = _get_object_tag(auth, manifest)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    tags = repo_obj.tags
    # filter out type tags
    tags = [tag] + [tag_ for tag_ in tags if tag_ not in tag_vals]

    # Insert it
    async with db.transaction():
        # get latest commit on repo, or none if no commits exist
        # locking on the commit row works because a racing create_commit
        # will have the same parent commit, and this will block that select
        row = await db.fetchrow(
            """SELECT id, commit_hash, manifest, example_run_ids, manifest_sha FROM hub_commits
            WHERE repo_id = $1 ORDER BY created_at DESC LIMIT 1 
            FOR UPDATE
            /*application='hub',action='commits.create_commit.latest_commit_hash'*/""",
            repo_obj.id,
        )
        (
            latest_commit_id,
            latest_commit_hash,
            latest_manifest,
            latest_examples,
            latest_manifest_sha,
        ) = (None, None, None, None, None) if row is None else row

        if latest_manifest is not None:
            latest_manifest = orjson.loads(latest_manifest)

        if config.settings.LANGCHAIN_ENV == "local_test":
            # Sleep to allow testing concurrent edits
            import asyncio

            await asyncio.sleep(0.25)

        # confirm parent id matches
        if not _commits_match(latest_commit_hash, req.parent_commit):
            raise HTTPException(
                status_code=409,
                detail=f"Parent commit ({req.parent_commit}) does not match"
                f" latest commit ({latest_commit_hash})). Please try again.",
            )

        example_run_ids = req.example_run_ids

        # check that each of the examples is associated with a run that is in a session which
        # belongs to the same tenant that owns the repo that is associated with the commit. Also
        # check that the tenant in the auth info is that same tenant.
        runs = await fetch_example_runs(auth, example_run_ids, [])
        if len(runs) != len(example_run_ids):
            raise HTTPException(
                status_code=400,
                detail="Invalid example runs",
            )

        # Compute manifest_sha in SQL to avoid mismatched sha256
        manifest_sha = await db.fetchval(
            """SELECT digest($1::text, 'sha256')""",
            json_dumps(manifest),
        )

        if latest_manifest is not None:
            # raise conflict if same as new manifest
            if (
                latest_manifest_sha == manifest_sha
                and example_run_ids == latest_examples
            ):
                raise HTTPException(
                    status_code=409,
                    detail="Nothing to commit: prompt has not changed since latest commit",
                )

        commit_hash = _generate_commit_hash(
            manifest_sha,
            latest_commit_hash,
            [str(example) for example in example_run_ids],
        )

        # insert commit with manifest
        commit = await db.fetchrow(
            f"""INSERT INTO hub_commits (manifest, repo_id, commit_hash, parent_id, example_run_ids, manifest_sha, ls_user_id)
                VALUES (
                    $1,
                    $2,
                    $3,
                    $4,
                    $5,
                    digest($6::text, 'sha256'),
                    $7
                ) ON CONFLICT DO NOTHING RETURNING *, {_commit_lookups_sql}
                /*application='hub',action='commits.create_commit.commit'*/""",
            manifest,
            repo_obj.id,
            commit_hash,
            latest_commit_id,
            example_run_ids,
            json_dumps(manifest),
            auth.ls_user_id,
        )
    if commit is None:
        raise HTTPException(
            status_code=409,
            detail="Conflicting commit already exists. Please try again with the most recent parent commit hash.",
        )

    # Increment repo cache version to invalidate all related caches
    try:
        await increment_repo_cache_version(req.owner, req.repo)
    except Exception:
        # Don't fail commit creation if cache version increment fails
        logger.error(
            f"Failed to increment cache version for {req.owner}/{req.repo}",
            exc_info=True,
        )

    commit = dict(commit)
    if commit["manifest"] is not None:
        commit["manifest"] = orjson.loads(commit["manifest"])
    # retag repo
    await repos.update_repo(
        db,
        auth,
        repo_obj.repo_handle,
        schema.UpdateRepoRequest(tags=tags, description=None, is_public=None),
        repo_obj.owner,
    )

    if req.skip_webhooks is not True:
        # Get the full_name from users table
        full_name = await db.fetchval(
            """SELECT full_name FROM users WHERE ls_user_id = $1""",
            auth.ls_user_id,
        )
        commit["full_name"] = full_name
        commit_with_lookups = schema.CommitWithLookups(**commit)

        manifest = convert_to_runnable_sequence(manifest)

        webhook_payload = PromptWebhookPayload(
            prompt_id=str(repo_obj.id),
            prompt_name=repo_obj.repo_handle,
            manifest=manifest,
            commit_hash=commit_hash,
            created_at=commit_with_lookups.created_at.isoformat(),
            created_by=commit_with_lookups.full_name or "",
        )

        await apply_prompt_webhooks(
            auth=auth,
            payload=webhook_payload,
            ignore_webhook_ids=req.skip_webhooks or [],
        )

    return commit_with_lookups
