from typing import Dict, List, Optional, Tuple


# Private prompts are passed with "-" in the URL in place of a tenant_handle
# but should be treated as None, this is how we convert it
def get_owner_from_passed_handle(owner: str | None) -> Optional[str]:
    effective_owner: Optional[str] = owner
    if owner == "-":
        effective_owner = None
    return effective_owner


def parse_owner_repo_commit(
    identifier: str,
) -> Tuple[Optional[str], str, Optional[str]]:
    """
    Parses a string in the format of `owner/repo:commit` and returns a tuple of
    (owner, repo, commit).
    """
    owner_repo = identifier
    commit = None
    if ":" in identifier:
        owner_repo, commit = identifier.split(":", 1)

    if "/" not in owner_repo:
        return None, owner_repo, commit

    owner, repo = owner_repo.split("/", 1)
    return owner, repo, commit


def is_runnable_sequence(manifest: dict) -> bool:
    return (
        "id" in manifest
        and isinstance(manifest["id"], list)
        and manifest["id"][-1] == "RunnableSequence"
    )


def is_valid_runnable_sequence(manifest: dict) -> bool:
    kwargs = manifest["kwargs"]

    return (
        is_runnable_sequence(manifest)
        and "first" in kwargs
        and tag_lookup.get(tuple(kwargs["first"]["id"])) is not None
        and "last" in kwargs
        # Note: we don't actually validate the bound object is an LLM/Chat model here.
        and runnable_tag_lookup.get(tuple(kwargs["last"]["id"])) is not None
        and "middle" not in kwargs
    )


def is_prompt_playground(manifest: dict) -> bool:
    return (
        "id" in manifest
        and isinstance(manifest["id"], list)
        and manifest["id"][-1] == "PromptPlayground"
    )


def get_prompt_manifest(manifest: dict) -> dict:
    if is_prompt_playground(manifest):
        return manifest["kwargs"]["first"]
    else:
        return manifest


def convert_to_prompt_playground(manifest: dict) -> dict:
    if is_runnable_sequence(manifest):
        manifest["id"] = ["langsmith", "playground", "PromptPlayground"]
        return manifest
    else:
        return manifest


def convert_to_runnable_sequence(manifest: dict) -> dict:
    if is_prompt_playground(manifest):
        manifest["id"] = ["langchain", "schema", "runnable", "RunnableSequence"]
        return manifest
    else:
        return manifest


runnable_tag_lookup: Dict[tuple[str, ...], str] = {
    # Chat Models
    ("langchain", "chat_models", "openai", "ChatOpenAI"): "ChatOpenAI",
    ("langchain", "chat_models", "anthropic", "ChatAnthropic"): "ChatAnthropic",
    ("langchain", "chat_models", "chat_integration", "ChatVertexAI"): "ChatVertexAI",
    ("langchain", "chat_models", "fireworks", "ChatFireworks"): "ChatFireworks",
    (
        "langchain",
        "chat_models",
        "googlellm",
        "ChatGoogleGenerativeAI",
    ): "ChatGoogleGenerativeAI",
    (
        "langchain_google_genai",
        "chat_models",
        "ChatGoogleGenerativeAI",
    ): "ChatGoogleGenerativeAI",
    ("langchain", "chat_models", "mistralai", "ChatMistralAI"): "ChatMistralAI",
    ("langchain", "chat_models", "mistral_ai", "ChatMistralAI"): "ChatMistralAI",
    ("langchain", "chat_models", "groq", "ChatGroq"): "ChatGroq",
    ("langchain", "chat_models", "azure_openai", "AzureChatOpenAI"): "AzureChatOpenAI",
    ("langchain", "llms", "bedrock", "BedrockChat"): "BedrockChat",
    ("langsmith", "playground", "ChatCustomModel"): "ChatCustomModel",
    # Text Models
    ("langchain", "llms", "openai", "OpenAI"): "OpenAI",
    ("langchain", "llms", "openai", "AzureOpenAI"): "AzureOpenAI",
    ("langchain", "llms", "fireworks", "Fireworks"): "Fireworks",
    ("langchain", "llms", "googlellm", "VertexAI"): "VertexAI",
    ("langchain", "llms", "bedrock", "Bedrock"): "Bedrock",
    ("langsmith", "playground", "CustomModel"): "CustomModel",
    # Output Parsers
    (
        "langchain",
        "output_parsers",
        "combining",
        "CombiningOutputParser",
    ): "CombiningOutputParser",
    (
        "langchain",
        "output_parsers",
        "fix",
        "OutputFixingParser",
    ): "OutputFixingParser",
    (
        "langchain",
        "output_parsers",
        "list",
        "CommaSeparatedListOutputParser",
    ): "CommaSeparatedListOutputParser",
    (
        "langchain_core",
        "output_parsers",
        "list",
        "CommaSeparatedListOutputParser",
    ): "CommaSeparatedListOutputParser",
    (
        "langchain",
        "output_parsers",
        "openai_functions",
        "JsonOutputFunctionsParser",
    ): "JsonOutputFunctionsParser",
    (
        "langchain",
        "output_parsers",
        "openai_functions",
        "JsonKeyOutputFunctionsParser",
    ): "JsonKeyOutputFunctionsParser",
    (
        "langchain",
        "output_parsers",
        "openai_tools",
        "JsonOutputToolsParser",
    ): "JsonOutputToolsParser",
    ("langchain", "output_parsers", "regex", "RegexParser"): "RegexParser",
    ("langchain", "schema", "output_parser", "StrOutputParser"): "StrOutputParser",
    (
        "langchain_core",
        "output_parsers",
        "string",
        "StrOutputParser",
    ): "StrOutputParser",
    # Structures
    ("langchain", "schema", "runnable", "RunnableSequence"): "RunnableSequence",
    ("langchain", "schema", "runnable", "RunnableMap"): "RunnableMap",
    ("langchain_core", "runnables", "RunnableSequence"): "RunnableSequence",
    ("langchain_core", "runnables", "RunnableMap"): "RunnableMap",
    ("langchain", "schema", "runnable", "RunnableBinding"): "RunnableBinding",
    ("langchain_core", "runnables", "RunnableBinding"): "RunnableBinding",
    # Fake test models
    (
        "langchain",
        "chat_models",
        "fake",
        "FakeStreamingMessagesListChatModel",
    ): "FakeStreamingMessagesListChatModel",
    (
        "langchain",
        "chat_models",
        "fake",
        "FakeMessagesListChatModel",
    ): "FakeMessagesListChatModel",
}

tag_lookup: Dict[tuple[str, ...], str] = {
    # Prompts
    ("langchain", "prompts", "prompt", "PromptTemplate"): "StringPromptTemplate",
    ("langchain", "prompts", "chat", "ChatPromptTemplate"): "ChatPromptTemplate",
    ("langchain_core", "prompts", "prompt", "PromptTemplate"): "StringPromptTemplate",
    ("langchain_core", "prompts", "chat", "ChatPromptTemplate"): "ChatPromptTemplate",
    (
        "langchain_core",
        "prompts",
        "structured",
        "StructuredPrompt",
    ): "StructuredPrompt",
    ("langsmith", "playground", "PromptPlayground"): "PromptPlayground",
}

tag_vals = set(tag_lookup.values())

core_rewrite_lookup: Dict[Tuple[str, ...], List[str]] = {
    # prompts
    ("langchain_core", "prompts", "prompt", "PromptTemplate"): [
        "langchain",
        "prompts",
        "prompt",
        "PromptTemplate",
    ],
    ("langchain_core", "prompts", "chat", "ChatPromptTemplate"): [
        "langchain",
        "prompts",
        "chat",
        "ChatPromptTemplate",
    ],
    # prompt messages
    ("langchain_core", "prompts", "chat", "AIMessagePromptTemplate"): [
        "langchain",
        "prompts",
        "chat",
        "AIMessagePromptTemplate",
    ],
    ("langchain_core", "prompts", "chat", "HumanMessagePromptTemplate"): [
        "langchain",
        "prompts",
        "chat",
        "HumanMessagePromptTemplate",
    ],
    ("langchain_core", "prompts", "chat", "SystemMessagePromptTemplate"): [
        "langchain",
        "prompts",
        "chat",
        "SystemMessagePromptTemplate",
    ],
    ("langchain_core", "prompts", "prompt", "MessagesPlaceholder"): [
        "langchain",
        "prompts",
        "prompt",
        "MessagesPlaceholder",
    ],
    # schema messages
    ("langchain_core", "messages", "ai", "AIMessage"): [
        "langchain",
        "schema",
        "messages",
        "AIMessage",
    ],
    ("langchain_core", "messages", "base", "BaseMessage"): [
        "langchain",
        "schema",
        "messages",
        "BaseMessage",
    ],
    ("langchain_core", "messages", "chat", "ChatMessage"): [
        "langchain",
        "schema",
        "messages",
        "ChatMessage",
    ],
    ("langchain_core", "messages", "function", "FunctionMessage"): [
        "langchain",
        "schema",
        "messages",
        "FunctionMessage",
    ],
    ("langchain_core", "messages", "human", "HumanMessage"): [
        "langchain",
        "schema",
        "messages",
        "HumanMessage",
    ],
    ("langchain_core", "messages", "system", "SystemMessage"): [
        "langchain",
        "schema",
        "messages",
        "SystemMessage",
    ],
}
