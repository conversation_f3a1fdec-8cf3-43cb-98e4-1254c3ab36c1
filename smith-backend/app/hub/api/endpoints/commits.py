"""Endpoints for commits."""

from typing import Optional

from fastapi import Depends
from lc_database import api_router, database
from lc_logging.audit_logs import audit_operation_name

from app.api import deps
from app.api.auth.schemas import Permissions
from app.hub import schema
from app.hub.api import pagination
from app.hub.crud import commits
from app.hub.utils import get_owner_from_passed_handle

router = api_router.TrailingSlashRouter()


@router.get("/{owner}/{repo}/")
@audit_operation_name("read_commits")
async def list_commits(
    owner: str,
    repo: str,
    pagination_params: pagination.PaginationQueryParams = Depends(),
    auth: Optional[deps.AuthInfo] = Depends(
        deps.Authorize(Permissions.PROMPTS_READ, allow_public=True)
    ),
) -> schema.ListCommitsResponse:
    """Get all commits."""
    effective_owner: Optional[str] = get_owner_from_passed_handle(owner)

    async with database.asyncpg_conn() as db:
        if auth is not None:
            commits_lst, total = await commits.get_commits(
                db=db,
                auth=auth,
                owner=effective_owner,
                repo=repo,
                pagination_params=pagination_params,
            )
        else:
            if effective_owner is None:
                raise deps.HTTPException(
                    status_code=400, detail="No prompt owner specified"
                )
            commits_lst, total = await commits.get_commits_public(
                db=db,
                owner=effective_owner,
                repo=repo,
                pagination_params=pagination_params,
            )
        return schema.ListCommitsResponse(commits=commits_lst, total=total)


@router.get("/{owner}/{repo}/{commit}")
@audit_operation_name("read_commit")
async def get_commit(
    owner: str,
    repo: str,
    commit: str,
    auth: Optional[deps.AuthInfo] = Depends(
        deps.Authorize(Permissions.PROMPTS_READ, allow_public=True)
    ),
    get_examples: bool = False,
    is_view: bool = False,
    include_model: Optional[bool] = False,
) -> schema.CommitManifestResponse:
    """Download a repo."""
    effective_owner: Optional[str] = get_owner_from_passed_handle(owner)

    async with database.asyncpg_conn() as db:
        if auth is not None:
            return await commits.get_commit_manifest(
                db=db,
                auth=auth,
                owner=effective_owner,
                repo=repo,
                commit_hash_or_tag=commit if commit != "latest" else None,
                get_examples=get_examples,
                is_view=is_view,
                include_model=include_model,
            )
        else:
            if effective_owner is None:
                raise deps.HTTPException(
                    status_code=400, detail="No prompt owner specified"
                )
            return await commits.get_commit_manifest_public(
                db=db,
                owner=effective_owner,
                repo=repo,
                commit_hash_or_tag=commit if commit != "latest" else None,
                get_examples=get_examples,
                is_view=is_view,
                include_model=include_model,
            )


@router.post("/{owner}/{repo}")
@audit_operation_name("create_commit")
async def create_commit(
    owner: str,
    repo: str,
    body: schema.CreateRepoCommitRequest,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(Permissions.PROMPTS_UPDATE, require_handle=False)
    ),
) -> schema.CreateRepoCommitResponse:
    """Upload a repo."""
    effective_owner: Optional[str] = get_owner_from_passed_handle(owner)

    async with database.asyncpg_conn() as db:
        try:
            req = commits.CreateCommitRequest(
                owner=effective_owner,
                repo=repo,
                manifest=body.manifest,
                parent_commit=body.parent_commit,
                example_run_ids=body.example_run_ids or [],
                skip_webhooks=body.skip_webhooks,
            )
        except ValueError as e:
            raise deps.HTTPException(400, detail=str(e))

        # Create the commit
        commit = await commits.create_commit(db, auth, req)

        return schema.CreateRepoCommitResponse(commit=commit)
