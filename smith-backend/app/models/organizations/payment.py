import asyncio
import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Tuple, cast

import asyncpg
import httpx
import structlog
from asyncpg import Connection
from dateutil import relativedelta
from fastapi import HTTPException
from lc_config.tenant_config import OrganizationConfig, TenantConfig
from lc_config.utils import is_payment_enabled
from lc_database import database, metronome, stripe
from pydantic import BaseModel
from stripe import Customer, InvalidRequestError, SetupIntent

from app import config, schemas
from app.api.auth.schemas import AuthInfo, OrgAuthInfo
from app.config import settings
from app.models.identities import crud as identities_crud
from app.models.organizations import metronome_cache, shared
from app.retry import retry_asyncpg, retry_stripe

logger = structlog.getLogger(__name__)


class PlanType(str, Enum):
    dev = "dev"
    plus = "plus"


PERSONAL_TIERS = [
    schemas.PaymentPlanTier.developer,
    schemas.PaymentPlanTier.free,
]

TIERS_WITH_SEAT_BASED_BILLING = [
    schemas.PaymentPlanTier.plus,
    schemas.PaymentPlanTier.startup,
]

APPROVAL_ONLY_TIERS = [
    schemas.PaymentPlanTier.startup,
    schemas.PaymentPlanTier.premier,
]


# This is only a method to make it mockable
def PLAN_TYPE_TO_TIER_MAP() -> dict[PlanType, schemas.PaymentPlanTier]:
    return (
        {
            PlanType.dev: schemas.PaymentPlanTier.developer_legacy,
            PlanType.plus: schemas.PaymentPlanTier.plus_legacy,
        }
        if not settings.FF_USE_PAID_PLANS
        else {
            PlanType.dev: schemas.PaymentPlanTier.free,
            # If FF_USE_PAID_PLANS is on then we leave the customer with no plan until they add their card info
            PlanType.plus: schemas.PaymentPlanTier.no_plan,
        }
    )


TIER_TO_CURRENT_PLAN_MAP = {
    schemas.PaymentPlanTier.developer.value: settings.METRONOME_DEV_PLAN_ID,
    schemas.PaymentPlanTier.plus.value: settings.METRONOME_PLUS_PLAN_ID,
    schemas.PaymentPlanTier.developer_legacy.value: settings.METRONOME_DEV_LEGACY_PLAN_ID,
    schemas.PaymentPlanTier.plus_legacy.value: settings.METRONOME_PLUS_LEGACY_PLAN_ID,
    schemas.PaymentPlanTier.free.value: settings.METRONOME_FREE_PLAN_ID,
    schemas.PaymentPlanTier.enterprise_legacy.value: settings.METRONOME_ENTERPRISE_LEGACY_PLAN_ID,
    schemas.PaymentPlanTier.startup.value: settings.METRONOME_STARTUP_PLAN_ID,
    schemas.PaymentPlanTier.partner.value: settings.METRONOME_PARTNER_PLAN_ID,
    schemas.PaymentPlanTier.premier.value: settings.METRONOME_PREMIER_PLAN_ID,
}

ALLOWED_TIER_SWITCHES = {
    schemas.PaymentPlanTier.developer_legacy: [schemas.PaymentPlanTier.developer],
    schemas.PaymentPlanTier.free: [
        schemas.PaymentPlanTier.developer,
        schemas.PaymentPlanTier.plus,
        schemas.PaymentPlanTier.startup,
        schemas.PaymentPlanTier.premier,
    ],
    schemas.PaymentPlanTier.plus_legacy: [
        schemas.PaymentPlanTier.plus,
        schemas.PaymentPlanTier.startup,
        schemas.PaymentPlanTier.premier,
    ],
    schemas.PaymentPlanTier.plus: [
        schemas.PaymentPlanTier.startup,
        schemas.PaymentPlanTier.plus,
        schemas.PaymentPlanTier.no_plan,
        schemas.PaymentPlanTier.premier,
    ],
    schemas.PaymentPlanTier.no_plan: [
        schemas.PaymentPlanTier.plus,
        schemas.PaymentPlanTier.startup,
        schemas.PaymentPlanTier.premier,
    ],
    schemas.PaymentPlanTier.startup: [
        schemas.PaymentPlanTier.startup,
        schemas.PaymentPlanTier.plus,
        schemas.PaymentPlanTier.no_plan,
        schemas.PaymentPlanTier.premier,
    ],
    schemas.PaymentPlanTier.developer: [
        schemas.PaymentPlanTier.free,
        schemas.PaymentPlanTier.developer,
        schemas.PaymentPlanTier.plus,
        schemas.PaymentPlanTier.startup,
        schemas.PaymentPlanTier.premier,
    ],
    schemas.PaymentPlanTier.premier: [
        schemas.PaymentPlanTier.premier,
        schemas.PaymentPlanTier.no_plan,
        schemas.PaymentPlanTier.plus,
    ],
}


def _get_approval_date_for_tier(
    tier: schemas.PaymentPlanTier, auth: OrgAuthInfo
) -> datetime | None:
    approval_date_str = None

    if tier == schemas.PaymentPlanTier.startup:
        approval_date_str = auth.org_config.startup_plan_approval_date
    elif tier == schemas.PaymentPlanTier.premier:
        approval_date_str = auth.org_config.premier_plan_approval_date

    if approval_date_str is None:
        return None
    else:
        return datetime.strptime(approval_date_str, "%Y-%m-%d").replace(
            tzinfo=timezone.utc
        )


async def _get_plan_type_for_customer(
    organization_id: uuid.UUID,
) -> PlanType:
    """Get the payment plan for an organization."""
    async with database.asyncpg_conn() as db:
        response = await db.fetchrow(
            """
            SELECT
                o.id,
                CASE
                    WHEN o.is_personal = TRUE
                        THEN 'dev'
                    ELSE 'plus'
                END AS plan_type
            FROM organizations o
            WHERE o.id = $1
            """,
            organization_id,
        )

    if response is None:
        raise ValueError("Organization not found")

    return PlanType(response["plan_type"])


async def get_organization_in_txn(
    db: asyncpg.Connection,
    organization_id: uuid.UUID,
) -> schemas.OrganizationPGSchema:
    """Get organization info."""
    response = await db.fetch(
        """
        SELECT
            o.*,
            w.credit_balance_micros as wallet_credit_balance_micros,
            w.inflight_balance_micros as wallet_inflight_balance_micros
        FROM organizations o
        LEFT JOIN wallets w ON o.id = w.organization_id
        WHERE o.id = $1
        """,
        organization_id,
    )
    if not response:
        raise HTTPException(status_code=404, detail="Organization not found")

    if len(response) > 1:
        raise HTTPException(
            status_code=500,
            detail="Multiple wallets found with the same organization_id",
        )

    response = response[0]

    wallet_fields = {
        "credit_balance_micros": int(response["wallet_credit_balance_micros"] or 0),
        "inflight_balance_micros": int(response["wallet_inflight_balance_micros"] or 0),
    }
    # Split response into organization and wallet fields
    org_fields = {k: v for k, v in response.items() if k not in wallet_fields}

    return schemas.OrganizationWithWalletPGSchema(
        **org_fields, wallet=schemas.Wallet(**wallet_fields)
    )


@retry_asyncpg
async def get_organization(
    organization_id: uuid.UUID,
) -> schemas.OrganizationWithWalletPGSchema:
    """Get organization info."""
    async with database.asyncpg_conn() as db:
        return await get_organization_in_txn(db, organization_id)


@retry_asyncpg
async def create_payment_setup_intent(
    auth: OrgAuthInfo,
) -> schemas.StripeSetupIntentResponse:
    if is_payment_enabled():
        stripe_customer, _ = await create_external_billing_customers_internal(
            cast(uuid.UUID, auth.organization_id),
            cast(str, auth.user_email),
        )
        intent = await _create_stripe_setup_intent(stripe_customer.id)
        return schemas.StripeSetupIntentResponse(client_secret=intent.client_secret)
    raise HTTPException(
        status_code=404, detail="Payment is not enabled for this organization"
    )


async def create_external_billing_customers(
    auth: AuthInfo,
) -> Tuple[Customer, schemas.OrganizationPGSchema] | None:
    if is_payment_enabled():
        return await create_external_billing_customers_internal(
            cast(uuid.UUID, auth.organization_id), cast(str, auth.user_email)
        )

    return None


async def create_external_billing_customers_internal(
    organization_id: uuid.UUID,
    user_email: str,
) -> Tuple[Customer, schemas.OrganizationPGSchema]:
    """Create a new customer and get a Stripe setup intent."""
    organization = await get_organization(organization_id)
    display_name = organization.display_name
    if organization.is_personal:
        display_name = user_email
    stripe_customer = await _ensure_stripe_customer(
        user_email, organization_id, display_name, organization.stripe_customer_id
    )
    metronome_ensure_customer_id = await _ensure_metronome_customer(
        organization.id,
        stripe_customer.id,
        display_name,
        organization.metronome_customer_id,
    )

    # Short-circuit if everything is already set up correctly
    if (
        metronome_ensure_customer_id == organization.metronome_customer_id
        and stripe_customer.id == organization.stripe_customer_id
        and stripe_customer.metadata.get("metronome_customer_id")
        == metronome_ensure_customer_id
    ):
        return (stripe_customer, organization)

    async with database.asyncpg_conn() as db:
        await asyncio.gather(
            _modify_stripe_customer_metadata(
                organization.metronome_customer_id, stripe_customer
            ),
            *(
                [
                    db.execute(
                        """
                UPDATE organizations
                SET metronome_customer_id = $1, stripe_customer_id = $2
                WHERE id = $3
                """,
                        metronome_ensure_customer_id,
                        stripe_customer.id,
                        organization_id,
                    )
                ]
                if metronome_ensure_customer_id != organization.metronome_customer_id
                or stripe_customer.id != organization.stripe_customer_id
                else []
            ),
        )

    # Ensure that future config requests will have the correct data
    # for the organization after the org's metronome customer ID is updated
    await metronome_cache.update(str(organization_id), force=True)

    organization.stripe_customer_id = stripe_customer.id
    organization.metronome_customer_id = metronome_ensure_customer_id
    updated_organization = await get_organization(organization_id)
    return (stripe_customer, updated_organization)


async def _add_metronome_customer_to_initial_plan(
    organization_id: uuid.UUID, metronome_customer_id: str
) -> None:
    """Add a Metronome customer to the initial plan or legacy plan."""
    async with metronome.metronome_client() as httpx_session:
        starting_on = _get_previous_midnight_utc()
        plan_type = await _get_plan_type_for_customer(organization_id)

        if plan_type not in PLAN_TYPE_TO_TIER_MAP():
            raise ValueError("Plan type not found")
        tier_for_customer = PLAN_TYPE_TO_TIER_MAP()[plan_type]
        if tier_for_customer == schemas.PaymentPlanTier.no_plan:
            return

        plan_for_customer = TIER_TO_CURRENT_PLAN_MAP[tier_for_customer.value]

        try:
            await httpx_session.post(
                f"/customers/{metronome_customer_id}/plans/add",
                data={
                    "plan_id": plan_for_customer,
                    "starting_on": starting_on,
                },
            )
            await logger.ainfo(
                "added metronome customer to initial plan",
                metadata={
                    "organization_id": str(organization_id),
                    "metronome_customer_id": metronome_customer_id,
                    "plan_id": plan_for_customer,
                },
            )
        except httpx.HTTPStatusError as e:
            await logger.aexception(
                f"Failed to add Metronome customer to base plan. {e}"
            )
            raise e

        if tier_for_customer in TIERS_WITH_SEAT_BASED_BILLING:
            await identities_crud.record_initial_seat_count(organization_id)


@retry_stripe
async def _create_stripe_setup_intent(customer_id: str) -> SetupIntent:
    """Create a new Stripe setup intent."""
    client = stripe.client
    return await client.setup_intents.create_async(params={"customer": customer_id})


@retry_stripe
async def _modify_stripe_customer_metadata(
    metronome_customer_id: str, stripe_customer: Customer
) -> None:
    await stripe.client.customers.update_async(
        stripe_customer.id,
        {"metadata": {"metronome_customer_id": metronome_customer_id}},
    )


async def _ensure_metronome_customer(
    organization_id: uuid.UUID,
    stripe_customer_id: str,
    org_display_name: str,
    metronome_customer_id: str | None,
) -> str:
    """Create a new Metronome customer if it doesn't already exist."""
    if metronome_customer_id:
        if await _check_metronome_customer(metronome_customer_id, stripe_customer_id):
            return metronome_customer_id
    async with metronome.metronome_client() as httpx_session:
        try:
            response = await httpx_session.post(
                "/customers",
                data={
                    "ingest_aliases": [str(organization_id)],
                    "name": org_display_name,
                    "billing_config": {
                        "billing_provider_type": "stripe",
                        "billing_provider_customer_id": stripe_customer_id,
                        "stripe_collection_method": "charge_automatically",
                    },
                },
            )
            customer_id = response["data"]["id"]
            await logger.ainfo(
                "created metronome customer",
                metadata={
                    "organization_id": str(organization_id),
                    "metronome_customer_id": customer_id,
                    "strip_customer_id": stripe_customer_id,
                },
            )
            await _add_metronome_customer_to_initial_plan(organization_id, customer_id)
            return customer_id
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 409:
                id = e.response.json()["conflicting_id"]
                if not id:
                    raise HTTPException(502, "Failed to fetch Metronome customer.")

                await logger.awarn(
                    "Found conflicting metronome customer for ingest alias. Using existing metronome customer.",
                    metadata={
                        "organization_id": str(organization_id),
                        "metronome_customer_id": str(id),
                    },
                )
                return id
            else:
                await logger.aexception(
                    "Failed to create Metronome customer.", exc_info=e
                )
                raise e


@retry_stripe
async def _ensure_stripe_customer(
    user_email: str,
    organization_id: uuid.UUID,
    org_display_name: str,
    stripe_customer_id: str | None,
) -> Customer:
    """Create a new Stripe customer if it doesn't already exist."""
    if stripe_customer_id:
        customer_info, customer = await _get_stripe_customer_billing_info(
            stripe_customer_id, ["invoice_settings.default_payment_method"]
        )
        if customer_info.customer_exists:
            return customer
    try:
        client = stripe.client
        return await client.customers.create_async(
            params={
                "email": user_email,
                "name": org_display_name,
                "metadata": {"organization_id": str(organization_id)},
            },
        )
    except Exception as e:
        await logger.aexception(f"Failed to create Stripe customer. {e}")
        raise e


class StripeCustomerBillingInfo(BaseModel):
    """Stripe customer billing info."""

    has_payment_method: bool
    customer_exists: bool


@retry_asyncpg
async def get_organization_info(
    auth: OrgAuthInfo,
) -> schemas.Organization:
    """Get info about the organization."""

    async with database.asyncpg_conn() as db:
        reached_max_workspaces = await auth_has_reached_max_workspaces(db, auth)
        organization = await get_organization(auth.organization_id)
    wallet = organization.wallet
    if is_payment_enabled():
        if (
            organization.metronome_customer_id is None
            or organization.stripe_customer_id is None
        ):
            _, organization = await create_external_billing_customers_internal(
                cast(uuid.UUID, auth.organization_id), cast(str, auth.user_email)
            )

        try:
            (
                stripe_customer_info,
                metronome_customer_exists,
                relevant_plans_for_customer,
            ) = await asyncio.gather(
                _get_stripe_customer_billing_info(
                    organization.stripe_customer_id,
                    ["invoice_settings.default_payment_method"],
                ),
                _check_metronome_customer(
                    organization.metronome_customer_id, organization.stripe_customer_id
                ),
                shared.get_relevant_plans_for_customer(
                    organization.metronome_customer_id
                ),
            )
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                raise HTTPException(
                    status_code=404, detail="Billing information not found"
                )
            raise e

        current_plan = relevant_plans_for_customer.current_plan
        midnight_start_of_next_billing_period_utc = _get_billing_period_end_date(
            current_plan
        )

        upcoming_plan = relevant_plans_for_customer.upcoming_plan
        has_cancelled = (
            current_plan is not None
            and current_plan.ends_on is not None
            and midnight_start_of_next_billing_period_utc is not None
            and current_plan.ends_on <= midnight_start_of_next_billing_period_utc
            and not upcoming_plan
        )

        billing_info, customer = stripe_customer_info
        payment_method = None
        try:
            if (
                billing_info.has_payment_method
                and customer
                and customer.invoice_settings.default_payment_method
                and customer.invoice_settings.default_payment_method.card
            ):
                payment_method = schemas.StripePaymentMethodInfo(
                    brand=customer.invoice_settings.default_payment_method.card.brand,
                    last4=customer.invoice_settings.default_payment_method.card.last4,
                    exp_month=customer.invoice_settings.default_payment_method.card.exp_month,
                    exp_year=customer.invoice_settings.default_payment_method.card.exp_year,
                    email=customer.invoice_settings.default_payment_method.billing_details.email,
                )

        except AttributeError:
            payment_method = schemas.StripePaymentMethodInfo(  # TODO: support more types of payments other than just cards
                brand=None,
                last4=None,
                exp_month=None,
                exp_year=None,
                email=customer.invoice_settings.default_payment_method.billing_details.email,
            )
        return schemas.Organization(
            id=organization.id,
            display_name=organization.display_name,
            connected_to_stripe=billing_info.customer_exists,
            connected_to_metronome=metronome_customer_exists,
            config=auth.org_config,
            tier=current_plan.tier if current_plan else None,
            is_personal=organization.is_personal,
            payment_method=payment_method,
            has_cancelled=has_cancelled,
            end_of_billing_period=midnight_start_of_next_billing_period_utc,
            current_plan=current_plan.to_customer_visible_plan_info()
            if current_plan
            else None,
            upcoming_plan=upcoming_plan.to_customer_visible_plan_info()
            if upcoming_plan
            else None,
            reached_max_workspaces=reached_max_workspaces,
            permissions=auth.identity_permissions,
            marketplace_payouts_enabled=organization.stripe_connected_account_id
            is not None,
            wallet={
                "credit_balance_micros": wallet.credit_balance_micros,
                "inflight_balance_micros": wallet.inflight_balance_micros,
            }
            if wallet
            else None,
        )
    else:
        # This route is called when payment is not enabled.
        return schemas.Organization(
            id=organization.id,
            display_name=organization.display_name,
            connected_to_stripe=True,
            connected_to_metronome=True,
            config=auth.org_config,
            tier="enterprise",
            is_personal=auth.organization_is_personal,
            payment_method=None,
            has_cancelled=False,
            end_of_billing_period=None,
            current_plan=None,
            upcoming_plan=None,
            reached_max_workspaces=reached_max_workspaces,
            permissions=auth.identity_permissions,
            marketplace_payouts_enabled=organization.stripe_connected_account_id
            is not None,
            wallet={
                "credit_balance_micros": wallet.credit_balance_micros,
                "inflight_balance_micros": wallet.inflight_balance_micros,
            }
            if wallet
            else None,
        )


@retry_asyncpg
async def get_organization_info_slim(
    auth: OrgAuthInfo,
) -> schemas.OrganizationInfo:
    """Get info about the organization."""
    async with database.asyncpg_conn() as db:
        reached_max_workspaces = await auth_has_reached_max_workspaces(db, auth)
    organization = await get_organization(auth.organization_id)
    wallet = organization.wallet

    if is_payment_enabled():
        metronome_details = await metronome_cache.force_get(organization.id)
        tier = (
            metronome_details.plan_details.get("data", {})
            .get("custom_fields", {})
            .get("__tier", schemas.PaymentPlanTier.no_plan)
        )
        return schemas.OrganizationInfo(
            id=organization.id,
            display_name=organization.display_name,
            config=auth.org_config,
            tier=tier,
            is_personal=organization.is_personal,
            reached_max_workspaces=reached_max_workspaces,
            permissions=auth.identity_permissions,
            disabled=organization.disabled,
            sso_login_slug=organization.sso_login_slug,
            sso_only=organization.sso_only,
            jit_provisioning_enabled=organization.jit_provisioning_enabled,
            marketplace_payouts_enabled=organization.stripe_connected_account_id
            is not None,
            wallet={
                "credit_balance_micros": wallet.credit_balance_micros,
                "inflight_balance_micros": wallet.inflight_balance_micros,
            }
            if wallet
            else None,
        )
    else:
        # This route is called when payment is not enabled.
        return schemas.OrganizationInfo(
            id=organization.id,
            display_name=organization.display_name,
            config=auth.org_config,
            tier=schemas.PaymentPlanTier.enterprise_legacy,
            is_personal=auth.organization_is_personal,
            reached_max_workspaces=reached_max_workspaces,
            permissions=auth.identity_permissions,
            disabled=organization.disabled,
            sso_login_slug=organization.sso_login_slug,
            sso_only=organization.sso_only,
            jit_provisioning_enabled=organization.jit_provisioning_enabled,
            marketplace_payouts_enabled=organization.stripe_connected_account_id
            is not None,
            wallet={
                "credit_balance_micros": wallet.credit_balance_micros,
                "inflight_balance_micros": wallet.inflight_balance_micros,
            }
            if wallet
            else None,
        )


@retry_asyncpg
async def get_organization_billing_info(
    auth: OrgAuthInfo,
) -> schemas.OrganizationBillingInfo:
    """Get info about the organization's billing configuration."""
    if not is_payment_enabled():
        raise HTTPException(
            status_code=404, detail="Payment is not enabled for this organization."
        )
    organization = await get_organization(auth.organization_id)
    if (
        organization.metronome_customer_id is None
        or organization.stripe_customer_id is None
    ):
        _, organization = await create_external_billing_customers_internal(
            cast(uuid.UUID, auth.organization_id), cast(str, auth.user_email)
        )
    try:
        (
            stripe_customer_info,
            metronome_customer_exists,
            relevant_plans_for_customer,
        ) = await asyncio.gather(
            _get_stripe_customer_billing_info(
                organization.stripe_customer_id,
                ["invoice_settings.default_payment_method"],
            ),
            _check_metronome_customer(
                organization.metronome_customer_id, organization.stripe_customer_id
            ),
            shared.get_relevant_plans_for_customer(organization.metronome_customer_id),
        )
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            raise HTTPException(status_code=404, detail="Billing information not found")
        raise e
    current_plan = relevant_plans_for_customer.current_plan
    upcoming_plan = relevant_plans_for_customer.upcoming_plan
    billing_info, customer = stripe_customer_info
    payment_method = None
    try:
        if (
            billing_info.has_payment_method
            and customer
            and customer.invoice_settings.default_payment_method
            and customer.invoice_settings.default_payment_method.card
        ):
            payment_method = schemas.StripePaymentMethodInfo(
                brand=customer.invoice_settings.default_payment_method.card.brand,
                last4=customer.invoice_settings.default_payment_method.card.last4,
                exp_month=customer.invoice_settings.default_payment_method.card.exp_month,
                exp_year=customer.invoice_settings.default_payment_method.card.exp_year,
                email=customer.invoice_settings.default_payment_method.billing_details.email,
            )
    except AttributeError:
        payment_method = schemas.StripePaymentMethodInfo(  # TODO: support more types of payments other than just cards
            brand=None,
            last4=None,
            exp_month=None,
            exp_year=None,
            email=customer.invoice_settings.default_payment_method.billing_details.email,
        )

    async with database.asyncpg_conn() as db:
        reached_max_workspaces = await auth_has_reached_max_workspaces(db, auth)

    midnight_start_of_next_billing_period_utc = _get_billing_period_end_date(
        current_plan
    )

    return schemas.OrganizationBillingInfo(
        id=organization.id,
        display_name=organization.display_name,
        config=auth.org_config,
        connected_to_stripe=billing_info.customer_exists,
        connected_to_metronome=metronome_customer_exists,
        tier=current_plan.tier if current_plan else None,
        is_personal=organization.is_personal,
        payment_method=payment_method,
        end_of_billing_period=midnight_start_of_next_billing_period_utc,
        reached_max_workspaces=reached_max_workspaces,
        current_plan=current_plan.to_customer_visible_plan_info()
        if current_plan
        else None,
        upcoming_plan=upcoming_plan.to_customer_visible_plan_info()
        if upcoming_plan
        else None,
        disabled=organization.disabled,
    )


@retry_stripe
async def _get_stripe_customer_billing_info(
    stripe_customer_id: str | None, expand_info: list[str]
) -> tuple[StripeCustomerBillingInfo, Customer | None]:
    """Get the billing info for a Stripe customer."""
    if stripe_customer_id is None:
        return (
            StripeCustomerBillingInfo(has_payment_method=False, customer_exists=False),
            None,
        )
    try:
        client = stripe.client
        customer = await client.customers.retrieve_async(
            stripe_customer_id, {"expand": expand_info}
        )
        if not customer.get("deleted"):
            return (
                StripeCustomerBillingInfo(
                    has_payment_method=customer.invoice_settings.default_payment_method
                    is not None,
                    customer_exists=True,
                ),
                customer,
            )
    except InvalidRequestError as e:
        # If 404, customer doesn't exist. Else, something else went wrong.
        if e.http_status != 404:
            await logger.aexception(f"Failed to fetch Stripe Customer. {e}")
            raise e
    return (
        StripeCustomerBillingInfo(has_payment_method=False, customer_exists=False),
        None,
    )


async def _check_metronome_customer(
    metronome_customer_id: str | None, stripe_customer_id: str | None
) -> bool:
    """Check if a Metronome customer exists."""
    if metronome_customer_id is None:
        return False
    async with metronome.metronome_client() as httpx_session:
        try:
            stripe_config = await httpx_session.get(
                f"/customers/{metronome_customer_id}/billing-config/stripe",
            )
            if (
                stripe_customer_id is not None
                and not stripe_config.get("data", {}).get(
                    "billing_provider_customer_id"
                )
                == stripe_customer_id
            ):
                await httpx_session.post(
                    f"/customers/{metronome_customer_id}/billing-config/stripe",
                    data={
                        "billing_provider_type": "stripe",
                        "billing_provider_customer_id": stripe_customer_id,
                        "stripe_collection_method": "charge_automatically",
                    },
                )
            return True
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                return False
            await logger.aexception(f"Failed to fetch Metronome Customer. {e}")
            raise e


@retry_asyncpg
@retry_stripe
async def on_payment_method_created(
    auth: OrgAuthInfo, payment_method: schemas.StripePaymentInformation
) -> None:
    """Handle a new payment method being created."""
    if is_payment_enabled():
        client = stripe.client
        organization, setup_intent_response = await asyncio.gather(
            get_organization(auth.organization_id),
            client.setup_intents.retrieve_async(payment_method.setup_intent),
        )
        if (
            organization.stripe_customer_id != setup_intent_response.customer
            or setup_intent_response.status != "succeeded"
        ):
            raise HTTPException(status_code=404, detail="Customer not found")
        customer = await client.customers.retrieve_async(
            organization.stripe_customer_id
        )

        payment_methods = await customer.list_payment_methods_async()
        if (
            payment_methods
            and organization.stripe_customer_id
            and organization.metronome_customer_id
            and payment_methods.get("data")
            and len(payment_methods.get("data"))
        ):
            # Get the payment method in data with the highest "created" timestamp
            latest_payment_method = max(
                payment_methods["data"], key=lambda x: x.get("created")
            )
            coroutines = [
                client.customers.update_async(
                    organization.stripe_customer_id,
                    {
                        "invoice_settings": {
                            "default_payment_method": latest_payment_method["id"],
                        },
                        "name": payment_method.billing_info.name,
                        "email": auth.user_email,
                        "address": {
                            "city": payment_method.billing_info.address.city,
                            "country": payment_method.billing_info.address.country,
                            "line1": payment_method.billing_info.address.line1,
                            "line2": payment_method.billing_info.address.line2,
                            "postal_code": payment_method.billing_info.address.postal_code,
                            "state": payment_method.billing_info.address.state,
                        },
                    },
                ),
            ]
            if auth.organization_is_personal:
                # If this is a personal tenant, automatically upgrade them to the developer plan. For non-personal tenants, we do this separately as it will cause them to be charged for seats.
                relevant_plans_for_customer = (
                    await shared.get_relevant_plans_for_customer(
                        organization.metronome_customer_id
                    )
                )

                current_plan = relevant_plans_for_customer.current_plan
                current_billing_period_end_date = _get_billing_period_end_date(
                    current_plan
                )

                if (
                    not current_plan
                    or not current_plan.tier == schemas.PaymentPlanTier.developer
                ):
                    coroutines.append(
                        _switch_payment_plan_unsafe(
                            schemas.PaymentPlanTier.developer,
                            organization.metronome_customer_id,
                            current_plan,
                            current_billing_period_end_date.strftime(
                                "%Y-%m-%dT%H:%M:%SZ"
                            )
                            if current_billing_period_end_date
                            else None,
                            organization.id,
                        )
                    )
                async with metronome.metronome_client() as httpx_session:
                    coroutines.append(
                        httpx_session.post(
                            f"/customers/{organization.metronome_customer_id}/setName",
                            data={
                                "name": payment_method.billing_info.name,
                            },
                        )
                    )

            # Now delete all other payment methods other than latest_payment_method
            for pm in payment_methods["data"]:
                if pm["id"] != latest_payment_method["id"]:
                    coroutines.append(client.payment_methods.detach_async(pm["id"]))
            await asyncio.gather(*coroutines)
        else:
            raise HTTPException(status_code=404, detail="Customer not found")
    else:
        raise HTTPException(
            status_code=404, detail="Payment is not enabled for this organization"
        )
    return


async def _verify_switch_payment_plan(
    auth: OrgAuthInfo,
    new_tier: schemas.PaymentPlanTier,
    current_tier: schemas.PaymentPlanTier,
    current_billing_info_opt: StripeCustomerBillingInfo | None,
) -> None:
    """Verify that the payment plan can be switched."""
    valid_tier_switches_for_current_tier = ALLOWED_TIER_SWITCHES.get(current_tier, [])
    if new_tier not in valid_tier_switches_for_current_tier:
        await logger.awarn(
            "Invalid upgrade plan request",
            metadata={
                "organization_id": str(auth.organization_id),
                "current_tier": current_tier.value,
                "new_tier": new_tier.value,
            },
        )
        raise HTTPException(
            status_code=400,
            detail=f"cannot upgrade from {current_tier.value} plan to {new_tier.value} plan",
        )

    if (
        current_billing_info_opt is None
        or not current_billing_info_opt.has_payment_method
    ) and new_tier != schemas.PaymentPlanTier.no_plan:
        raise HTTPException(
            status_code=404,
            detail="No payment method found for this organization",
        )

    if new_tier in APPROVAL_ONLY_TIERS:
        approval_date = _get_approval_date_for_tier(new_tier, auth)

        if not approval_date:
            raise HTTPException(
                status_code=400,
                detail=f"not approved for {new_tier.value} plan",
            )

        now = datetime.now(timezone.utc)
        if (now - approval_date).days > settings.PLAN_APPROVAL_VALIDITY_DAYS:
            raise HTTPException(
                status_code=400,
                detail=f"{new_tier.value} approval has expired",
            )


@retry_asyncpg
async def switch_payment_plan(
    auth: OrgAuthInfo,
    change_plan_req: schemas.ChangePaymentPlanSchema,
) -> None:
    """Switch the payment plan for an organization."""
    if is_payment_enabled():
        organization = await get_organization(auth.organization_id)
        metronome_customer_id = organization.metronome_customer_id
        stripe_customer_id = organization.stripe_customer_id
        if metronome_customer_id is None or stripe_customer_id is None:
            _, organization = await create_external_billing_customers_internal(
                cast(uuid.UUID, auth.organization_id), cast(str, auth.user_email)
            )
            metronome_customer_id = organization.metronome_customer_id
            stripe_customer_id = organization.stripe_customer_id
        (
            billing_info,
            relevant_plans_for_customer,
        ) = await asyncio.gather(
            _get_stripe_customer_billing_info(
                stripe_customer_id, ["invoice_settings.default_payment_method"]
            ),
            shared.get_relevant_plans_for_customer(metronome_customer_id),
        )
        current_plan = relevant_plans_for_customer.current_plan
        current_billing_period_end_date = _get_billing_period_end_date(current_plan)

        upcoming_plan = relevant_plans_for_customer.upcoming_plan
        billing_info, _ = billing_info
        new_tier = change_plan_req.tier.to_payment_plan_tier()
        await _verify_switch_payment_plan(
            auth,
            new_tier=new_tier,
            current_tier=current_plan.tier
            if current_plan
            else schemas.PaymentPlanTier.no_plan,
            current_billing_info_opt=billing_info,
        )

        await _switch_payment_plan_unsafe(
            new_tier,
            metronome_customer_id,
            current_plan,
            current_billing_period_end_date.strftime("%Y-%m-%dT%H:%M:%SZ")
            if current_billing_period_end_date
            else None,
            organization.id,
            upcoming_plan=upcoming_plan,
        )

        if (
            current_plan
            and current_plan.tier in PERSONAL_TIERS
            and new_tier not in PERSONAL_TIERS
        ):
            log_md = {
                "organization_id": str(organization.id),
                "metronome_customer_id": metronome_customer_id,
                "new_plan_tier": new_tier.value,
                "current_plan_tier": current_plan.tier.value if current_plan else None,
            }

            await logger.ainfo(
                "Customer upgrading from personal to non-personal tier. Updating customer and org information",
                metadata=log_md,
            )

            await _update_info_from_personal_to_non_personal(
                metronome_customer_id,
                stripe_customer_id,
                organization.id,
                organization.display_name,
            )
    else:
        raise HTTPException(
            status_code=404, detail="Payment is not enabled for this organization"
        )
    return


async def _switch_payment_plan_unsafe(
    new_tier: schemas.PaymentPlanTier,
    metronome_customer_id: str,
    current_plan: schemas.PaymentPlanInfo | None,
    current_billing_period_end_date: str | None,
    organization_id: uuid.UUID,
    upcoming_plan: schemas.PaymentPlanInfo | None = None,
) -> None:
    """Switch the payment plan for an organization. Skips the verification check that ensures that the plan is allowed for the current auth."""
    starting_today = _get_previous_midnight_utc()

    end_plan_req: dict[str, str | None] | None = None

    new_plan_base_dict: dict | None = None

    log_md = {
        "organization_id": str(organization_id),
        "metronome_customer_id": metronome_customer_id,
        "new_plan_tier": new_tier.value,
        "current_plan_tier": current_plan.tier.value if current_plan else None,
    }

    # The customer has an existing plan and wants to make a change
    if current_plan:
        if new_tier == schemas.PaymentPlanTier.no_plan:
            if current_billing_period_end_date is None:
                raise HTTPException(
                    status_code=500,
                    detail="An error while cancelling your subscription - please reach <NAME_EMAIL>",
                )

            await logger.ainfo("Cancelling plan", metadata=log_md)
            # Give access until end of billing period.
            end_plan_req = {"ending_before": current_billing_period_end_date}

        elif new_tier == current_plan.tier:
            if current_plan.ends_on is not None:
                await logger.ainfo("Un-cancelling plan", metadata=log_md)
                # Remove the cancellation date to re-subscribe them.
                # Note: we do not support uncanceling to a different plan.
                end_plan_req = {}

            else:
                await logger.awarn(
                    "received unexpected switch plan request to the same plan. Ignoring.",
                    metadata=log_md,
                )
                return

        elif (
            # end previous plan at the end of the billing period, since they
            # have already paid for the seats for this month, so we can't end
            # the plan immediately
            current_plan.tier in TIERS_WITH_SEAT_BASED_BILLING
            or (
                # If the customer is switching from a paid plan to a free plan, we
                # should also end the current plan at the end of the billing period,
                # otherwise you can just switch from paid <> unpaid every day and
                # get free traces, since we restart an invoice every time you switch
                new_tier == schemas.PaymentPlanTier.free
                and current_plan.tier == schemas.PaymentPlanTier.developer
            )
        ):
            await logger.ainfo(
                "Customer switching plans at end of billing period",
                metadata={
                    **log_md,
                    "new_plan_start_date": current_billing_period_end_date,
                },
            )

            end_plan_req = {"ending_before": current_billing_period_end_date}
            new_plan_base_dict = {
                "starting_on": current_billing_period_end_date,
            }

        else:
            await logger.ainfo(
                "Customer switching plans now",
                metadata={
                    **log_md,
                    "new_plan_start_date": starting_today,
                },
            )
            # end previous plan immediately to enable switch
            new_plan_base_dict = {
                "starting_on": starting_today,
            }
            end_plan_req = {"ending_before": starting_today}

    # The customer has no existing plan and wants to start a new one
    elif new_tier != schemas.PaymentPlanTier.no_plan:
        await logger.ainfo(
            "Customer starting plan",
            metadata=log_md,
        )
        new_plan_base_dict = {
            "starting_on": starting_today,
        }

    # End approval plans after two years
    if new_plan_base_dict is not None and new_tier in APPROVAL_ONLY_TIERS:
        new_plan_base_dict["ending_before"] = datetime.strftime(
            (
                datetime.strptime(
                    new_plan_base_dict["starting_on"], "%Y-%m-%dT%H:%M:%SZ"
                )
                + relativedelta.relativedelta(years=2)
            ),
            "%Y-%m-%dT%H:%M:%SZ",
        )

    async with (
        metronome.metronome_client() as httpx_session,
        metronome_cache.force_update_for_org(organization_id),
    ):
        try:
            if end_plan_req is not None:
                if not current_plan:
                    raise ValueError("Cannot end a plan that does not exist")

                await httpx_session.post(
                    f"/customers/{metronome_customer_id}/plans/{current_plan.id}/end",
                    data=end_plan_req,
                )

                # Remove any upcoming plans if switching plans too.
                # This was to fix bug when upcoming plan is free (after cancelling developer) and user
                # can't upgrade to plus before waiting out the period before their dev plan ends.
                if upcoming_plan:
                    await httpx_session.post(
                        f"/customers/{metronome_customer_id}/plans/{upcoming_plan.id}/end",
                        data={
                            "ending_before": upcoming_plan.started_on.strftime(
                                "%Y-%m-%dT%H:%M:%SZ"
                            )
                        },
                    )

            if new_tier == schemas.PaymentPlanTier.no_plan:
                response = await httpx_session.get(
                    f"/customers/{metronome_customer_id}/plans", params={"limit": 100}
                )

                for plan in response.get("data", []):
                    starting_on = shared.parse_metronome_timestamp(
                        plan.get("starting_on")
                    )
                    if (
                        starting_on is not None
                        and current_plan is not None
                        and current_plan.started_on is not None
                        and starting_on > current_plan.started_on
                    ):
                        await logger.ainfo(
                            "Cancelling future plan",
                            metadata={
                                **log_md,
                                "customer_plan_id": plan["id"],
                                "plan_id": plan["plan_id"],
                                "starting_on": plan["starting_on"],
                            },
                        )
                        await httpx_session.post(
                            f"/customers/{metronome_customer_id}/plans/{plan['id']}/end",
                            data={"ending_before": plan["starting_on"]},
                        )

            if new_plan_base_dict:
                plan_id_to_start = TIER_TO_CURRENT_PLAN_MAP.get(new_tier.value)
                if not plan_id_to_start:
                    raise NotImplementedError(
                        f"Payment plan {new_tier.value} is not yet supported"
                    )

                await httpx_session.post(
                    f"/customers/{metronome_customer_id}/plans/add",
                    data={
                        "plan_id": TIER_TO_CURRENT_PLAN_MAP.get(new_tier.value),
                        **new_plan_base_dict,
                    },
                )

        except httpx.HTTPStatusError as e:
            await logger.aexception(
                f"Failed to switch payment plan. {e.response.json()}"
            )
            raise e

    if new_tier in TIERS_WITH_SEAT_BASED_BILLING:
        await identities_crud.record_initial_seat_count(organization_id)


async def update_metronome_customer_name(
    metronome_customer_id: str,
    organization_name: str,
    organization_id: uuid.UUID,
) -> None:
    async with (
        metronome.metronome_client() as httpx_session,
        metronome_cache.force_update_for_org(organization_id),
    ):
        try:
            await httpx_session.post(
                f"/customers/{metronome_customer_id}/setName",
                data={
                    "name": organization_name,
                },
            )
        except httpx.HTTPStatusError as e:
            await logger.aexception(
                f"Failed to switch payment plan. {e.response.json()}"
            )
            raise e


async def update_stripe_customer_name(
    stripe_customer_id: str,
    organization_name: str,
) -> None:
    await stripe.client.customers.update_async(
        stripe_customer_id,
        {
            "name": organization_name,
        },
    )


async def _update_info_from_personal_to_non_personal(
    metronome_customer_id: str,
    stripe_customer_id: str,
    organization_id: uuid.UUID,
    organization_name: str,
) -> None:
    await update_metronome_customer_name(
        metronome_customer_id,
        organization_name,
        organization_id,
    )

    await update_stripe_customer_name(
        stripe_customer_id,
        organization_name,
    )

    async with database.asyncpg_conn() as db:
        await db.execute(
            "UPDATE organizations SET config = config || $1, is_personal = $2 where id = $3",
            config.settings.SHARED_ORG_DEFAULT_CONFIG.model_dump(exclude={"flags"}),
            False,
            organization_id,
        )
        workspace_ids = await db.fetch(
            "SELECT id FROM tenants WHERE organization_id = $1", organization_id
        )

        await db.execute(
            "UPDATE tenants SET config = config || $1 where id = ANY($2)",
            config.settings.SHARED_TENANT_DEFAULT_CONFIG.model_dump(exclude={"flags"}),
            workspace_ids,
        )


def _get_previous_midnight_utc() -> str:
    """Get the next midnight in UTC."""
    # metronome requires a timestamp at UTC midnight lol
    current_time = datetime.now(timezone.utc)
    previous_midnight_utc = (current_time).replace(
        hour=0, minute=0, second=0, microsecond=0
    )
    return previous_midnight_utc.strftime("%Y-%m-%dT%H:%M:%SZ")


async def get_num_workspaces_in_org(db: Connection, organization_id: uuid.UUID) -> int:
    query = """
    SELECT count(*) FROM tenants WHERE organization_id = $1 AND NOT is_deleted;
    """
    return await db.fetchval(query, organization_id)


# This one should only be used if OrgAuthInfo isn't available. prefer auth_has_reached_max_workspaces
async def config_has_reached_max_workspaces(
    db: Connection,
    config: TenantConfig | OrganizationConfig,
    organization_id: uuid.UUID,
) -> bool:
    # Check to see if this org has reached the maximum number of workspaces.
    # -1 means unlimited workspaces.
    workspace_count = await get_num_workspaces_in_org(db, organization_id)
    return config.max_workspaces != -1 and workspace_count >= config.max_workspaces


async def auth_has_reached_max_workspaces(
    db: Connection,
    auth: OrgAuthInfo,
) -> bool:
    """Check from provided auth if the organization has reached its maximum number of workspaces."""
    return await config_has_reached_max_workspaces(
        db, auth.org_config, auth.organization_id
    )


# Get company info stored in stripe
async def get_company_info(auth: OrgAuthInfo) -> schemas.StripeBusinessInfo:
    if is_payment_enabled():
        organization = await get_organization(auth.organization_id)
        stripe_customer_id = organization.stripe_customer_id
        if stripe_customer_id is None:
            raise HTTPException(status_code=404, detail="Stripe customer not found")

        _, customer = await _get_stripe_customer_billing_info(
            stripe_customer_id, ["tax_ids"]
        )

        try:
            tax_id = None
            if customer.tax_ids.data and len(customer.tax_ids.data) > 0:
                s_tax_id = customer.tax_ids.data[0]
                tax_id = schemas.StripeTaxId(value=s_tax_id.value, type=s_tax_id.type)

            is_business = customer.metadata.get("has_business_info") or False
            return schemas.StripeBusinessInfo(
                company_info=schemas.StripeBusinessBillingInfo(
                    name=customer.name,
                    address=schemas.StripeCustomerAddress(
                        city=customer.address.city,
                        country=customer.address.country,
                        line1=customer.address.line1,
                        line2=customer.address.line2,
                        postal_code=customer.address.postal_code,
                        state=customer.address.state,
                    ),
                ),
                tax_id=tax_id,
                invoice_email=customer.email,
                is_business=is_business,
            )

        except AttributeError:
            return schemas.StripeBusinessInfo(
                company_info=None, tax_ids=[], invoice_email=None, is_business=False
            )

    else:
        raise HTTPException(
            status_code=404, detail="Payment is not enabled for this organization"
        )


# Get company info stored in stripe
async def set_company_info(auth: OrgAuthInfo, info: schemas.StripeBusinessInfo) -> None:
    if is_payment_enabled():
        organization = await get_organization(auth.organization_id)
        stripe_customer_id = organization.stripe_customer_id
        if stripe_customer_id is None:
            raise HTTPException(status_code=404, detail="Stripe customer not found")

        try:
            update_info: dict = {}
            if info.is_business is not None:
                update_info["metadata"] = {"has_business_info": info.is_business}
            if info.invoice_email is not None:
                update_info["email"] = info.invoice_email
            if info.company_info is not None:
                if info.company_info.address is not None:
                    update_info["address"] = {
                        "city": info.company_info.address.city,
                        "country": info.company_info.address.country,
                        "line1": info.company_info.address.line1,
                        "line2": info.company_info.address.line2,
                        "postal_code": info.company_info.address.postal_code,
                        "state": info.company_info.address.state,
                    }
                if info.company_info.name is not None:
                    update_info["name"] = info.company_info.name

            stripe.client.customers.update(
                stripe_customer_id,
                update_info,
            )
        except AttributeError:
            raise HTTPException(
                status_code=400, detail="Failed to update business info"
            )

        _, customer = await _get_stripe_customer_billing_info(
            stripe_customer_id, ["tax_ids"]
        )

        if info.tax_id is not None:
            prev_tax_id = None
            try:
                prev_tax_id = (
                    customer.tax_ids.data[0]
                    if customer.tax_ids.data and len(customer.tax_ids.data) > 0
                    else None
                )
            except AttributeError:
                raise HTTPException(status_code=400, detail="Failed to retrieve tax id")

            try:
                if prev_tax_id:
                    stripe.client.customers.tax_ids.delete(
                        stripe_customer_id, prev_tax_id.id
                    )
            except Exception:
                raise HTTPException(status_code=500, detail="Failed to delete tax ids")

            try:
                stripe.client.customers.tax_ids.create(
                    stripe_customer_id, info.tax_id.model_dump()
                )

            except Exception:
                raise HTTPException(status_code=500, detail="Failed to add tax id")
    else:
        raise HTTPException(
            status_code=404, detail="Payment is not enabled for this organization"
        )


def _get_billing_period_end_date(
    current_plan: schemas.PaymentPlanInfo | None,
) -> datetime | None:
    if not current_plan:
        return None

    else:
        # NOTE: THIS CODE ASSUMES ALL PLANS ARE MONTH ALIGNED
        #       WILL NEED TO CHANGE THIS (ALONG WITH MANY OTHER
        #       ASSUMPTIONS) IF WE EVER SUPPORT PLANS THAT AREN'T
        #       MONTH ALIGNED
        curr_month_start = datetime.now(timezone.utc).replace(
            day=1, hour=0, minute=0, second=0, microsecond=0
        )
        return curr_month_start + relativedelta.relativedelta(months=1)


# Credit purchases


@retry_stripe
async def create_checkout_session(
    organization_id: uuid.UUID, user_email: str, amount_cents: int, success_path: str
):
    """Create a new customer and get a Stripe setup intent."""
    organization = await get_organization(organization_id)
    display_name = organization.display_name
    if organization.is_personal:
        display_name = user_email
    stripe_customer = await _ensure_stripe_customer(
        user_email, organization_id, display_name, organization.stripe_customer_id
    )
    if amount_cents <= 0 or amount_cents >= 100000:
        raise HTTPException(
            status_code=400, detail="Amount must be greater than 0 and less than $1000."
        )
    checkout_session = await stripe.client.checkout.sessions.create_async(
        {
            "customer": stripe_customer.id,
            "line_items": [
                {
                    "price_data": {
                        "currency": "usd",
                        "product_data": {"name": "LangSmith Agent Marketplace Credits"},
                        "unit_amount": amount_cents,
                    },
                    "quantity": 1,
                },
            ],
            "metadata": {
                "ls_marketplace_credit_purchase": True,
                "organization_id": organization.id,
            },
            "mode": "payment",
            "success_url": f"{settings.MARKETPLACE_URL}/{success_path}"
            + "?stripe_session_id={CHECKOUT_SESSION_ID}",
        }
    )
    return {"url": checkout_session.url}


@retry_stripe
async def confirm_checkout_session_completion(
    organization_id: uuid.UUID, stripe_session_id: str
):
    organization = await get_organization(organization_id)

    checkout_session = await stripe.client.checkout.sessions.retrieve_async(
        stripe_session_id,
        {"expand": ["payment_intent.latest_charge.balance_transaction"]},
    )
    if checkout_session.status != "complete":
        raise HTTPException(
            status_code=400,
            detail="The specified session has not yet completed.",
        )
    if (
        checkout_session.payment_intent is None
        or checkout_session.payment_intent.latest_charge is None
        or checkout_session.payment_intent.latest_charge.balance_transaction is None
    ):
        raise HTTPException(
            status_code=500,
            detail="There is no balance transaction associated with this checkout session.",
        )
    if organization.stripe_customer_id != checkout_session.customer:
        raise HTTPException(
            status_code=500,
            detail="The provided session does not match the Stripe customer of the current organization.",
        )
    funding_amount_micros = (
        checkout_session.payment_intent.latest_charge.balance_transaction.net * 10000
    )
    async with database.asyncpg_conn() as db, db.transaction():
        wallet = await _find_or_create_wallet_for_organization(organization.id, db)
        try:
            credit_purchase = await db.fetchrow(
                """
                INSERT INTO marketplace_credit_purchases (
                    wallet_id,
                    initial_amount_micros,
                    amount_micros_remaining,
                    stripe_charge_id
                ) VALUES ($1, $2, $3, $4)
                RETURNING *
                """,
                wallet["id"],
                funding_amount_micros,
                funding_amount_micros,
                checkout_session.payment_intent.latest_charge.id,
            )
        except asyncpg.UniqueViolationError:
            # Transaction has been processed, just return
            return wallet
        await db.execute(
            """
            INSERT INTO wallet_transactions (
                wallet_id,
                credit_balance_delta_micros,
                type,
                credit_purchase_id
            )
            VALUES ($1, $2, $3, $4);
            """,
            wallet["id"],
            funding_amount_micros,
            "credit_purchase",
            credit_purchase["id"],
        )
        # Get the updated wallet information after the transaction
        updated_wallet = await db.fetchrow(
            """
            SELECT * FROM wallets WHERE id = $1
            """,
            wallet["id"],
        )
        return updated_wallet


async def _find_or_create_wallet_for_organization(
    organization_id: uuid.UUID, db: asyncpg.Connection
):
    wallet = await db.fetchrow(
        """
        SELECT id FROM wallets WHERE organization_id = $1 FOR UPDATE;
        """,
        organization_id,
    )
    if wallet is not None:
        return wallet

    new_wallet = await db.fetchrow(
        """
        INSERT INTO wallets (organization_id)
        VALUES ($1)
        RETURNING id;
        """,
        organization_id,
    )
    return new_wallet


# Payouts


@retry_stripe
async def find_or_create_stripe_connected_account(organization_id: uuid.UUID):
    organization = await get_organization(organization_id)
    if organization.stripe_connected_account_id is not None:
        return organization.stripe_connected_account_id
    account = await stripe.client.accounts.create_async()
    async with database.asyncpg_conn() as db:
        await db.execute(
            """
            UPDATE organizations
            SET stripe_connected_account_id = $1
            WHERE id = $2
            """,
            account.id,
            organization_id,
        )
        return account.id


async def create_stripe_account_link(
    connected_account_id: uuid.UUID, success_path: str
):
    try:
        account_link = await stripe.client.account_links.create_async(
            {
                "account": connected_account_id,
                "return_url": f"{settings.LANGSMITH_URL}/{success_path}",
                "refresh_url": f"{settings.LANGSMITH_URL}/{success_path}",
                "type": "account_onboarding",
            }
        )
        return {"url": account_link.url}
    except Exception as e:
        logger.warning(
            f"An error occurred when calling the Stripe API to create an account link: {e}",
        )
        raise HTTPException(
            status_code=500,
            detail="An error occurred when calling the Stripe API to create an account link.",
        )
