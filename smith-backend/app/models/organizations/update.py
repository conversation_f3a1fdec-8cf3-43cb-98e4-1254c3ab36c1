from typing import Dict, cast
from uuid import UUID

import asyncpg
import structlog
from fastapi import HTTPException
from lc_database.database import asyncpg_conn, kwargs_to_pgpos

from app import schemas
from app.api.auth.schemas import OrgAuthInfo
from app.models.organizations.payment import (
    get_organization_in_txn,
    update_metronome_customer_name,
    update_stripe_customer_name,
)
from app.schemas import OrganizationInfo, OrganizationUpdate

logger = structlog.get_logger(__name__)


async def unshare_datasets_for_org_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
) -> None:
    """Unshare all datasets for an organization."""
    await db.execute(
        """
        DELETE FROM datasets_share_keys
        WHERE dataset_id = ANY(
            SELECT id
            FROM dataset
            WHERE tenant_id = ANY(
                SELECT id
                FROM tenants
                WHERE organization_id = $1
            )
        )
        """,
        auth.organization_id,
    )


async def unshare_runs_for_org_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
) -> None:
    """Unshare all runs for an organization."""
    await db.execute(
        """
        DELETE FROM share_keys
        WHERE tenant_id = ANY(
            SELECT id
            FROM tenants
            WHERE organization_id = $1
        )
        """,
        auth.organization_id,
    )


async def unshare_repos_for_org_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
) -> None:
    await db.execute(
        """
        UPDATE hub_repos
        SET is_public = FALSE,
        updated_at = NOW()
        WHERE tenant_id = ANY(
            SELECT id FROM tenants WHERE organization_id = $1
        )
        AND is_public = TRUE
        """,
        auth.organization_id,
    )

    return None


async def unshare_all_for_org_in_txn(db: asyncpg.Connection, auth: OrgAuthInfo) -> None:
    """Unshare all entities in the organization: datasets, runs/traces, and prompts."""
    await unshare_datasets_for_org_in_txn(db, auth)
    await unshare_runs_for_org_in_txn(db, auth)
    await unshare_repos_for_org_in_txn(db, auth)


async def update_organization_info(
    auth: OrgAuthInfo, payload: OrganizationUpdate
) -> OrganizationInfo:
    """
    Update the organization display_name or other self-serve settings.

    Args:
        auth (OrgAuthInfo): Authentication information containing the organization ID.
        update_data (OrganizationUpdate): Data to update.

    Returns:
        OrganizationInfo: Updated organization information.

    Raises:
        HTTPException: If the update fails.
    """
    org_id = auth.organization_id
    display_name_set = "display_name" in payload.model_fields_set
    public_sharing_disabled_set = "public_sharing_disabled" in payload.model_fields_set
    jit_provisioning_enabled_set = (
        "jit_provisioning_enabled" in payload.model_fields_set
    )

    try:
        async with asyncpg_conn() as db, db.transaction():
            org = await get_organization_in_txn(db, org_id)
            if org.is_personal:
                raise HTTPException(
                    status_code=400, detail="Personal organizations cannot be updated"
                )

            if (
                public_sharing_disabled_set
                and not auth.org_config.can_disable_public_sharing
            ):
                raise HTTPException(
                    status_code=403,
                    detail="This organization does not have permission to disable public sharing",
                )

            updates = []
            sql_kwargs: Dict[str, str | bool | UUID | None] = {
                "id": org_id,
            }

            if display_name_set:
                updates.append("display_name = $display_name")
                sql_kwargs["display_name"] = payload.display_name

            if public_sharing_disabled_set:
                updates.append("public_sharing_disabled = $public_sharing_disabled")
                sql_kwargs["public_sharing_disabled"] = payload.public_sharing_disabled

            if jit_provisioning_enabled_set:
                updates.append("jit_provisioning_enabled = $jit_provisioning_enabled")
                sql_kwargs["jit_provisioning_enabled"] = (
                    payload.jit_provisioning_enabled
                )

            updates.append("modified_at = NOW()")
            updates_str = ", ".join(updates)
            sql_template = f"""
                UPDATE organizations
                SET {updates_str}
                WHERE id = $id
                RETURNING *
            """
            sql = kwargs_to_pgpos(sql_template, sql_kwargs)

            updated_org = await db.fetchrow(
                sql.sql,
                *sql.args,
            )

            if not updated_org:
                raise HTTPException(status_code=404, detail="Organization not found")

        if display_name_set:
            await update_metronome_customer_name(
                cast(str, org.metronome_customer_id),
                payload.display_name,
                org_id,
            )

            await update_stripe_customer_name(
                cast(str, org.stripe_customer_id),
                payload.display_name,
            )

        if payload.unshare_all is True:
            # Separate transaction to unshare any entities shared in parallel to org update above
            async with asyncpg_conn() as db, db.transaction():
                await unshare_all_for_org_in_txn(db, auth)

        return schemas.OrganizationInfo(**updated_org)

    except HTTPException as e:
        raise e
    except Exception as e:
        # Log the error
        logger.exception(f"Failed to update organization info: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to update organization information"
        )
