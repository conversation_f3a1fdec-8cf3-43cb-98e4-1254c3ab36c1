import logging
from datetime import datetime
from typing import cast
from uuid import UUID

import asyncpg
from fastapi import HTTPException

from app import config, schemas
from app.api.auth.schemas import AuthInfo, OrgAuthInfo
from app.models.identities import crud as identities_crud
from app.models.identities import seat_txn
from app.models.organizations.payment import (
    auth_has_reached_max_workspaces,
)
from app.models.tenants.tags import create_default_tag_keys
from app.retry import retry_asyncpg

logger = logging.getLogger(__name__)


@retry_asyncpg
async def create_workspace(
    auth: OrgAuthInfo, workspace: schemas.WorkspaceCreate
) -> schemas.Tenant:
    """Create a workspace in an existing organization."""
    if auth.organization_is_personal:
        raise HTTPException(
            status_code=400,
            detail="Personal organizations cannot create workspaces.",
        )

    tenant_values = workspace.model_dump()
    organization_id = auth.organization_id

    if config.settings.SINGLETON_TENANT_ID is not None:
        tenant_values["id"] = cast(UUID, config.settings.SINGLETON_TENANT_ID)
        tenant_values["tenant_handle"] = "default"

    if not tenant_values.get("config"):
        tenant_values["config"] = (
            config.settings.SHARED_TENANT_DEFAULT_CONFIG.model_dump()
        )

    if not tenant_values.get("is_personal"):
        tenant_values["is_personal"] = False

    async with seat_txn.seat_event_modification_transaction(
        seat_txn.IDForSeatChangeOperation(organization_id=auth.organization_id),
        seat_txn.SeatChangeOperation.NEW_WORKSPACE,
    ) as db:
        if await auth_has_reached_max_workspaces(db, auth):
            raise HTTPException(
                status_code=400,
                detail="This organization has reached its maximum number of workspaces.",
            )
        # Insert the tenant into the database
        query = """
        INSERT INTO tenants (id, created_at, display_name, config, tenant_handle, organization_id)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (id)
        DO NOTHING
        RETURNING *;
        """

        # Execute the insertion query and get the newly inserted tenant record
        tenant_record = await db.fetchrow(
            query,
            tenant_values.get("id"),
            tenant_values.get("created_at", datetime.now()),
            tenant_values["display_name"],
            tenant_values["config"],
            tenant_values.get("tenant_handle"),
            organization_id,
        )

        if tenant_record is None:
            raise HTTPException(
                status_code=409,
                detail="A tenant with this ID already exists.",
            )

        tenant_record = dict(tenant_record)

        # Insert identity if user is provided
        if auth.user_id is not None and auth.ls_user_id is not None:
            identity_query = """
            INSERT INTO identities (user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
            VALUES (
                $1,
                $2,
                $3,
                (SELECT id FROM roles WHERE name like $4),
                'workspace',
                (SELECT id FROM identities WHERE ls_user_id = $5 AND organization_id = $3 AND access_scope = 'organization'),
                $5
            )
            """
            await db.execute(
                identity_query,
                auth.user_id,
                tenant_record["id"],
                tenant_record["organization_id"],
                "WORKSPACE_ADMIN",
                auth.ls_user_id,
            )

        await identities_crud.ensure_all_org_admin_pending_identities_in_txn(
            db,
            tenant_record["id"],
        )
        await identities_crud.ensure_all_org_admin_identities_in_txn(
            db,
            tenant_record["id"],
        )

        # Create default tag keys for the workspace
        tag_key_ids = await create_default_tag_keys(db, tenant_record["id"])

        if not tag_key_ids:
            logger.error(
                f"Failed to create default tag keys for tenant {tenant_record['id']}"
            )

        tenant_record["is_personal"] = tenant_values["is_personal"]
        return schemas.Tenant(**tenant_record)


async def patch_workspace(
    db: asyncpg.Connection,
    auth: AuthInfo,
    workspace_id: UUID,
    workspace_update: schemas.WorkspacePatch,
) -> None:
    """Update an workspace."""
    row = await db.fetchrow(
        """
        UPDATE tenants
        SET display_name = COALESCE($1, display_name)
        WHERE id = $2 AND organization_id = $3
        RETURNING *
        """,
        workspace_update.display_name,
        workspace_id,
        auth.organization_id,
    )

    if not row:
        raise HTTPException(
            status_code=404,
            detail=f"Workspace with id {workspace_id} does not exist or does not belong to the right organization.",
        )


async def delete_workspace(
    db: asyncpg.Connection,
    auth: AuthInfo,
    workspace_id: UUID,
) -> None:
    """Delete an workspace."""
    row = await db.fetch(
        """
        DELETE FROM tenants
        WHERE id = $1 and organization_id = $2
        RETURNING id;
        """,
        workspace_id,
        auth.organization_id,
    )

    if not row:
        raise HTTPException(
            status_code=404,
            detail=f"Workspace with id {workspace_id} does not exist or does not belong to the right organization.",
        )
