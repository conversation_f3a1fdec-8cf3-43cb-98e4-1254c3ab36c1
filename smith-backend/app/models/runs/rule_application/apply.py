import time
import uuid
from abc import ABC, abstractmethod
from typing import Any

import structlog
from ddtrace import tracer  # noqa: E402
from ddtrace.constants import SPAN_KIND  # noqa: E402
from ddtrace.ext import SpanKind  # noqa: E402
from lc_config.settings import shared_settings as settings
from opentelemetry import trace as otel_trace
from opentelemetry.trace import SpanKind as OtelSpanKind

from app import schemas
from app.api.auth.schemas import AuthInfo

NULL_UUID = uuid.UUID("00000000-0000-0000-0000-000000000000")

logger = structlog.get_logger(__name__)


class RuleApplicationExpectedError(Exception):
    pass


class RuleApplicationUsageLimitError(RuleApplicationExpectedError):
    pass


class RuleApplier(ABC):
    @classmethod
    async def apply(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled: list[dict[str, Any]],
        start_time: str,
        end_time: str,
    ) -> dict[str, dict[str, schemas.RuleLogActionResponse]]:
        if settings.DATADOG_ENABLED:
            expected_error = None
            with tracer.trace(
                "run_rule_actions.action",
                service="run_rule_actions",
                resource=f"apply: {cls.__name__}",
            ) as span:
                span.set_tag_str(SPAN_KIND, SpanKind.CONSUMER)
                span.set_tag("rule_id", rule.id)
                logger.info(
                    "Applying Rule Action", rule_id=str(rule.id), action=cls.__name__
                )
                elapsed_time = time.time()
                try:
                    return await cls._apply(rule, auth, sampled, start_time, end_time)
                except RuleApplicationExpectedError as e:
                    expected_error = e
                finally:
                    logger.info(
                        "Finished Applying Rule Action",
                        rule_id=str(rule.id),
                        action=cls.__name__,
                        elapsed_time=time.time() - elapsed_time,
                    )

            raise expected_error
        elif settings.OTEL_TRACING_ENABLED:
            expected_error = None
            with otel_trace.get_tracer(__name__).start_as_current_span(
                "run_rule_actions.action",
                attributes={
                    "service.name": "run_rule_actions",
                    "resource.name": f"apply: {cls.__name__}",
                },
                kind=OtelSpanKind.CONSUMER,
            ) as span:
                span.set_attribute("rule_id", str(rule.id))
                logger.info(
                    "Applying Rule Action", rule_id=str(rule.id), action=cls.__name__
                )
                elapsed_time = time.time()
                try:
                    return await cls._apply(rule, auth, sampled, start_time, end_time)
                except RuleApplicationExpectedError as e:
                    expected_error = e
                finally:
                    logger.info(
                        "Finished Applying Rule Action",
                        rule_id=str(rule.id),
                        action=cls.__name__,
                        elapsed_time=time.time() - elapsed_time,
                    )

            raise expected_error
        else:
            return await cls._apply(rule, auth, sampled, start_time, end_time)

    @classmethod
    @abstractmethod
    async def _apply(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled: list[dict[str, Any]],
        start_time: str,
        end_time: str,
    ) -> dict[str, dict[str, schemas.RuleLogActionResponse]]:
        pass

    @classmethod
    async def filter_samples(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled: list[dict[str, Any]],
        start_time: str,
        end_time: str,
    ) -> list[dict[str, Any]]:
        if settings.DATADOG_ENABLED:
            expected_error = None
            with tracer.trace(
                "run_rule_actions.action",
                service="run_rule_actions",
                resource=f"filter_samples: {cls.__name__}",
            ) as span:
                span.set_tag_str(SPAN_KIND, SpanKind.CONSUMER)
                span.set_tag("rule_id", rule.id)

                try:
                    return await cls._filter_samples(
                        rule, auth, sampled, start_time, end_time
                    )
                except RuleApplicationExpectedError as e:
                    expected_error = e

            raise expected_error
        elif settings.OTEL_TRACING_ENABLED:
            expected_error = None
            with otel_trace.get_tracer(__name__).start_as_current_span(
                "run_rule_actions.action",
                attributes={
                    "service.name": "run_rule_actions",
                    "resource.name": f"filter_samples: {cls.__name__}",
                },
                kind=OtelSpanKind.CONSUMER,
            ) as span:
                span.set_attribute("rule_id", str(rule.id))
                try:
                    return await cls._filter_samples(
                        rule, auth, sampled, start_time, end_time
                    )
                except RuleApplicationExpectedError as e:
                    expected_error = e

            raise expected_error
        else:
            return await cls._filter_samples(rule, auth, sampled, start_time, end_time)

    @classmethod
    async def _filter_samples(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled: list[dict[str, Any]],
        start_time: str,
        end_time: str,
    ) -> list[dict[str, Any]]:
        return sampled
