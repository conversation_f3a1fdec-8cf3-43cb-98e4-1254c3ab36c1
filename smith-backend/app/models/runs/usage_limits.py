from datetime import datetime, timezone
from uuid import UUID

import structlog
from fastapi import HTTPException
from lc_database import redis

from app import config, schemas
from app.api.auth import AuthInfo, BaseAuthInfo
from app.models.usage_limits import user_defined_limits

logger = structlog.getLogger(__name__)


def _current_minute() -> str:
    return datetime.now(timezone.utc).strftime("%Y-%m-%d:%H:%M")


def _current_hour() -> str:
    return datetime.now(timezone.utc).strftime("%Y-%m-%d:%H")


def _current_month() -> str:
    return datetime.now(timezone.utc).strftime("%Y-%m")


def ingestion_events_per_minute_limit_reject_key(tenant_id: UUID) -> str:
    current_minute = _current_minute()
    return f"smith:runs:usage_limit_reject:total_requests:{current_minute}:{tenant_id}"


def hourly_payload_size_limit_reject_key(tenant_id: UUID) -> str:
    current_hour = _current_hour()
    return f"smith:runs:usage_limit_reject:payload_size:{current_hour}:{tenant_id}"


def hourly_events_ingested_limit_reject_key(tenant_id: UUID) -> str:
    current_hour = _current_hour()
    return f"smith:runs:usage_limit_reject:total_requests:{current_hour}:{tenant_id}"


def monthly_usage_limit_reject_key(tenant_id: UUID) -> str:
    current_month = _current_month()
    return f"smith:runs:usage_limit_reject:unique_traces:{current_month}:{tenant_id}"


def usage_limit_events_ingested_per_minute_counter_key(auth_id: UUID) -> str:
    current_minute = _current_minute()
    return f"smith:runs:usage_limit_total_events_ingested_per_minute:{current_minute}:{auth_id}"


def usage_limit_events_ingested_per_hour_counter_key(auth_id: UUID) -> str:
    current_hour = _current_hour()
    return f"smith:runs:usage_limit_total_requests:{current_hour}:{auth_id}"


def usage_limit_payload_size_per_hour_counter_key(auth_id: UUID) -> str:
    current_hour = _current_hour()
    return f"smith:runs:usage_limit_payload_size:{current_hour}:{auth_id}"


async def _set_reject_and_throw(
    pipe,
    reject_key: str,
    message: str,
    tenant_id: UUID,
    limit_name: str,
    addl_log_metadata: dict[str, str] | None = None,
):
    if addl_log_metadata is None:
        addl_log_metadata = {}

    await logger.awarn(
        "Limit Exceeded",
        metadata={
            "tenant_id": str(tenant_id),
            "limit": limit_name,
            **addl_log_metadata,
        },
    )
    # Set reject key with an irrelevant value, it's just key
    # existence that matters
    pipe.set(reject_key, "1", ex=config.settings.RATE_LIMIT_REJECT_EXPIRY_SEC, nx=True)
    raise HTTPException(status_code=429, detail=message)


async def check_longlived_usage_limits(auth: BaseAuthInfo) -> None:
    """Check if tenant has exceeded long-lived usage limits."""
    if config.settings.IS_SELF_HOSTED:
        return

    longlived_usage_limits = [
        limit
        for limit in await user_defined_limits.list_user_defined_usage_limits(auth)
        if limit.limit_type.get_unit()
        == user_defined_limits.UsageLimitUnit.LONGLIVED_TRACES
    ]
    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(
        str(auth.tenant_id)
    )
    async with (
        redis.aredis_routed_pool(
            str(auth.tenant_id), redis.RedisOperation.READ
        ) as aredis,
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
    ):
        for limit in longlived_usage_limits:
            limit.check_reject_set_from_pipe(pipe)
            limit.get_current_level_from_pipe(pipe)

        results = await pipe.execute()
        rejects = results[::2]
        current_levels = results[1::2]

        for limit, reject in zip(longlived_usage_limits, rejects):
            if reject:
                limit.throw_limit()

    async with (
        redis.aredis_routed_pool(
            str(auth.tenant_id), redis.RedisOperation.WRITE
        ) as aredis,
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
    ):
        # dual-write: read/write
        try:
            for limit, current_level in zip(longlived_usage_limits, current_levels):
                await limit.check_level_with_pipe(current_level, pipe)
        finally:
            await redis.execute_write_pipeline(
                str(auth.tenant_id), pipe, redis.RedisOperationType.USAGE_LIMITS
            )


async def has_tenant_exceeded_usage_limits(auth: AuthInfo) -> bool:
    """Check if tenant has exceeded usage limits, return true if so."""
    if config.settings.IS_SELF_HOSTED:
        return False
    # get redis keys
    ingestion_events_per_minute_reject_key = (
        ingestion_events_per_minute_limit_reject_key(auth.tenant_id)
    )
    hourly_payload_size_reject_key = hourly_payload_size_limit_reject_key(
        auth.tenant_id
    )
    hourly_total_requests_reject_key = hourly_events_ingested_limit_reject_key(
        auth.tenant_id
    )
    monthly_reject_key = monthly_usage_limit_reject_key(auth.tenant_id)
    hll_key = user_defined_limits.usage_limit_unique_traces_per_month_hll_key(
        auth.tenant_id
    )

    user_defined_trace_limits = [
        limit
        for limit in await user_defined_limits.list_user_defined_usage_limits(auth)
        if limit.limit_type.get_unit() == user_defined_limits.UsageLimitUnit.TRACES
    ]
    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(
        str(auth.tenant_id)
    )

    # get redis counters and reject sets
    async with redis.aredis_routed_pool(
        str(auth.tenant_id), redis.RedisOperation.READ
    ) as aredis:
        async with aredis.pipeline(transaction=use_redis_transaction) as pipe:
            # dual-write: readonly
            pipe.pfcount(hll_key)
            pipe.exists(ingestion_events_per_minute_reject_key)
            pipe.exists(hourly_payload_size_reject_key)
            pipe.exists(hourly_total_requests_reject_key)
            pipe.exists(monthly_reject_key)
            pipe.get(usage_limit_events_ingested_per_minute_counter_key(auth.tenant_id))
            pipe.get(usage_limit_events_ingested_per_hour_counter_key(auth.tenant_id))
            pipe.get(usage_limit_payload_size_per_hour_counter_key(auth.tenant_id))

            for limit in user_defined_trace_limits:
                limit.check_reject_set_from_pipe(pipe)
                limit.get_current_level_from_pipe(pipe)

            # TODO: Unpack based on cluster mode
            (
                total_unique_trace_count,
                tenant_rejected_total_requests_per_minute,
                tenant_rejected_payload_size,
                tenant_rejected_total_requests,
                tenant_rejected_monthly,
                total_events_ingested_in_current_minute,
                total_events_ingested_in_current_hour,
                total_payload_size_in_current_hour,
                *user_defined_values,
            ) = await pipe.execute()

        user_defined_rejects = user_defined_values[::2]
        user_defined_current_levels = user_defined_values[1::2]

        # reject if tenant in any reject set
        if tenant_rejected_total_requests_per_minute:
            raise HTTPException(
                status_code=429,
                detail="Per minute events ingested rate limit exceeded",
            )
        if tenant_rejected_total_requests:
            raise HTTPException(
                status_code=429,
                detail="Hourly events ingested usage limit exceeded",
            )
        if tenant_rejected_payload_size:
            raise HTTPException(
                status_code=429,
                detail="Hourly payload size usage limit exceeded",
            )
        if tenant_rejected_monthly:
            raise HTTPException(
                status_code=429,
                detail="Monthly unique traces usage limit exceeded",
            )

        for limit, reject in zip(user_defined_trace_limits, user_defined_rejects):
            if reject:
                limit.throw_limit()

        # update reject sets if counters breached
        async with (
            redis.aredis_routed_pool(
                str(auth.tenant_id), redis.RedisOperation.WRITE
            ) as aredis,
            aredis.pipeline(transaction=use_redis_transaction) as pipe,
        ):
            # dual-write: read/write
            try:
                for limit, current_level in zip(
                    user_defined_trace_limits, user_defined_current_levels
                ):
                    await limit.check_level_with_pipe(current_level, pipe)

                # -1 means unlimited
                if (
                    total_events_ingested_in_current_minute is not None
                    and auth.tenant_config.max_events_ingested_per_minute != -1
                    and int(total_events_ingested_in_current_minute)
                    >= auth.tenant_config.max_events_ingested_per_minute
                ):
                    await _set_reject_and_throw(
                        pipe,
                        ingestion_events_per_minute_reject_key,
                        "Per minute events ingested rate limit exceeded",
                        auth.tenant_id,
                        schemas.TenantUsageLimitType.events_ingested_per_minute.value,
                        addl_log_metadata={
                            "limit_threshold": str(
                                auth.tenant_config.max_events_ingested_per_minute
                            ),
                            "count": str(total_events_ingested_in_current_minute),
                        },
                    )

                if (
                    total_events_ingested_in_current_hour is not None
                    and auth.tenant_config.max_hourly_tracing_requests != -1
                    and int(total_events_ingested_in_current_hour)
                    >= auth.tenant_config.max_hourly_tracing_requests
                ):
                    await _set_reject_and_throw(
                        pipe,
                        hourly_total_requests_reject_key,
                        "Hourly events ingested usage limit exceeded",
                        auth.tenant_id,
                        schemas.TenantUsageLimitType.events_ingested_per_hour.value,
                        addl_log_metadata={
                            "limit_threshold": str(
                                auth.tenant_config.max_hourly_tracing_requests
                            ),
                            "count": str(total_events_ingested_in_current_hour),
                        },
                    )

                if (
                    total_payload_size_in_current_hour is not None
                    and auth.tenant_config.max_hourly_tracing_bytes != -1
                    and int(total_payload_size_in_current_hour)
                    >= auth.tenant_config.max_hourly_tracing_bytes
                ):
                    await _set_reject_and_throw(
                        pipe,
                        hourly_payload_size_reject_key,
                        "Hourly payload size usage limit exceeded",
                        auth.tenant_id,
                        schemas.TenantUsageLimitType.payload_size.value,
                        addl_log_metadata={
                            "limit_threshold": str(
                                auth.tenant_config.max_hourly_tracing_bytes
                            ),
                            "count": str(total_payload_size_in_current_hour),
                        },
                    )

                if (
                    total_unique_trace_count is not None
                    and auth.tenant_config.max_monthly_total_unique_traces != -1
                    and int(total_unique_trace_count)
                    >= auth.tenant_config.max_monthly_total_unique_traces
                ):
                    await _set_reject_and_throw(
                        pipe,
                        monthly_reject_key,
                        "Monthly unique traces usage limit exceeded",
                        auth.tenant_id,
                        schemas.TenantUsageLimitType.total_unique_traces.value,
                        addl_log_metadata={
                            "limit_threshold": str(
                                auth.tenant_config.max_monthly_total_unique_traces
                            ),
                            "count": str(total_unique_trace_count),
                        },
                    )

            finally:
                await redis.execute_write_pipeline(
                    str(auth.tenant_id), pipe, redis.RedisOperationType.USAGE_LIMITS
                )

        return False


async def has_tenant_exceeded_monthly_limits(auth: AuthInfo) -> bool:
    """Check if tenant has exceeded monthly usage limits, return true if so."""
    if config.settings.IS_SELF_HOSTED:
        return False

    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(
        str(auth.tenant_id)
    )
    # get redis keys
    monthly_reject_key = monthly_usage_limit_reject_key(auth.tenant_id)
    hll_key = user_defined_limits.usage_limit_unique_traces_per_month_hll_key(
        auth.tenant_id
    )

    user_defined_trace_limits = [
        limit
        for limit in await user_defined_limits.list_user_defined_usage_limits(auth)
        if limit.limit_type.get_unit() == user_defined_limits.UsageLimitUnit.TRACES
    ]

    # get redis counters and reject sets
    async with redis.aredis_routed_pool(
        str(auth.tenant_id), redis.RedisOperation.WRITE
    ) as aredis:
        async with aredis.pipeline(transaction=use_redis_transaction) as pipe:
            # dual-write: readonly
            pipe.pfcount(hll_key)
            pipe.exists(monthly_reject_key)
            for limit in user_defined_trace_limits:
                limit.check_reject_set_from_pipe(pipe)
                limit.get_current_level_from_pipe(pipe)

            (
                total_unique_trace_count,
                tenant_rejected_monthly,
                *user_defined_values,
            ) = await pipe.execute()

        user_defined_rejects = user_defined_values[::2]
        user_defined_current_levels = user_defined_values[1::2]

        if tenant_rejected_monthly:
            raise HTTPException(
                status_code=429,
                detail="Monthly unique traces usage limit exceeded",
            )

        for limit, reject in zip(user_defined_trace_limits, user_defined_rejects):
            if reject:
                limit.throw_limit()

        # update reject sets if counters breached
        # -1 means unlimited
        async with aredis.pipeline(transaction=use_redis_transaction) as pipe:
            # dual-write: read/write
            try:
                for limit, current_level in zip(
                    user_defined_trace_limits, user_defined_current_levels
                ):
                    await limit.check_level_with_pipe(current_level, pipe)

                if (
                    total_unique_trace_count is not None
                    and int(total_unique_trace_count)
                    > auth.tenant_config.max_monthly_total_unique_traces
                    and auth.tenant_config.max_monthly_total_unique_traces != -1
                ):
                    await _set_reject_and_throw(
                        pipe,
                        monthly_reject_key,
                        "Monthly unique traces usage limit exceeded",
                        auth.tenant_id,
                        schemas.TenantUsageLimitType.total_unique_traces.value,
                    )

            finally:
                await redis.execute_write_pipeline(
                    str(auth.tenant_id), pipe, redis.RedisOperationType.USAGE_LIMITS
                )

        return False
