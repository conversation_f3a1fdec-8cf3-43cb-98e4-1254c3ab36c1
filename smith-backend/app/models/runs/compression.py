import asyncio
import enum
import mmap
import os
import tempfile
import zlib
from contextlib import AsyncExitStack
from typing import BinaryIO, Mapping, Optional

import cramjam
import or<PERSON><PERSON>
from ddtrace import tracer
from lc_logging.trace import wrap
from opentelemetry import trace as otel_tracer

from app import config
from app.models.runs.encoding import get_msgpack_decoder
from app.utils import arun_in_executor


class CompressionMethod(enum.Enum):
    GZIP = "gzip"  # lowercase to match content-encoding header
    BROTLI = "BROTLI"
    NONE = "NONE"


BytesLike = bytes | bytearray | memoryview | mmap.mmap

# --- compress / decompress dictionary of payloads ---


async def compress_based_on_size_many(
    payloads: Mapping[str, tuple[str, BytesLike]], exit: AsyncExitStack
) -> dict[str, tuple[bytes | memoryview, str]]:
    """
    Compress payload if it is larger than the threshold (and not already compressed)

    Returns the compressed payload and the compression method used (or NONE if not compressed)
    """
    if not payloads:
        return {}
    coros = (compress_based_on_size(*payload, exit) for payload in payloads.values())
    compressed = await asyncio.gather(*coros)
    return dict(zip(payloads.keys(), compressed))


async def decompress_and_parse_many(
    payloads: Mapping[str, tuple[bytes | None, bytes | None, BytesLike]],
) -> dict[str, dict | list | BaseException]:
    parsed = await asyncio.gather(
        *(
            decompress_and_parse(
                payload,
                compression_method.decode() if compression_method else None,
                ctype.decode() if ctype else None,
            )
            for ctype, compression_method, payload in payloads.values()
        ),
        return_exceptions=True,
    )
    return dict(zip(payloads.keys(), parsed))


async def decompress_many(
    payloads: Mapping[str, tuple[bytes | None, bytes | None, BytesLike]],
) -> dict[str, tuple[str, memoryview | bytes]]:
    decompressed = await asyncio.gather(
        *(
            decompress_based_on_method(
                payload, compression_method.decode() if compression_method else None
            )
            for _, compression_method, payload in payloads.values()
        )
    )
    return {
        key: (content_type.decode() if content_type else "", value)
        for (key, (content_type, _, _)), value in zip(payloads.items(), decompressed)
    }


# --- compress / decompress single payload ---


async def compress_based_on_size(
    content_encoding: str, payload: BytesLike, exit: AsyncExitStack
) -> tuple[bytes | memoryview, str]:
    """
    Compress payload if it is larger than the threshold, and not already compressed.

    Returns the compressed payload and the compression method used (or NONE if not compressed)
    """
    size = len(payload)
    if content_encoding != "":
        if content_encoding == CompressionMethod.GZIP.value:
            if isinstance(payload, (bytes, memoryview)):
                return payload, CompressionMethod.GZIP.value
            else:
                view = memoryview(payload)
                exit.callback(view.release)
                return view, CompressionMethod.GZIP.value
        else:
            raise NotImplementedError(
                f"Unsupported content encoding: {content_encoding}"
            )
    elif size < config.settings.COMPRESS_MIN_SIZE_KB * 1024:
        if isinstance(payload, (bytes, memoryview)):
            return payload, CompressionMethod.NONE.value
        else:
            view = memoryview(payload)
            exit.callback(view.release)
            return view, CompressionMethod.NONE.value
    elif size < config.settings.SPOOL_MIN_SIZE_KB * 1024:
        compressed = await arun_in_executor(brotli_compress_payload, payload, exit)
        return (compressed, CompressionMethod.BROTLI.value)
    else:
        # create temp file, mark it for deletion
        fd, path = tempfile.mkstemp()
        exit.callback(os.remove, path)
        # write compressed payload to file
        rfile = cramjam.File(path)
        try:
            await arun_in_executor(brotli_compress_payload_into, payload, rfile)
        finally:
            del rfile
        # mmap file, mark it for closing
        mm = mmap.mmap(fd, 0)
        exit.callback(mm.close)
        # return memoryview of mmap, mark it for release
        view = memoryview(mm)
        exit.callback(view.release)
        return (view, CompressionMethod.BROTLI.value)


async def decompress_based_on_method(
    payload: BytesLike, compression_method: Optional[str]
) -> memoryview | bytes:
    """
    Decompress payload based on the compression method used.
    If empty, default to brotli decompression.
    """
    if not compression_method or compression_method == CompressionMethod.BROTLI.value:
        return await arun_in_executor(brotli_decompress_payload, payload)
    elif compression_method == CompressionMethod.GZIP.value:
        # https://stackoverflow.com/questions/1838699/how-can-i-decompress-a-gzip-stream-with-zlib#answer-22311297
        # | 32 is to enable zlib and gzip decoding
        return await arun_in_executor(zlib.decompress, payload, zlib.MAX_WBITS | 32)
    elif isinstance(payload, (bytes, memoryview)):
        return payload
    else:
        return memoryview(payload)


async def decompress_and_parse(
    payload: BytesLike,
    compression_method: Optional[str],
    content_type: Optional[str] = None,
) -> dict:
    """
    Decompress payload and return it as a dictionary
    """
    decompressed = await decompress_based_on_method(payload, compression_method)
    if content_type is None or content_type == "application/json":
        parsed = await arun_in_executor(orjson.loads, decompressed)
    elif content_type == "application/msgpack":
        with get_msgpack_decoder() as decoder:
            parsed = decoder.decode(decompressed)
    else:
        raise NotImplementedError(f"Unsupported content type: {content_type}")
    if isinstance(decompressed, memoryview):
        decompressed.release()
    return parsed


@wrap(name="decompress_payload", service="brotli")
def brotli_decompress_payload(payload: BytesLike) -> memoryview:
    add_payload_size_to_span(payload)
    return memoryview(cramjam.brotli.decompress(payload))


@wrap(name="compress_payload", service="brotli")
def brotli_compress_payload(payload: BytesLike, exit: AsyncExitStack) -> memoryview:
    add_payload_size_to_span(payload)
    buffer = cramjam.brotli.compress(payload, level=1)
    view = memoryview(buffer)
    exit.callback(view.release)
    return view


@wrap(name="compress_payload_into", service="brotli")
def brotli_compress_payload_into(payload: BytesLike, buffer: BinaryIO) -> None:
    add_payload_size_to_span(payload)
    cramjam.brotli.compress_into(payload, buffer, level=1)
    buffer.seek(0)


def add_payload_size_to_span(payload: BytesLike) -> None:
    if config.settings.DATADOG_ENABLED and (span := tracer.current_span()):
        span.set_tag("payload_size", len(payload))
    elif config.settings.OTEL_TRACING_ENABLED and (
        span := otel_tracer.get_current_span()
    ):
        span.set_attribute("payload_size", len(payload))
