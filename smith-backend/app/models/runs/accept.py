import asyncio
import zlib
from collections import deque
from contextlib import AbstractAsyncContextManager, AsyncExitStack, asynccontextmanager
from typing import (
    Any,
    AsyncGenerator,
    AsyncIterator,
    Coroutine,
    Iterator,
    TypeVar,
)

import orjson
import pycurl
import structlog
import zstandard
from fastapi import HTTPException, Request
from lc_config.tracing import trace_async_function_call
from lc_database.curl import internal_platform_request
from lc_database.s3_client import mk_s3_key
from lc_logging.trace import TraceSpanKind
from starlette.requests import ClientDisconnect

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.config import settings
from app.crud import check_longlived_usage_limits, start_or_fetch_tracer_session
from app.models.runs.ingest import ALLOWED_OUT_OF_BAND_KEYS
from app.models.runs.validate import (
    convert_timestamp_to_isostring,
    parse_validate_patch,
    parse_validate_post,
)
from app.multipart import (  # type: ignore[attr-defined]
    MultipartError,
    MultipartPart,
    MultipartSegment,
    MultipartSegmentInfo,
    PushMultipartParser,
    parse_options_header,
)
from app.schemas import TracerSession, TraceTier

logger = structlog.stdlib.get_logger(__name__)

KNOWN_EVENTS = ("post", "patch", "attachment", "feedback")

SessionKey = tuple[str | None, str | None]

SENTINEL = object()

T = TypeVar("T")


@asynccontextmanager
async def parse_multipart_form(
    request: Request,
    auth: AuthInfo,
) -> AsyncIterator[
    tuple[
        list[dict],
        list[dict],
        list[dict],
        dict[str, dict[str, str | tuple[str, str, bytes]]],
    ]
]:
    UPLOAD_MIN = settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
    MEMFILE_LIMIT = settings.SPOOL_MIN_SIZE_KB * 1024

    ctype = request.headers.get("Content-Type", "")
    if not ctype.startswith("multipart/form-data"):
        logger.warning(
            "422: Invalid request content-type",
            content_type=ctype,
        )
        raise HTTPException(
            status_code=422, detail="Invalid Content-Type, expected multipart/form-data"
        )

    cenc = request.headers.get("Content-Encoding", "")
    if cenc not in ("", "zstd"):
        logger.warning(
            "422: Unknown content-encoding",
            encoding=cenc,
        )
        raise HTTPException(
            status_code=422,
            detail=f"Invalid Content-Encoding: {cenc}, expected zstd or none",
        )

    _, params = parse_options_header(ctype)
    if boundary := params.get("boundary"):
        parser = PushMultipartParser(boundary=boundary)
    else:
        logger.warning(
            "422: Invalid request content-type",
            content_type=ctype,
        )
        raise HTTPException(status_code=422, detail="Missing boundary in Content-Type")

    feedback: list[dict] = []
    runs: dict[str, dict[str, dict]] = {"post": {}, "patch": {}}
    # post | patch -> run_id -> payload
    sessions_by_run: dict[str, TracerSession] = {}
    sessions_by_key: dict[SessionKey, TracerSession] = {}
    # run id -> session
    extras: dict[str, dict[str, str | tuple[str, str, bytes]]] = {}
    # run id -> field -> s3_url | (content_type, raw)

    seen: set[str] = set()
    body = request.stream()
    current: MultipartPart | None = None
    did_check_longlived_limit = False
    chunks: Iterator[MultipartSegment | bytearray | None]
    n_uploads = 0
    n_parts = 0
    n_bytes = 0
    received_any = False

    async def decompressed(stream: AsyncIterator[bytes]) -> AsyncIterator[bytes]:
        decomp = zstandard.ZstdDecompressor().decompressobj(read_across_frames=True)
        async for chunk in stream:
            if chunk := decomp.decompress(chunk):
                yield chunk
        yield decomp.flush()

    body = await asyncio.to_thread(decompressed, body) if cenc == "zstd" else body

    async with AsyncExitStack() as exit, SimpleTaskGroup() as tg:
        exit.push_async_callback(_try_close, body)

        while not parser.closed:
            try:
                if received_any:
                    chunks = parser.parse(await _read(body))
                else:
                    chunks = parser.parse(
                        await _read(body, settings.MULTIPART_FIRST_READ_TIMEOUT)
                    )
                    received_any = True
            except asyncio.TimeoutError:
                if received_any:
                    raise HTTPException(status_code=408, detail="Request timeout")
                else:
                    raise HTTPException(status_code=400, detail="Empty request")
            except ClientDisconnect:
                raise HTTPException(status_code=499, detail="Client disconnected")
            while True:
                try:
                    chunk = next(chunks, SENTINEL)
                except MultipartError:
                    logger.warning(
                        "422: Parsing error in main loop",
                        current_parts=n_parts,
                        current_bytes=n_bytes,
                        exc_info=True,
                    )
                    raise HTTPException(
                        status_code=422, detail="Malformed multipart/form-data"
                    )
                if chunk is SENTINEL:
                    break
                if isinstance(chunk, MultipartSegment):
                    n_parts += 1
                    # parse part name
                    name_parts = chunk.name.split(".", 2)
                    if len(name_parts) == 2:
                        # {event}.{run_id}
                        event, run_id = name_parts
                        field = None
                    elif len(name_parts) == 3:
                        # {event}.{run_id}.{field}
                        event, run_id, field = name_parts
                    else:
                        logger.warning(
                            "422: Invalid name",
                            name=chunk.name,
                            headers=chunk.headerlist,
                        )
                        raise HTTPException(
                            status_code=422, detail=f"Invalid name: {chunk.name}"
                        )
                    # validate event
                    if event not in KNOWN_EVENTS:
                        logger.warning(
                            "422: Invalid name prefix",
                            name=chunk.name,
                            headers=chunk.headerlist,
                        )
                        raise HTTPException(
                            status_code=422, detail=f"Invalid event: {event}"
                        )
                    if chunk.content_type is None:
                        logger.warning(
                            "422: Part without content-type",
                            name=chunk.name,
                            headers=chunk.headerlist,
                        )
                        raise HTTPException(
                            status_code=422,
                            detail=f"Missing content-type for part {chunk.name}",
                        )
                    ctype, part_params = parse_options_header(
                        chunk.header("Content-Type")
                    )
                    if chunk._clen != -1:
                        size = chunk._clen
                    elif size_str := part_params.get("length"):
                        try:
                            size = int(size_str)
                        except ValueError:
                            logger.warning(
                                "422: Non-integer content-length",
                                name=chunk.name,
                                headers=chunk.headerlist,
                            )
                            raise HTTPException(
                                status_code=422,
                                detail=f"Non-integer content-length for part {chunk.name}",
                            )
                    else:
                        logger.warning(
                            "422: Part without content-length",
                            name=chunk.name,
                            headers=chunk.headerlist,
                        )
                        raise HTTPException(
                            status_code=422,
                            detail=f"Missing content-length for part {chunk.name}",
                        )
                    if size < 1:
                        logger.warning(
                            "422: Non-positive content-length",
                            name=chunk.name,
                            headers=chunk.headerlist,
                        )
                        raise HTTPException(
                            status_code=422,
                            detail=f"Non-positive content-length for part {chunk.name}",
                        )
                    n_bytes += size
                    cenc = chunk.header(
                        "Content-Encoding",
                        part_params.get("encoding", ""),
                    )
                    if cenc not in ("", "gzip"):
                        logger.warning(
                            "422: Unknown content-encoding for part",
                            encoding=cenc,
                            name=chunk.name,
                            headers=chunk.headerlist,
                        )
                        raise HTTPException(
                            status_code=422,
                            detail=f"Invalid encoding for part {chunk.name}: {cenc}",
                        )
                    # handle part start
                    if event == "attachment":
                        if not settings.FF_BLOB_STORAGE_ENABLED:
                            raise HTTPException(
                                status_code=501,
                                detail="S3 storage is not enabled",
                            )
                        # validate attachment
                        if not field:
                            logger.warning(
                                "422: Attachment without name",
                                name=chunk.name,
                                headers=chunk.headerlist,
                            )
                            raise HTTPException(
                                status_code=422,
                                detail="Missing field for attachment",
                            )
                        if (
                            field.endswith("_s3_url")
                            or field.endswith("_content_type")
                            or field.endswith("_compression_method")
                        ):
                            logger.warning(
                                "422: Invalid attachment name",
                                name=chunk.name,
                                headers=chunk.headerlist,
                            )
                            raise HTTPException(
                                status_code=422,
                                detail=f"Invalid attachment name: {field}",
                            )
                    elif field:
                        # validate out of band fields
                        if field not in ALLOWED_OUT_OF_BAND_KEYS:
                            logger.warning(
                                "422: Invalid field",
                                name=chunk.name,
                                headers=chunk.headerlist,
                            )
                            raise HTTPException(
                                status_code=422, detail=f"Invalid field: {field}"
                            )
                        if (
                            chunk.content_type is not None
                            and not chunk.content_type.startswith("application/json")
                        ):
                            logger.warning(
                                "422: Invalid content type",
                                name=chunk.name,
                                headers=chunk.headerlist,
                            )
                            raise HTTPException(
                                status_code=422,
                                detail=f"Invalid content type: {chunk.content_type}",
                            )
                    # continue parsing body
                    current = MultipartPart(
                        memfile_limit=MEMFILE_LIMIT,
                        segment=chunk,
                    )
                    exit.callback(current.close)
                elif current is not None and chunk is not None:
                    # handle part continue
                    current._write(chunk)
                elif current is not None:
                    # handle part end
                    current._mark_complete()
                    # some parts may produce other parts
                    parts = deque((current,))
                    while parts and (current := parts.popleft()):
                        if current.name in seen:
                            logger.warning(
                                "422: Ignoring duplicate part",
                                name=current.name,
                                headers=current.headerlist,
                            )
                            current.close()
                            current = None
                            continue
                        seen.add(current.name)
                        name_parts = current.name.split(".", 2)
                        cenc = current.headers.get(
                            "Content-Encoding",
                            part_params.get("encoding", ""),
                        )
                        if len(name_parts) == 2:
                            # {event}.{run_id}
                            event, run_id = name_parts

                            # validate and parse run
                            if cenc == "gzip":
                                # https://stackoverflow.com/questions/1838699/how-can-i-decompress-a-gzip-stream-with-zlib#answer-22311297
                                # | 32 is to enable zlib and gzip decoding
                                raw: bytes | memoryview = await asyncio.to_thread(
                                    zlib.decompress, current.raw, zlib.MAX_WBITS | 32
                                )
                            else:
                                raw = current.raw
                            if event == "feedback":
                                parsed = schemas.FeedbackCreateSchema.model_validate(
                                    orjson.loads(raw)
                                ).model_dump()
                                if not parsed.get("trace_id"):
                                    logger.warning(
                                        "422: Feedback part without trace_id",
                                        name=current.name,
                                        headers=current.headerlist,
                                    )
                                    raise HTTPException(
                                        status_code=422,
                                        detail="Feedback parts must have a trace_id",
                                    )
                                if str(parsed["id"]) != run_id:
                                    logger.warning(
                                        "422: Run id mismatch",
                                        payload_id=str(parsed["id"]),
                                        name=current.name,
                                        headers=current.headerlist,
                                    )
                                    raise HTTPException(
                                        status_code=422,
                                        detail=f"Run id mismatch: {run_id} != {parsed.get('id')}",
                                    )
                            else:
                                parsed = await asyncio.to_thread(
                                    parse_validate_patch
                                    if event == "patch"
                                    else parse_validate_post,
                                    raw,
                                )
                                if parsed.get("id") != run_id:
                                    logger.warning(
                                        "422: Run id mismatch",
                                        payload_id=parsed.get("id"),
                                        name=current.name,
                                        headers=current.headerlist,
                                    )
                                    raise HTTPException(
                                        status_code=422,
                                        detail=f"Run id mismatch: {run_id} != {parsed.get('id')}",
                                    )

                            # close part
                            current.close()
                            current = None

                            # save feedback
                            if event == "feedback":
                                feedback.append(parsed)
                                continue

                            # save run
                            runs[event][run_id] = parsed
                            extras.setdefault(run_id, {})

                            # get session
                            skey: SessionKey = (
                                parsed.get("session_id"),
                                parsed.get("session_name"),
                            )
                            if skey not in sessions_by_key:
                                sessions_by_key[
                                    skey
                                ] = await start_or_fetch_tracer_session(
                                    auth,
                                    session_id=skey[0],
                                    session_name=skey[1],
                                    start_time_str=convert_timestamp_to_isostring(
                                        parsed.get("start_time")
                                    ),
                                )
                            sessions_by_run[run_id] = sessions_by_key[skey]

                            # check longlived limit, if needed
                            if (
                                settings.FF_TRACE_TIERS_ENABLED
                                and not did_check_longlived_limit
                                and sessions_by_run[run_id].trace_tier
                                == TraceTier.longlived
                            ):
                                await check_longlived_usage_limits(auth)
                                did_check_longlived_limit = True

                            # extract inline fields
                            for key in ALLOWED_OUT_OF_BAND_KEYS:
                                if value := parsed.pop(key, None):
                                    b = orjson.dumps(value)
                                    ct = "application/json"
                                    s = MultipartSegmentInfo(
                                        headerlist=[],
                                        name=f"{event}.{run_id}.{key}",
                                        filename=None,
                                        content_type=ct,
                                        charset=None,
                                        size=len(b),
                                    )
                                    p = MultipartPart(segment=s)
                                    p._write(b)
                                    p._mark_complete()
                                    exit.callback(p.close)
                                    parts.append(p)
                        elif len(name_parts) == 3:
                            # {event}.{run_id}.{field}
                            event, run_id, field = name_parts
                            if run_id not in extras or run_id not in sessions_by_run:
                                logger.warning(
                                    "422: Ignoring field/attachment for unknown run_id",
                                    name=current.name,
                                    headers=current.headerlist,
                                )
                                continue
                            if event == "attachment":
                                recast = f"attachment.{field}"
                            else:
                                recast = field
                            if settings.FF_BLOB_STORAGE_ENABLED and (
                                event == "attachment" or size > UPLOAD_MIN
                            ):
                                # if should upload
                                s3_url = mk_s3_key(
                                    sessions_by_run[run_id].tenant_id,
                                    sessions_by_run[run_id].id,
                                    run_id,
                                    sessions_by_run[run_id].trace_tier,
                                    "attachments" if field == "attachment" else field,
                                )
                                # start upload to s3
                                n_uploads += 1
                                tg.create_task(
                                    _upload_part(
                                        current,
                                        {
                                            "Content-Length": str(current.size),
                                            "Content-Type": current.content_type,
                                            "Content-Encoding": cenc,
                                            "X-Object-Key": s3_url,
                                        },
                                    ),
                                    name=f"upload-{current.name}",
                                )
                                # save s3 url
                                extras[run_id][recast] = s3_url
                            else:
                                # inline part in redis
                                extras[run_id][recast] = (
                                    current.content_type,
                                    cenc,
                                    current.raw,
                                )
                                current = None
                        else:
                            raise RuntimeError("Unreachable")
                else:
                    logger.warning(
                        "422: Unexpected chunk",
                        current_parts=n_parts,
                        current_bytes=n_bytes,
                        chunk=chunk,
                    )
                    raise HTTPException(
                        status_code=422, detail="Malformed multipart/form-data"
                    )

        logger.info(
            "Finished parsing form",
            n_parts=n_parts,
            n_bytes=n_bytes,
            n_uploads=n_uploads,
            n_runs=len(runs["post"]) + len(runs["patch"]),
            n_feedback=len(feedback),
        )

        # wait for all uploads to finish
        if tg.tasks:
            await asyncio.wait(tg.tasks)

        # yield the parsed feedback, runs and extras
        yield (
            feedback,
            list(runs["post"].values()),
            list(runs["patch"].values()),
            extras,
        )


async def _upload_part(
    part: MultipartPart,
    headers: dict[str, str],
) -> None:
    buf = part.file

    def ioctlfunc(cmd: int) -> int:
        """Callback for pycurl to seek the file."""
        if cmd == pycurl.IOCMD_RESTARTREAD:
            buf.seek(0)
        return pycurl.IOE_OK

    def prepare_curl_callback(curl: pycurl.Curl) -> None:
        """Callback to modify pycurl options, we use it to set the file to read from."""
        buf.seek(0)  # start each attempt from the beginning
        curl.setopt(pycurl.HTTP_VERSION, pycurl.CURL_HTTP_VERSION_2_PRIOR_KNOWLEDGE)
        curl.setopt(pycurl.READFUNCTION, buf.read)
        curl.setopt(pycurl.IOCTLFUNCTION, ioctlfunc)
        curl.setopt(pycurl.POSTFIELDSIZE, part.size)

    await internal_platform_request(
        "POST",
        "/internal/upload",
        headers=headers,
        body=b"",  # placeholder, will be replaced in prepare_curl_callback
        prepare_curl_callback=prepare_curl_callback,
    )

    # close the part early if successful
    # (the main loop closes it on exit either way)
    part.close()


class ValueEvent(asyncio.Event):
    def set(self, value: bool | BaseException = True) -> None:
        """Set the internal flag to true. All coroutines waiting for it to
        become set are awakened. Coroutine that call wait() once the flag is
        true will not block at all.
        """
        if not self._value:  # type: ignore[has-type]
            self._value = value

            for fut in self._waiters:  # type: ignore[attr-defined]
                if not fut.done():
                    if isinstance(value, BaseException):
                        fut.set_exception(value)
                    else:
                        fut.set_result(value)

    async def wait(self):
        """Block until the internal flag is set.

        If the internal flag is set on entry, return value
        immediately.  Otherwise, block until another coroutine calls
        set() to set the flag, then return the value.
        """
        if isinstance(self._value, BaseException):
            raise self._value
        elif self._value:
            return self._value

        fut = self._get_loop().create_future()
        self._waiters.append(fut)
        try:
            return await fut
        finally:
            self._waiters.remove(fut)


class SimpleTaskGroup(AbstractAsyncContextManager["SimpleTaskGroup"]):
    """An async task group that waits tasks on exit,
    and cancels them if an exception occurred."""

    tasks: set[asyncio.Task]

    def __init__(self) -> None:
        self.tasks = set()

    def _create_task_done_callback(self, task: asyncio.Task) -> None:
        try:
            self.tasks.discard(task)
        except AttributeError:
            pass
        try:
            if exc := task.exception():
                logger.error("Background task failed", exc_info=exc)
        except asyncio.CancelledError:
            pass

    def create_task(
        self,
        coro: Coroutine[Any, Any, T],
        *,
        name: str | None = None,
    ) -> asyncio.Task[T]:
        """Create a new task in the current task group and return it."""
        task = asyncio.create_task(coro, name=name)
        self.tasks.add(task)
        task.add_done_callback(self._create_task_done_callback)
        return task

    async def __aexit__(self, exc_type, exc_value, traceback) -> None:
        tasks = self.tasks
        # break reference cycles between tasks and task group
        del self.tasks
        # cancel all tasks if an exception occurred
        if exc_type is not None:
            for task in tasks:
                task.cancel()
        # wait for all tasks
        try:
            await asyncio.gather(*tasks)
        except asyncio.CancelledError:
            pass


async def _try_close(
    body: AsyncGenerator[bytes, None],
) -> None:
    try:
        await body.aclose()
    except Exception:
        pass


async def _read(
    body: AsyncGenerator[bytes, None], timeout: int = settings.MULTIPART_READ_TIMEOUT
) -> bytes:
    return await trace_async_function_call(
        name="multipart.read",
        func=asyncio.wait_for,
        args=(anext(body, b""), timeout),
        kwargs={},
        kind=TraceSpanKind.SERVER,
    )
