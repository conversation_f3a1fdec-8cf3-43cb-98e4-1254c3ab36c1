from datetime import datetime
from typing import Any

import orjson
import structlog
from ddtrace.trace import Span as DDSpan
from fastapi import H<PERSON><PERSON>Ex<PERSON>
from httpx import Response
from lc_config.tracing import trace_async_function_call
from lc_database.quickwit import quickwit_client
from lc_logging.trace import Trace<PERSON><PERSON>K<PERSON>
from opentelemetry.trace import Span as OtelSpan

from app import schemas
from app.api.auth import AuthInfo
from app.config import settings
from app.models.query_lang.parse import Comparator as C
from app.models.query_lang.parse import (
    Comparison,
    Operation,
    Operator,
    parse_as_filter_directive_ftsearch,
)
from app.models.query_lang.translate_qw import QuickwitVisitor
from app.models.runs import fetch_ch
from app.models.runs.qw_attrs import get_quickwit_attributes

logger = structlog.get_logger(__name__)


async def quickwit_search(
    search_params: dict[str, Any],
    index_ids: list[str] = [settings.QUICKWIT_RUNS_INDEX],
) -> Response:
    async with quickwit_client() as client:
        url = f"{settings.QUICKWIT_SEARCH_URL}/api/v1/{','.join(index_ids)}/search"
        return await client.post(url, json=search_params)


def add_quickwit_tags_to_span(
    span: DDSpan | OtelSpan, tenant_id: str, query_str: str
) -> None:
    if settings.DATADOG_ENABLED:
        span.set_tag_str("tenant_id", tenant_id)
        span.set_tag_str("quickwit.query", query_str)
    elif settings.OTEL_TRACING_ENABLED:
        span.set_attribute("tenant_id", tenant_id)
        span.set_attribute("quickwit.query", query_str)


async def execute_quickwit_search(
    tenant_id: str,
    search_params: dict[str, Any],
    query_name: str,
    query_str: str,
    index_ids: list[str] = [settings.QUICKWIT_RUNS_INDEX],
) -> dict:
    try:
        response = await trace_async_function_call(
            name="quickwit.request",
            service="quickwit",
            resource="quickwit_search: " + query_name,
            span_modifier=lambda span: add_quickwit_tags_to_span(
                span, tenant_id, query_str
            ),
            func=quickwit_search,
            args=(search_params, index_ids),
            kwargs={},
            kind=TraceSpanKind.CLIENT,
        )
    except Exception as e:
        # handle connection errors or other unexpected errors
        logger.error(
            "Unexpected error during Quickwit search",
            error=str(e),
            query=query_str,
            tenant_id=tenant_id,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to execute search query.",
        )

    # process response status codes
    status_code = response.status_code
    if status_code != 200:
        logger.error(
            "Quickwit search failed",
            status_code=status_code,
            query=query_str,
            tenant_id=tenant_id,
        )

        if status_code == 400:
            raise HTTPException(
                status_code=422,
                detail="Invalid search query format or parameters",
            )
        elif status_code == 401 or status_code == 403:
            raise HTTPException(
                status_code=422,
                detail="Authentication failed when querying search index",
            )
        elif status_code >= 500:
            raise HTTPException(
                status_code=503,
                detail="Search service temporarily unavailable. Please try again later.",
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to execute search query.",
            )

    # only parse JSON if we have a successful response
    try:
        return orjson.loads(response.content)
    except Exception as e:
        logger.error(
            "Failed to parse Quickwit response",
            error=str(e),
            query=query_str,
            tenant_id=tenant_id,
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to parse search results.",
        )


# TODO: handle other auth types
async def fetch_runs(
    auth: AuthInfo, body: schemas.BodyParamsForRunSchema
) -> fetch_ch.FetchRunsResult:
    """Fetch runs from Quickwit based on the search criteria."""

    tenant_id = auth.tenant_id
    session_ids = body.session

    # build up the query
    where = Operation(operator=Operator.AND, arguments=[])
    args = where.arguments

    # Add tenant_id to the query
    # This is auth-safe because even if other tenant_ids are specified in the query,
    # the conditions are ANDed with the tenant_id from the auth context.
    args.append(Comparison(C.EQ, "tenant_id", tenant_id))
    if session_ids:
        args.append(
            Operation(
                operator=Operator.OR,
                arguments=[
                    Comparison(C.EQ, "session_id", session_id)
                    for session_id in session_ids
                ],
            )
        )
    if body.start_time:
        args.append(Comparison(C.GTE, "start_time", body.start_time))
    if body.end_time:
        args.append(Comparison(C.LTE, "start_time", body.end_time))
    if body.cursor:
        args.append(
            parse_as_filter_directive_ftsearch(body.cursor, "Unable to parse cursor")
        )
    if body.parent_run:
        args.append(Comparison(C.EQ, "parent_run_id", body.parent_run))
    # Add the rest of the query
    if body.search_filter:
        args.append(
            parse_as_filter_directive_ftsearch(
                body.search_filter, "Unable to parse filter."
            )
        )

    visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
    try:
        result = where.accept(visitor)
    except ValueError as e:
        logger.error(
            "Failed to parse query",
            error=str(e),
            query=where,
            tenant_id=tenant_id,
        )
        raise HTTPException(
            status_code=400,
            detail="Invalid search query format or parameters",
        )

    query_str = result["query_str"]

    if settings.QUICKWIT_SEARCH_QUERY_LOGS:
        logger.info("Quickwit query", query=query_str)

    # Fetch one more than the limit to determine if there are more results
    extended_limit = body.limit + 1

    if settings.FF_TRACE_TIERS_ENABLED:
        index_ids = [
            settings.QUICKWIT_RUNS_INDEX,
            settings.QUICKWIT_RUNS_INDEX_LONG_TTL,
        ]
        # TODO - figure out if we need to add a delta here on top of multiplier
        extended_limit *= settings.QUICKWIT_SEARCH_PAGE_MULTIPLIER
    else:
        index_ids = [settings.QUICKWIT_RUNS_INDEX]

    # Prepare search request parameters
    search_params = {
        "query": query_str,
        "max_hits": extended_limit,
        "sort_by": "start_time,id_sort",
    }

    search_results = await execute_quickwit_search(
        query_name="fetch_runs",
        search_params=search_params,
        query_str=query_str,
        tenant_id=str(tenant_id),
        index_ids=index_ids,
    )

    hits = search_results.get("hits", [])

    if settings.FF_TRACE_TIERS_ENABLED:
        # Dedupe results if we queried both tiers
        seen = set()
        hits_deduped = []

        for hit in hits:
            if not hit.get("id"):
                continue
            if hit["id"] not in seen:
                seen.add(hit["id"])
                hits_deduped.append(hit)

        hits = hits_deduped

    has_next_page = len(hits) > body.limit

    if has_next_page:
        runs = hits[: body.limit]
    else:
        runs = hits

    # Stop-gap to ensure all hits we are returning belong to the tenant querying for the data
    # Until we are confident in our ability to prevent query injection
    # (e.g. through inability to quote a value in certain cases)
    if not all(str(auth.tenant_id) == run["tenant_id"] for run in runs):
        logger.warn(
            "Blocked likely query injection",
            query=query_str,
            tenant_id=auth.tenant_id,
        )
        raise HTTPException(
            status_code=422,
            detail="Invalid search query",
        )

    cursor_next_page = None

    if has_next_page and runs:
        last_item = runs[-1]
        cursor_next_page = f"{'lt' if body.order == 'desc' else 'gt'}(cursor, '{last_item['start_time_micros']}|{last_item['id_sort']}')"

    if not runs:
        return fetch_ch.FetchRunsResult(
            runs=[],
            cursors={
                "next": None,
                "prev": None,
            },
            parsed_query=query_str,
        )

    first_item = runs[0]
    cursor_prev_page = f"{'gt' if body.order == 'desc' else 'lt'}(cursor, '{first_item['start_time_micros']}|{first_item['id_sort']}')"

    if settings.QUICKWIT_SEARCH_USE_HYDRATE_RUNS:
        # Use hydrate_runs which does not require additional ClickHouse call for pagination cursor logic
        # as it is fully handled by Quickwit
        hydrated_runs = await fetch_ch.hydrate_runs(
            auth=auth,
            runs=runs,
            select=body.select,
        )

        return fetch_ch.FetchRunsResult(
            runs=hydrated_runs,
            cursors={
                "next": cursor_next_page,
                "prev": cursor_prev_page,
            },
            parsed_query=query_str,
        )

    min_start_time = min(run["start_time"] for run in runs)

    is_root = None
    if all(run["is_root"] for run in runs):
        is_root = True
    elif not any(run["is_root"] for run in runs):
        is_root = False

    # Hydrate the results with the ClickHouse data
    body_params_ch = schemas.BodyParamsForRunSchema(
        id=[run["id"] for run in runs],
        session_id=[run["session_id"] for run in runs],
        is_root=is_root,
        start_time=datetime.fromisoformat(min_start_time),
        limit=body.limit,
        select=body.select,
    )

    results = await fetch_ch.fetch_runs(auth, body_params_ch)
    # override the cursors with the ones from Quickwit
    results["cursors"] = {
        "next": cursor_next_page,
        "prev": cursor_prev_page,
    }
    return results
