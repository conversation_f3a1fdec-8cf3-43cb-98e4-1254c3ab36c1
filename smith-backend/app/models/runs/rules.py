import asyncio
import datetime
import json
import logging
import re
from typing import Any, List, Literal, Optional, cast
from uuid import UUID, uuid4

import asyncpg
from aiohttp import ClientSession
from asyncpg import ForeignKeyViolationError
from fastapi import HTTPException
from httpx import HTTPStatusError
from langchain_core.load import dumpd
from langchain_core.prompts.structured import StructuredPrompt
from langchain_core.runnables import RunnableConfig
from langchain_core.runnables.utils import Input
from langchain_core.utils.function_calling import convert_to_json_schema
from lc_config.service_communication_settings import ServiceName
from lc_config.settings import shared_settings as settings
from lc_database import redis
from lc_database.custom_code import custom_code_client
from lc_database.database import asyncpg_conn, asyncpg_pool, kwargs_to_pgpos
from lc_database.service_client import (
    get_internal_tenant_scoped_service_client,
    get_service_client,
)

from app import crud, schemas
from app.api.auth.schemas import AuthInfo
from app.hub import schema as hub_schema
from app.hub.crud import commits, repos
from app.hub.crud.commits import get_commit_manifest
from app.hub.utils import (
    is_prompt_playground,
    is_runnable_sequence,
    parse_owner_repo_commit,
)
from app.models.custom_code.constants import CUSTOM_CODE_EXECUTION_FUNC_NAME
from app.models.feedback.fetch import fetch_feedback
from app.models.query_lang.parse import parse_as_filter_directive
from app.models.runs.fetch_ch import fetch_runs
from app.models.runs.stats import backfill_run_stats
from app.models.runs.utils import verify_rule_valid_for_corrections_and_get_feedback_key
from app.models.tenants.secrets import list_secrets_as_dict
from app.models.tracer_sessions.create import (
    create_tracer_session_within_existing_transaction,
)
from app.models.utils import get_first_path
from app.models.utils.encryption import (
    decrypt_dict_values,
    decrypt_string,
    encrypt_dict_values,
    encrypt_string,
)
from app.retry import retry_asyncpg
from app.utils import base_exception_mapper, gated_coro

logger = logging.getLogger(__name__)

EXAMPLES_FEW_SHOT_KEY = "examples_few_shot"


_ALIGNMENT_EVALUATOR_CODE = """\
def perform_eval(run, example):
    if "{correction_feedback_key}" not in run["outputs"]:
        raise ValueError(
            "Evaluator outputs did not contain the key '{correction_feedback_key}'."
        )
    elif "{correction_feedback_key}" not in example["outputs"]:
        raise ValueError(
            "Example outputs did not contain the key '{correction_feedback_key}'."
        )
    else:
        actual =  run["outputs"]["{correction_feedback_key}"]
        expected =  example["outputs"]["{correction_feedback_key}"]
        return {{"key": "alignment", "score": int(actual) == int(expected)}}
"""


async def decrypt_webhooks(webhooks: list[schemas.RunRulesWebhookSchema]):
    for webhook in webhooks:
        if webhook.headers:
            webhook.headers = await decrypt_dict_values(webhook.headers)

        if webhook.url and not webhook.url.startswith("http"):
            decrypted_url = await decrypt_string(webhook.url)
            if decrypted_url is not None:
                webhook.url = decrypted_url


async def encrypt_webhooks(webhooks: list[schemas.RunRulesWebhookSchema]):
    for webhook in webhooks:
        if webhook.headers:
            webhook.headers = await encrypt_dict_values(webhook.headers)

        if webhook.url:
            encrypted_url = await encrypt_string(webhook.url)
            if encrypted_url is not None:
                webhook.url = encrypted_url


async def validate_and_decrypt_rule(rule_dict: dict) -> schemas.RunRulesSchema:
    eval_dict = {
        key[len("evaluator_") :]: rule_dict.pop(key, None)
        for key in (
            "evaluator_prompt_handle",
            "evaluator_variable_mapping",
            "evaluator_commit_hash_or_tag",
        )
    }
    if any(eval_dict.values()) and not rule_dict.get("evaluators"):
        commit_or_tag = eval_dict.get("commit_hash_or_tag") or "latest"
        hub_ref = ":".join([eval_dict["prompt_handle"], commit_or_tag])
        rule_dict["evaluators"] = [
            {
                "structured": {
                    "hub_ref": hub_ref,
                    "variable_mapping": eval_dict["variable_mapping"],
                }
            }
        ]
    rule = schemas.RunRulesSchema.model_validate(rule_dict)
    if rule.webhooks:
        await decrypt_webhooks(rule.webhooks)
    return rule


def verify_python_function_with_name_and_args(code_string, function_name, num_args):
    pattern = re.compile(rf"def {function_name}\((.*?)\)(\s*->\s*[\w\[\],\s]*)?:")

    match = pattern.search(code_string)
    if match:
        args = match.group(1).split(",")
        if len(args) != num_args:
            raise HTTPException(
                status_code=400,
                detail=f"Function should take exactly {num_args} positional arguments.",
            )
        return True
    raise HTTPException(
        status_code=400,
        detail=f"Code evaluator does not contain function with name {function_name}",
    )


async def validate_code_evaluators(
    auth: AuthInfo,
    code_evaluators: list[schemas.CodeEvaluatorTopLevel],
    has_dataset: bool = False,
) -> None:
    async with custom_code_client() as client:
        for code_eval in code_evaluators:
            response = await client.post(
                "/validate",
                json={
                    "code": code_eval.code,
                    "function": CUSTOM_CODE_EXECUTION_FUNC_NAME,
                },
            )
            try:
                response.raise_for_status()
            except HTTPStatusError as e:
                stack_trace = e.response.json().get("stacktrace")
                raise HTTPException(
                    status_code=e.response.status_code,
                    detail=f"Code evaluator failed validation: {stack_trace or e.response.text}",
                )

            verify_python_function_with_name_and_args(
                code_eval.code, CUSTOM_CODE_EXECUTION_FUNC_NAME, 2 if has_dataset else 1
            )


@retry_asyncpg
async def create_rule_with_existing_transaction(
    db: asyncpg.Connection,
    auth: AuthInfo,
    rule: schemas.RunRulesCreateSchema,
) -> schemas.RunRulesSchema:
    if rule.filter:
        parse_as_filter_directive(rule.filter)
    if rule.trace_filter:
        parse_as_filter_directive(rule.trace_filter)
    if rule.tree_filter:
        parse_as_filter_directive(rule.tree_filter)
    db_evaluator, db_llm_evaluator = None, None
    session_name = None
    dataset_name = None
    if rule.evaluators or rule.code_evaluators:
        if rule.session_id:
            session_name = await _fetch_session_name(auth, rule.session_id, db)
            if session_name == "evaluators":
                raise HTTPException(
                    status_code=400,
                    detail="The 'evaluators' project is reserved, and you cannot create a run rule with evaluators on it.",
                )

        if rule.evaluators:
            secrets = await list_secrets_as_dict(auth)
            for evaluator in rule.evaluators:
                # will raise http exception if invalid
                await validate_evaluator(
                    evaluator, auth, db, secrets, rule.evaluator_version
                )
        elif rule.code_evaluators:
            await validate_code_evaluators(
                auth, rule.code_evaluators, bool(rule.dataset_id)
            )
        if (
            rule.evaluators
            and auth.tenant_config.organization_config.new_rule_evaluator_creation_version
            >= 3
            and rule.dataset_id
        ):
            # Construct the evaluator to insert from here
            evaluator = rule.evaluators[0].structured  # type: ignore[index]
            if hub_ref := evaluator.hub_ref:
                owner, existing_prompt_name, commit_hash = parse_owner_repo_commit(
                    hub_ref
                )
                existing_repo = await repos.get_repo_by_full_name(
                    db, auth, existing_prompt_name, owner=owner
                )
                prompt_id = existing_repo.id
                manifest = await get_commit_manifest(
                    db,
                    auth,
                    owner,
                    existing_prompt_name,
                    commit_hash if commit_hash != "latest" else None,
                    get_examples=False,
                    is_view=False,
                )
                # I believe this accessing is safe due to the validate_evaluator call above
                manifest_kwargs = manifest.manifest.get("kwargs", {})
                evaluator_schema = manifest_kwargs.get(
                    "schema_", manifest_kwargs.get("schema", {})
                )
            else:
                # Create the prompt
                new_repo = await _create_evaluator_hub_repo(auth, rule, evaluator, db)
                prompt_id = new_repo.id
                commit_hash = None
                evaluator_schema = evaluator.tool_schema or {}
            try:
                evaluator_schema = convert_to_json_schema(evaluator_schema)
            except ValueError as e:
                logger.error(f"Failed to convert evaluator schema: {e}")
                raise HTTPException(
                    status_code=422,
                    detail=(
                        "Feedback configuration is malformed. "
                        "Please pass in a valid JSON schema with 'title', 'description', and 'properties' specified. "
                        "Must include at least one property."
                    ),
                )
            evaluator_id = uuid4()
            properties = evaluator_schema.get("properties", {})
            feedback_key = _get_single_feedback_key(properties)
            if not (feedback_key and properties[feedback_key].get("type") == "boolean"):
                annotation_queue_id = None
            else:
                # Create the annotation queue for labeling
                if rule.dataset_id:
                    dataset_name = await _fetch_dataset_name(auth, rule.dataset_id, db)
                    parent_name = dataset_name
                    aq_description = f"Annotate examples to create a reference dataset for evaluator '{rule.display_name}' running on dataset '{parent_name}'"
                elif rule.session_id:
                    session_name = await _fetch_session_name(auth, rule.session_id, db)
                    parent_name = session_name
                    aq_description = f"Annotate examples to create a reference dataset for evaluator '{rule.display_name}' running on tracing project '{parent_name}'"
                else:
                    parent_name = None
                    aq_description = f"Annotate examples to create a reference dataset for evaluator '{rule.display_name}'"
                aq_name = _eval_resource_name(
                    rule.display_name, evaluator_id, parent_name=parent_name
                )
                queue = schemas.AnnotationQueueCreateSchema(
                    name=aq_name,
                    description=aq_description,
                    rubric_items=[
                        schemas.AnnotationQueueRubricItemSchema(
                            feedback_key=feedback_key,
                            description=evaluator_schema.get("description"),
                            # Need to figure this out, leaving blank for now - can be updated by user later
                            score_descriptions={"0": "", "1": ""},
                        )
                    ],
                    rubric_instructions="Label the examples according the feedback criteria",
                    evaluator_id=evaluator_id,
                )
                annotation_queue = await crud.create_annotation_queue(db, auth, queue)
                annotation_queue_id = annotation_queue.id
            db_evaluator = schemas.Evaluator(
                id=evaluator_id, name=rule.display_name, type=schemas.EvaluatorType.llm
            )
            db_llm_evaluator = schemas.LLMEvaluator(
                evaluator_id=evaluator_id,
                prompt_id=prompt_id,
                commit_hash_or_tag=commit_hash,
                annotation_queue_id=annotation_queue_id,
                variable_mapping=evaluator.variable_mapping,
            )
    if (
        await db.fetchval(
            "select count(*) from run_rules where tenant_id = $1 and parent_rule_id is NULL",
            auth.tenant_id,
        )
        >= auth.tenant_config.max_run_rules
        and not settings.IS_SELF_HOSTED
    ):
        raise HTTPException(
            status_code=400,
            detail="Maximum number of run rules exceeded, <NAME_EMAIL> to increase limit",
        )

    rule_id = uuid4()
    corrections_dataset_id = None
    correction_feedback_key = verify_rule_valid_for_corrections_and_get_feedback_key(
        rule
    )
    if correction_feedback_key is None and rule.use_corrections_dataset:
        raise HTTPException(
            status_code=400, detail="Invalid rule for corrections dataset"
        )
    if correction_feedback_key is not None:
        corrections_dataset_id = (
            await create_and_setup_corrections_dataset_with_existing_transaction(
                db,
                auth,
                rule_id,
                rule,
                evaluator_id=db_llm_evaluator.evaluator_id
                if db_llm_evaluator
                else None,
                dataset_name=dataset_name,
                project_name=session_name,
            )
        )

    if rule.backfill_from:
        if rule.session_id:
            sessions = [rule.session_id]
        elif rule.dataset_id:
            rows = await db.fetch(
                """
                select id
                from tracer_session
                where tenant_id = $1 and reference_dataset_id = $2
                and (end_time is null or end_time + interval '1 day' > $3)""",
                auth.tenant_id,
                rule.dataset_id,
                datetime.datetime.now(datetime.timezone.utc),
            )
            sessions = [UUID(row["id"].hex) for row in rows]

        if sessions:
            query_params = schemas.RunStatsQueryParams(
                session=sessions,
                start_time=rule.backfill_from,
                filter=rule.filter,
                trace_filter=rule.trace_filter,
                tree_filter=rule.tree_filter,
            )
            backfill_stats = await backfill_run_stats(
                auth,
                query_params=query_params,
                select=[schemas.RunStatsSelect.run_count],
                tenant_id=auth.tenant_id,
            )
            row_count = backfill_stats["run_count"]
            if rule.sampling_rate:
                row_count = int(row_count * rule.sampling_rate)

            row_limit = (
                settings.RUN_RULES_BACKFILL_FOR_EXTENDED_RETENTION_PROJECT_LIMIT
                if rule.extend_only
                else settings.RUN_RULES_BACKFILL_PROJECT_LIMIT
            )
            if row_count > row_limit:
                raise HTTPException(
                    status_code=400,
                    detail=f"Backfill from date records exceed {row_limit} records",
                )

    try:
        if rule.webhooks:
            await encrypt_webhooks(rule.webhooks)

        evaluators = (
            [
                e.model_dump(exclude_none=True, by_alias=True)
                for e in (rule.evaluators or [])
            ]
            if db_evaluator is None
            else []
        )

        code_evaluators = [
            ce.model_dump(exclude_none=True, by_alias=True)
            for ce in (rule.code_evaluators or [])
        ]

        alerts = [a.model_dump() for a in (rule.alerts or [])]
        webhooks = [w.model_dump() for w in (rule.webhooks or [])]
        evaluator_version = (
            auth.tenant_config.organization_config.new_rule_evaluator_creation_version
        )

        base_params = {
            "tenant_id": auth.tenant_id,
            "filter": rule.filter,
            "trace_filter": rule.trace_filter,
            "tree_filter": rule.tree_filter,
            "sampling_rate": rule.sampling_rate,
            "add_to_annotation_queue_id": rule.add_to_annotation_queue_id,
            "add_to_dataset_id": rule.add_to_dataset_id,
            "evaluators": evaluators,
            "code_evaluators": code_evaluators,
            "alerts": alerts,
            "display_name": rule.display_name,
            "backfill_from": rule.backfill_from,
            "webhooks": webhooks,
            "add_to_dataset_prefer_correction": rule.add_to_dataset_prefer_correction,
            "id": rule_id,
            "corrections_dataset_id": corrections_dataset_id,
            "extend_only": rule.extend_only,
            "use_corrections_dataset": rule.use_corrections_dataset,
            "num_few_shot_examples": rule.num_few_shot_examples,
            "evaluator_version": evaluator_version,
            "transient": rule.transient,
        }

        # Branch-specific logic
        extra_params: dict[str, Any] = {}
        if rule.session_id:
            source_table = "tracer_session"
            source_column = "session_id"
            source_id_field = "session_id"
            source_id_value = rule.session_id
            extra_fields = ""
            extra_values = ""
        elif rule.dataset_id:
            source_table = "dataset"
            source_column = "dataset_id"
            source_id_field = "dataset_id"
            source_id_value = rule.dataset_id
            extra_fields = ", evaluator_id"
            extra_values = ", $evaluator_id"
            extra_params.update(
                {"evaluator_id": db_evaluator.id if db_evaluator else None}
            )

        # Build and execute final query
        query = f"""
            INSERT INTO run_rules (
                {source_column},
                tenant_id,
                filter,
                trace_filter,
                tree_filter,
                sampling_rate,
                add_to_annotation_queue_id,
                add_to_dataset_id,
                evaluators,
                code_evaluators,
                alerts,
                display_name,
                backfill_from,
                webhooks,
                add_to_dataset_prefer_correction,
                id,
                corrections_dataset_id,
                extend_only,
                use_corrections_dataset,
                num_few_shot_examples,
                evaluator_version,
                transient
                {extra_fields}
            )
            SELECT id, $tenant_id, $filter, $trace_filter, $tree_filter, $sampling_rate,
                $add_to_annotation_queue_id, $add_to_dataset_id, $evaluators, $code_evaluators,
                $alerts, $display_name, $backfill_from, $webhooks, $add_to_dataset_prefer_correction,
                $id, $corrections_dataset_id, $extend_only, $use_corrections_dataset,
                $num_few_shot_examples, $evaluator_version, $transient
                {extra_values}
            FROM {source_table}
            WHERE tenant_id = $tenant_id AND id = ${source_id_field}
            RETURNING *
        """
        if (
            rule.dataset_id
            and db_evaluator is not None
            and db_llm_evaluator is not None
        ):
            extra_params.update(
                {
                    "db_evaluator_id": db_evaluator.id,
                    "db_evaluator_name": db_evaluator.name,
                    "db_evaluator_type": db_evaluator.type,
                    "db_llm_evaluator_prompt_id": db_llm_evaluator.prompt_id,
                    "db_llm_evaluator_commit_hash_or_tag": db_llm_evaluator.commit_hash_or_tag,
                    "db_llm_evaluator_variable_mapping": db_llm_evaluator.variable_mapping,
                    "db_llm_evaluator_annotation_queue_id": db_llm_evaluator.annotation_queue_id,
                    "tenant_id_for_hub": auth.tenant_id,
                }
            )

            query = f"""
                WITH 
                
                evaluator_insert AS (
                    INSERT INTO evaluators (id, name, type) 
                    VALUES ($db_evaluator_id, $db_evaluator_name, $db_evaluator_type)
                ),

                llm_evaluator_insert AS (
                    INSERT INTO llm_evaluators (
                        evaluator_id, prompt_id, commit_hash_or_tag, variable_mapping, annotation_queue_id
                    )
                    VALUES ($db_evaluator_id, $db_llm_evaluator_prompt_id, $db_llm_evaluator_commit_hash_or_tag, $db_llm_evaluator_variable_mapping, $db_llm_evaluator_annotation_queue_id)
                    RETURNING *
                ),
                
                rules_insert AS (
                    {query}
                )

                SELECT 
                    rules_insert.*, 
                    lei.commit_hash_or_tag as evaluator_commit_hash_or_tag,
                    lei.variable_mapping as evaluator_variable_mapping,
                    lei.annotation_queue_id as alignment_annotation_queue_id,
                    hub_repos.repo_handle as evaluator_prompt_handle
                FROM rules_insert
                CROSS JOIN llm_evaluator_insert lei
                JOIN hub_repos ON lei.prompt_id = hub_repos.id
                WHERE hub_repos.tenant_id = $tenant_id_for_hub
            """

        sql_kwargs = {
            **base_params,
            source_id_field: source_id_value,
            **extra_params,
        }
        sql = kwargs_to_pgpos(query, sql_kwargs)
        params = sql.args
        insert = sql.sql
        row = await db.fetchrow(insert, *params)
        if (
            corrections_dataset_id is not None
            and correction_feedback_key is not None
            and evaluator_version < 3
        ):
            await upsert_sub_rule_with_existing_transaction(
                db,
                auth,
                rule_id,
                corrections_dataset_id,
                correction_feedback_key,
                rule.display_name,
            )
    except ForeignKeyViolationError:
        if rule.add_to_annotation_queue_id:
            raise HTTPException(
                status_code=400,
                detail=f"Annotation queue not found: {rule.add_to_annotation_queue_id}",
            )
        elif rule.add_to_dataset_id:
            raise HTTPException(
                status_code=400, detail=f"Dataset not found: {rule.add_to_dataset_id}"
            )
        else:
            raise

    if not row:
        if rule.dataset_id:
            raise HTTPException(
                status_code=404, detail=f"Dataset not found: {rule.dataset_id}"
            )
        else:
            raise HTTPException(
                status_code=404,
                detail=f"Project (tracing session) not found: {rule.session_id}",
            )

    return await validate_and_decrypt_rule(dict(row))


@retry_asyncpg
async def create_rule(
    auth: AuthInfo,
    rule: schemas.RunRulesCreateSchema,
) -> schemas.RunRulesSchema:
    """Create a new run rule."""
    async with asyncpg_conn() as db, db.transaction():
        return await create_rule_with_existing_transaction(db, auth, rule)


async def create_and_setup_corrections_dataset_with_existing_transaction(
    db: asyncpg.Connection,
    auth: AuthInfo,
    rule_id: UUID,
    rule: schemas.RunRulesCreateSchema,
    corrections_feedback_tag: str | None = None,
    evaluator_id: UUID | None = None,
    dataset_name: str | None = None,
    project_name: str | None = None,
) -> UUID:
    correction_feedback_key = (
        corrections_feedback_tag
        or verify_rule_valid_for_corrections_and_get_feedback_key(rule)
    )
    evaluators_project_id = await db.fetchval(
        """
        select id
        from tracer_session
        where tenant_id = $1 and name = 'evaluators'""",
        auth.tenant_id,
    )
    extra = {"rule_id": str(rule_id), "feedback_key": correction_feedback_key}
    if evaluator_id:
        name = _eval_resource_name(
            rule.display_name, evaluator_id, parent_name=dataset_name or project_name
        )
        extra["evaluator_id"] = str(evaluator_id)
    else:
        name = f"ds-corrections-{rule.display_name}-{correction_feedback_key}-{rule_id}"

    if dataset_name:
        description = f"Reference examples for evaluator '{rule.display_name}' running on dataset '{dataset_name}'. Used for alignment and few-shot."
    elif project_name:
        description = f"Reference examples for evaluator '{rule.display_name}' running on tracing project '{project_name}'. Used for alignment and few-shot."
    else:
        description = f"Reference examples for evaluator '{rule.display_name}'. Used for alignment and few-shot."

    corrections_dataset = await crud.create_dataset_with_existing_transaction(
        db,
        auth,
        schemas.DatasetCreate(
            name=name,
            description=description,
            extra=extra,
            # Each corrected example will have a few shot examples key in the inputs.
            # Ideally we'd remove this key completely (TODO) but we don't support
            # removing a single field today. Instead we just empty out all of the few
            # shot examples.
            inputs_schema_definition={
                "type": "object",
                "title": "dataset_input_schema",
                "properties": {
                    EXAMPLES_FEW_SHOT_KEY: {
                        "type": "array",
                        "items": {"type": "object"},
                    }
                },
                "required": [],
            },
            transformations=[
                schemas.DatasetTransformation(
                    path=["inputs", EXAMPLES_FEW_SHOT_KEY, "*"],
                    transformation_type=schemas.DatasetTransformationType.remove_extra_fields,
                )
            ],
        ),
    )
    corrections_dataset_id = corrections_dataset.id

    if not evaluators_project_id:
        await create_tracer_session_within_existing_transaction(
            db,
            auth,
            schemas.TracerSessionCreate(
                name="evaluators",
            ),
        )

    # Create code evaluator rule on corrections dataset for version >= 3
    if auth.tenant_config.organization_config.new_rule_evaluator_creation_version >= 3:
        # Create Python code for the evaluator

        # Create the code evaluator
        code_evaluator = schemas.CodeEvaluatorTopLevel(
            code=_ALIGNMENT_EVALUATOR_CODE.format(
                correction_feedback_key=correction_feedback_key
            )
        )

        # Create a rule for the corrections dataset with the code evaluator
        corrections_rule = schemas.RunRulesCreateSchema(
            display_name="Alignment",
            dataset_id=corrections_dataset_id,
            filter="eq(is_root, true)",
            sampling_rate=1.0,  # Process all corrections
            is_enabled=True,
            code_evaluators=[code_evaluator],
            use_corrections_dataset=False,  # This rule itself doesn't need corrections
        )

        # Create the rule
        await create_rule_with_existing_transaction(db, auth, corrections_rule)

    return corrections_dataset_id


async def upsert_sub_rule_with_existing_transaction(
    db: asyncpg.Connection,
    auth: AuthInfo,
    parent_rule_id: UUID,
    corrections_dataset_id: UUID,
    correction_feedback_key: str,
    parent_display_name: str,
):
    evaluator_project_rule_filter = f"and(eq(is_root, true), and(eq(metadata_key, 'rule_id'), eq(metadata_value, '{str(parent_rule_id)}')), eq(feedback_key, '{correction_feedback_key}'))"
    await db.fetchrow(
        """
        with existing_rule as (
            select id
            from run_rules
            where parent_rule_id = $7
        ),
        update_rule as (
            update run_rules
            set
                session_id = tracer_session.id,
                tenant_id = $1,
                filter = $2,
                sampling_rate = $3,
                add_to_dataset_id = $4,
                display_name = $5,
                add_to_dataset_prefer_correction = $6
            from tracer_session
            where run_rules.parent_rule_id = $7
              and tracer_session.tenant_id = $1
              and run_rules.tenant_id = $1
              and tracer_session.name = 'evaluators'
            returning run_rules.*
        )
        insert into run_rules (
            session_id,
            tenant_id,
            filter,
            sampling_rate,
            add_to_dataset_id,
            display_name,
            add_to_dataset_prefer_correction,
            parent_rule_id
        )
        select id, $1, $2, $3, $4, $5, $6, $7
        from tracer_session
        where tenant_id = $1
          and name = 'evaluators'
          and not exists (select 1 from existing_rule)
        returning *
        """,
        auth.tenant_id,
        evaluator_project_rule_filter,
        1,
        corrections_dataset_id,
        f"Corrections for run rule {parent_display_name}",
        True,
        parent_rule_id,
    )


def create_playground_manifest_from_prompt_and_model(prompt, model):
    return {
        "lc": 1,
        "type": "constructor",
        "id": ["langsmith", "playground", "PromptPlayground"],
        "kwargs": {"first": prompt, "last": model},
    }


@retry_asyncpg
async def update_rule(
    auth: AuthInfo,
    rule_id: UUID,
    rule: schemas.RunRulesCreateSchema,
) -> schemas.RunRulesSchema:
    if rule.filter:
        parse_as_filter_directive(rule.filter)
    if rule.trace_filter:
        parse_as_filter_directive(rule.trace_filter)
    if rule.tree_filter:
        parse_as_filter_directive(rule.tree_filter)
    if len(rule.evaluators or []) + len(rule.code_evaluators or []) > 1:
        raise HTTPException(
            status_code=422,
            detail="Cannot update a run rule with more than 1 combined code or llm evaluator.",
        )
    if rule.code_evaluators:
        await validate_code_evaluators(
            auth, rule.code_evaluators, bool(rule.dataset_id)
        )
    async with asyncpg_conn() as db, db.transaction():
        evaluator_id = None
        llm_evaluator_update = None
        if rule.at_least_v3:
            row = await db.fetchrow(
                """
                    select evaluator_id, evaluator_version from run_rules
                    where id = $1 and tenant_id = $2
                    """,
                rule_id,
                auth.tenant_id,
            )
            if not row:
                raise HTTPException(status_code=404, detail="Rule not found")
            evaluator_id = row["evaluator_id"]
            if row["evaluator_version"] != rule.evaluator_version:
                raise HTTPException(
                    status_code=422,
                    detail="Cannot update a run rule to an evaluator version greater than or equal to 3.",
                )
        if rule.evaluators:
            secrets = await list_secrets_as_dict(auth, db=db)
            for eval_update in rule.evaluators:
                # will raise http exception if invalid
                await validate_evaluator(
                    eval_update, auth, db, secrets, rule.evaluator_version
                )
            if not evaluator_id:
                evaluator_id = await db.fetchval(
                    """
                    select evaluator_id from run_rules
                    where id = $1 and tenant_id = $2
                    """,
                    rule_id,
                    auth.tenant_id,
                )
            # The newest evaluators/rules will have an evaluator id.
            # For these we need to create a new prompt hub repo if the rule update
            # includes a prompt manifest.
            if evaluator_id:
                # We know there is only 1 from above check
                evaluator_update = rule.evaluators[0].structured
                if hub_ref := evaluator_update.hub_ref:
                    owner, prompt_name, commit_hash = parse_owner_repo_commit(hub_ref)

                    existing_repo = await repos.get_repo_by_full_name(
                        db, auth, prompt_name, owner=owner
                    )
                    prompt_id = existing_repo.id
                else:
                    # Create the prompt
                    new_repo = await _create_evaluator_hub_repo(
                        auth, rule, evaluator_update, db
                    )
                    prompt_id = new_repo.id
                    # Indicates we'll always use the latest commit.
                    commit_hash = None
                llm_evaluator_update = schemas.LLMEvaluator(
                    evaluator_id=evaluator_id,
                    prompt_id=prompt_id,
                    commit_hash_or_tag=commit_hash,
                    variable_mapping=evaluator_update.variable_mapping,
                )

                # Changing the schema for boolean evaluators isn't allowed.
                # Check the existing evaluator to see if it's boolean.
                existing_evaluator_info = await db.fetchrow(
                    """
                    SELECT 
                        le.prompt_id as prompt_id, 
                        le.commit_hash_or_tag as commit_hash_or_tag, 
                        hr.repo_handle as repo_handle,
                        hc.manifest as manifest
                    FROM llm_evaluators le
                    JOIN hub_repos hr ON hr.id = le.prompt_id
                    LEFT JOIN hub_commits hc ON hc.repo_id = le.prompt_id
                    LEFT JOIN hub_repo_tags t ON t.repo_id = le.prompt_id AND t.commit_id = hc.id
                    WHERE le.evaluator_id = $1
                      AND hr.tenant_id = $2
                      AND (
                          le.commit_hash_or_tag IS NULL
                          OR le.commit_hash_or_tag = 'latest' 
                          OR hc.commit_hash ^@ le.commit_hash_or_tag 
                          OR t.tag_name = le.commit_hash_or_tag
                      )
                    ORDER BY 
                        (hc.commit_hash ^@ le.commit_hash_or_tag) DESC,
                        t.tag_name IS NOT NULL DESC,
                        hc.created_at DESC
                    """,
                    evaluator_id,
                    auth.tenant_id,
                )

                # Get existing schema
                existing_manifest = json.loads(existing_evaluator_info["manifest"])
                existing_schema = _get_schema_from_hub_manifest(existing_manifest) or {}
                properties = existing_schema.get("properties") or {}
                feedback_key = _get_single_feedback_key(properties)
                boolean_feedback = (
                    properties.get(feedback_key, {}).get("type") == "boolean"
                )
                if boolean_feedback:
                    # Get new schema
                    if evaluator_update.hub_ref:
                        new_manifest_info = await get_commit_manifest(
                            db,
                            auth,
                            owner,
                            prompt_name,
                            commit_hash if commit_hash != "latest" else None,
                            False,
                            False,
                        )
                        new_schema = _get_schema_from_hub_manifest(
                            new_manifest_info.manifest
                        )
                    else:
                        # New evaluator uses tool_schema
                        new_schema = evaluator_update.tool_schema
                    new_schema = convert_to_json_schema(new_schema or {})
                    new_properties = cast(dict, new_schema).get("properties") or {}
                    new_output_keys = get_non_reasoning_properties(new_properties)
                    if new_output_keys != [feedback_key]:
                        raise HTTPException(
                            status_code=422,
                            detail=(
                                "Cannot change the structure of a boolean evaluator. "
                                "The existing evaluator has output key: "
                                f"'{feedback_key}', but the new evaluator has the "
                                f"following output key(s): '{new_output_keys}'."
                            ),
                        )

                    # Check property type is still boolean
                    new_type = new_properties[feedback_key].get("type")
                    if new_type != "boolean":
                        raise HTTPException(
                            status_code=422,
                            detail=(
                                "Cannot change the output type of a boolean evaluator. "
                                f"The feedback key '{feedback_key}'is currently typed as "
                                f"'boolean', but the new schema has type: '{new_type}'.",
                            ),
                        )

        corrections_dataset_id = None
        if (
            rule.evaluators
            and rule.evaluators[0].structured.hub_ref
            and rule.at_least_v3
        ):
            commit_hash = rule.evaluators[0].structured.hub_ref.split(":")[1]
            if commit_hash == "latest":
                commit_hash = None
            commit = await get_commit_manifest(
                db,
                auth,
                None,
                rule.evaluators[0].structured.hub_ref.split(":")[0],
                commit_hash,
                False,
                False,
            )
            schema = _get_schema_from_hub_manifest(commit.manifest) or {}
            corrections_feedback_key = _get_single_feedback_key(
                schema.get("properties") or {}
            )
        else:
            corrections_feedback_key = (
                verify_rule_valid_for_corrections_and_get_feedback_key(rule)
            )
        if corrections_feedback_key is None and rule.use_corrections_dataset:
            raise HTTPException(
                status_code=400, detail="Invalid rule for corrections dataset"
            )
        elif corrections_feedback_key is not None:
            # Need to check if a corrections dataset exists with the correct rule id and feedback tag, and create one if it doesn't.
            corrections_dataset_id = await db.fetchval(
                """
                SELECT id
                FROM dataset
                WHERE tenant_id = $1
                AND extra @> jsonb_build_object('rule_id', $2::text, 'feedback_key', $3::text);
                """,
                auth.tenant_id,
                str(rule_id),
                corrections_feedback_key,
            )
            if corrections_dataset_id is None:
                dataset_name = None
                session_name = None
                if rule.dataset_id:
                    dataset_name = await _fetch_dataset_name(auth, rule.dataset_id, db)
                if rule.session_id:
                    session_name = await _fetch_session_name(auth, rule.session_id, db)
                corrections_dataset_id = await create_and_setup_corrections_dataset_with_existing_transaction(
                    db,
                    auth,
                    rule_id,
                    rule,
                    corrections_feedback_key,
                    evaluator_id=evaluator_id,
                    dataset_name=dataset_name,
                    project_name=session_name,
                )
            if not rule.at_least_v3:
                await upsert_sub_rule_with_existing_transaction(
                    db,
                    auth,
                    rule_id,
                    corrections_dataset_id,
                    corrections_feedback_key or "",
                    rule.display_name,
                )
        else:
            await db.fetch(
                """
                DELETE FROM run_rules
                WHERE tenant_id = $1 and parent_rule_id = $2;""",
                auth.tenant_id,
                rule_id,
            )
        try:
            if rule.webhooks:
                await encrypt_webhooks(rule.webhooks)

            if rule.session_id:
                from_clause = "tracer_session"
                set_clause_id = "session_id = tracer_session.id,"
                null_clause = "dataset_id = NULL,"
                where_clause_id = "tracer_session.id = $session_or_dataset_id"
                session_or_dataset_id = rule.session_id
            elif rule.dataset_id:
                from_clause = "dataset"
                set_clause_id = "dataset_id = dataset.id,"
                null_clause = "session_id = NULL,"
                where_clause_id = "dataset.id = $session_or_dataset_id"
                session_or_dataset_id = rule.dataset_id
            else:
                # TODO: Handle updates where rule dataset_id/session_id isn't updated.
                raise HTTPException(
                    status_code=400,
                    detail="Must specify rule.dataset_id or rule.session_id.",
                )

            update = f"""
                UPDATE run_rules SET
                    {set_clause_id}
                    is_enabled = $is_enabled,
                    {null_clause}
                    filter = $filter,
                    trace_filter = $trace_filter,
                    tree_filter = $tree_filter,
                    sampling_rate = $sampling_rate,
                    add_to_annotation_queue_id = $add_to_annotation_queue_id,
                    add_to_dataset_id = $add_to_dataset_id,
                    evaluators = $evaluators,
                    alerts = $alerts,
                    display_name = $display_name,
                    webhooks = $webhooks,
                    add_to_dataset_prefer_correction = $add_to_dataset_prefer_correction,
                    updated_at = now(),
                    corrections_dataset_id = $corrections_dataset_id,
                    extend_only = $extend_only,
                    use_corrections_dataset = $use_corrections_dataset,
                    num_few_shot_examples = $num_few_shot_examples,
                    code_evaluators = $code_evaluators,
                    evaluator_version = COALESCE($evaluator_version, evaluator_version)
                FROM {from_clause}
                WHERE {from_clause}.tenant_id = $tenant_id
                AND run_rules.tenant_id = $tenant_id
                AND run_rules.id = $rule_id
                AND {where_clause_id}
                RETURNING *
            """

            sql_kwargs = {
                "tenant_id": auth.tenant_id,
                "rule_id": rule_id,
                "session_or_dataset_id": session_or_dataset_id,
                "is_enabled": rule.is_enabled,
                "filter": rule.filter,
                "trace_filter": rule.trace_filter,
                "tree_filter": rule.tree_filter,
                "sampling_rate": rule.sampling_rate,
                "add_to_annotation_queue_id": rule.add_to_annotation_queue_id,
                "add_to_dataset_id": rule.add_to_dataset_id,
                "evaluators": (
                    [
                        evaluator.model_dump(exclude_none=True, by_alias=True)
                        for evaluator in (rule.evaluators or [])
                    ]
                    if rule.session_id or llm_evaluator_update is None
                    else []
                ),
                "alerts": [alert.model_dump() for alert in rule.alerts or []],
                "display_name": rule.display_name,
                "webhooks": [webhook.model_dump() for webhook in rule.webhooks or []],
                "add_to_dataset_prefer_correction": rule.add_to_dataset_prefer_correction,
                "corrections_dataset_id": corrections_dataset_id,
                "extend_only": rule.extend_only,
                "use_corrections_dataset": rule.use_corrections_dataset,
                "num_few_shot_examples": rule.num_few_shot_examples,
                "code_evaluators": [
                    code_eval.model_dump(exclude_none=True, by_alias=True)
                    for code_eval in rule.code_evaluators or []
                ],
                "evaluator_version": rule.evaluator_version,
            }

            if rule.dataset_id and llm_evaluator_update is not None:
                update = f"""
                    WITH 
                    
                    llm_evaluator_update AS (
                        UPDATE llm_evaluators
                        SET
                            prompt_id = $prompt_id,
                            commit_hash_or_tag = $commit_hash_or_tag,
                            variable_mapping = $variable_mapping
                        FROM run_rules
                        WHERE llm_evaluators.evaluator_id = run_rules.evaluator_id
                        AND llm_evaluators.evaluator_id = $evaluator_id
                        AND run_rules.tenant_id = $tenant_id
                        RETURNING *
                    ),

                    rule_update AS (
                        {update}
                    )

                    SELECT 
                        rule_update.*, 
                        llm_evaluator_update.commit_hash_or_tag as evaluator_commit_hash_or_tag,
                        llm_evaluator_update.variable_mapping as evaluator_variable_mapping,
                        hub_repos.repo_handle as evaluator_prompt_handle
                    FROM rule_update
                    CROSS JOIN llm_evaluator_update
                    JOIN hub_repos ON llm_evaluator_update.prompt_id = hub_repos.id
                    WHERE hub_repos.tenant_id = $tenant_id
                """

                sql_kwargs.update(
                    {
                        "evaluator_id": llm_evaluator_update.evaluator_id,
                        "prompt_id": llm_evaluator_update.prompt_id,
                        "commit_hash_or_tag": llm_evaluator_update.commit_hash_or_tag,
                        "variable_mapping": llm_evaluator_update.variable_mapping,
                    }
                )

            sql = kwargs_to_pgpos(update, sql_kwargs)
            params = sql.args
            update = sql.sql
            row = await db.fetchrow(update, *params)
        except ForeignKeyViolationError:
            if rule.add_to_annotation_queue_id:
                raise HTTPException(
                    status_code=400, detail="Annotation queue not found"
                )
            elif rule.add_to_dataset_id:
                raise HTTPException(status_code=400, detail="Dataset not found")
            else:
                raise

    if not row:
        raise HTTPException(status_code=404, detail="Rule not found")

    return await validate_and_decrypt_rule(dict(row))


@retry_asyncpg
async def delete_rule(
    auth: AuthInfo,
    rule_id: str,
) -> None:
    async with asyncpg_pool() as db:
        row = await db.fetchrow(
            """delete from run_rules
            where tenant_id = $1 and id = $2
            returning id""",
            auth.tenant_id,
            rule_id,
        )

    if not row:
        raise HTTPException(status_code=404, detail="Rule not found")


@retry_asyncpg
async def trigger_rules_by_dataset(
    auth: AuthInfo,
    dataset_id: UUID,
    enforce_user_id: bool = True,
) -> None:
    async with asyncpg_pool() as db:
        row = await db.fetch(
            """
            select id from run_rules where tenant_id=$1 and dataset_id=$2 limit $3
            """,
            auth.tenant_id,
            dataset_id,
            settings.RUN_RULES_TRIGGER_LIMIT,
        )
        coroutines = [
            trigger_rule(auth, rule["id"], db, enforce_user_id=enforce_user_id)
            for rule in row
        ]
        semaphore = asyncio.Semaphore(settings.RULES_TRIGGER_SEMAPHORE)
        await asyncio.gather(*(gated_coro(coro, semaphore) for coro in coroutines))


@retry_asyncpg
async def trigger_rule(
    auth: AuthInfo,
    rule_id: UUID,
    db: asyncpg.Connection,
    enforce_user_id: bool = True,
) -> None:
    async with redis.async_queue(settings.RUN_RULES_QUEUE) as queue:
        row = await db.fetchrow(
            """
            with

            last_applied as (
                select rule_id, max(end_time) as last_applied_at
                from run_rules_applications
                where run_id = '00000000-0000-0000-0000-000000000000'
                and committed = true
                group by rule_id
            )

            select 
                run_rules.*, 
                last_applied_at,
                llm_evaluators.commit_hash_or_tag as evaluator_commit_hash_or_tag,
                llm_evaluators.variable_mapping as evaluator_variable_mapping,
                hub_repos.repo_handle as evaluator_prompt_handle
            from run_rules
            inner join tenants
                on tenants.id = run_rules.tenant_id
            inner join organizations
                on tenants.organization_id = organizations.id
            left join last_applied
                on last_applied.rule_id = run_rules.id
            left join llm_evaluators
                on llm_evaluators.evaluator_id = run_rules.evaluator_id
            left join hub_repos 
                on llm_evaluators.prompt_id = hub_repos.id
            where not tenants.is_deleted and not organizations.disabled and (
                run_rules.add_to_annotation_queue_id is not null
                or run_rules.add_to_dataset_id is not null
                or run_rules.evaluators is not null
                or run_rules.evaluator_id is not null
            )
            and run_rules.id = $1 and run_rules.tenant_id = $2
            """,
            rule_id,
            auth.tenant_id,
        )
        if not row:
            raise HTTPException(
                status_code=404,
                detail="Rule not found or workspace/org is deleted/disabled",
            )

        rule = dict(row)
        if rule["last_applied_at"]:
            start_time = rule["last_applied_at"]
        elif rule["backfill_from"]:
            start_time = rule["backfill_from"]
        else:
            start_time = rule["created_at"]

        await queue.enqueue(
            "apply_run_rule",
            start_time=start_time,
            end_time=datetime.datetime.now(datetime.timezone.utc),
            rule_dict=rule,
            **redis.DEFAULT_JOB_KWARGS,
        )


@retry_asyncpg
async def list_rules(
    auth: AuthInfo,
    dataset_id: UUID | None = None,
    session_id: UUID | None = None,
    type: Literal["session", "dataset"] | None = None,
    name_contains: str | None = None,
    id: list[UUID] | None = None,
) -> list[schemas.RunRulesSchema]:
    """List all run rules."""

    where_clause = ["run_rules.tenant_id = $1"]
    params: list[Any] = [auth.tenant_id]

    if dataset_id:
        where_clause.append("run_rules.dataset_id = $2")
        params.append(dataset_id)
    elif session_id:
        where_clause.append("run_rules.session_id = $2")
        params.append(session_id)
    elif name_contains:
        where_clause.append("run_rules.display_name ILIKE $2")
        params.append(f"%{name_contains}%")
    elif id:
        where_clause.append("run_rules.id = ANY($2)")
        params.append(id)
    if type == "session":
        where_clause.append("run_rules.session_id is not null")
    elif type == "dataset":
        where_clause.append("run_rules.dataset_id is not null")

    where_sql = " and ".join(where_clause)

    async with asyncpg_conn(exception_mapper=base_exception_mapper) as db:
        rows = await db.fetch(
            f"""
            select
                run_rules.*,
                tracer_session.name as session_name,
                dataset.name as dataset_name,
                add_dataset.name as add_to_dataset_name,
                annotation_queues.name as add_to_annotation_queue_name,
                evaluators.name as evaluator_name,
                llm_evaluators.commit_hash_or_tag as evaluator_commit_hash_or_tag,
                llm_evaluators.variable_mapping as evaluator_variable_mapping,
                hub_repos.repo_handle as evaluator_prompt_handle,
                llm_evaluators.annotation_queue_id as alignment_annotation_queue_id
            from run_rules
            left join tracer_session ON tracer_session.id = run_rules.session_id
            left join dataset on dataset.id = run_rules.dataset_id
            left join dataset as add_dataset ON add_dataset.id = run_rules.add_to_dataset_id
            left join annotation_queues ON annotation_queues.id = run_rules.add_to_annotation_queue_id
            left join evaluators ON evaluators.id = run_rules.evaluator_id
            left join llm_evaluators ON evaluators.id = llm_evaluators.evaluator_id
            left join hub_repos ON llm_evaluators.prompt_id = hub_repos.id
            where {where_sql}
            order by run_rules.updated_at desc""",
            *params,
        )

    return await asyncio.gather(*[validate_and_decrypt_rule({**row}) for row in rows])


async def get_last_application_for_rule(
    auth: AuthInfo,
    rule_id: UUID,
) -> schemas.RuleLogSchema:
    async with asyncpg_pool() as db:
        last_applied = await db.fetchrow(
            """
            WITH rule_application as (
                select *
                from run_rules_applications
                where rule_id = $1
                    AND application_time IS NOT NULL
                    AND committed = true
                ORDER BY application_time DESC
                LIMIT 1
            )
            SELECT rule_application.*
            FROM rule_application
                INNER JOIN run_rules ON run_rules.id = rule_application.rule_id
            WHERE run_rules.tenant_id = $2
            """,
            rule_id,
            auth.tenant_id,
        )

    if not last_applied:
        raise HTTPException(
            status_code=404, detail="No rule applications found not found"
        )

    return schemas.RuleLogSchema(**last_applied)


@retry_asyncpg
async def list_rule_logs(
    auth: AuthInfo,
    rule_id: str,
    limit: int,
    offset: int,
    start_time: datetime.datetime,
    end_time: datetime.datetime,
) -> tuple[list[schemas.RuleLogSchema], int]:
    async with asyncpg_pool() as db:
        query = """
            SELECT run_rules_applications.*
            FROM run_rules_applications
            INNER JOIN run_rules ON run_rules.id = run_rules_applications.rule_id
            WHERE run_rules.tenant_id = $1
            AND rule_id = $2
            AND committed = true
        """
        params = [auth.tenant_id, rule_id]

        idx = 3
        if start_time is not None:
            query += f" AND start_time >= ${str(idx)}"
            params.append(start_time)
            idx += 1
        if end_time is not None:
            query += f" AND start_time <= ${str(idx)}"
            params.append(end_time)
            idx += 1

        query += f"""
            ORDER BY start_time DESC, run_id ASC
            LIMIT ${str(idx)} OFFSET ${str(idx + 1)}
        """
        params.extend([limit + 1, offset])
        rows = await db.fetch(query, *params)

    run_ids = set(
        str(row["run_id"])
        for row in rows
        if row["run_id"] != "00000000-0000-0000-0000-000000000000"
    )

    runs_query = (
        await fetch_runs(
            auth,
            schemas.BodyParamsForRunSchema.model_construct(
                select=[
                    schemas.RunSelect.run_type,
                    schemas.RunSelect.session_id,
                ],
                id=run_ids,
            ),
        )
        if run_ids
        else {}
    )

    run_metadata = {run["id"]: run for run in runs_query.get("runs", [])}

    feedback_ids = [
        ((row["evaluators"] or {}).get("payload", {}) or {}).get("feedback_ids", None)
        for row in rows
        if row["run_id"] != "00000000-0000-0000-0000-000000000000" and row["evaluators"]
    ]

    feedback_ids = [id for ids in feedback_ids if ids for id in ids]
    if not feedback_ids:
        feedback_rows = []
    else:
        feedback_rows = await fetch_feedback(feedback_ids, auth)
    feedback_metadata = {str(feedback.id): feedback for feedback in feedback_rows}

    mutable_rows = [{**row} for row in rows]

    for row in mutable_rows:
        row_feedback_ids: list[str] | None = (
            (row["evaluators"] or {}).get("payload", {}) or {}
        ).get("feedback_ids", None)
        if row_feedback_ids:
            row["evaluators"]["payload"] |= {
                "feedback_metadata": {
                    id: feedback_metadata.get(id) for id in row_feedback_ids
                }
            }

    return [
        schemas.RuleLogSchema(
            **row,
            run_name=run_metadata.get(row["run_id"], {}).get("name"),
            run_type=run_metadata.get(row["run_id"], {}).get("run_type"),
            run_session_id=run_metadata.get(row["run_id"], {}).get("session_id"),
        )
        for row in mutable_rows
    ], len(rows)


async def send_rules_alert(
    run_rule: schemas.RunRulesSchema,
    alert_config: schemas.RunRulesAlertSchema,
    runs: list[dict],
) -> None:
    if alert_config.type == schemas.RunRulesAlertType.pagerduty:
        event_link = (
            "https://smith.langchain.com/"
            if not settings.LANGCHAIN_ENDPOINT
            or "api.smith.langchain.com" in settings.LANGCHAIN_ENDPOINT
            else settings.LANGCHAIN_ENDPOINT
        )
        async with ClientSession() as session:
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
            }
            response = await session.post(
                "https://events.pagerduty.com/v2/enqueue",
                headers=headers,
                json={
                    "payload": {
                        "summary": alert_config.summary or "Run rule alert triggered",
                        "timestamp": datetime.datetime.now().strftime(
                            "%Y-%m-%dT%H:%M:%S.%f+0000"
                        ),
                        "severity": alert_config.severity,
                        "source": f"Project: {run_rule.session_id}",
                        "component": f"Rule: {run_rule.id}",
                        "custom_details": {
                            "# of runs alerted": str(len(runs)),
                        },
                    },
                    "routing_key": alert_config.routing_key,
                    "event_action": "trigger",
                    "client": "LangSmith",
                    "client_url": event_link,
                },
            )
            logger.info(
                "Alert sent to pagerduty response={}".format(await response.text())
            )

    else:
        raise Exception("Unknown alert type")


def chat_to_structured(prompt, schema, **structured_output_kwargs):
    return StructuredPrompt(
        schema=schema,
        **prompt.to_json()["kwargs"],
        structured_output_kwargs=structured_output_kwargs,
    )


async def get_chain_from_evaluator(
    evaluator: schemas.EvaluatorTopLevel,
    auth: AuthInfo,
    db: asyncpg.Connection,
    rule_name: str | None = None,
    evaluator_version: int | None = None,
) -> tuple[dict, Optional[dict]]:
    manifest = None
    # load prompt, build chain
    if evaluator.structured.prompt:
        chain = _v2_eval_to_chain_manifest(evaluator.structured, rule_name)
    elif evaluator.structured.hub_ref:
        owner, repo, commit = parse_owner_repo_commit(evaluator.structured.hub_ref)
        if commit == "latest":
            commit = None
        manifest_response = await get_commit_manifest(
            db,
            auth,
            owner,
            repo,
            commit,
            get_examples=False,
            is_view=False,
            include_model=((evaluator_version or 0) >= 3) or None,
        )
        manifest = manifest_response.manifest
        if is_runnable_sequence(manifest) or is_prompt_playground(manifest):
            chain = manifest
        else:
            chain = {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain", "schema", "runnable", "RunnableSequence"],
                "kwargs": {"name": rule_name, "first": manifest},
            }
    else:
        raise HTTPException(status_code=400, detail="Invalid evaluator")

    prompt_schema = get_first_path(
        chain,
        ["kwargs", "first", "kwargs", "schema"],
        ["kwargs", "first", "kwargs", "schema_"],
    )
    if isinstance(prompt_schema, dict):
        # Some model providers don't support spaces in schema title/name.
        if "title" in prompt_schema:
            prompt_schema["title"] = prompt_schema["title"].replace(" ", "_")
        if "name" in prompt_schema:
            prompt_schema["name"] = prompt_schema["name"].replace(" ", "_")
        # Some model providers require a title/name and description.
        if "title" not in prompt_schema and "name" not in prompt_schema:
            prompt_schema["title"] = "extract"
        prompt_schema.setdefault("description", "")

    if "last" not in chain["kwargs"]:
        chain["kwargs"]["last"] = evaluator.structured.model

    return chain, manifest


async def validate_evaluator(
    evaluator: schemas.EvaluatorTopLevel,
    auth: AuthInfo,
    db: asyncpg.Connection,
    secrets: dict[str, str] | None = None,
    evaluator_version: int | None = None,
) -> None:
    chain, _ = await get_chain_from_evaluator(
        evaluator, auth, db, evaluator_version=evaluator_version
    )

    body = {
        "manifest": chain,
        "secrets": secrets,
        "options": {},
    }
    try:
        async with get_service_client(
            ServiceName.PLAYGROUND,
        ) as playground_client:
            response = await playground_client.post(
                "/internal/playground/validate",
                json=body,
            )
        response.raise_for_status()
        return response.json()
    except HTTPStatusError as e:
        logger.error(f"Failed to validate evaluator: {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail="Evaluator failed validation",
        )


async def batch_invoke_evaluator(
    chain: dict,
    auth: AuthInfo,
    inputs: List[Input],
    configs: List[RunnableConfig],
    secrets: dict[str, str] | None = None,
) -> List[dict]:
    for config in configs:
        config["run_id"] = str(config["run_id"])
    body = {
        "manifest": chain,
        "secrets": secrets,
        "options": configs,
        "input": inputs,
        "project_name": "evaluators",
    }

    # Call batch in playground service
    try:
        async with get_internal_tenant_scoped_service_client(
            ServiceName.PLAYGROUND, auth.tenant_id
        ) as playground_client:
            response = await playground_client.post(
                "/playground/batch", json=body, headers=auth.to_headers()
            )
        response.raise_for_status()
        return response.json()
    except HTTPStatusError as e:
        logger.error(f"Failed to batch invoke evaluator: {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Evaluator failed to batch invoke {e.response.text}",
        )


async def _create_evaluator_hub_repo(
    auth: AuthInfo,
    rule_request: schemas.RunRulesSchema,
    structured_evaluator: schemas.EvaluatorStructuredOutput,
    db: asyncpg.Connection,
) -> hub_schema.RepoWithLookups:
    if not (
        structured_evaluator.prompt
        and structured_evaluator.schema
        and structured_evaluator.model
    ):
        raise ValueError(
            "Evaluator must specify 'prompt', 'model', and 'schema'/'schema'."
        )
    repo_id = uuid4()
    if rule_request.dataset_id:
        parent_name = await _fetch_dataset_name(auth, rule_request.dataset_id, db)
    elif rule_request.session_id:
        parent_name = await _fetch_session_name(auth, rule_request.session_id, db)
    else:
        parent_name = ""
    repo_name = _format_for_prompt_name(
        f"eval_{parent_name}_{rule_request.display_name}_{str(repo_id)[:8]}"
    )
    description = f"Prompt created for evaluator '{rule_request.display_name}'"
    if rule_request.dataset_id:
        dataset_name = await _fetch_dataset_name(auth, rule_request.dataset_id, db)
        description += f" against dataset '{dataset_name}'"
    repo_req = hub_schema.CreateRepoRequest(
        repo_handle=repo_name, is_public=False, description=description
    )
    repo = await repos.create_repo(db, auth, repo_req, repo_id=repo_id)
    # Create the first commit
    commit_req = commits.CreateCommitRequest(
        repo=repo_name,
        manifest=_v2_eval_to_chain_manifest(
            structured_evaluator, rule_request.display_name
        ),
        example_run_ids=[],
    )
    await commits.create_commit(db, auth, commit_req)
    return repo


def _v2_eval_to_chain_manifest(
    structured_evaluator: schemas.EvaluatorStructuredOutput, name: str | None
) -> dict:
    prompt = dumpd(
        StructuredPrompt(
            messages=structured_evaluator.prompt,
            template_format=structured_evaluator.template_format or "mustache",
            schema=structured_evaluator.schema,
        )
    )
    return {
        "lc": 1,
        "type": "constructor",
        "id": ["langchain", "schema", "runnable", "RunnableSequence"],
        "kwargs": {"name": name, "first": prompt, "last": structured_evaluator.model},
    }


def get_non_reasoning_properties(properties: dict) -> list[str]:
    return [p for p in list(properties.keys()) if p != "comment"]


def _get_single_feedback_key(properties: dict) -> str | None:
    feedback_keys = get_non_reasoning_properties(properties)
    if len(feedback_keys) == 0:
        raise HTTPException(
            status_code=422,
            detail="Must have at least one feedback key not named 'comment' in your evaluator output schema.",
        )

    return feedback_keys[0] if len(feedback_keys) == 1 else None


def _get_schema_from_hub_manifest(
    manifest: dict, *, as_json_schema: bool = True
) -> dict | None:
    schema = get_first_path(
        manifest,
        ["kwargs", "schema"],
        ["kwargs", "schema_"],
        ["kwargs", "first", "kwargs", "schema"],
        ["kwargs", "first", "kwargs", "schema_"],
    )
    if as_json_schema and schema:
        return convert_to_json_schema(schema)
    else:
        return schema


def _eval_resource_name(
    eval_name: str, eval_id: UUID | str, parent_name: str | None = None
) -> str:
    eval_id = str(eval_id)[:8]
    if parent_name:
        return f"Evaluator: {parent_name}:{eval_name} ({eval_id})"
    else:
        return f"Evaluator: {eval_name} ({eval_id})"


async def _fetch_session_name(
    auth: AuthInfo, session_id: str | UUID, db: asyncpg.Connection
) -> str:
    return await db.fetchval(
        """
        select name
        from tracer_session
        where tenant_id = $1 and id = $2""",
        auth.tenant_id,
        session_id,
    )


async def _fetch_dataset_name(
    auth: AuthInfo, dataset_id: str | UUID, db: asyncpg.Connection
) -> str:
    return await db.fetchval(
        """
        select name
        from dataset
        where id = $1 and tenant_id = $2
        """,
        dataset_id,
        auth.tenant_id,
    )


def _format_for_prompt_name(x: str) -> str:
    return x.lower().replace(" ", "_").replace("-", "_")
