"""Utilities for calculating token usage for a run."""

import json
import logging
import textwrap
from functools import lru_cache
from typing import List
from uuid import UUID

from ddtrace import tracer
from lc_logging.trace import wrap
from opentelemetry import trace as otel_tracer
from pydantic import ValidationError
from pydantic.dataclasses import dataclass
from tiktoken import Encoding

from app import config, schemas

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class TokenUsage:
    prompt_tokens: int | None = None
    completion_tokens: int | None = None
    total_tokens: int | None = None
    prompt_token_details: dict | None = None
    completion_token_details: dict | None = None
    prompt_cost: float | None = None
    completion_cost: float | None = None
    total_cost: float | None = None
    prompt_cost_details: dict | None = None
    completion_cost_details: dict | None = None


# List of OpenAI models:
# This pre-fetches them to cache expensive token counting operations
# and also allows us to quickly check if a model is an OpenAI model.
OPENAI_MODELS = {
    "gpt-4o",
    "gpt-4o-mini",
    "gpt-4",
    "gpt-4-0314",
    "gpt-4-0613",
    "gpt-4-32k",
    "gpt-4-32k-0314",
    "gpt-4-32k-0613",
    "gpt-4-vision-preview",
    "gpt-3.5-turbo",
    "gpt-3.5-turbo-0301",
    "gpt-3.5-turbo-0613",
    "text-ada-001",
    "ada",
    "text-babbage-001",
    "babbage",
    "text-curie-001",
    "curie",
    "davinci",
    "text-davinci-003",
    "text-davinci-002",
    "code-davinci-002",
    "code-davinci-001",
    "code-cushman-002",
    "code-cushman-001",
}

SPECIAL_TOKENS_MAP = {
    "openai": {
        "<|im_start|>": 100264,
        "<|im_end|>": 100265,
        "<|im_sep|>": 100266,
    }
}


def _is_openai_description_object(x):
    return (
        isinstance(x, dict)
        and x is not None
        and "description" in x
        and bool(x["description"])
    )


def _get_openai_functions_prompt(functions: list[dict], function_call=None):
    def _is_iterable(obj):
        try:
            iter(obj)
            return True
        except TypeError:
            return False

    def fn_str(fn: dict) -> str:
        definitions = fn.get("parameters", {}).get("definitions", {})

        def resolve_ref(schema: dict):
            if "$ref" in schema and isinstance(schema["$ref"], str):
                ref = schema["$ref"][len("#/definitions/") :]
                return definitions.get(ref, None)
            return schema

        def schema_str(schema: dict, level=0) -> str:
            schema = resolve_ref(schema) or {}
            if isinstance(schema, str):
                return schema
            if schema.get("type") == "object":
                if schema.get("additionalProperties", False):
                    return "object"

                prefix = "  " * level
                properties = []
                for key, value in schema.get("properties", {}).items():
                    key_sep = ":" if key in schema.get("required", []) else "?:"
                    decl = f"{key}{key_sep} {schema_str(value, level + 1)}"

                    if level == 0 and _is_openai_description_object(value):
                        desc = ""
                        for line in (
                            textwrap.dedent(value["description"]).strip().split("\n")
                        ):
                            if line.strip() != "":
                                desc += f"// {line.strip()}\n"

                        properties.append(f"{desc}{decl},")
                    else:
                        properties.append(f"{prefix}{decl},")
                return "{\n" + "\n".join(properties) + "\n" + "}"

            if schema.get("enum") is not None and schema.get("type") is not None:
                return " | ".join(json.dumps(val) for val in schema["enum"])
            if schema.get("type") == "array" and schema.get("items") is not None:
                return f"{schema_str(schema['items'], level + 1)}[]"
            if schema.get("anyOf") is not None and _is_iterable(schema["anyOf"]):
                return " | ".join(schema_str(item, level) for item in schema["anyOf"])
            if schema.get("oneOf") is not None and _is_iterable(schema["oneOf"]):
                return " | ".join(schema_str(item, level) for item in schema["oneOf"])
            if schema.get("allOf") is not None and _is_iterable(schema["allOf"]):
                return " & ".join(schema_str(item, level) for item in schema["allOf"])
            schema_type = schema.get("type")
            if isinstance(schema_type, list) and all(
                isinstance(x, str) for x in schema_type
            ):
                return " | ".join(schema_type)
            elif isinstance(schema_type, str):
                return schema_type
            else:
                return "unknown"

        output = ""
        description = fn.get("description", None)
        if description and isinstance(description, str):
            for line in textwrap.dedent(description).strip().split("\n"):
                if line.strip() != "":
                    output += f"// {line}\n"

        if fn.get("name") and fn.get("parameters"):
            output += f"type {fn['name']} = (_: {schema_str(fn['parameters'])}) => any;"
        return output

    fn_call_str = (
        ""
        if function_call is None or function_call == "auto"
        else function_call
        if isinstance(function_call, str)
        else json.dumps(function_call)
    )

    fns = "\n".join(fn_str(fn) for fn in functions)
    return f"""You are given the following tools to answer if deemed necessary {fn_call_str}:\nnamespace functions {{\n{fns}\n}}"""


def _calculate_prompt_tokens_default(
    model_name: str | None, text: str, special_tokens_key: str | None = None
) -> int:
    # tokenize the text using the GPT-2 tokenizer
    import tiktoken

    enc = tiktoken.get_encoding(config.settings.DEFAULT_TOKENIZER)
    return len(enc.encode(text, allowed_special="all"))


def _calculate_message_tokens_default(
    model_name: str | None,
    messages: List[schemas.ChatMessage | schemas.SerializedChatMessage],
    invocation_params: dict | None = None,
) -> int:
    message_str = ""
    for message in messages:
        if isinstance(message, schemas.ChatMessage):
            message_str += f"{message.type}: {message.data.content}"
        else:
            message_str += f"{message.id[-1].replace('Message', '').lower()}: {message.kwargs.content}"
    return _calculate_prompt_tokens_default(model_name, message_str)


def _calculate_function_tokens_openai(
    model_name: str | None,
    invocation_params: dict | None = None,
) -> int:
    if not invocation_params or not invocation_params.get("functions"):
        return 0

    functions = invocation_params.get("functions") or []
    if (
        not (
            isinstance(functions, list)
            and all(isinstance(item, dict) for item in functions)
        )
        or len(functions) == 0
    ):
        return 0

    return _calculate_prompt_tokens_openai(
        model_name,
        _get_openai_functions_prompt(
            functions,
            invocation_params.get("function_call", None),
        ),
    )


def _calculate_message_tokens_openai(
    model_name: str | None,
    messages: List[schemas.ChatMessage | schemas.SerializedChatMessage],
    invocation_params: dict | None = None,
) -> int:
    openai_messages: list[dict] = []
    for message in messages:
        message_type = (
            message.type
            if isinstance(message, schemas.ChatMessage)
            # TODO: maybe we should deserialise message instead of relying on ID
            else message.id[-1].replace("Message", "").lower()
        )

        message_data = (
            message.data if isinstance(message, schemas.ChatMessage) else message.kwargs
        )

        # convert to openai dict
        if message_type == "chat":
            openai_messages.append(
                {"role": message_data.role, "content": message_data.content}
            )
        elif message_type == "human":
            openai_messages.append({"role": "user", "content": message_data.content})
        elif message_type == "ai":
            openai_messages.append(
                {"role": "assistant", "content": message_data.content}
            )
        elif message_type == "system":
            openai_messages.append({"role": "system", "content": message_data.content})
        elif message_type == "function":
            openai_messages.append(
                {
                    "role": "function",
                    "content": message_data.content,
                    "name": message_data.name,
                }
            )

    # serialise to ChatML
    is_old_gpt_3 = (
        model_name.startswith("gpt-3.5-turbo") and "0301" in model_name
        if model_name
        else False
    )
    msg_sep = "\n" if is_old_gpt_3 else ""
    role_sep = "\n" if is_old_gpt_3 else "<|im_sep|>"

    message_str = msg_sep.join(
        [
            f"<|im_start|>{msg.get('name') or msg.get('role')}{role_sep}{msg.get('content')}<|im_end|>"
            for msg in openai_messages
        ]
        + [f"<|im_start|>assistant{role_sep}"]
    )

    prompt_token_count = _calculate_prompt_tokens_openai(
        model_name, message_str, "openai"
    )
    function_token_count = _calculate_function_tokens_openai(
        model_name, invocation_params
    )
    return prompt_token_count + function_token_count


@lru_cache(maxsize=50)
def get_tiktoken_encoding(model_name, special_tokens_key=None) -> Encoding:
    import tiktoken

    enc = tiktoken.encoding_for_model(model_name)
    if special_tokens_key:
        enc = tiktoken.Encoding(
            name=enc.name,
            pat_str=enc._pat_str,
            mergeable_ranks=enc._mergeable_ranks,
            special_tokens={
                **enc._special_tokens,
                **SPECIAL_TOKENS_MAP[special_tokens_key],
            },
        )
    return enc


def _calculate_prompt_tokens_openai(
    model_name: str | None, text: str, special_tokens_key: str | None = None
) -> int:
    if model_name is None:
        return 0

    enc = get_tiktoken_encoding(model_name, special_tokens_key)
    return len(enc.encode(text, allowed_special="all"))


def _parse_model_input(
    input_dict: dict | None,
) -> (
    schemas.ChatInput
    | schemas.ChatPluralInput
    | schemas.InstructInput
    | schemas.InstructPluralInput
    | None
):
    try:
        return schemas.InstructInput.model_validate(input_dict)
    except ValidationError:
        try:
            return schemas.ChatInput.model_validate(input_dict)
        except ValidationError:
            try:
                return schemas.InstructPluralInput.model_validate(input_dict)
            except ValidationError:
                try:
                    return schemas.ChatPluralInput.model_validate(input_dict)
                except ValidationError:
                    return None


def _parse_model_output(
    output_dict: dict | None,
) -> schemas.ChatOutput | schemas.InstructOutput | None:
    if (
        output_dict is not None
        and isinstance(output_dict.get("generations"), list)
        and len(output_dict["generations"]) == 1
        and isinstance(output_dict["generations"][0], list)
    ):
        output_dict["generations"] = output_dict["generations"][0]
    try:
        return schemas.InstructOutput.model_validate(output_dict)
    except ValidationError:
        try:
            return schemas.ChatOutput.model_validate(output_dict)
        except ValidationError:
            return None


def _is_openai_model(model_name: str | None) -> bool:
    if model_name is None:
        return False
    if model_name in OPENAI_MODELS:
        return True
    try:
        import tiktoken

        tiktoken.encoding_for_model(model_name)
        return True
    except (ValueError, KeyError):
        return False


@wrap(name="calculate_token_count", service="token_counts")
def calculate_token_count(
    tenant_id: UUID,
    inputs: dict | None,
    outputs: dict | None,
    model_name: str | None,
    invocation_params: dict | None,
) -> TokenUsage:
    if (
        config.settings.DATADOG_ENABLED
        and model_name
        and (span := tracer.current_span())
    ):
        span.set_tag_str("model", str(model_name))
        span.set_tag_str("tenant_id", str(tenant_id))
    elif (
        config.settings.OTEL_TRACING_ENABLED
        and model_name
        and (span := otel_tracer.get_current_span())
    ):
        span.set_attribute("model", str(model_name))

    # None is fine for model_name, but it should be a string if it's not None
    if model_name is not None and not isinstance(model_name, str):
        return TokenUsage(0, 0)  # type: ignore[call-arg]

    is_openai = _is_openai_model(model_name)
    prompt_token_fn = (
        _calculate_prompt_tokens_openai
        if is_openai
        else _calculate_prompt_tokens_default
    )
    message_token_fn = (
        _calculate_message_tokens_openai
        if is_openai
        else _calculate_message_tokens_default
    )
    prompt_tokens = 0
    parsed_input = _parse_model_input(inputs)
    if parsed_input is not None:
        if isinstance(parsed_input, schemas.ChatInput):
            prompt_tokens = message_token_fn(
                model_name, parsed_input.messages, invocation_params
            )
        elif isinstance(parsed_input, schemas.ChatPluralInput):
            prompt_tokens = message_token_fn(
                model_name, parsed_input.messages[0], invocation_params
            )
        elif isinstance(parsed_input, schemas.InstructInput):
            prompt_tokens = prompt_token_fn(model_name, parsed_input.prompt)
        elif isinstance(parsed_input, schemas.InstructPluralInput):
            prompt_tokens = prompt_token_fn(model_name, parsed_input.prompts[0])
    completion_tokens = 0
    parsed_output = _parse_model_output(outputs)
    if parsed_output is not None:
        if isinstance(parsed_output, schemas.ChatOutput):
            completion_tokens = message_token_fn(model_name, parsed_output.generations)  # type: ignore[arg-type]
        elif isinstance(parsed_output, schemas.InstructOutput):
            # handle function calls in instruct output
            for gen in parsed_output.generations:
                if gen.message is None:
                    continue

                message_data = (
                    gen.message.data
                    if isinstance(gen.message, schemas.ChatMessage)
                    else gen.message.kwargs
                )

                function_call = (message_data.additional_kwargs or {}).get(
                    "function_call"
                )

                if function_call:
                    completion_tokens += 5
                    completion_tokens += prompt_token_fn(
                        model_name, function_call.get("name")
                    )
                    completion_tokens += prompt_token_fn(
                        model_name, function_call.get("arguments")
                    )

            completion_tokens += prompt_token_fn(
                model_name, "".join(gen.text for gen in parsed_output.generations)
            )
    return TokenUsage(prompt_tokens, completion_tokens)  # type: ignore[call-arg]
