from collections import deque
from typing import Dict, List, Tuple, Union

import orjson
import structlog
from lc_config.settings import shared_settings as settings
from lc_logging.trace import wrap

logger = structlog.getLogger(__name__)


def _flatten_json(
    results: set[tuple[str, str]],
    json_obj: Union[Dict, List],
):
    """
    Flatten a nested JSON object into a list of key-value pairs using breadth-first search.

    Args:
        json_obj (Union[Dict, List]): The JSON object to flatten.
        prefix (str): The prefix to use for nested keys.

    Returns:
        List[Tuple[str, str]]: A list of flattened key-value pairs.
    """
    queue = deque([(json_obj, "")])

    while queue and len(results) < settings.KV_SEARCH_MAX_KEYS:
        current_obj, current_prefix = queue.popleft()

        if isinstance(current_obj, dict):
            for key, value in current_obj.items():
                new_prefix = f"{current_prefix}.{key}" if current_prefix else key
                if isinstance(value, (dict, list)):
                    queue.append((value, new_prefix))
                elif new_prefix:
                    _append_result(results, new_prefix, value)

        elif isinstance(current_obj, list):
            if all(item and isinstance(item, (int, float)) for item in current_obj):
                continue  # ignore all numeric values, such as embedding vectors

            has_dict_list = any(isinstance(item, dict) for item in current_obj)
            for i, item in enumerate(current_obj):
                if not has_dict_list and i >= settings.KV_SEARCH_MAX_ARRAY_INDEXED:
                    break
                if isinstance(item, (dict, list)):
                    queue.append((item, current_prefix))
                elif current_prefix:
                    _append_result(results, current_prefix, item)

        elif current_prefix:
            _append_result(results, current_prefix, current_obj)


def _append_result(
    results: set[Tuple[str, str]], key: str, value: Union[str, int, float, bool]
) -> None:
    if (
        len(results) < settings.KV_SEARCH_MAX_KEYS
        and key is not None
        and _should_index(value)
    ):
        if isinstance(value, str):
            value = value.lower()
        results.add((str(key).lower(), orjson.dumps(value).decode("utf-8")))


def _should_index(value: Union[str, int, float, bool]) -> bool:
    return value is not None and (
        not isinstance(value, str)
        or (
            len(value) > 0
            and len(value) <= settings.KV_SEARCH_MAX_LENGTH
            and not value.startswith("data:image/")
        )
    )


@wrap(name="tokenize_runs", service="tokenize_kv")
def break_out_nested_kv_pairs(
    json_obj: Dict,
) -> List[tuple[str, str]]:
    """
    Break out nested key-value pairs from a JSON object.

    Args:
        json_obj (Dict): The JSON object to process.

    Returns:
        List[tuple[str, str]]: A list of flattened key-value pairs.
    """
    try:
        results: set[tuple[str, str]] = set([])
        _flatten_json(results, json_obj)
        return list(results)
    except Exception as e:
        logger.error("Error breaking out input/output KV pairs", exc_info=e)
        return []
