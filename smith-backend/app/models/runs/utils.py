import re
from datetime import datetime
from typing import Any
from uuid import UUID, uuid4

import orjson
import structlog
from lc_database.queue.serializer import ORJSONSerializer
from pydantic import ValidationError

from app import schemas
from app.models.feedback.ingest import FeedbackInsert


def extract_code_evaluator_feedback_keys(
    python_code: str, evaluator_name: str | None = None
) -> list:
    # Find the start of the perform_eval function
    function_start = python_code.find("def perform_eval")
    if function_start == -1:
        return []

    # Find the next function definition or the end of the string
    next_function_start = python_code.find("def ", function_start + 1)
    function_end = (
        len(python_code) if next_function_start == -1 else next_function_start
    )

    # Extract the function content
    function_content = python_code[function_start:function_end]

    # Remove comments first (both single-line and inline comments)
    without_comments = re.sub(r"#.*$", "", function_content, flags=re.MULTILINE)

    # Check for dict() constructor pattern
    return_dict_match = re.search(r"return\s+dict\s*\(([^)]*)\)", without_comments)
    if return_dict_match:
        dict_args = return_dict_match.group(1)
        # Extract keys from kwargs format: key1=val1, key2=val2
        dict_keys = [
            param.split("=")[0].strip()
            for param in dict_args.split(",")
            if "=" in param
        ]
        if dict_keys:
            return dict_keys

    # Find the last dictionary-like structure
    dict_matches = list(
        re.finditer(r"{\s*(['\"])((?:(?!\1)[^:])*)\1(?:\s*#[^\n]*)?:", without_comments)
    )

    if len(dict_matches) == 0:
        return [evaluator_name] if evaluator_name else []

    last_dict = dict_matches[-1]
    dict_start = last_dict.start()
    dict_text_slice = without_comments[dict_start:]

    # Extract all keys from the last dictionary
    keys = []
    key_regex = r"(['\"])((?:(?!\1)[^:])*)\1(?:\s*#[^\n]*)?:"
    for match in re.finditer(key_regex, without_comments[dict_start:]):
        keys.append(match.group(2))

    # Case 1: {"key": "foo", "score": 1, "comment": "bar"}
    if "key" in keys and ("score" in keys or "value" in keys):
        # Extract the value of the "key" field
        key_value_match = re.search(
            r'["\']key["\']\s*:\s*["\']([^"\']+)["\']', dict_text_slice
        )
        if key_value_match:
            return [key_value_match.group(1)]

    # Case 2: {"score": 1, "comment": "foo"} or {"value": 1, "comment": "foo"}
    if ("score" in keys or "value" in keys) and "key" not in keys:
        return [evaluator_name] if evaluator_name else []

    # Default case: return all keys
    return keys


logger = structlog.getLogger(__name__)


def verify_rule_valid_for_corrections_and_get_feedback_key(
    rule: schemas.RunRulesCreateSchema,
) -> str | None:
    if (
        rule.evaluators is None
        or len(rule.evaluators) != 1
        or rule.evaluators[0].structured.tool_schema is None
    ):
        return None
    tool_keys = [
        key
        for key in list(
            rule.evaluators[0].structured.tool_schema.get("properties", {}).keys()
        )
        if key != "comment"
    ]
    if len(tool_keys) != 1:
        return None
    return tool_keys[0]


def convert_to_string_if_present(value: Any) -> Any:
    if value is None:
        return value
    return str(value)


def process_feedback_value_stats(
    feedback: dict[str, Any],
    max_values: int | None = None,
    bucketed_stats: dict[datetime, Any] | None = None,
) -> list[dict[str, Any]]:
    feedback_values: dict[str, Any] = {}
    total_feedback_values: dict[str, Any] = {}
    for combined_key, value_count in zip(
        feedback.get("feedback_value_keys", []) or [],
        feedback.get("feedback_value_counts", []) or [],
    ):
        key, value = combined_key.split("|~|")
        try:
            value = orjson.loads(value)
        except orjson.JSONDecodeError:
            pass
        if not isinstance(value, str):
            continue
        if key not in feedback_values:
            feedback_values[key] = {}
        if value not in feedback_values[key]:
            feedback_values[key][value] = 0
        feedback_values[key][value] += value_count
    if bucketed_stats is not None and max_values is not None:
        for single_bucket in bucketed_stats.values():
            for combined_key, value_count in zip(
                single_bucket.get("feedback_value_keys", []) or [],
                single_bucket.get("feedback_value_counts", []) or [],
            ):
                key, value = combined_key.split("|~|")
                try:
                    value = orjson.loads(value)
                except orjson.JSONDecodeError:
                    pass
                if not isinstance(value, str):
                    continue
                if key not in total_feedback_values:
                    total_feedback_values[key] = {}
                if value not in total_feedback_values[key]:
                    total_feedback_values[key][value] = 0
                total_feedback_values[key][value] += value_count

    if max_values is not None:
        # prune the feedback values to only include the top max_values
        if bucketed_stats is not None:
            # take the top n across all buckets (total_feedback_values) - we want to take the same values in each bucket, so they should be
            # the same across all buckets
            for key in total_feedback_values:
                total_feedback_values[key] = dict(
                    sorted(
                        total_feedback_values[key].items(),
                        key=lambda x: x[1],
                        reverse=True,
                    )[:max_values]
                )
            for key in feedback_values:
                feedback_values[key] = {
                    k: v
                    for k, v in feedback_values[key].items()
                    if k in total_feedback_values[key]
                }
        else:
            for key in feedback_values:
                feedback_values[key] = dict(
                    sorted(
                        feedback_values[key].items(), key=lambda x: x[1], reverse=True
                    )[:max_values]
                )
    feedback_values_sorted = [
        feedback_values.get(key, {}) for key in feedback.get("feedback_keys", []) or []
    ]
    return feedback_values_sorted


async def parse_feedback(
    obj: Any,
    run: dict[str, Any],
    rule_id: UUID,
    run_id: str,
    feedback_inserts: list[FeedbackInsert],
    evaluator_feedbacks_ids: dict[str, list[UUID]],
    depth=0,
    error: str | None = None,
) -> None:
    """Log a warning if the feedback fails validation."""
    if obj is None or depth > 3:
        return
    comment = None
    if len(obj) in [2, 3] and "comment" in obj:
        comment = obj["comment"]
        del obj["comment"]
    if len(obj) == 2 and "key" in obj and ("score" in obj or "value" in obj):
        key_to_use = obj["key"]
        del obj["key"]
        remaining_key = next(iter(obj))
        obj = {key_to_use: obj[remaining_key]}
    for key, score_or_value in obj.items():
        if isinstance(score_or_value, dict):
            await parse_feedback(
                score_or_value,
                run,
                rule_id,
                run_id,
                feedback_inserts,
                evaluator_feedbacks_ids,
                depth + 1,
                error=error,
            )
        elif isinstance(score_or_value, list):
            for obj in score_or_value:
                if isinstance(obj, dict) or isinstance(obj, list):
                    await parse_feedback(
                        obj,
                        run,
                        rule_id,
                        run_id,
                        feedback_inserts,
                        evaluator_feedbacks_ids,
                        depth + 1,
                        error=error,
                    )
                else:
                    await parse_feedback(
                        {key: obj},
                        run,
                        rule_id,
                        run_id,
                        feedback_inserts,
                        evaluator_feedbacks_ids,
                        depth + 1,
                        error=error,
                    )
        else:
            try:
                payload = orjson.loads(
                    ORJSONSerializer.dumps(
                        schemas.FeedbackCreateSchemaInternal(
                            id=uuid4(),
                            run_id=run["id"],
                            key=key,
                            score=(
                                score_or_value
                                if not isinstance(score_or_value, str) and not error
                                else None
                            ),
                            value=(
                                score_or_value
                                if isinstance(score_or_value, str) and not error
                                else None
                            ),
                            feedback_source=schemas.AutoEvalFeedbackSource(
                                metadata={
                                    "rule_id": rule_id,
                                    "__run": {"run_id": str(run_id)},
                                }
                            ),
                            comment=comment if comment is not None else error,
                            error=error is not None,
                        )
                    )
                )
                feedback_inserts.append(
                    FeedbackInsert(
                        payload=payload,
                        trace_id=run["trace_id"],
                        session_id=run["session_id"],
                        start_time=run["start_time"],
                        redis=None,
                    )
                )
                evaluator_feedbacks_ids[str(run["id"])].append(payload["id"])
            except ValidationError as e:
                await logger.awarn(
                    "Evaluator produced invalid feedback for rule %s. Error: %s",
                    rule_id,
                    e,
                    exc_info=True,
                )


def merge_run_infos(first_run_infos):
    merged_first_run_infos = []

    # Merge every 3 dictionaries into one
    for i in range(0, len(first_run_infos), 3):
        merged_dict = {}
        for dict_item in first_run_infos[i : i + 3]:
            # Strip keys and values for each dictionary
            cleaned_dict = {
                k.strip("'"): v.strip("'") if isinstance(v, str) else v
                for k, v in dict_item.items()
            }
            merged_dict.update(cleaned_dict)
        merged_first_run_infos.append(merged_dict)

    return merged_first_run_infos


def get_run_thread_id(run: dict[str, Any]) -> str | None:
    if run["extra"].get("metadata"):
        return convert_to_string_if_present(
            run["extra"]["metadata"].get("session_id")
            or run["extra"]["metadata"].get("conversation_id")
            or run["extra"]["metadata"].get("thread_id")
        )
    return None


graph_interrupt_regex = re.compile(
    r"^GraphInterrupt(?:\(.*Interrupt.*\)|:\s*\[.*\])",
    re.MULTILINE | re.DOTALL | re.IGNORECASE,
)


def is_graph_interrupt(error: str | None) -> bool:
    if not error:
        return False
    return bool(graph_interrupt_regex.search(error))


def get_run_status(run: dict) -> str:
    if run["error"]:
        if is_graph_interrupt(run["error"]):
            return "interrupted"
        return "error"
    elif run["end_time"]:
        return "success"
    return "pending"
