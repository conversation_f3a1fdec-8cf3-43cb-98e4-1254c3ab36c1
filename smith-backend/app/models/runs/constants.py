LANGCHAIN_GENERIC_RUN_NAMES = [
    # LCEL
    "RunnableAssign",
    "RunnableBinding",
    "RunnableBranch",
    "RunnableEach",
    "RunnableLambda",
    "RunnableMap",
    "RunnablePassthrough",
    "RunnablePick",
    "RunnableRetry",
    "RunnableSequence",
    "RunnableWithFallbacks",
    # LangGraph
    "LangGraph",
    "__start__",
    "_write",
    "ChannelWrite",
    "ChannelWrite<...>",
    "ChannelRead",
    "ChannelRead<...>",
    "ChannelInvoke",
    "ChannelInvoke<...>",
    "ChannelBatch",
    "ChannelBatch<...>",
    "Pregel",
]
