import asyncio
import copy
import csv
import json
import math
import random
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from io import String<PERSON>
from time import perf_counter
from typing import Any, Dict, List, Literal, Optional, Tuple, Union, cast
from uuid import UUID

import orjson
import structlog
import zstandard
from aiochclient import Record
from fastapi import HTT<PERSON>Exception
from fastapi.responses import StreamingResponse
from lc_config.settings import shared_settings as settings
from lc_config.utils import arun_in_executor
from lc_database import database
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.database import asyncpg_pool
from lc_database.redis import aredis_caching_pool
from lc_database.s3_client import get_run_data, get_run_manifest, get_signed_url_go
from prometheus_client import Counter, Histogram
from pydantic import BaseModel
from typing_extensions import TypedDict

import app.models.examples as examples_module
from app import config, schemas
from app.api.auth import AuthInfo, ShareDatasetInfo, ShareRunInfo
from app.api.auth.schemas import BaseAuthInfo, FeedbackTokenInfo, OrgAuthInfo
from app.models.examples.fetch import populate_attachment_urls_correct_format
from app.models.feedback_configs.ingest import is_feedback_configs_fetch_enabled
from app.models.query_lang.generate import AttributeInfo, generate_filter_directive
from app.models.query_lang.parse import Comparator as C
from app.models.query_lang.parse import (
    Comparison,
    FilterDirective,
    Operation,
    Operator,
    parse_as_filter_directive,
)
from app.models.query_lang.translate import (
    BaseAttributeInfo,
    SqlVisitorClickhouse,
    comparison_sql,
    convert_primary_key_attribute_value,
)
from app.models.runs.attrs import (
    JOIN_ATTR_NAMES,
    RUN_ATTRIBUTES,
    RUN_ATTRIBUTES_DIRECT,
    RUN_ATTRIBUTES_SAFE_AI_QUERY,
    RUN_HISTORY_ATTRIBUTES,
    RUN_HISTORY_ATTRIBUTES_DIRECT,
    RUNS_LITE_ATTRIBUTES,
    RUNS_LITE_ATTRIBUTES_DIRECT,
)
from app.models.runs.ingest import (
    ERROR_S3_KEY,
    EVENTS_S3_KEY,
    EXTRA_S3_KEY,
    KNOWN_S3_KEYS,
    ROOT_S3_KEY,
    SERIALIZED_S3_KEY,
)
from app.models.runs.preview import preview_inputs, preview_outputs
from app.models.runs.utils import merge_run_infos, process_feedback_value_stats
from app.models.shared.utils import is_user_auth, list_workspace_ids_for_auth
from app.retry import retry_asyncpg, retry_clickhouse_read
from app.utils import completed_future, gated_coro, load_json

logger = structlog.get_logger(__name__)

SELF_QUERY_ATTRIBUTES = [
    AttributeInfo(name=a.name, description=a.description, type=a.type)
    for a in RUN_ATTRIBUTES_SAFE_AI_QUERY
    if a.name is not None and a.description is not None
]

RunSelectAfter = {
    schemas.RunSelect.app_path,
    schemas.RunSelect.in_dataset,
    schemas.RunSelect.last_queued_at,
    schemas.RunSelect.feedback_stats,
    schemas.RunSelect.parent_run_ids,
    schemas.RunSelect.child_run_ids,
    schemas.RunSelect.serialized,
    schemas.RunSelect.share_token,
    schemas.RunSelect.total_tokens,
    schemas.RunSelect.prompt_tokens,
    schemas.RunSelect.completion_tokens,
    schemas.RunSelect.first_token_time,
    schemas.RunSelect.total_cost,
    schemas.RunSelect.prompt_cost,
    schemas.RunSelect.completion_cost,
    schemas.RunSelect.inputs_or_signed_url,
    schemas.RunSelect.outputs_or_signed_url,
    schemas.RunSelect.error_or_signed_url,
    schemas.RunSelect.events_or_signed_url,
    schemas.RunSelect.extra_or_signed_url,
    schemas.RunSelect.serialized_or_signed_url,
}

PREFETCH_SELECT_FIELDS = {
    schemas.RunSelect.end_time,
    schemas.RunSelect.status,
    schemas.RunSelect.total_tokens,
    schemas.RunSelect.completion_tokens,
    schemas.RunSelect.prompt_tokens,
    schemas.RunSelect.total_cost,
    schemas.RunSelect.prompt_cost,
    schemas.RunSelect.tags,
    schemas.RunSelect.name,
    schemas.RunSelect.run_type,
    schemas.RunSelect.parent_run_id,
    schemas.RunSelect.dotted_order,
    schemas.RunSelect.first_token_time,
    schemas.RunSelect.extra,
    schemas.RunSelect.completion_cost,
}

run_fields_for_comparison_view = "id, name, run_type, status, tenant_id, session_id, is_root, start_time, end_time, inputs_preview, inputs_s3_urls, outputs_preview, outputs_s3_urls, s3_urls, reference_example_id, trace_id, dotted_order, inputs, outputs, error, events, extra, total_tokens, completion_tokens, prompt_tokens, total_cost, completion_cost, prompt_cost, first_token_time, trace_tier, parent_run_id, manifest_id, modified_at"

PREFETCH_LATENCY = Histogram(
    "smith_prefetch_traces_time",
    "Time spent fetching traces",
    ["hit"],
    buckets=(0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0),
)

PREFETCH_HIT = Counter(
    "smith_prefetch_traces_attempt",
    "Number of prefetch hits",
    ["hit"],
)

PREFETCH_CAN_USE = Counter(
    "smith_prefetch_traces_can_prefetch",
    "Number of /runs/query requests that could use prefetched traces",
    ["can_prefetch"],
)

NON_LITE_COLUMNS = [
    "extra",
    "error",
    "inputs",
    "outputs",
    "events",
    "inputs_s3_urls",
    "outputs_s3_urls",
    "manifest_s3_id",
    "input_tokens",
    "output_tokens",
    "price_model_id",
    "input_size",
    "output_size",
    "ttl_seconds",
    "s3_urls",
    "inputs_preview",
    "outputs_preview",
    "error_tokens",
    "manifest",
    "prompt_token_details",
    "completion_token_details",
    "prompt_cost_details",
    "completion_cost_details",
]

NON_HISTORICAL_COLUMNS = [
    "error",
    "inputs",
    "outputs",
    "input_tokens",
    "output_tokens",
    "price_model_id",
    "trace_tier",
    "ttl_seconds",
    "trace_first_received_at",
    "trace_upgrade",
    "s3_urls",
    "inputs_kv",
    "outputs_kv",
    "inputs_preview",
    "outputs_preview",
    "thread_id",
    "error_tokens",
    "manifest",
    "prompt_token_details",
    "completion_token_details",
    "prompt_cost_details",
    "completion_cost_details",
]


class ProcessQueryParamsResult(BaseModel):
    where: Operation
    where_primary_key_str: str
    where_primary_key_params: dict[str, Any]
    parsed: Optional[str]
    run_attributes: list[BaseAttributeInfo]
    all_attrs_joined: bool
    can_use_topk_agg_merge_tree: bool
    can_use_session_agg_merge_tree: bool
    apply_final_for_mutable_search: bool
    use_runs_lite: bool


def _all_attrs(args: list[FilterDirective]) -> set[str]:
    attrs = set()
    for arg in args:
        if isinstance(arg, Comparison) and arg.attribute is not None:
            attrs.add(arg.attribute)
        elif isinstance(arg, Operation):
            attrs |= _all_attrs(arg.arguments)
    return attrs


def _can_use_topk_agg_merge_tree(args: list[FilterDirective]) -> bool:
    # in order to use the topk aggregated merge tree,
    # we need to make sure that no filters are applied in
    # the where clause other than those grouped by the aggregation
    allowed_attributes_in_where = [
        "tenant_id",
        "session_id",
        "is_root",
        "start_time",
        "is_trace_expired",  # note we are not accounting for expired traces.
    ]

    return all(
        isinstance(a, Comparison) and a.attribute in allowed_attributes_in_where
        for a in args
    )


def _can_use_session_agg_merge_tree(args: list[FilterDirective]) -> bool:
    # in order to use the aggregated merge tree for session stats,
    # we need to make sure that no filters are applied in
    # the where clause other than those grouped by the aggregation
    allowed_attributes_in_where = [
        "tenant_id",
        "session_id",
        "is_root",
        "start_time",
        "is_trace_expired",  # note we are not accounting for expired traces.
        "reference_dataset_id",
    ]

    # make sure that all the filters are in the allowed list
    return all(
        isinstance(a, Comparison) and a.attribute in allowed_attributes_in_where
        for a in args
    )


def _select_fields_allowed_for_prefetch(select: list[schemas.RunSelect]) -> bool:
    res = all(field in PREFETCH_SELECT_FIELDS for field in select)
    return res


def get_run_attributes(
    args: list[FilterDirective],
    data_source_type: schemas.RunsFilterDataSourceTypeEnum = schemas.RunsFilterDataSourceTypeEnum.current,
) -> list[BaseAttributeInfo]:
    if data_source_type == schemas.RunsFilterDataSourceTypeEnum.lite:
        direct_attributes = RUNS_LITE_ATTRIBUTES_DIRECT
        attributes = RUNS_LITE_ATTRIBUTES
        join_attributes = JOIN_ATTR_NAMES - {"is_root"}
    elif data_source_type == schemas.RunsFilterDataSourceTypeEnum.historical:
        direct_attributes = RUN_HISTORY_ATTRIBUTES_DIRECT
        attributes = RUN_HISTORY_ATTRIBUTES
        join_attributes = JOIN_ATTR_NAMES
    else:
        direct_attributes = RUN_ATTRIBUTES_DIRECT
        attributes = RUN_ATTRIBUTES
        join_attributes = JOIN_ATTR_NAMES

    if not (join_attributes - _all_attrs(args)):
        # all 5 sorting key columns are present, so we can skip these joins
        names_to_replace = {a.name for a in direct_attributes}
        return [
            a for a in attributes if a.name not in names_to_replace
        ] + direct_attributes

    return attributes


def is_valid_field(value):
    """
    Returns True if value is non-empty.
    We consider a field empty if:
      - it is None, or
      - it is a string of an empty dict '{}' or empty list '[]'
    """
    if value is None:
        return False
    # Convert value to string and remove any extraneous whitespace.
    clean = str(value).strip()
    return clean not in ("{}", "[]")


def build_primary_key_condition(primary_key_args: list[Comparison]) -> tuple[str, dict]:
    conditions = []
    params = {}

    for i, arg in enumerate(primary_key_args):
        param_name = f"primary_key_param_{i}"
        params[param_name] = convert_primary_key_attribute_value(arg.value)
        condition = comparison_sql(
            comparison=arg,
            expression=str(arg.attribute),
            param_name="{" + param_name + "}",
            ignore_negative=False,
        )
        conditions.append(condition)

    where_clause = " AND ".join(conditions)
    return where_clause, params


def _contains_runs_exclusive_columns(
    args: list[FilterDirective],
    data_source_type: schemas.RunsFilterDataSourceTypeEnum,
    depth: int = 0,
) -> bool:
    """Checks if the query contains columns that are in the runs table but not in the table specified by data_source_type"""
    # Prevent stack overflow from deeply nested filters
    if depth > 100:
        raise ValueError("Filter nesting depth exceeds maximum allowed depth of 100")

    columns = (
        NON_HISTORICAL_COLUMNS
        if data_source_type == schemas.RunsFilterDataSourceTypeEnum.historical
        else NON_LITE_COLUMNS
    )

    for arg in args:
        if isinstance(arg, Comparison) and (
            arg.attribute in columns or arg.comparator == C.SEARCH
        ):
            return True
        else:
            if isinstance(arg, Operation):
                if _contains_runs_exclusive_columns(
                    arg.arguments,
                    data_source_type,
                    depth + 1,
                ):
                    return True
    return False


def can_use_runs_lite(tenant_id: UUID) -> bool:
    return (
        str(tenant_id) in settings.RUNS_LITE_TENANTS
        or "*" in settings.RUNS_LITE_TENANTS
    )


def _get_data_source_type(
    auth: BaseAuthInfo,
    args: list[FilterDirective],
    query_params: schemas.FilterQueryParamsForRunSchema,
    use_optimized_fetch: bool = False,
    is_root_true_query: bool | None = None,
) -> schemas.RunsFilterDataSourceTypeEnum:
    """Set the data source type based on the query params"""
    if (
        query_params.data_source_type == schemas.RunsFilterDataSourceTypeEnum.historical
        and _contains_runs_exclusive_columns(
            args, schemas.RunsFilterDataSourceTypeEnum.historical
        )
    ):
        # if the query contains filters such as error, inputs, ouputs (FT search)
        # we need to fetch from the runs table since runs_history table does not
        # have these columns
        query_params.data_source_type = schemas.RunsFilterDataSourceTypeEnum.current
    elif (
        not _contains_runs_exclusive_columns(
            args, schemas.RunsFilterDataSourceTypeEnum.lite
        )
        and not use_optimized_fetch
        and isinstance(auth, BaseAuthInfo)
        and can_use_runs_lite(auth.tenant_id)
        and query_params.data_source_type is None
        and not is_root_true_query
    ):
        # if the query does not contain filters exclusive to the runs table
        # and the feature flag is enabled, we can use the runs_lite as a data source
        query_params.data_source_type = schemas.RunsFilterDataSourceTypeEnum.lite

    return query_params.data_source_type or schemas.RunsFilterDataSourceTypeEnum.current


async def _get_args_from_query_params(
    tenant_id: UUID | None,
    auth: BaseAuthInfo | OrgAuthInfo,
    query_params: schemas.FilterQueryParamsForRunSchema,
    data_source_type: schemas.RunsFilterDataSourceTypeEnum,
) -> tuple[list[FilterDirective], list[FilterDirective], Optional[str]]:
    """Get the args from the query params"""
    parsed = None
    args = []
    primary_key_args = []

    if isinstance(auth, AuthInfo):
        args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
        primary_key_args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
    elif isinstance(auth, OrgAuthInfo):
        # Must have either tenant_ids or tenant_id
        # If tenant_id is not part of the organization, raise an error.
        # This should be verified upstream, but we do it here as well to be safe.
        tenant_ids = await list_workspace_ids_for_auth(auth)
        if tenant_id and tenant_id not in tenant_ids:
            raise ValueError("Workspace not found")
        if tenant_id:
            args.append(Comparison(C.EQ, "tenant_id", tenant_id))
            primary_key_args.append(Comparison(C.EQ, "tenant_id", tenant_id))
        else:
            args.append(Comparison(C.IN, "tenant_id", tenant_ids))
            primary_key_args.append(Comparison(C.IN, "tenant_id", tenant_ids))
    elif isinstance(auth, ShareRunInfo):
        args.append(Comparison(C.LIKE, "dotted_order", f"%Z{str(auth.run_id)}%"))
        args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
        primary_key_args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
        if auth.session_id:
            args.append(Comparison(C.EQ, "session_id", auth.session_id))
            primary_key_args.append(Comparison(C.EQ, "session_id", auth.session_id))
    elif isinstance(auth, ShareDatasetInfo):
        async with asyncpg_pool() as pg:
            sessions = await pg.fetch(
                """SELECT tracer_session.id FROM tracer_session WHERE tracer_session.reference_dataset_id = $1""",
                auth.dataset_id,
            )
        args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
        args.append(
            Comparison(C.IN, "session_id", [UUID(s["id"].hex) for s in sessions])
        )
        primary_key_args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
        primary_key_args.append(
            Comparison(C.IN, "session_id", [UUID(s["id"].hex) for s in sessions])
        )
    elif isinstance(auth, FeedbackTokenInfo):
        args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
        primary_key_args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
        if auth.session_id:
            args.append(Comparison(C.EQ, "session_id", auth.session_id))
            primary_key_args.append(Comparison(C.EQ, "session_id", auth.session_id))
        if auth.run_id == auth.trace_id:
            args.append(Comparison(C.EQ, "is_root", True))
            primary_key_args.append(Comparison(C.EQ, "is_root", True))
        if auth.start_time:
            args.append(Comparison(C.EQ, "start_time", auth.start_time))
            primary_key_args.append(Comparison(C.EQ, "start_time", auth.start_time))
        args.append(Comparison(C.EQ, "id", auth.run_id))
        primary_key_args.append(Comparison(C.EQ, "id", auth.run_id))
    elif isinstance(auth, BaseAuthInfo):
        args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
        primary_key_args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
    # add the query filters
    if query_params.execution_order:
        assert query_params.execution_order == 1
        args.append(Comparison(C.EQ, "is_root", True))
    if query_params.is_root is not None:
        args.append(Comparison(C.EQ, "is_root", query_params.is_root))
        primary_key_args.append(Comparison(C.EQ, "is_root", query_params.is_root))
    if query_params.run_type:
        args.append(Comparison(C.EQ, "run_type", query_params.run_type.value))
    if query_params.parent_run:
        args.append(Comparison(C.EQ, "parent_run_id", query_params.parent_run))
        args.append(Comparison(C.EQ, "is_root", False))
        primary_key_args.append(Comparison(C.EQ, "is_root", False))
    if query_params.trace:
        args.append(Comparison(C.EQ, "trace_id", query_params.trace))
    if query_params.id:
        args.append(Comparison(C.IN, "id", query_params.id))
        primary_key_args.append(Comparison(C.IN, "id", query_params.id))
    if query_params.session:
        args.append(Comparison(C.IN, "session_id", query_params.session))
        primary_key_args.append(Comparison(C.IN, "session_id", query_params.session))
    if query_params.start_time:
        args.append(Comparison(C.GTE, "start_time", query_params.start_time))
        primary_key_args.append(
            Comparison(C.GTE, "start_time", query_params.start_time)
        )
    if query_params.end_time:
        args.append(Comparison(C.LTE, "start_time", query_params.end_time))
        primary_key_args.append(Comparison(C.LTE, "start_time", query_params.end_time))
    if query_params.reference_example:
        args.append(
            Comparison(C.IN, "reference_example_id", query_params.reference_example)
        )
        if query_params.is_root is not False:
            args.append(Comparison(C.EQ, "is_root", True))
            primary_key_args.append(Comparison(C.EQ, "is_root", True))
    if query_params.error is not None:
        args.append(
            Comparison(C.EQ, "status", "error" if query_params.error else "success")
        )
    if (
        data_source_type == schemas.RunsFilterDataSourceTypeEnum.current
        or data_source_type == schemas.RunsFilterDataSourceTypeEnum.lite
    ):
        args.append(Comparison(C.EQ, "is_trace_expired", False))

    # get the freeform query filters from the query params
    if query_params.query:
        try:
            directive, parsed = await generate_filter_directive(
                SELF_QUERY_ATTRIBUTES,
                "Information about chain/tool/llm runs.",
                query_params.query,
                {
                    "tenant_id": auth.tenant_id,
                    "session_id": query_params.session,
                    "endpoint": "/runs/query",
                },
            )
        except Exception as e:
            raise HTTPException(
                status_code=400, detail="Failed to generate filter from freeform query"
            ) from e
        args.append(directive)

    return args, primary_key_args, parsed


async def process_query_params(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo | FeedbackTokenInfo | OrgAuthInfo,
    query_params: schemas.FilterQueryParamsForRunSchema,
    tenant_id: UUID | None = None,
    use_optimized_fetch: bool = False,
) -> ProcessQueryParamsResult:
    # validate
    if (
        not query_params.session
        and not query_params.reference_example
        and not query_params.parent_run
        and not query_params.trace
        and not query_params.id
        and not isinstance(auth, ShareDatasetInfo)
        and not isinstance(auth, OrgAuthInfo)
    ):
        raise HTTPException(
            status_code=400,
            detail="At least one of 'session', 'id', 'parent_run', 'trace' or 'reference_example' must be specified",
        )

    parsed = None
    apply_final_for_mutable_search = False
    # build up the query
    where = Operation(operator=Operator.AND, arguments=[])
    args = where.arguments
    # primary key where clause
    where_primary_key = Operation(operator=Operator.AND, arguments=[])
    primary_key_args = where_primary_key.arguments
    is_root_true_query = query_params.is_root

    if query_params.filter:
        filters = parse_as_filter_directive(query_params.filter)
        # check if final should be applied due to mutable values that may not be merged yet
        apply_final_for_mutable_search = (
            any(
                isinstance(arg, Comparison)
                and (
                    arg.attribute == "status"
                    and arg.value == "pending"
                    or arg.attribute == "status"
                    and arg.comparator == C.NEQ
                )
                for arg in filters.arguments
            )
            if filters and isinstance(filters, Operation)
            else False
        )

        args.append(filters)

        # extract the is_root filter from the filters operation
        if isinstance(filters, Operation):
            for arg in filters.arguments:
                if isinstance(arg, Comparison) and arg.attribute == "is_root":
                    primary_key_args.append(Comparison(C.EQ, "is_root", arg.value))
                break
        elif isinstance(filters, Comparison):
            if filters.attribute == "is_root":
                primary_key_args.append(Comparison(C.EQ, "is_root", filters.value))
                is_root_true_query = filters.value

    # resolve the source table to use for filtering
    data_source_type = _get_data_source_type(
        auth, args, query_params, use_optimized_fetch, is_root_true_query
    )

    # get where clause filters from the query params
    qp_args, qp_pk_args, parsed = await _get_args_from_query_params(
        tenant_id, auth, query_params, data_source_type
    )
    args += qp_args
    primary_key_args += qp_pk_args

    # get the primary key where clause to pushdown to all subqueries
    where_primary_key_str, where_primary_key_params = build_primary_key_condition(
        primary_key_args
    )

    # get the join attributes to use for the subqueries or joins
    join_attrs = (
        JOIN_ATTR_NAMES
        if data_source_type == schemas.RunsFilterDataSourceTypeEnum.lite
        else JOIN_ATTR_NAMES - {"is_root"}
    )

    return ProcessQueryParamsResult(
        where=where,
        where_primary_key_str=where_primary_key_str,
        where_primary_key_params=where_primary_key_params,
        parsed=parsed,
        run_attributes=get_run_attributes(args, data_source_type),
        all_attrs_joined=_all_attrs(args) == join_attrs,
        can_use_topk_agg_merge_tree=_can_use_topk_agg_merge_tree(args),
        can_use_session_agg_merge_tree=_can_use_session_agg_merge_tree(args),
        apply_final_for_mutable_search=apply_final_for_mutable_search,
        use_runs_lite=data_source_type == schemas.RunsFilterDataSourceTypeEnum.lite,
    )


class FetchRunsResult(TypedDict):
    runs: list[dict[str, Any]]
    cursors: dict[str, Optional[str]]
    parsed_query: Optional[str]


def _get_urls_for_s3_fetch(
    runs, s3_urls_list, url_type
) -> list[tuple[dict, str, str, str | None]]:
    return [
        (run, url_type, key, get_signed_url_go(url))
        for run, s3_urls in zip(runs, s3_urls_list)
        for key, url in s3_urls.items()
    ]


def _enrich_trace_result(result: List[dict]) -> FetchRunsResult:
    for i, run in enumerate(result):
        result[i]["app_path"] = (
            f"/o/{run['tenant_id']}/projects/p/{run['session_id']}/r/{run['id']}?trace_id={run['trace_id']}&start_time={datetime.fromisoformat(cast(str, run['dotted_order']).split('.')[0].split('Z')[0]).isoformat()}"
        )

    # cursor required for type validation (not used by FE)
    return FetchRunsResult(
        runs=result, cursors={"next": None, "prev": None}, parsed_query=None
    )


# frontend can initiate prefetching of traces into redis
# this function will return the trace if it is found in redis
async def _get_prefetched_trace(
    auth: AuthInfo, query_params: schemas.BodyParamsForRunSchema
) -> Optional[FetchRunsResult]:
    async with aredis_caching_pool() as aredis:
        tenant_id = auth.tenant_id
        session_id = query_params.session[0] if query_params.session else None
        trace_id = query_params.trace
        if tenant_id is None or session_id is None or trace_id is None:
            return None
        key = f"smith:cache:traces_prefetch:{tenant_id}:{session_id}:{trace_id}"
        payload = await aredis.get(key)
        if payload is not None:
            decomp = await arun_in_executor(
                zstandard.ZstdDecompressor().decompress, payload
            )
            result = await arun_in_executor(orjson.loads, decomp)
            return _enrich_trace_result(result)
    return None


async def _attempt_prefetch_trace(
    auth: AuthInfo,
    fetching_by_trace: Optional[Union[UUID, bool]],
    query_params: schemas.BodyParamsForRunSchema,
) -> Optional[FetchRunsResult]:
    can_prefetch_trace = (
        fetching_by_trace
        and not query_params.is_root
        and _select_fields_allowed_for_prefetch(query_params.select)
        and is_user_auth(auth)  # only prefetch for queries from FE (not API)
    )
    # record if we could use prefetched traces for this query
    if settings.DATADOG_ENABLED:
        PREFETCH_CAN_USE.labels(can_prefetch=can_prefetch_trace).inc()

    # TODO: remove the sampling after experiment
    if (
        can_prefetch_trace
        and str(auth.tenant_id) in settings.TRACE_PREFETCH_ENABLED_TENANT_IDS
    ):
        if random.random() < 0.5:
            response = await _get_prefetched_trace(auth, query_params)
            if settings.DATADOG_ENABLED:
                PREFETCH_HIT.labels(hit=response is not None).inc()
            return response
        else:
            PREFETCH_HIT.labels(hit=False).inc()
    return None


def _modify_cursor_field_for_runs_lite(params: dict[str, Any]) -> dict[str, Any]:
    for key, value in params.items():
        if "cursor" in key and isinstance(value, str):
            params[key] = (value[:26], value[26:])
    return params


@retry_clickhouse_read
async def fetch_runs_from_ch(
    ch: ClickhouseClient,
    query_params: schemas.BodyParamsForRunSchema,
    use_pk_order_by: bool,
    use_optimized_fetch: bool,
    cursor_field: str,
    sql_from_join_where: str,
    use_limit: bool,
    is_paginated_query: bool,
    use_prewhere: bool,
    processed_query_params: ProcessQueryParamsResult,
    projection: str,
    query_name_postfix: str,
    include_timeout: bool,
    query_timeout_when_enabled: int,
    params: dict[str, Any],
    runs_sort_order: str,
    max_threads: Optional[int] = None,
    use_in_order_dual_query: bool = False,
    query_meta: Optional[dict[str, Any]] = None,
):
    runs_table = "runs_lite" if processed_query_params.use_runs_lite else "runs"

    if processed_query_params.use_runs_lite:
        order_by_clause = f"ORDER BY (runs_lite.tenant_id, runs_lite.session_id, runs_lite.{cursor_field}, runs_lite.id) {runs_sort_order}"
    elif use_pk_order_by or use_optimized_fetch:
        order_by_clause = f"ORDER BY (runs.tenant_id, runs.session_id, runs.is_root, runs.start_time, runs.id) {runs_sort_order}"
    else:
        order_by_clause = f"ORDER BY runs.{cursor_field} {runs_sort_order}, toString(runs.id) {runs_sort_order}"

    if processed_query_params.use_runs_lite:
        params = _modify_cursor_field_for_runs_lite(params)

    runs_select = f"""
        SELECT
            {runs_table}.tenant_id as tenant_id,
            {runs_table}.session_id as session_id,
            {runs_table}.is_root as is_root,
            {runs_table}.start_time as start_time,
            {runs_table}.id as id
        {sql_from_join_where}
        {order_by_clause}
    """

    runs_settings = """SETTINGS multiple_joins_try_to_keep_original_names = 1, optimize_read_in_order = 1"""

    # Only set this for run_rules, checking for this by cursor_field
    if cursor_field != "start_time":
        runs_settings += ", max_final_threads = {}".format(
            settings.RUN_RULES_MAX_THREADS
        )
        runs_settings += ", max_memory_usage = {}".format(settings.RUN_RULES_MAX_MEMORY)

    if use_limit:
        runs_select = f"""
            {runs_select}
            LIMIT {query_params.limit + (1 if is_paginated_query else 0)}
        """

    primary_key_where = (
        f"AND {processed_query_params.where_primary_key_str}"
        if processed_query_params.where_primary_key_str
        else ""
    )

    runs_query = f"""
    WITH filtered_runs_cte AS (
    {runs_select}
    )
    SELECT {projection}
    FROM runs {"FINAL" if cursor_field != "start_time" else ""}
    {"PREWHERE" if use_prewhere else "WHERE"}
        (
            runs.tenant_id,
            runs.session_id,
            runs.is_root,
            runs.start_time,
            runs.id
        ) IN (
            select
                tenant_id,
                session_id,
                is_root,
                start_time,
                id
            from filtered_runs_cte
        )
        {primary_key_where}
    ORDER BY runs.{cursor_field} {runs_sort_order}, {"runs.id" if processed_query_params.use_runs_lite else "toString(runs.id)"} {runs_sort_order}, coalesce(runs.modified_at, runs.end_time, runs.start_time) DESC
    LIMIT 1 BY runs.id
    {runs_settings}"""

    if use_in_order_dual_query and "is_root__eq" in params:
        params_root_zero = copy.deepcopy(params)
        params_root_zero["is_root__eq"] = False

        # Run both fetches in parallel
        results = await asyncio.gather(
            ch.fetch(
                "fetch_runs" + query_name_postfix,
                runs_query,
                params=params,
                with_timeout=include_timeout,
                query_timeout_when_enabled=query_timeout_when_enabled,
                max_threads=max_threads,
                query_meta=query_meta,
            ),
            ch.fetch(
                "fetch_runs" + query_name_postfix,
                runs_query,
                params=params_root_zero,
                with_timeout=include_timeout,
                query_timeout_when_enabled=query_timeout_when_enabled,
                max_threads=max_threads,
                query_meta=query_meta,
            ),
        )
        # Combine the results
        runs = results[0] + results[1]
        runs = sorted(
            runs,
            key=lambda x: (x["start_time"], x["id"]),
            reverse=runs_sort_order == "DESC",
        )
        runs = runs[: query_params.limit]
    else:
        runs = await ch.fetch(
            "fetch_runs" + query_name_postfix,
            runs_query,
            params=params,
            with_timeout=include_timeout,
            query_timeout_when_enabled=query_timeout_when_enabled,
            max_threads=max_threads,
            query_meta=query_meta,
        )

    return runs


def _get_fetch_page_query(
    page_type: Literal["next", "prev"],
    cursor_field: str,
    sql_from_join_where_no_cursor: str,
    filtering_table: str,
    runs_sort_order: str,
) -> str:
    op = ">" if page_type == "next" else "<"
    expr = (
        f"({filtering_table}.{cursor_field}, {filtering_table}.id)"
        if filtering_table == "runs_lite"
        else f"concat(toString({filtering_table}.{cursor_field}), toString({filtering_table}.id))"
    )
    order_by = f"ORDER BY ({filtering_table}.{cursor_field}, {filtering_table}.id) {runs_sort_order}"
    cursor_param = "{cursor__gt}" if page_type == "next" else "{cursor__lt}"
    res = f"""
        SELECT
            1
            {sql_from_join_where_no_cursor}
        AND {expr} {op} {cursor_param}
        {order_by}
        LIMIT 1
    """
    return res


@retry_clickhouse_read
async def fetch_runs(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo | FeedbackTokenInfo,
    query_params: schemas.BodyParamsForRunSchema,
    additional_sql_where: str | None = None,
    cursor_field: str = "start_time",  # field for stable sorting and cursor pagination
    include_feedback: bool = True,
    skip_expensive_enabled: bool = True,
    include_timeout: bool = True,
    query_timeout_when_enabled: int = settings.CLICKHOUSE_FETCH_RUNS_TIMEOUT,
    max_threads: Optional[int] = None,
    clickhouse_client_type: ClickhouseClient = ClickhouseClient.USER_QUERIES,
    query_name_postfix: str = "",
    use_optimized_fetch: bool = False,
) -> FetchRunsResult:
    return await fetch_runs_no_retry(
        auth=auth,
        query_params=query_params,
        additional_sql_where=additional_sql_where,
        cursor_field=cursor_field,
        include_feedback=include_feedback,
        skip_expensive_enabled=skip_expensive_enabled,
        include_timeout=include_timeout,
        query_timeout_when_enabled=query_timeout_when_enabled,
        max_threads=max_threads,
        clickhouse_client_type=clickhouse_client_type,
        query_name_postfix=query_name_postfix,
        use_optimized_fetch=use_optimized_fetch,
    )


async def fetch_runs_no_retry(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo | FeedbackTokenInfo,
    query_params: schemas.BodyParamsForRunSchema,
    additional_sql_where: str | None = None,
    cursor_field: str = "start_time",  # field for stable sorting and cursor pagination
    include_feedback: bool = True,
    skip_expensive_enabled: bool = True,
    include_timeout: bool = True,
    query_timeout_when_enabled: int = settings.CLICKHOUSE_FETCH_RUNS_TIMEOUT,
    max_threads: Optional[int] = None,
    clickhouse_client_type: ClickhouseClient = ClickhouseClient.USER_QUERIES,
    query_name_postfix: str = "",
    use_optimized_fetch: bool = False,
) -> FetchRunsResult:
    if (
        isinstance(auth, BaseAuthInfo)
        and auth.tenant_id is not None
        and str(auth.tenant_id) in settings.FETCH_RUNS_BLOCKED_TENANTS
    ):
        raise HTTPException(
            status_code=403,
            detail="This tenant is temporarily blocked from fetching runs, please reach out to support",
        )

    func_start_time = perf_counter()
    processed_query_params = await process_query_params(
        auth, query_params, use_optimized_fetch=use_optimized_fetch
    )

    query_meta: dict[str, Any] = {}
    # when fetching by id, we don't apply limits or ordering
    fetching_by_id = query_params.id and query_params.cursor is None
    if fetching_by_id:
        query_meta["run_id"] = query_params.id

    fetching_by_trace = query_params.trace and query_params.cursor is None
    if fetching_by_trace:
        query_meta["trace_id"] = query_params.trace

    semaphore = asyncio.Semaphore(settings.BLOB_STORAGE_FETCH_SEMAPHORE)
    use_limit = not fetching_by_id and not fetching_by_trace and query_params.limit
    skip_pagination = query_params.skip_pagination
    has_prev_page_skip = query_params.skip_prev_cursor
    is_paginated_query = bool(use_limit) and not skip_pagination
    use_prewhere = query_name_postfix in ["_rules"]

    is_root_filter_present = query_params.is_root is not None or (
        query_params.filter is not None and "is_root" in query_params.filter
    )
    use_pk_order_by = (
        settings.FF_USE_PK_ORDER_BY
        and is_root_filter_present
        and cursor_field == "start_time"
    )

    # When is_root is not defined, we can parallelly fetch is_root = 1 and is_root = 0
    # and combine the results. This is done to take advantage of the
    # optimize_in_order setting from clickhouse which prevents reading all
    # data into memory to sort in order making the query much faster.
    use_in_order_dual_query = (
        is_paginated_query
        and cursor_field == "start_time"
        and use_optimized_fetch
        and not is_root_filter_present
    )

    # attempt to return prefetched trace
    response = await _attempt_prefetch_trace(auth, fetching_by_trace, query_params)
    prefetch_hit = response is not None
    if response is not None:
        PREFETCH_LATENCY.labels(hit=prefetch_hit).observe(
            perf_counter() - func_start_time
        )
        return response

    if use_in_order_dual_query:
        query_name_postfix += "_optimized_parallel_fetch"
        processed_query_params.where.arguments.append(Comparison(C.EQ, "is_root", True))

    filtering_table = "runs_lite" if processed_query_params.use_runs_lite else "runs"
    if filtering_table == "runs_lite":
        query_name_postfix += "_lite"
    final_str = (
        "FINAL"
        if (cursor_field == "inserted_at" and query_params.limit)
        or processed_query_params.apply_final_for_mutable_search
        else ""
    )

    # calculate the sql filters without cursor for the next/previous pagination
    try:
        (sql_from_join_where_no_cursor, no_cursor_params, _, _, _) = (
            processed_query_params.where.accept(
                SqlVisitorClickhouse(
                    attributes=processed_query_params.run_attributes,
                    main_table=f"{filtering_table} {final_str}",
                    sql_subquery_skip_enabled=(
                        use_optimized_fetch or processed_query_params.use_runs_lite
                    ),
                )
            )
        )
    except (ValueError, TypeError) as e:
        raise HTTPException(status_code=400, detail=str(e)) from e

    # apply the additional sql where
    if additional_sql_where:
        if processed_query_params.use_runs_lite:
            additional_sql_where = additional_sql_where.replace("runs.", "runs_lite.")
        sql_from_join_where_no_cursor = (
            f"{sql_from_join_where_no_cursor}\n{additional_sql_where}"
        )

    # apply the cursor
    if query_params.cursor:
        processed_query_params.where.arguments.append(
            parse_as_filter_directive(query_params.cursor, "Unable to parse cursor")
        )

    # translate the query to clickhouse sql
    try:
        sql_from_join_where, params, _, _, _ = processed_query_params.where.accept(
            SqlVisitorClickhouse(
                attributes=processed_query_params.run_attributes,
                main_table=f"{filtering_table} {final_str}",
                sql_subquery_skip_enabled=(
                    use_optimized_fetch or processed_query_params.use_runs_lite
                ),
            )
        )
    except (ValueError, TypeError) as e:
        raise HTTPException(status_code=400, detail=str(e)) from e

    if additional_sql_where:
        sql_from_join_where = f"{sql_from_join_where}\n{additional_sql_where}"

    if query_params.trace_filter:
        where_trace = Operation(
            operator=Operator.AND, arguments=[Comparison(C.EQ, "is_root", True)]
        )
        # copy tenant_id and session_id filters from the main query
        for arg in processed_query_params.where.arguments:
            if isinstance(arg, Comparison) and arg.attribute in (
                "tenant_id",
                "session_id",
                "start_time",
            ):
                where_trace.arguments.append(arg)

        # add the trace filter expression, and translate to clickhouse sql
        where_trace.arguments.append(
            parse_as_filter_directive(query_params.trace_filter)
        )
        try:
            trace_sql_from_join_where, trace_params, _, _, _ = where_trace.accept(
                SqlVisitorClickhouse(
                    attributes=RUN_ATTRIBUTES,
                    main_table="runs",
                    param_suffix="_trace_filter",
                )
            )
        except (ValueError, TypeError) as e:
            raise HTTPException(status_code=400, detail=str(e)) from e

        # apply the trace filter using a subquery via runs_trace_id
        params = {**params, **trace_params}
        sql_from_join_where = f"""{sql_from_join_where}
AND (tenant_id, session_id, is_root, start_time, id) IN (
    SELECT tenant_id, session_id, is_root, start_time, id
    FROM runs_trace_id
    WHERE (tenant_id, session_id, trace_id) IN (
        SELECT runs.tenant_id, runs.session_id, runs.id
        {trace_sql_from_join_where}
    )
)"""

    if query_params.tree_filter:
        where_child = Operation(operator=Operator.AND, arguments=[])
        # copy tenant_id and session_id filters from the main query
        for arg in processed_query_params.where.arguments:
            if isinstance(arg, Comparison) and arg.attribute in (
                "tenant_id",
                "session_id",
                "start_time",
            ):
                where_child.arguments.append(arg)

        # add the child filter expression, and translate to clickhouse sql
        where_child.arguments.append(
            parse_as_filter_directive(query_params.tree_filter)
        )
        try:
            child_sql_from_join_where, child_params, _, _, _ = where_child.accept(
                SqlVisitorClickhouse(
                    attributes=RUN_ATTRIBUTES,
                    main_table="runs",
                    param_suffix="_child_filter",
                )
            )
        except (ValueError, TypeError) as e:
            raise HTTPException(status_code=400, detail=str(e)) from e

        # apply the child filter using a subquery via runs_trace_id
        params = {**params, **child_params}
        sql_from_join_where = f"""{sql_from_join_where}
AND trace_id IN (
SELECT runs.trace_id
{child_sql_from_join_where}
)"""

    # build projection
    projection = ", ".join(
        f"runs.{col} AS {col}"
        for col in set(
            # we always want the sorting key columns
            [
                "tenant_id",
                "session_id",
                "is_root",
                "start_time",
                "id",
                "modified_at",
                "end_time",
                "run_type",
                "name",
                "status",
                "trace_id",
                "dotted_order",
                cursor_field,
            ]
            # add all requested columns that are not handled afterwards
            + [s.value for s in query_params.select if s not in RunSelectAfter]
            + (
                ["inputs_s3_urls"]
                if schemas.RunSelect.inputs in query_params.select
                or schemas.RunSelect.inputs_or_signed_url in query_params.select
                or schemas.RunSelect.inputs_preview in query_params.select
                else []
            )
            + (
                ["outputs_s3_urls"]
                if schemas.RunSelect.outputs in query_params.select
                or schemas.RunSelect.outputs_or_signed_url in query_params.select
                or schemas.RunSelect.outputs_preview in query_params.select
                else []
            )
            + (
                ["s3_urls"]
                if schemas.RunSelect.error in query_params.select
                or schemas.RunSelect.error_or_signed_url in query_params.select
                or schemas.RunSelect.events in query_params.select
                or schemas.RunSelect.events_or_signed_url in query_params.select
                or schemas.RunSelect.extra in query_params.select
                or schemas.RunSelect.extra_or_signed_url in query_params.select
                or schemas.RunSelect.serialized in query_params.select
                or schemas.RunSelect.serialized_or_signed_url in query_params.select
                else []
            )
            + (
                ["inputs"]
                if schemas.RunSelect.inputs_or_signed_url in query_params.select
                or schemas.RunSelect.inputs_preview in query_params.select
                else []
            )
            + (
                ["outputs"]
                if schemas.RunSelect.outputs_or_signed_url in query_params.select
                or schemas.RunSelect.outputs_preview in query_params.select
                else []
            )
            + (
                ["error"]
                if schemas.RunSelect.error_or_signed_url in query_params.select
                else []
            )
            + (
                ["events"]
                if schemas.RunSelect.events_or_signed_url in query_params.select
                else []
            )
            + (
                ["extra"]
                if schemas.RunSelect.extra_or_signed_url in query_params.select
                else []
            )
            # add manifest ids if serialized is requested
            + (
                ["manifest_id", "manifest_s3_id", "manifest"]
                if schemas.RunSelect.serialized in query_params.select
                or schemas.RunSelect.serialized_or_signed_url in query_params.select
                else []
            )
        )
    )

    only_input_preview = (
        schemas.RunSelect.inputs_preview in query_params.select
        and not (
            schemas.RunSelect.inputs in query_params.select
            or schemas.RunSelect.inputs_s3_urls in query_params.select
            or schemas.RunSelect.inputs_or_signed_url in query_params.select
        )
    )

    only_output_preview = (
        schemas.RunSelect.outputs_preview in query_params.select
        and not (
            schemas.RunSelect.outputs in query_params.select
            or schemas.RunSelect.outputs_s3_urls in query_params.select
            or schemas.RunSelect.outputs_or_signed_url in query_params.select
        )
    )

    params = {**params, **processed_query_params.where_primary_key_params}
    runs_sort_order = (
        "DESC" if query_params.order is schemas.RunDateOrder.desc else "ASC"
    )

    async with clickhouse_client(clickhouse_client_type) as ch, asyncpg_pool() as pg:
        # fetch the runs from clickhouse
        runs = await fetch_runs_from_ch(
            ch,
            query_params,
            use_pk_order_by,
            use_optimized_fetch,
            cursor_field,
            sql_from_join_where,
            bool(use_limit),
            is_paginated_query,
            use_prewhere,
            processed_query_params,
            projection,
            query_name_postfix,
            include_timeout,
            query_timeout_when_enabled,
            params,
            runs_sort_order,
            max_threads,
            use_in_order_dual_query,
            query_meta,
        )

        # try to skip pagination if we have one more than the limit
        has_next_page_skip = False
        # fetch the other attributes needed to render runs table
        if runs:
            large_results_processing_skip = (
                skip_expensive_enabled
                and len(runs) >= config.settings.FETCH_RUNS_THRESHOLD_SKIP_EXPENSIVE
            )
            cursor_attrib = (
                "cursor" if cursor_field == "start_time" else f"cursor_{cursor_field}"
            )

            if is_paginated_query and len(runs) > query_params.limit:
                has_next_page_skip = is_paginated_query
                runs = runs[:-1]

            cursor_next_page = (
                f"lt({cursor_attrib}, '{runs[-1][cursor_field].isoformat(sep=' ', timespec='microseconds')}{runs[-1]['id']}')"
                if query_params.order is schemas.RunDateOrder.desc
                else f"gt({cursor_attrib}, '{runs[-1][cursor_field].isoformat(sep=' ', timespec='microseconds')}{runs[-1]['id']}')"
            )
            cursor_prev_page = (
                f"gt({cursor_attrib}, '{runs[0][cursor_field].isoformat(sep=' ', timespec='microseconds')}{runs[0]['id']}')"
                if query_params.order is schemas.RunDateOrder.desc
                else f"lt({cursor_attrib}, '{runs[0][cursor_field].isoformat(sep=' ', timespec='microseconds')}{runs[0]['id']}')"
            )

            input_s3_urls_list = [
                load_json(run.get("inputs_s3_urls")) or {} for run in runs
            ]
            output_s3_urls_list = [
                load_json(run.get("outputs_s3_urls")) or {} for run in runs
            ]
            s3_urls_list = [load_json(run.get("s3_urls")) or {} for run in runs]

            # if the data exists in ClickHouse, skip fetching from S3
            # if the inputs/outputs_preview is present in CH and only preview
            # is requested, skip hitting S3
            input_s3_urls_list = [
                {}
                if (run.get("inputs_preview") is not None and only_input_preview)
                or is_valid_field(run.get("inputs"))
                else input_s3_urls_list[idx]
                for idx, run in enumerate(runs)
            ]

            output_s3_urls_list = [
                {}
                if (run.get("outputs_preview") is not None and only_output_preview)
                or is_valid_field(run.get("outputs"))
                else output_s3_urls_list[idx]
                for idx, run in enumerate(runs)
            ]

            # For other data types in s3_urls (error, events, extra), don't fetch if data exists in ClickHouse
            s3_urls_list = [
                {
                    k: v
                    for k, v in s3_urls.items()
                    # For keys in KNOWN_S3_KEYS, include them only if the corresponding run field is empty.
                    # Keys not in KNOWN_S3_KEYS are included by default.
                    if k not in KNOWN_S3_KEYS
                    or not is_valid_field(run.get(KNOWN_S3_KEYS[k]))
                }
                for s3_urls, run in zip(s3_urls_list, runs)
            ]

            presigned_urls = (
                _get_urls_for_s3_fetch(runs, input_s3_urls_list, "inputs_s3_urls")
                + _get_urls_for_s3_fetch(runs, output_s3_urls_list, "outputs_s3_urls")
                + _get_urls_for_s3_fetch(runs, s3_urls_list, "s3_urls")
            )

            root_s3_input_urls = [
                input_s3_urls.get(ROOT_S3_KEY) for input_s3_urls in input_s3_urls_list
            ]
            root_s3_output_urls = [
                output_s3_urls.get(ROOT_S3_KEY)
                for output_s3_urls in output_s3_urls_list
            ]

            run_ids = list(set([run["id"] for run in runs]))
            if len(run_ids) > settings.CLICKHOUSE_FETCH_RUNS_MAX_IDS:
                logger.warning(
                    "fetch_runs: truncating run ids",
                    num_ids=len(run_ids),
                    max_ids=settings.CLICKHOUSE_FETCH_RUNS_MAX_IDS,
                )
                run_ids = run_ids[: settings.CLICKHOUSE_FETCH_RUNS_MAX_IDS]
            sorting_key_params = {
                # runs guaranteed to have same tenant
                "tenant_id": runs[0]["tenant_id"],
                "session_ids": list(set([run["session_id"] for run in runs])),
                "is_roots": list(set([run["is_root"] for run in runs])),
                "start_times": list(set([run["start_time"] for run in runs])),
                "ids": run_ids,
            }
            min_start_time = min(sorting_key_params["start_times"])
            min_start_time = min_start_time.replace(
                microsecond=min_start_time.microsecond // 1000 * 1000
            )
            min_start_time = min_start_time - timedelta(
                milliseconds=settings.TOKEN_STATS_START_TIME_BUFFER_MS
            )
            str_min_start_time = min_start_time.strftime("%Y-%m-%d %H:%M:%S.%f")
            max_start_time = max(sorting_key_params["start_times"])
            max_start_time = max_start_time.replace(
                microsecond=max_start_time.microsecond // 1000 * 1000 + 999
            )
            max_start_time = max_start_time + timedelta(
                milliseconds=settings.TOKEN_STATS_START_TIME_BUFFER_MS
            )
            str_max_start_time = max_start_time.strftime("%Y-%m-%d %H:%M:%S.%f")
            manifest_ids = [r["manifest_id"] for r in runs if r.get("manifest_id")]
            manifest_s3_ids = [
                r["manifest_s3_id"] for r in runs if r.get("manifest_s3_id")
            ]

            max_end_time = max(
                [run["end_time"] for run in runs if run.get("end_time")],
                default=datetime.now(timezone.utc),
            )
            max_end_time = max_end_time.replace(
                microsecond=max_end_time.microsecond // 1000 * 1000 + 999
            )
            str_max_end_time = max_end_time.strftime("%Y-%m-%d %H:%M:%S.%f")

            # NOTE: if adding anything here, be sure to add to fetch_runs_comparison_view as well for parity
            (
                has_next_page,
                has_prev_page,
                feedback,
                token_counts,
                child_dotted_orders,
                in_dataset,
                last_queued_at,
                serialized,
                s3_serialized_from_id,
                share_tokens,
                s3_inputs,
                s3_outputs,
                s3_errors,
                s3_events,
                s3_extras,
                s3_serialized_from_url,
            ) = await asyncio.gather(
                completed_future(True)
                if has_next_page_skip
                else ch.fetchval(
                    "fetch_runs_previous_page",
                    _get_fetch_page_query(
                        "prev",
                        cursor_field,
                        sql_from_join_where_no_cursor,
                        filtering_table,
                        runs_sort_order,
                    ),
                    params={
                        **sorting_key_params,
                        **no_cursor_params,
                        "cursor__lt": f"{runs[-1][cursor_field].isoformat(sep=' ', timespec='microseconds')}{runs[-1]['id']}"
                        if filtering_table != "runs_lite"
                        else (runs[-1][cursor_field], runs[-1]["id"]),
                    },
                    max_threads=max_threads,
                )
                if is_paginated_query
                else empty(),
                ch.fetchval(
                    "fetch_runs_next_page",
                    _get_fetch_page_query(
                        "next",
                        cursor_field,
                        sql_from_join_where_no_cursor,
                        filtering_table,
                        runs_sort_order,
                    ),
                    params={
                        **sorting_key_params,
                        **no_cursor_params,
                        "cursor__gt": f"{runs[0][cursor_field].isoformat(sep=' ', timespec='microseconds')}{runs[0]['id']}"
                        if filtering_table != "runs_lite"
                        else (runs[0][cursor_field], runs[0]["id"]),
                    },
                    max_threads=max_threads,
                )
                if is_paginated_query and not has_prev_page_skip
                else empty(),
                # TODO: the replaceAll below is a temporary fix for square brackets getting interpreted strangely in feedback comments
                # Revert when we fix the ClickHouse client
                ch.fetch(
                    "fetch_runs_feedback_stats",
                    """
                WITH session_stats AS (
                    SELECT
                        key,
                        min(COALESCE(
                            CASE
                                WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                                ELSE NULL
                            END,
                            score
                        )) as session_min_score,
                        max(COALESCE(
                            CASE
                                WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                                ELSE NULL
                            END,
                            score
                        )) as session_max_score
                    FROM feedbacks_rmt FINAL
                    PREWHERE tenant_id = {tenant_id}
                    AND session_id IN {session_ids}
                    AND is_root IN {is_roots}
                    AND start_time >= {min_start_time}
                    AND start_time <= {max_start_time}
                    AND run_id IN {ids}
                    GROUP BY key
                )
                SELECT
                    run_id,
                    mapKeys(avgMap(map(key, score))) as feedback_keys,
                    mapValues(avgMap(map(key, COALESCE(
                        CASE
                            WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                            ELSE NULL
                        END,
                        score
                    )))) as feedback_avgs,
                    mapValues(stddevPopMap(map(key, COALESCE(
                        CASE
                            WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                            ELSE NULL
                        END,
                        score
                    )))) as feedback_stdevs,
                    mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
                    mapValues(countMap(map(key, score))) as feedback_counts,
                    mapValues(sumMap(map(key, JSONExtractString(feedback_source, 'metadata', '__run', 'run_id') != ''))) as feedback_show_arrows,
                    mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
                    mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
                    mapValues(anyMap(map(key, session_min_score))) as session_min_score,
                    mapValues(anyMap(map(key, session_max_score))) as session_max_score,
                    mapValues(groupArrayMap(map(key, replaceAll(replaceAll(comment, '[', '('), ']', ')')))) as feedback_comments
                FROM feedbacks_rmt FINAL
                JOIN session_stats ON feedbacks_rmt.key = session_stats.key
                PREWHERE tenant_id = {tenant_id}
                AND session_id IN {session_ids}
                AND is_root IN {is_roots}
                AND start_time >= {min_start_time}
                AND start_time <= {max_start_time}
                AND run_id IN {ids}
                GROUP BY run_id""",
                    params={
                        **sorting_key_params,
                        "min_start_time": str_min_start_time,
                        "max_start_time": str_max_start_time,
                    },
                    max_threads=max_threads,
                )
                if schemas.RunSelect.feedback_stats in query_params.select
                and include_feedback
                and not large_results_processing_skip
                else empty(),
                ch.fetch(
                    "fetch_runs_token_counts",
                    """
                SELECT
                    id,
                    sum(total_tokens) as total_tokens,
                    sum(completion_tokens) as completion_tokens,
                    sum(prompt_tokens) as prompt_tokens,
                    IF(uniqExact(source_id) = 1 AND any(source_id) = id, argMax(completion_token_details, modified_at), NULL) as completion_token_details,
                    IF(uniqExact(source_id) = 1 AND any(source_id) = id, argMax(prompt_token_details, modified_at), NULL) as prompt_token_details,
                    sum(total_cost) as total_cost,
                    sum(completion_cost) as completion_cost,
                    sum(prompt_cost) as prompt_cost,
                    IF(uniqExact(source_id) = 1 AND any(source_id) = id, argMax(completion_cost_details, modified_at), NULL) as completion_cost_details,
                    IF(uniqExact(source_id) = 1 AND any(source_id) = id, argMax(prompt_cost_details, modified_at), NULL) as prompt_cost_details,
                    min(first_token_time) as first_token_time
                FROM runs_token_counts FINAL
                PREWHERE tenant_id = {tenant_id}
                AND session_id IN {session_ids}
                AND is_root IN {is_roots}
                AND start_time >= {min_start_time}
                AND start_time <= {max_start_time}
                AND id IN {ids}
                AND runs_token_counts.total_tokens < 4000000000
                GROUP BY id """,
                    params={
                        **sorting_key_params,
                        "min_start_time": str_min_start_time,
                        "max_start_time": str_max_start_time,
                    },
                    max_threads=max_threads,
                )
                if any(
                    [
                        field in query_params.select
                        for field in [
                            schemas.RunSelect.total_tokens,
                            schemas.RunSelect.completion_tokens,
                            schemas.RunSelect.prompt_tokens,
                            schemas.RunSelect.first_token_time,
                            schemas.RunSelect.total_cost,
                            schemas.RunSelect.completion_cost,
                            schemas.RunSelect.prompt_cost,
                        ]
                    ]
                )
                and not large_results_processing_skip
                else empty(),
                ch.fetch(
                    "fetch_runs_child_run_ids",
                    """
                SELECT
                    filtered_runs.id as run_id,
                    groupUniqArray(filtered_child_runs.dotted_order) as children
                FROM
                    (SELECT id, dotted_order, trace_id
                     FROM runs
                     WHERE tenant_id = {tenant_id}
                         AND session_id IN {session_ids}
                         AND is_root IN {is_roots}
                         AND start_time >= {min_start_time}
                         AND start_time <= {max_start_time}
                         AND id IN {ids} AS filtered_runs) AS filtered_runs
                INNER JOIN
                    (SELECT id, dotted_order, trace_id
                     FROM runs
                     WHERE is_root = 0
                     AND tenant_id = {tenant_id}
                     AND session_id IN {session_ids}
                         AND start_time >= {min_start_time}
                         -- start_time of children will NOT be later than the max end time of the parent
                         AND start_time <= {max_end_time}
                         ) AS filtered_child_runs
                    ON filtered_child_runs.trace_id = filtered_runs.trace_id
                WHERE startsWith(filtered_child_runs.dotted_order, filtered_runs.dotted_order)
                AND filtered_child_runs.id != filtered_runs.id
                GROUP BY filtered_runs.id
                """,
                    params={
                        **sorting_key_params,
                        "min_start_time": str_min_start_time,
                        "max_start_time": str_max_start_time,
                        "max_end_time": str_max_end_time,
                    },
                    max_threads=max_threads,
                )
                if schemas.RunSelect.child_run_ids in query_params.select
                and not large_results_processing_skip
                else empty(),
                pg.fetch(
                    """select distinct source_run_id
                    from examples_log
                    where source_run_id = any($1)
                    and examples_log.inputs is not null""",
                    sorting_key_params["ids"],
                )
                if schemas.RunSelect.in_dataset in query_params.select
                and not large_results_processing_skip
                else empty(),
                pg.fetch(
                    """select run_id, max(added_at) as last_queued_at
                    from (
                        select run_id, added_at
                        from annotation_queue_runs
                        where annotation_queue_runs.run_id = any($1)
                        union all
                        select run_id, added_at
                        from annotation_queue_runs_archive
                        where annotation_queue_runs_archive.run_id = any($1)
                    ) as combined_runs
                    group by 1""",
                    sorting_key_params["ids"],
                )
                if schemas.RunSelect.last_queued_at in query_params.select
                and not large_results_processing_skip
                else empty(),
                pg.fetch(
                    """
                   select id, manifest as serialized
                    from run_manifests
                    where id = any($1)""",
                    manifest_ids,
                )
                if schemas.RunSelect.serialized in query_params.select
                and manifest_ids
                and not large_results_processing_skip
                else empty(),
                asyncio.gather(
                    *(
                        [
                            gated_coro(get_run_manifest(s3_id), semaphore)
                            for s3_id in manifest_s3_ids
                        ]
                    )
                )
                if schemas.RunSelect.serialized in query_params.select
                and manifest_s3_ids
                and not large_results_processing_skip
                else empty(),
                pg.fetch(
                    """
                    select run_id, share_token
                    from share_keys
                    where run_id = any($1)""",
                    sorting_key_params["ids"],
                )
                if schemas.RunSelect.share_token in query_params.select
                and not large_results_processing_skip
                else empty(),
                asyncio.gather(
                    *(
                        [
                            gated_coro(get_run_data(s3_path), semaphore)
                            if s3_path
                            else completed_future(None)
                            for s3_path in root_s3_input_urls
                        ]
                    )
                )
                if settings.FF_BLOB_STORAGE_ENABLED
                and (
                    schemas.RunSelect.inputs in query_params.select
                    or schemas.RunSelect.inputs_preview in query_params.select
                )
                and root_s3_input_urls
                and not large_results_processing_skip
                else empty(),
                asyncio.gather(
                    *(
                        [
                            gated_coro(get_run_data(s3_path), semaphore)
                            if s3_path
                            else completed_future(None)
                            for s3_path in root_s3_output_urls
                        ]
                    )
                )
                if settings.FF_BLOB_STORAGE_ENABLED
                and (
                    schemas.RunSelect.outputs in query_params.select
                    or schemas.RunSelect.outputs_preview in query_params.select
                )
                and root_s3_output_urls
                and not large_results_processing_skip
                else empty(),
                asyncio.gather(
                    *(
                        [
                            gated_coro(
                                get_run_data(s3_urls.get(ERROR_S3_KEY)), semaphore
                            )
                            if s3_urls and s3_urls.get(ERROR_S3_KEY)
                            else completed_future(None)
                            for s3_urls in s3_urls_list
                        ]
                    )
                )
                if settings.FF_BLOB_STORAGE_ENABLED
                and schemas.RunSelect.error in query_params.select
                and s3_urls_list
                and not large_results_processing_skip
                else empty(),
                asyncio.gather(
                    *(
                        [
                            gated_coro(
                                get_run_data(s3_urls.get(EVENTS_S3_KEY)), semaphore
                            )
                            if s3_urls and s3_urls.get(EVENTS_S3_KEY)
                            else completed_future(None)
                            for s3_urls in s3_urls_list
                        ]
                    )
                )
                if settings.FF_BLOB_STORAGE_ENABLED
                and schemas.RunSelect.events in query_params.select
                and s3_urls_list
                and not large_results_processing_skip
                else empty(),
                asyncio.gather(
                    *(
                        [
                            gated_coro(
                                get_run_data(s3_urls.get(EXTRA_S3_KEY)), semaphore
                            )
                            if s3_urls and s3_urls.get(EXTRA_S3_KEY)
                            else completed_future(None)
                            for s3_urls in s3_urls_list
                        ]
                    )
                )
                if settings.FF_BLOB_STORAGE_ENABLED
                and schemas.RunSelect.extra in query_params.select
                and s3_urls_list
                and not large_results_processing_skip
                else empty(),
                asyncio.gather(
                    *(
                        [
                            gated_coro(
                                get_run_data(s3_urls.get(SERIALIZED_S3_KEY)), semaphore
                            )
                            if s3_urls and s3_urls.get(SERIALIZED_S3_KEY)
                            else completed_future(None)
                            for s3_urls in s3_urls_list
                        ]
                    )
                )
                if settings.FF_BLOB_STORAGE_ENABLED
                and schemas.RunSelect.serialized in query_params.select
                and s3_urls_list
                and not large_results_processing_skip
                else empty(),
            )

        else:
            has_next_page = False
            has_prev_page = False
            cursor_next_page = None
            cursor_prev_page = None
            runs = []
            feedback = []
            token_counts = []
            child_dotted_orders = []
            in_dataset = []
            last_queued_at = []
            presigned_urls = []
            serialized = []
            s3_serialized_from_id = []
            manifest_s3_ids = []
            share_tokens = []
            s3_inputs = []
            s3_outputs = []
            s3_errors = []
            s3_events = []
            s3_extras = []
            s3_serialized_from_url = []

    s3_serialized_objects = []
    for id, serialized_result in zip(manifest_s3_ids, s3_serialized_from_id):
        s3_serialized_objects.append({"id": id, "serialized": serialized_result})
    s3_inputs_map = {run["id"]: result for run, result in zip(runs, s3_inputs)}
    s3_outputs_map = {run["id"]: result for run, result in zip(runs, s3_outputs)}
    s3_errors_map = {run["id"]: result for run, result in zip(runs, s3_errors)}
    s3_events_map = {run["id"]: result for run, result in zip(runs, s3_events)}
    s3_extras_map = {run["id"]: result for run, result in zip(runs, s3_extras)}
    s3_serialized_from_url_map = {
        run["id"]: result for run, result in zip(runs, s3_serialized_from_url)
    }

    # key accessory data by run id
    feedback = {f["run_id"]: f for f in feedback}
    token_counts = {
        tc["id"]: {
            k: orjson.loads(v) if isinstance(v, str) else v for k, v in tc.items()
        }
        for tc in token_counts
    }
    in_dataset = {r["source_run_id"] for r in in_dataset}
    last_queued_at = {r["run_id"]: r["last_queued_at"] for r in last_queued_at}
    child_dotted_orders = {r["run_id"]: r["children"] for r in child_dotted_orders}
    blobs = await arun_in_executor(
        _prepare_blog_storage_dict,
        runs,
        presigned_urls,
    )
    manifests_from_id_map = {
        manifest["id"]: manifest["serialized"]
        for manifest in serialized + s3_serialized_objects
    }

    # we can have manifests either in postgres, clickhouse, s3 via ID field, or s3 via the s3_urls:
    run_manifest_map = {}
    for run in runs:
        manifest_value = None
        if run.get("manifest", "{}") != "{}":
            manifest_value = load_json(run.get("manifest"))
        elif run.get("manifest_id") or run.get("manifest_s3_id"):
            manifest_value = manifests_from_id_map.get(
                run["manifest_id"] if run.get("manifest_id") else run["manifest_s3_id"]
            )
        elif run["id"] in s3_serialized_from_url_map:
            manifest_value = s3_serialized_from_url_map[run["id"]]
        if manifest_value:
            run_manifest_map[run["id"]] = manifest_value

    share_tokens = {r["run_id"]: r["share_token"] for r in share_tokens}

    PREFETCH_LATENCY.labels(hit=prefetch_hit).observe(perf_counter() - func_start_time)

    runs = {
        "runs": await asyncio.gather(
            *(
                arun_in_executor(
                    map_run,
                    auth,
                    run,
                    feedback.get(run["id"]),
                    token_counts.get(run["id"]),
                    run["id"] in in_dataset,
                    last_queued_at.get(run["id"]),
                    run_manifest_map.get(run["id"]),
                    child_dotted_orders.get(run["id"]),
                    blobs.get(run["id"], {}).get("inputs_s3_urls"),
                    blobs.get(run["id"], {}).get("outputs_s3_urls"),
                    blobs.get(run["id"], {}).get("s3_urls"),
                    share_tokens.get(run["id"]),
                    s3_inputs_map.get(run["id"]),
                    s3_outputs_map.get(run["id"]),
                    s3_errors_map.get(run["id"]),
                    s3_events_map.get(run["id"]),
                    s3_extras_map.get(run["id"]),
                    with_inputs_preview="only"
                    if only_input_preview
                    else schemas.RunSelect.inputs_preview in query_params.select,
                    with_outputs_preview="only"
                    if only_output_preview
                    else schemas.RunSelect.outputs_preview in query_params.select,
                )
                for run in runs
            )
        ),
        "cursors": {
            "next": cursor_next_page if has_next_page else None,
            "prev": cursor_prev_page if has_prev_page else None,
        },
        "parsed_query": processed_query_params.parsed,
    }
    return runs


def _prepare_blog_storage_dict(
    runs: list[dict[str, Any]],
    presigned_urls: list[Tuple[dict[str, Any], str, str, str | None]],
) -> dict[str, dict]:
    blobs: dict[str, dict] = {
        run["id"]: {
            "inputs_s3_urls": None,
            "outputs_s3_urls": None,
            "s3_urls": None,
        }
        for run in runs
    }

    # Only add s3 URLs that have corresponding presigned URLs
    for run, url_type, key, url in presigned_urls:
        # only include these if they are in select or x_or_signed_url is selected
        if url:
            original_urls = load_json(run.get(url_type))
            if original_urls and key in original_urls:
                if blobs[run["id"]][url_type] is None:
                    blobs[run["id"]][url_type] = {}
                blobs[run["id"]][url_type][key] = {
                    "presigned_url": url,
                    "s3_url": original_urls[key],
                }
    return blobs


async def fetch_single_run(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo | FeedbackTokenInfo,
    run_id: UUID,
    select: list[schemas.RunSelect] | None = None,
    include_feedback: bool = True,
    is_root: bool | None = None,
    start_time: datetime | None = None,
    session_id: UUID | None = None,
    query_name_postfix: str = "",
    max_threads: int | None = None,
    clickhouse_client_type: ClickhouseClient = ClickhouseClient.USER_QUERIES,
) -> dict[str, Any] | None:
    runs = await fetch_runs(
        auth,
        schemas.BodyParamsForRunSchema(
            id=[run_id],
            is_root=is_root,
            start_time=start_time,
            session=[session_id] if session_id else [],
            select=select or [],
        ),
        query_name_postfix=query_name_postfix,
        clickhouse_client_type=clickhouse_client_type,
        include_feedback=include_feedback,
        max_threads=max_threads,
    )
    return runs["runs"][0] if runs["runs"] else None


async def fetch_and_assert_run(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo | FeedbackTokenInfo,
    run_id: UUID,
    select: list[schemas.RunSelect] | None = None,
    include_feedback: bool = True,
    is_root: bool | None = None,
    start_time: datetime | None = None,
    session_id: UUID | None = None,
    max_threads: int | None = None,
) -> dict[str, Any]:
    if start_time is not None:
        # subtract 1 second from start_time
        start_time = start_time - timedelta(seconds=1)

    run = await fetch_single_run(
        auth,
        run_id,
        select,
        include_feedback=include_feedback,
        is_root=is_root,
        start_time=start_time,
        session_id=session_id,
        max_threads=max_threads,
    )
    if run:
        return run
    else:
        raise HTTPException(status_code=404, detail="Run not found")


def map_run(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo,
    run: Record,
    feedback: Record | None,
    token_counts: Record | None,
    in_dataset: bool,
    last_queued_at: datetime | None,
    serialized: dict[str, Any] | None,
    child_dotted_orders: list[str] | None,
    inputs_s3_urls: dict[str, str] | None,
    outputs_s3_urls: dict[str, str] | None,
    s3_urls: dict[str, str] | None,
    share_token: UUID | None,
    s3_inputs: bytes | None,
    s3_outputs: bytes | None,
    s3_errors: bytes | None,
    s3_events: bytes | None,
    s3_extras: bytes | None,
    *,
    with_inputs_preview: bool | Literal["only"] = False,
    with_outputs_preview: bool | Literal["only"] = False,
) -> dict:
    app_path = f"/o/{run['tenant_id']}/projects/p/{run['session_id']}/r/{run['id']}?trace_id={run['trace_id']}&start_time={datetime.fromisoformat(cast(str, run['dotted_order']).split('.')[0].split('Z')[0]).isoformat()}"
    child_runs = (
        [
            (child.count("."), child.split(".")[-1].split("Z")[1])
            for child in sorted(child_dotted_orders or [])
        ]
        if child_dotted_orders is not None
        else None
    )
    min_child_run_depth = min(d for d, _ in child_runs) if child_runs else None
    feedback_values_sorted = []
    if feedback is not None:
        feedback_values_sorted = process_feedback_value_stats(feedback)

    # if inputs_preview or outputs_preview is selected exclusively,
    # the s3_inputs and s3_outputs will be empty in cases where
    # the preview is already present in the run
    inputs = (
        load_json(s3_inputs or run["inputs"])
        if s3_inputs or run.get("inputs")
        else None
    )
    outputs = (
        load_json(s3_outputs or run["outputs"])
        if s3_outputs or run.get("outputs")
        else None
    )

    error = s3_errors or run.get(ERROR_S3_KEY)
    events = (
        load_json(s3_events or run.get(EVENTS_S3_KEY))
        if s3_events or run.get(EVENTS_S3_KEY)
        else None
    )
    extra = (
        load_json(s3_extras or run.get(EXTRA_S3_KEY))
        if s3_extras or run.get(EXTRA_S3_KEY)
        else None
    )

    run = {
        **run,
        "parent_run_ids": [
            UUID(part.split("Z")[1])
            for part in cast(str, run["dotted_order"]).split(".")[:-1]
        ]
        if run.get("dotted_order")
        else None,
        "child_run_ids": [UUID(child) for _, child in child_runs]
        if child_runs
        else None,
        "direct_child_run_ids": [
            UUID(child) for depth, child in child_runs if depth == min_child_run_depth
        ]
        if child_runs
        else None,
        "total_tokens": token_counts["total_tokens"] if token_counts else 0,
        "completion_tokens": token_counts["completion_tokens"] if token_counts else 0,
        "prompt_tokens": token_counts["prompt_tokens"] if token_counts else 0,
        "completion_token_details": token_counts.get("completion_token_details")
        if token_counts
        else None,
        "prompt_token_details": token_counts.get("prompt_token_details")
        if token_counts
        else None,
        "total_cost": token_counts["total_cost"] if token_counts else None,
        "completion_cost": token_counts["completion_cost"] if token_counts else None,
        "prompt_cost": token_counts["prompt_cost"] if token_counts else None,
        "completion_cost_details": token_counts.get("completion_cost_details")
        if token_counts
        else None,
        "prompt_cost_details": token_counts.get("prompt_cost_details")
        if token_counts
        else None,
        "first_token_time": token_counts["first_token_time"] if token_counts else None,
        "app_path": app_path,
        "in_dataset": in_dataset,
        "last_queued_at": last_queued_at,
        "error": error,
        "inputs": inputs if with_inputs_preview != "only" else None,
        "inputs_preview": run["inputs_preview"]
        or preview_inputs(inputs, config.settings.RUN_PREVIEW_IO_MAX_CHARS)
        if with_inputs_preview
        else None,
        "inputs_s3_urls": inputs_s3_urls,
        "outputs": outputs if with_outputs_preview != "only" else None,
        "outputs_preview": run["outputs_preview"]
        or preview_outputs(outputs, inputs, config.settings.RUN_PREVIEW_IO_MAX_CHARS)
        if with_outputs_preview
        else None,
        "outputs_s3_urls": outputs_s3_urls,
        "s3_urls": s3_urls,
        "extra": extra,
        "events": events,
        "feedback_stats": {
            key: dict(
                n=n,
                avg=avg,
                stdev=stdev,
                errors=errors,
                show_feedback_arrow=show_feedback_arrow > 0,
                comments=comments,
                session_min_score=float(session_min_score)
                if session_min_score is not None
                else None,
                session_max_score=float(session_max_score)
                if session_max_score is not None
                else None,
                values=values,
            )
            for key, n, avg, stdev, errors, show_feedback_arrow, comments, session_min_score, session_max_score, values in zip(
                feedback["feedback_keys"] or [],
                feedback["feedback_counts"] or [],
                feedback["feedback_avgs"] or [],
                feedback["feedback_stdevs"] or [],
                feedback["feedback_errors"] or [],
                feedback["feedback_show_arrows"] or [],
                feedback["feedback_comments"] or [],
                feedback["session_min_score"] or [],
                feedback["session_max_score"] or [],
                feedback_values_sorted,
            )
        }
        if feedback
        else None,
        "serialized": serialized,
        "share_token": share_token,
        "trace_tier": run.get("trace_tier"),
    }

    if isinstance(auth, ShareRunInfo):
        run = sanitize_public_run(run, auth)
    if isinstance(auth, ShareDatasetInfo):
        run = sanitize_public_dataset_run(run)

    return run


async def empty() -> list[dict]:
    return []


def sanitize_public_dataset_run(run: dict) -> dict:
    if run.get("app_path"):
        run["app_path"] = None
    if run.get("manifest_id"):
        run["manifest_id"] = None
    return run


def sanitize_public_run(run: dict, auth: ShareRunInfo) -> dict:
    run = sanitize_public_dataset_run(run)
    allowed_ids = set(auth.allowed_run_ids)
    if run.get("parent_run_ids"):
        run["parent_run_ids"] = [
            rid for rid in run["parent_run_ids"] if rid in allowed_ids
        ]
    if run.get("parent_run_id") and run["parent_run_id"] not in allowed_ids:
        run["parent_run_id"] = None
    if run.get("reference_example_id"):
        run["reference_example_id"] = None

    return run


def build_example_ids_query(
    use_sort_params: bool,
    has_filters: bool,
    examples_query: str,
    feedback_sort_order: str | None = None,
    has_start_time: bool | None = None,
) -> str:
    if use_sort_params:
        return f"""
        WITH avg_scores AS (
            WITH filtered_runs AS (
                SELECT id, reference_example_id
                FROM runs
                WHERE tenant_id = {{tenant_id}}
                AND session_id IN ({{session_ids}})
                AND is_root = 1
                {"AND start_time >= {start_time}" if has_start_time else ""}
                {f"AND reference_example_id IN ({examples_query})" if has_filters else ""}
            ),
            filtered_feedbacks AS (
                SELECT
                    tenant_id,
                    session_id,
                    is_root,
                    start_time,
                    id,
                    run_id,
                    argMax(key, modified_at) as key,
                    argMax(score, modified_at) as score
                FROM feedbacks_rmt
                WHERE tenant_id = {{tenant_id}}
                AND session_id IN ({{session_ids}})
                AND is_root = 1
                {"AND start_time >= {start_time}" if has_start_time else ""}
                GROUP BY tenant_id, session_id, is_root, start_time, id, run_id
            )
            SELECT reference_example_id, AVG(CASE WHEN filtered_feedbacks.key = {{feedback_sort_key}} THEN filtered_feedbacks.score ELSE NULL END) AS avg_score
            FROM filtered_runs
            LEFT JOIN filtered_feedbacks ON filtered_runs.id = filtered_feedbacks.run_id
            GROUP BY run_id, reference_example_id
        )
        SELECT reference_example_id
        FROM (
            SELECT
                reference_example_id,
                AVG(avg_score) as example_avg_score
            FROM avg_scores
            GROUP BY reference_example_id
        )
        ORDER BY (example_avg_score IS NOT NULL) {feedback_sort_order}, example_avg_score {feedback_sort_order}
        LIMIT {{limit}} OFFSET {{offset}}
        """
    else:
        if has_filters:
            return f"""
                SELECT reference_example_id
                FROM ({examples_query})
                ORDER BY toString(reference_example_id)
                LIMIT {{limit}} OFFSET {{offset}}
            """
        else:
            return """
                SELECT DISTINCT reference_example_id
                FROM runs
                WHERE tenant_id = {tenant_id}
                AND is_root = 1
                AND session_id IN ({session_ids})
                ORDER BY toString(reference_example_id)
                LIMIT {limit} OFFSET {offset}
            """


@retry_clickhouse_read
async def fetch_groups_comparison_view(
    auth: AuthInfo | ShareDatasetInfo,
    dataset_id: UUID,
    query_params: schemas.QueryGroupedExamplesWithRuns,
) -> Tuple[schemas.GroupedExamplesWithRunsResponse, int]:
    metadata_key = (
        query_params.metadata_key
        if query_params.group_by == schemas.GroupExampleRunsByField.run_metadata
        else f"ls_example_{query_params.metadata_key}"
    )
    # We need to pick a sentinel value for nulls because Clickhouse groupArray ignores nulls: https://github.com/ClickHouse/ClickHouse/issues/1980
    query = """
        WITH filtered_feedbacks_agg AS (
            SELECT
                run_id,
                tenant_id,
                session_id,
                is_root,
                groupArray(key)         AS keys,
                groupArray(id)          AS ids,
                groupArray(ifNull(score, CAST(99999.9999 AS Decimal(9,4)))) AS scores,
                groupArray(value)       AS values,
                groupArray(ifNull(correction, ''))  AS corrections,
                groupArray(extra)       AS extras
            FROM feedbacks_rmt FINAL
            WHERE is_root = 1
            AND session_id IN {session_ids}
            AND tenant_id = {tenant_id}
            GROUP BY
                run_id,
                tenant_id,
                session_id,
                is_root
        ),
        filtered_runs_cte AS (
            SELECT *
            FROM runs
            WHERE is_root = 1
            AND session_id IN {session_ids}
            AND tenant_id = {tenant_id}
            ORDER BY id DESC, modified_at DESC
            LIMIT 1 BY id
        ),
        filtered_token_counts_cte AS (
            SELECT *
            FROM runs_token_counts
            WHERE is_root = 1
            AND session_id IN {session_ids}
            AND tenant_id = {tenant_id}
            ORDER BY id DESC, modified_at DESC
            LIMIT 1 BY id
        )
        SELECT
            -- 1) Run-level aggregations
            JSONExtractRaw(r.extra, 'metadata', {metadata_key}) AS group_key,
            uniq(r.id) AS run_count,
            max(r.start_time) AS max_start_time,
            min(r.start_time) AS min_start_time,
            arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(
                dateDiff('millisecond', r.start_time, r.end_time)
            )) AS latency_ptiles,
            IF(uniq(r.id) = 0, NULL, uniqIf(r.id, r.status = 'error') / uniq(r.id)) AS error_rate,
            arraySlice(
                arraySort(
                    x -> x['reference_example_id'],
                    groupArray(
                        map(
                            'id', toString(r.id),
                            'reference_example_id', toString(r.reference_example_id),
                            'start_time', toString(cast(r.start_time, 'DateTime64(6)'))
                        )
                    )
                ),
                1, 5
            ) AS first_run_infos,

            -- 2) feedback stats
            mapKeys(uniqMap(mapFromArrays(f.keys, f.ids))) as feedback_keys,
            mapValues(avgMap(mapFromArrays(f.keys, arrayMap((x, y) ->
                COALESCE(
                    CASE
                        WHEN JSONHas(x, 'score') THEN CAST(JSONExtract(x, 'score', 'Float32') AS Decimal(9, 4))
                        ELSE NULL
                    END,
                    NULLIF(y, CAST(99999.9999 AS Decimal(9,4)))
                )
            , f.corrections, f.scores)))) AS feedback_avgs,
            mapValues(countMap(mapFromArrays(f.keys, arrayMap((x) -> NULLIF(x, CAST(99999.9999 AS DECIMAL(9,4))), f.scores)))) AS feedback_counts,
            mapValues(stddevPopMap(mapFromArrays(f.keys, arrayMap((x, y) ->
                COALESCE(
                    CASE
                        WHEN JSONHas(x, 'score') THEN CAST(JSONExtract(x, 'score', 'Float32') AS Decimal(9, 4))
                        ELSE NULL
                    END,
                    NULLIF(y, CAST(99999.9999 AS Decimal(9,4)))
                )
            , f.corrections, f.scores)))) AS feedback_stdevs,
            mapKeys(countMap(mapFromArrays(arrayMap((x, y) -> (x || '|~|' || y), f.keys, f.values), f.values))) as feedback_value_keys,
            mapValues(countMap(mapFromArrays(arrayMap((x, y) -> (x || '|~|' || y), f.keys, f.values), f.values))) as feedback_value_counts,
            mapValues(sumMap(mapFromArrays(f.keys, arrayMap(x -> (JSONExtractBool(x, 'error')), f.extras)))) as feedback_errors,

            -- 3) Token stats
            sum(t.total_tokens) AS total_tokens,
            sum(t.completion_tokens) AS completion_tokens,
            sum(t.prompt_tokens) AS prompt_tokens,
            sum(t.total_cost) AS total_cost,
            sum(t.completion_cost) AS completion_cost,
            sum(t.prompt_cost) AS prompt_cost

        FROM filtered_runs_cte AS r
        LEFT JOIN filtered_feedbacks_agg AS f
            ON  f.run_id     = r.id
            AND f.tenant_id  = r.tenant_id
            AND f.session_id = r.session_id
            AND f.is_root    = r.is_root
        LEFT JOIN filtered_token_counts_cte AS t
            ON  t.id         = r.id
            AND t.tenant_id  = r.tenant_id
            AND t.session_id = r.session_id
            AND t.is_root    = r.is_root
        GROUP BY group_key
        ORDER BY group_key
        LIMIT {limit} OFFSET {offset}
        """
    params = {
        "session_ids": query_params.session_ids,
        "tenant_id": auth.tenant_id,
        "limit": query_params.limit + 1,
        "offset": query_params.offset,
        "metadata_key": metadata_key,
        "per_group_limit": query_params.per_group_limit,
    }

    async with clickhouse_client(ClickhouseClient.USER_QUERIES) as ch:
        results_raw = await ch.fetch("fetch_groups_comparison", query, params=params)

        groups = []
        run_dicts: list[dict[str, Any]] = []
        results = []
        example_ids_to_fetch = []
        for g in results_raw:
            new_g = dict(g)
            first_run_infos = new_g["first_run_infos"]
            if first_run_infos and len(first_run_infos[0]) == 1:
                new_g["first_run_infos"] = merge_run_infos(first_run_infos)
                first_run_infos = new_g["first_run_infos"]
                results.append(new_g)
            else:
                results.append(new_g)
            logger.warning(
                "first_run_infos when fetching grouped comparison view",
                first_run_infos=first_run_infos,
            )
            for example_and_run in first_run_infos:
                logger.warning(
                    "example_and_run when fetching grouped comparison view",
                    example_and_run=example_and_run,
                )
                run_dict = {
                    "start_time": datetime.strptime(
                        example_and_run["start_time"].strip("'"), "%Y-%m-%d %H:%M:%S.%f"
                    ),
                    "id": UUID(example_and_run["id"]),
                }
                run_dicts.append(run_dict)
                example_ids_to_fetch.append(example_and_run["reference_example_id"])

        if run_dicts and example_ids_to_fetch:
            from app.crud import get_examples

            as_of = await fetch_example_as_of_from_sessions(
                query_params.session_ids, auth.tenant_id, ch
            )
            earliest_start_time = min(r["start_time"] for r in run_dicts)
            runs, (examples, _) = await asyncio.gather(
                ch.fetch(
                    "fetch_runs_for_grouped_comparison",
                    f"""
                    SELECT {run_fields_for_comparison_view}
                    FROM runs
                    WHERE id IN {{run_ids}}
                            and tenant_id = {{tenant_id}}
                            and session_id IN {{session_ids}}
                            and is_root = 1
                            and start_time >= {{start_time}}
                    ORDER BY id DESC, modified_at DESC
                    LIMIT 1 BY id
                    """,
                    params={
                        "run_ids": [r["id"] for r in run_dicts],
                        "tenant_id": auth.tenant_id,
                        "session_ids": query_params.session_ids,
                        "start_time": earliest_start_time,
                    },
                ),
                get_examples(
                    auth,
                    schemas.FilterQueryParamsForExampleSchema.model_construct(
                        id=example_ids_to_fetch,
                        dataset=dataset_id,
                        as_of=as_of or "latest",
                    ),
                ),
            )
            runs_by_reference_example_id = await fetch_runs_for_comparison_view(
                ch,
                auth,
                runs,
                query_params.preview,
                None,
            )
            examples_by_id = {e.id: e for e in examples}
        else:
            runs_by_reference_example_id = {}
            examples_by_id = {}

        for g in results:
            examples_with_runs = []
            for run_and_example in g["first_run_infos"]:
                example = examples_by_id.get(
                    UUID(run_and_example["reference_example_id"])
                )
                if example:
                    examples_with_runs.append(
                        schemas.ExampleWithRunsCH(
                            **{
                                **example.model_dump(),
                                "run_count": len(
                                    runs_by_reference_example_id[example.id]
                                ),
                                "runs": runs_by_reference_example_id[example.id],
                            }
                        )
                    )

            group_feedback_stats = {}

            def load_stats(stats: list[str]):
                return [
                    json.loads(stats[i]) if isinstance(stats[i], str) else stats[i]
                    for i in range(len(stats))
                ]

            for i, key in enumerate(g["feedback_keys"]):
                g["feedback_counts"] = load_stats(g["feedback_counts"])
                g["feedback_value_counts"] = load_stats(g["feedback_value_counts"])
                feedback_values_sorted = process_feedback_value_stats(g)
                group_feedback_stats[key] = {
                    "n": g["feedback_counts"][i],
                    "avg": float(g["feedback_avgs"][i])
                    if g["feedback_avgs"][i] is not None
                    else None,
                    "stdev": g["feedback_stdevs"][i],
                    "errors": g["feedback_errors"][i],
                    "values": feedback_values_sorted[i],
                }

            if g["group_key"] == "":
                filter = f'and(eq(is_root, true), neq(metadata_key, "{metadata_key}"))'
            elif g["group_key"] == "null":
                filter = f'and(eq(is_root, true), and(eq(metadata_key, "{metadata_key}"), eq(metadata_value, null)))'
            else:
                filter = f'and(eq(is_root, true), and(eq(metadata_key, "{metadata_key}"), eq(metadata_value, {g["group_key"]})))'
            try:
                group_key = json.loads(g["group_key"])
                if group_key is None:
                    group_key = "null"
            except json.JSONDecodeError:
                group_key = "Missing"

            group = schemas.ExampleWithRunsGroup(
                group_key=group_key,
                filter=filter,
                count=g["run_count"],
                total_tokens=g["total_tokens"],
                completion_tokens=g["completion_tokens"],
                prompt_tokens=g["prompt_tokens"],
                total_cost=g["total_cost"],
                completion_cost=g["completion_cost"],
                prompt_cost=g["prompt_cost"],
                error_rate=g["error_rate"],
                latency_p50=g["latency_ptiles"][0]
                if not math.isnan(g["latency_ptiles"][0])
                else None,
                latency_p99=g["latency_ptiles"][1]
                if not math.isnan(g["latency_ptiles"][1])
                else None,
                feedback_stats=group_feedback_stats,
                examples=examples_with_runs,
                min_start_time=g["min_start_time"],
                max_start_time=g["max_start_time"],
            )
            groups.append(group)

        return (
            schemas.GroupedExamplesWithRunsResponse(
                groups=groups[: query_params.limit],
            ),
            len(results) + query_params.offset,
        )


async def fetch_example_as_of_from_sessions(
    session_ids: list[UUID], tenant_id: UUID, ch: ClickhouseClient
) -> datetime | str:
    as_of_from_ch = await ch.fetchval(
        "fetch_latest_run_time",
        """SELECT max(COALESCE(end_time, start_time))
            FROM runs
            WHERE tenant_id = {tenant_id}
            AND session_id IN {session_ids}
            AND is_root = 1""",
        params={
            "session_ids": session_ids,
            "tenant_id": tenant_id,
        },
    )
    async with asyncpg_pool() as db:
        # sdk evaluate() method adds dataset_version to session metadata
        as_of_from_session_metadata = await db.fetch(
            """SELECT extra->'metadata'->>'dataset_version' as version
            FROM tracer_session
            WHERE tenant_id = $1
            AND id = ANY($2)""",
            tenant_id,
            session_ids,
        )
    as_of_from_session_metadata = [
        a.get("version") for a in as_of_from_session_metadata if a is not None
    ]
    as_of = as_of_from_ch
    if as_of_from_session_metadata and all(as_of_from_session_metadata):
        as_of_str = max(as_of_from_session_metadata)
        try:
            as_of = datetime.fromisoformat(as_of_str)
        except ValueError:
            logger.warning(
                "Failed to parse as_of from session metadata",
                as_of_from_session_metadata=as_of_from_session_metadata,
            )
    if as_of is None:
        return "latest"
    if as_of.tzinfo is None:
        as_of = as_of.replace(tzinfo=timezone.utc)
    return as_of


async def fetch_runs_for_comparison_view(
    ch: ClickhouseClient,
    auth: AuthInfo,
    runs: list,
    preview: bool,
    comparative_experiment_id: UUID | None,
    include_feedback: bool = True,
    comparative_session_dict: dict | None = None,
):
    if comparative_session_dict is None:
        comparative_session_dict = {}
    if runs:
        sorting_key_params = {
            # Assumptions: runs guaranteed to have same tenant
            "tenant_id": runs[0]["tenant_id"],
            "session_ids": list(set([run["session_id"] for run in runs])),
            "is_roots": list(set([run["is_root"] for run in runs])),
            "start_times": list(set([run["start_time"] for run in runs])),
            "ids": list(set([run["id"] for run in runs])),
            **comparative_session_dict,
        }

        min_start_time = min(sorting_key_params["start_times"])
        min_start_time = min_start_time.replace(
            microsecond=min_start_time.microsecond // 1000 * 1000
        )
        min_start_time = min_start_time - timedelta(
            milliseconds=settings.TOKEN_STATS_START_TIME_BUFFER_MS
        )
        str_min_start_time = min_start_time.strftime("%Y-%m-%d %H:%M:%S.%f")
        max_start_time = max(sorting_key_params["start_times"])
        max_start_time = max_start_time.replace(
            microsecond=max_start_time.microsecond // 1000 * 1000 + 999
        )
        max_start_time = max_start_time + timedelta(
            milliseconds=settings.TOKEN_STATS_START_TIME_BUFFER_MS
        )
        str_max_start_time = max_start_time.strftime("%Y-%m-%d %H:%M:%S.%f")

        # These are to determine whether to load content from s3
        # in preview mode, we don't fetch from s3 but will return
        # preview and input/outputs in clickhouse (<20kb in prod)
        input_s3_urls_list = [
            {}
            if preview and run.get("inputs_preview") is not None
            else load_json(run.get("inputs_s3_urls")) or {}
            for run in runs
        ]

        output_s3_urls_list = [
            {}
            if preview and run.get("outputs_preview") is not None
            else load_json(run.get("outputs_s3_urls")) or {}
            for run in runs
        ]
        s3_urls_list = [
            # in preview mode only fetch errors from s3
            {
                k: v
                for k, v in (load_json(run.get("s3_urls")) or {}).items()
                if k == ERROR_S3_KEY
            }
            if preview
            else load_json(run.get("s3_urls")) or {}
            for run in runs
        ]
        root_s3_input_urls = [
            input_s3_urls.get(ROOT_S3_KEY) for input_s3_urls in input_s3_urls_list
        ]
        root_s3_output_urls = [
            output_s3_urls.get(ROOT_S3_KEY) for output_s3_urls in output_s3_urls_list
        ]
        (
            feedback,
            tokens,
            s3_inputs,
            s3_outputs,
            s3_errors,
            s3_events,
            s3_extras,
        ) = await asyncio.gather(
            # TODO: the replaceAll below is a temporary fix for square brackets getting interpreted strangely in feedback comments
            # Revert when we fix the ClickHouse client
            ch.fetch(
                "fetch_comparison_feedback_stats",
                f"""
            WITH session_stats AS (
                SELECT
                    key,
                    min(COALESCE(
                        CASE
                            WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                            ELSE NULL
                        END,
                        score
                    )) as session_min_score,
                    max(COALESCE(
                        CASE
                            WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                            ELSE NULL
                        END,
                        score
                    )) as session_max_score
                FROM feedbacks_rmt FINAL
                PREWHERE tenant_id = {{tenant_id}}
                AND session_id IN {{session_ids}}
                AND is_root IN {{is_roots}}
                AND start_time >= {{min_start_time}}
                AND start_time <= {{max_start_time}}
                {"AND (comparative_experiment_id = {comparative_experiment_id} OR comparative_experiment_id is NULL)" if comparative_experiment_id else "AND comparative_experiment_id is NULL"}
                AND run_id IN {{ids}}
                GROUP BY key
            )
            SELECT
                run_id,
                mapKeys(avgMap(map(key, score))) as feedback_keys,
                mapValues(avgMap(map(key, COALESCE(
                    CASE
                        WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                        ELSE NULL
                    END,
                    score
                )))) as feedback_avgs,
                mapValues(stddevPopMap(map(key, COALESCE(
                    CASE
                        WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                        ELSE NULL
                    END,
                    score
                )))) as feedback_stdevs,
                mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
                mapValues(countMap(map(key, score))) as feedback_counts,
                mapValues(sumMap(map(key, JSONExtractString(feedback_source, 'metadata', '__run', 'run_id') != ''))) as feedback_show_arrows,
                mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
                mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
                mapValues(anyMap(map(key, session_min_score))) as session_min_score,
                mapValues(anyMap(map(key, session_max_score))) as session_max_score,
                mapValues(groupArrayMap(map(key, replaceAll(replaceAll(comment, '[', '('), ']', ')')))) as feedback_comments
            FROM feedbacks_rmt FINAL
            JOIN session_stats ON feedbacks_rmt.key = session_stats.key
            PREWHERE tenant_id = {{tenant_id}}
            AND session_id IN {{session_ids}}
            AND is_root IN {{is_roots}}
            AND start_time >= {{min_start_time}}
            AND start_time <= {{max_start_time}}
            {"AND (comparative_experiment_id = {comparative_experiment_id} OR comparative_experiment_id is NULL)" if comparative_experiment_id else "AND comparative_experiment_id is NULL"}
            AND run_id IN {{ids}}
            GROUP BY run_id""",
                params={
                    **sorting_key_params,
                    "min_start_time": str_min_start_time,
                    "max_start_time": str_max_start_time,
                    "comparative_experiment_id": comparative_experiment_id,
                },
            )
            if include_feedback
            else empty(),
            ch.fetch(
                "fetch_runs_token_counts",
                """
            SELECT
                id,
                sum(total_tokens) as total_tokens,
                sum(completion_tokens) as completion_tokens,
                sum(prompt_tokens) as prompt_tokens,
                sum(total_cost) as total_cost,
                sum(completion_cost) as completion_cost,
                sum(prompt_cost) as prompt_cost,
                min(first_token_time) as first_token_time
            FROM runs_token_counts FINAL
            PREWHERE tenant_id = {tenant_id}
            AND session_id IN {session_ids}
            AND is_root IN {is_roots}
            AND start_time >= {min_start_time}
            AND start_time <= {max_start_time}
            AND id IN {ids}
            AND runs_token_counts.total_tokens < 4000000000
            GROUP BY id""",
                params={
                    **sorting_key_params,
                    "min_start_time": str_min_start_time,
                    "max_start_time": str_max_start_time,
                },
            ),
            asyncio.gather(
                *(
                    [
                        get_run_data(s3_path) if s3_path else completed_future(None)
                        for s3_path in root_s3_input_urls
                    ]
                )
            )
            if settings.FF_BLOB_STORAGE_ENABLED and root_s3_input_urls
            else empty(),
            asyncio.gather(
                *(
                    [
                        get_run_data(s3_path) if s3_path else completed_future(None)
                        for s3_path in root_s3_output_urls
                    ]
                )
            )
            if settings.FF_BLOB_STORAGE_ENABLED and root_s3_output_urls
            else empty(),
            asyncio.gather(
                *(
                    [
                        get_run_data(s3_urls.get(ERROR_S3_KEY))
                        if s3_urls and s3_urls.get(ERROR_S3_KEY)
                        else completed_future(None)
                        for s3_urls in s3_urls_list
                    ]
                )
            )
            if settings.FF_BLOB_STORAGE_ENABLED and s3_urls_list
            else empty(),
            asyncio.gather(
                *(
                    [
                        get_run_data(s3_urls.get(EVENTS_S3_KEY))
                        if s3_urls and s3_urls.get(EVENTS_S3_KEY)
                        else completed_future(None)
                        for s3_urls in s3_urls_list
                    ]
                )
            )
            if settings.FF_BLOB_STORAGE_ENABLED and s3_urls_list
            else empty(),
            asyncio.gather(
                *(
                    [
                        get_run_data(s3_urls.get(EXTRA_S3_KEY))
                        if s3_urls and s3_urls.get(EXTRA_S3_KEY)
                        else completed_future(None)
                        for s3_urls in s3_urls_list
                    ]
                )
            )
            if settings.FF_BLOB_STORAGE_ENABLED and s3_urls_list
            else empty(),
        )
    else:
        runs = []
        feedback = []
        tokens = []
        s3_inputs = []
        s3_outputs = []
        s3_errors = []
        s3_events = []
        s3_extras = []

    feedback = {f["run_id"]: f for f in feedback}
    token_counts = {tc["id"]: tc for tc in tokens}
    s3_inputs_map = {run["id"]: result for run, result in zip(runs, s3_inputs)}
    s3_outputs_map = {run["id"]: result for run, result in zip(runs, s3_outputs)}
    s3_errors_map = {run["id"]: result for run, result in zip(runs, s3_errors)}
    s3_events_map = {run["id"]: result for run, result in zip(runs, s3_events)}
    s3_extras_map = {run["id"]: result for run, result in zip(runs, s3_extras)}
    runs_by_reference_example_id = defaultdict(list)

    # load all s3 urls and prepare presigned urls to return in runs regardless of preview
    input_s3_urls_list = [load_json(run.get("inputs_s3_urls")) or {} for run in runs]
    output_s3_urls_list = [load_json(run.get("outputs_s3_urls")) or {} for run in runs]
    s3_url_list = [load_json(run.get("s3_urls")) or {} for run in runs]

    # if the data exists in ClickHouse, skip fetching from S3
    # if the inputs/outputs_preview is present in CH and only preview
    # is requested, skip hitting S3
    input_s3_urls_list = [
        {} if is_valid_field(run.get("inputs")) else input_s3_urls_list[idx]
        for idx, run in enumerate(runs)
    ]

    output_s3_urls_list = [
        {} if is_valid_field(run.get("outputs")) else output_s3_urls_list[idx]
        for idx, run in enumerate(runs)
    ]

    # Don't fetch if data exists in ClickHouse
    s3_urls_list = [
        {
            k: v
            for k, v in s3_urls.items()
            # For keys in KNOWN_S3_KEYS, include them only if the corresponding run field is empty.
            # Keys not in KNOWN_S3_KEYS are included by default.
            if k not in KNOWN_S3_KEYS or not is_valid_field(run.get(KNOWN_S3_KEYS[k]))
        }
        for s3_urls, run in zip(s3_urls_list, runs)
    ]

    presigned_urls = (
        _get_urls_for_s3_fetch(runs, input_s3_urls_list, "inputs_s3_urls")
        + _get_urls_for_s3_fetch(runs, output_s3_urls_list, "outputs_s3_urls")
        + _get_urls_for_s3_fetch(runs, s3_url_list, "s3_urls")
    )

    blob_urls = await arun_in_executor(
        _prepare_blog_storage_dict,
        runs,
        presigned_urls,
    )

    mapped_runs = await asyncio.gather(
        *(
            arun_in_executor(
                map_run,
                auth,
                run,
                feedback.get(run["id"]),
                token_counts.get(run["id"]),
                False,
                None,
                None,
                None,
                blob_urls.get(run["id"], {}).get("inputs_s3_urls"),
                blob_urls.get(run["id"], {}).get("outputs_s3_urls"),
                blob_urls.get(run["id"], {}).get("s3_urls"),
                None,
                s3_inputs_map.get(run["id"]),
                s3_outputs_map.get(run["id"]),
                s3_errors_map.get(run["id"]),
                s3_events_map.get(run["id"]),
                s3_extras_map.get(run["id"]),
                with_inputs_preview=True if preview else False,
                with_outputs_preview=True if preview else False,
            )
            for run in runs
        )
    )
    for mapped_run in mapped_runs:
        runs_by_reference_example_id[mapped_run["reference_example_id"]].append(
            mapped_run
        )
    return runs_by_reference_example_id


@retry_clickhouse_read
async def fetch_runs_comparison_view_csv(
    auth: AuthInfo | ShareDatasetInfo,
    dataset_id: UUID,
    schema: schemas.QueryExampleSchemaWithRuns,
) -> StreamingResponse:
    schema_ = schemas.QueryExampleSchemaWithRuns(
        format="csv",
        **{k: v for k, v in schema.model_dump().items() if k != "format"},
    )
    from app.crud import get_example_runs_from_session_ch

    rows, total = await get_example_runs_from_session_ch(auth, dataset_id, schema_)

    # Track repetitions per example per session
    example_session_rep_counts: dict[str, int] = {}

    # Collect all feedback keys and count reps
    all_feedback_keys = set()
    for row in rows:
        if row.runs:
            for raw_run in row.runs:
                run = raw_run.model_dump()
                session_id = str(run.get("session_id"))
                example_id = str(row.id)

                # Track reps per example-session pair
                example_session_key = f"{example_id}_{session_id}"
                example_session_rep_counts[example_session_key] = (
                    example_session_rep_counts.get(example_session_key, 0) + 1
                )

                if run and "feedback_stats" in run and run["feedback_stats"]:
                    all_feedback_keys.update(run["feedback_stats"].keys())

    # Define CSV headers
    common_fieldnames = ["id", "inputs", "reference_outputs"]
    per_session_fieldnames = [
        "outputs",
        "run",
        "status",
        "error",
        "latency",
        "tokens",
        "total_cost",
    ] + list(all_feedback_keys)

    sessions: list[schemas.TracerSession] = []
    from app.crud import get_tracer_sessions

    sessions_fetch = await get_tracer_sessions(
        auth,
        schemas.FilterQueryParamsForTracerSessionSchema(
            id=schema_.session_ids,
            reference_dataset=[dataset_id],
        ),
    )
    sessions = sessions_fetch[0]
    sessions.sort(key=lambda x: x.name)
    fieldnames = common_fieldnames
    for fieldname in per_session_fieldnames:
        for session in sessions:
            session_id = str(session.id)
            # Get max reps across all examples for this session
            max_reps = max(
                (
                    count
                    for key, count in example_session_rep_counts.items()
                    if key.endswith(f"_{session_id}") and count > 0
                ),
                default=1,
            )
            prefix = ""
            if len(schema_.session_ids) > 1:
                prefix = f"{session.name}_"

            if max_reps > 1:
                fieldnames.extend(
                    [f"{prefix}rep_{i + 1}_{fieldname}" for i in range(max_reps)]
                )
            else:
                fieldnames.append(f"{prefix}{fieldname}")

    # Convert rows to CSV format using StringIO
    output = StringIO()
    writer = csv.DictWriter(
        output, fieldnames=fieldnames, extrasaction="ignore"
    )  # Use extrasaction to skip extra fields
    writer.writeheader()

    for row in rows:
        if row.runs:
            csv_row = {
                "id": row.id,
                "inputs": json.dumps(row.inputs),
                "reference_outputs": json.dumps(row.outputs),
            }

            # Reset rep indices for each example
            example_session_rep_indices: dict[str, int] = {}

            for run in row.runs:
                run = run.model_dump()
                # Extract run data
                run_inputs = run.get("inputs")
                run_outputs = run.get("outputs")
                session_id = run.get("session_id")
                run_error = run.get("error")
                run_extra = run.get("extra")
                run_type = run.get("run_type")
                run_id = run.get("id")
                total_tokens = run.get("total_tokens")
                start_time = run.get("start_time")
                end_time = run.get("end_time")
                total_cost = run.get("total_cost")
                status = run.get("status")

                # Calculate latency
                if start_time and end_time:
                    latency = (
                        datetime.fromisoformat(str(end_time)).timestamp()
                        - datetime.fromisoformat(str(start_time)).timestamp()
                    )
                else:
                    latency = None

                # Extract feedback stats
                feedback_data: Dict[str, Any] = {}

                for key in all_feedback_keys:
                    if "feedback_stats" in run and run["feedback_stats"]:
                        if key in run["feedback_stats"]:
                            if (
                                "values" in run["feedback_stats"][key]
                                and run["feedback_stats"][key]["values"]
                            ):
                                all_keys = list(
                                    run["feedback_stats"][key]["values"].items()
                                )
                                if len(all_keys) > 1:
                                    feedback_data[key] = []
                                    for k, v in all_keys:
                                        if isinstance(v, int):
                                            feedback_data[key].extend([k] * v)
                                        else:
                                            feedback_data[key].append(v)
                                elif len(all_keys) == 1:
                                    if (
                                        isinstance(all_keys[0][1], int)
                                        and all_keys[0][1] > 1
                                    ):
                                        feedback_data[key] = [
                                            all_keys[0][0]
                                        ] * all_keys[0][1]
                                    else:
                                        feedback_data[key] = all_keys[0][0]
                                else:
                                    feedback_data[key] = None
                            else:
                                feedback_data[key] = run["feedback_stats"][key].get(
                                    "avg"
                                )
                        else:
                            feedback_data[key] = None
                    else:
                        feedback_data[key] = None

                # Create a dictionary for the current run
                run_data = {
                    "inputs": run_inputs,
                    "outputs": run_outputs,
                    "session_id": str(session_id),
                    "error": run_error,
                    "extra": run_extra,
                    "run_type": run_type,
                    "id": str(run_id),
                    "status": status,
                }

                key_prefix = ""
                current_session = next(
                    (session for session in sessions if session.id == session_id),
                    None,
                )
                if current_session and len(schema_.session_ids) > 1:
                    key_prefix = current_session.name + "_"

                example_session_key = f"{row.id}_{session_id}"
                if example_session_rep_counts[example_session_key] > 1:
                    # Get and increment the rep index for this example-session pair
                    current_rep = example_session_rep_indices.get(
                        example_session_key, 1
                    )
                    example_session_rep_indices[example_session_key] = current_rep + 1
                    key_prefix = f"{key_prefix}rep_{current_rep}_"

                feedback_data = {
                    f"{key_prefix}{k}": v for k, v in feedback_data.items()
                }
                csv_row = {
                    **csv_row,
                    f"{key_prefix}outputs": json.dumps(run_outputs),
                    f"{key_prefix}run": json.dumps(run_data),
                    f"{key_prefix}latency": latency,
                    f"{key_prefix}status": status,
                    f"{key_prefix}error": run_error,
                    f"{key_prefix}tokens": total_tokens,
                    f"{key_prefix}total_cost": total_cost,
                    **feedback_data,
                }
            writer.writerow(csv_row)

    # Prepare the response with CSV data
    output.seek(0)
    session_names_joined = "_".join([session.name for session in sessions])
    headers = {"Content-Disposition": f'attachment; filename="{session_names_joined}"'}
    return StreamingResponse(
        iter([output.getvalue()]),
        media_type="text/csv",
        headers=headers,
        status_code=206 if total > schema_.limit else 200,
    )


@retry_clickhouse_read
async def fetch_runs_comparison_view(
    auth: AuthInfo | ShareDatasetInfo,
    dataset_id: UUID,
    query_params: schemas.QueryExampleSchemaWithRuns,
    include_feedback: bool = True,
) -> Tuple[List[schemas.ExampleWithRunsCH], int]:
    if not query_params.session_ids:
        raise HTTPException(status_code=400, detail="No session ids provided")

    tenant_id = auth.tenant_id
    use_sort_params = (
        query_params.sort_params is not None and len(query_params.session_ids) == 1
    )
    global_params: dict[str, Any] = {
        "session_ids": query_params.session_ids,
        "tenant_id": tenant_id,
        "limit": query_params.limit + 1,
        "offset": query_params.offset,
        "feedback_sort_key": query_params.sort_params.sort_by
        if query_params.sort_params
        else None,
    }

    if len(query_params.session_ids) == 1:
        # fetch session start_time from postgres
        async with asyncpg_pool() as pg:
            session = await pg.fetchrow(
                "SELECT start_time FROM tracer_session WHERE id = $1",
                query_params.session_ids[0],
            )
            if session:
                global_params["start_time"] = session["start_time"].strftime(
                    "%Y-%m-%d %H:%M:%S.%f"
                )

    preview = query_params.preview

    if query_params.filters:
        session_filters = []
        for session_id, queries in query_params.filters.items():
            # build up the query
            where = Operation(operator=Operator.AND, arguments=[])
            args = where.arguments
            args.append(Comparison(C.EQ, "session_id", session_id))
            args.append(Comparison(C.EQ, "is_root", True))
            if tenant_id:
                args.append(Comparison(C.EQ, "tenant_id", tenant_id))
            if isinstance(auth, ShareDatasetInfo) and auth.dataset_id:
                args.append(Comparison(C.EQ, "reference_dataset_id", auth.dataset_id))

            args.append(Comparison(C.EQ, "is_trace_expired", False))
            for query in queries:
                args.append(parse_as_filter_directive(query))
            session_filters.append(where)

        examples_query = ""
        for idx, session_filter in enumerate(session_filters):
            try:
                sql_where, params, _, _, _ = session_filter.accept(
                    SqlVisitorClickhouse(
                        attributes=RUN_ATTRIBUTES,
                        main_table="runs",
                        param_suffix=str(idx),
                    )
                )
            except (ValueError, TypeError) as e:
                raise HTTPException(status_code=400, detail=str(e)) from e
            if examples_query:
                examples_query += """
            INTERSECT
                """
            if use_sort_params:
                examples_query += f"""
                SELECT DISTINCT reference_example_id
                {sql_where}
                """
            else:
                examples_query += f"""
                SELECT DISTINCT reference_example_id
                {sql_where}
                """

            global_params = {**global_params, **params}

        example_ids_query = build_example_ids_query(
            use_sort_params=use_sort_params,
            has_filters=True,
            examples_query=examples_query,
            feedback_sort_order="DESC"
            if query_params.sort_params
            and query_params.sort_params.sort_order == "DESC"
            else "ASC",
            has_start_time=bool(global_params.get("start_time")),
        )
    else:
        example_ids_query = build_example_ids_query(
            use_sort_params=use_sort_params,
            has_filters=False,
            examples_query="",
            feedback_sort_order="DESC"
            if query_params.sort_params
            and query_params.sort_params.sort_order == "DESC"
            else "ASC",
            has_start_time=bool(global_params.get("start_time")),
        )

    where_clause_runs_fetch = f"reference_example_id IN ({example_ids_query})"

    # TODO: be more selective about which run fields we fetch
    runs_select = f"""
    SELECT tenant_id, session_id, is_root, start_time, id FROM runs
    WHERE {where_clause_runs_fetch}
    AND is_root = 1
    AND session_id IN ({{session_ids}})
    AND tenant_id = '{tenant_id}'
    """

    runs_settings = f"""SETTINGS multiple_joins_try_to_keep_original_names = 1,
    optimize_read_in_order = 1,
    max_threads = {settings.FETCH_RUNS_COMPARISON_VIEW_MAX_THREADS}
    """

    runs_query = f"""
    WITH filtered_runs_cte AS (
    {runs_select}
    )
    SELECT {run_fields_for_comparison_view}
    FROM runs
    PREWHERE (
        tenant_id, 
        session_id, 
        is_root, 
        start_time, 
        id
    ) IN (
        SELECT 
            tenant_id, 
            session_id, 
            is_root, 
            start_time, 
            id 
        FROM filtered_runs_cte
    )
    ORDER BY toString(reference_example_id) ASC, modified_at DESC
    LIMIT 1 BY id
    {runs_settings}
    """
    pg_examples = []
    comparative_session_dict: dict[str, Any] = {}
    async with clickhouse_client(ClickhouseClient.USER_QUERIES) as ch:
        runs = await ch.fetch("fetch_comparison_runs", runs_query, params=global_params)
        if runs:
            as_of = await fetch_example_as_of_from_sessions(
                query_params.session_ids, tenant_id, ch
            )

            # first, fetch the examples
            example_ids = {run["reference_example_id"] for run in runs}
            async with asyncpg_pool() as pg:
                pg_examples = await examples_module.fetch.fetch_examples(
                    pg,
                    auth=auth,
                    query_params=schemas.FilterQueryParamsForExampleSchema(
                        dataset=dataset_id,
                        id=example_ids,
                        as_of=as_of or "latest",
                        limit=query_params.limit,
                    ),
                )

                # short circuit if no examples
                if not pg_examples:
                    return [], 0

                for example in pg_examples:
                    if example.attachment_urls:
                        example.attachment_urls = (
                            populate_attachment_urls_correct_format(
                                example.attachment_urls
                            )
                        )

            comparative_session_dict = {
                "comparative_experiment_id": query_params.comparative_experiment_id
                if query_params.comparative_experiment_id
                else {}
            }

            runs_by_reference_example_id = await fetch_runs_for_comparison_view(
                ch,
                auth,
                runs,
                preview,
                query_params.comparative_experiment_id,
                include_feedback,
                comparative_session_dict,
            )

    examples_with_runs = [
        schemas.ExampleWithRunsCH(
            **{
                **example.model_dump(),
                "run_count": len(runs_by_reference_example_id[example.id]),
                "runs": runs_by_reference_example_id[example.id],
            }
        )
        for example in pg_examples
    ]
    # do a final sort by the average score now that we have all of the examples
    if use_sort_params and query_params.sort_params:
        desc = query_params.sort_params.sort_order == "DESC"

        def get_sort_key(example: schemas.ExampleWithRunsCH):
            scores = []
            for run in example.runs:
                if not run.feedback_stats:
                    continue
                if query_params.sort_params is None:
                    # should never happen because we check above, this is to make python happy
                    continue
                feedback_key_stats = run.feedback_stats.get(
                    query_params.sort_params.sort_by
                )
                if not feedback_key_stats or feedback_key_stats.get("avg") is None:
                    continue
                scores.append(feedback_key_stats.get("avg"))

            valid_scores = [score for score in scores if score is not None]
            if not valid_scores:
                return float("-inf")
            return sum(valid_scores) / len(valid_scores)

        examples_with_runs = sorted(
            examples_with_runs,
            key=get_sort_key,
            reverse=desc,
        )
    else:
        examples_with_runs = sorted(
            examples_with_runs,
            key=lambda example: example.id,
        )

    return (
        examples_with_runs[: query_params.limit],
        len(pg_examples) + query_params.offset,
    )


@retry_clickhouse_read
async def _fetch_delta_ch(query: str, params: dict[str, Any]):
    async with clickhouse_client(ClickhouseClient.USER_QUERIES) as ch:
        return await ch.fetch("fetch_feedback_delta", query, params=params)


@retry_clickhouse_read
async def _fetch_is_lower_ch(tenant_id: UUID, feedback_key: str):
    async with clickhouse_client(ClickhouseClient.USER_QUERIES) as ch:
        return await ch.fetchval(
            "check_lower_score_better_feedback_configs",
            """
            SELECT is_lower_score_better
              FROM feedback_configs FINAL
             WHERE tenant_id = {tenant_id}
               AND feedback_key = {feedback_key}
               AND is_deleted = 0
            """,
            params={"tenant_id": tenant_id, "feedback_key": feedback_key},
        )


@retry_asyncpg
async def _fetch_is_lower_pg(tenant_id: UUID, feedback_key: str):
    async with database.asyncpg_conn() as conn:
        return await conn.fetchval(
            """
            SELECT is_lower_score_better
              FROM feedback_configs
             WHERE tenant_id = $1
               AND feedback_key = $2
            """,
            tenant_id,
            feedback_key,
        )


async def fetch_feedback_delta(
    auth: AuthInfo | ShareDatasetInfo,
    dataset_id: UUID,
    query_params: schemas.QueryFeedbackDelta,
) -> Tuple[schemas.SessionFeedbackDelta, int]:
    if not query_params.comparison_session_ids:
        raise HTTPException(status_code=400, detail="No session ids provided")
    if dataset_id is None:
        raise HTTPException(status_code=400, detail="No dataset id provided")
    if isinstance(auth, ShareDatasetInfo) and dataset_id != auth.dataset_id:
        raise HTTPException(status_code=403, detail="Forbidden")
    async with asyncpg_pool() as pg:
        pg_sessions = await pg.fetch(
            """SELECT id, tenant_id
                FROM tracer_session
                WHERE id = any($1)"""
            + (
                " AND tenant_id = $3 AND reference_dataset_id = $2"
                if isinstance(auth, AuthInfo)
                else " AND reference_dataset_id = $2"
            ),
            query_params.comparison_session_ids + [query_params.baseline_session_id],
            dataset_id,
            *([auth.tenant_id] if isinstance(auth, AuthInfo) else []),
        )
    if (
        not pg_sessions
        or len(pg_sessions) != len(query_params.comparison_session_ids) + 1
    ):
        return schemas.SessionFeedbackDelta(feedback_deltas={}), 0

    tenant_id = UUID(str(pg_sessions[0]["tenant_id"]))
    global_params: dict[str, Any] = {
        "baseline_session_id": query_params.baseline_session_id,
        "comparison_session_ids": query_params.comparison_session_ids,
        "tenant_id": tenant_id,
        "feedback_key": query_params.feedback_key,
        "limit": query_params.limit + 1,
        "offset": query_params.offset,
    }
    if query_params.filters:
        session_filters = []
        for session_id, queries in query_params.filters.items():
            # build up the query
            where = Operation(operator=Operator.AND, arguments=[])
            args = where.arguments
            args.append(Comparison(C.EQ, "session_id", session_id))
            args.append(Comparison(C.EQ, "is_root", True))
            if isinstance(auth, AuthInfo):
                args.append(Comparison(C.EQ, "tenant_id", tenant_id))
            else:
                args.append(Comparison(C.EQ, "reference_dataset_id", auth.dataset_id))
            for query in queries:
                args.append(parse_as_filter_directive(query))
            session_filters.append(where)

        examples_query = ""
        for idx, session_filter in enumerate(session_filters):
            try:
                sql_where, params, _, _, _ = session_filter.accept(
                    SqlVisitorClickhouse(
                        attributes=RUN_ATTRIBUTES,
                        main_table="runs",
                        param_suffix=str(idx),
                    )
                )
            except (ValueError, TypeError) as e:
                raise HTTPException(status_code=400, detail=str(e)) from e
            if examples_query:
                examples_query += """
            INTERSECT
                """
            examples_query += f"""
            SELECT
            DISTINCT reference_example_id
            {sql_where}
            """

            global_params = {**global_params, **params}
    else:
        # Just use the example ids from postgres
        examples_query = None

    ch_feedback_query = f"""
    WITH feedbacks_cte AS (
        SELECT *
        FROM feedbacks_rmt FINAL
        WHERE tenant_id = {{tenant_id}}
        AND is_root = 1
        AND key = {{feedback_key}}
        AND has(arrayMap(x -> cast(x as String), arrayConcat({{comparison_session_ids}}, [{{baseline_session_id}}])), cast(session_id as String))
        {"AND comparative_experiment_id = {comparative_experiment_id}" if query_params.comparative_experiment_id else ""}
    ),
    scores as (
        SELECT
            session_id,
            reference_example_id,
            avg(
                COALESCE(
                    CASE
                        WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                        ELSE NULL
                    END,
                    score
                )
            ) as score
        FROM feedbacks_cte AS feedbacks
        INNER JOIN (
            SELECT reference_example_id, tenant_id, session_id, start_time, id, is_root
            FROM runs
            WHERE tenant_id = {{tenant_id}}
            AND session_id IN {{comparison_session_ids}}
            AND is_root = 1
            {f"AND reference_example_id IN ({examples_query})" if examples_query is not None else ""}
        ) AS runs ON feedbacks.tenant_id = runs.tenant_id
        AND feedbacks.session_id = runs.session_id
        AND feedbacks.is_root = runs.is_root
        AND feedbacks.start_time = runs.start_time
        AND feedbacks.run_id = runs.id
        GROUP BY reference_example_id, session_id
    ),
    baseline_scores as (
        SELECT
            reference_example_id,
            avg(
                COALESCE(
                    CASE
                        WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                        ELSE NULL
                    END,
                    score
                )
            ) as score
        FROM feedbacks_cte AS feedbacks
        INNER JOIN (
            SELECT reference_example_id, tenant_id, session_id, start_time, id, is_root
            FROM runs
            WHERE tenant_id = {{tenant_id}}
            AND session_id = {{baseline_session_id}}
            AND is_root = 1
            {f"AND reference_example_id IN ({examples_query})" if examples_query is not None else ""}
        ) AS runs ON feedbacks.tenant_id = runs.tenant_id
        AND feedbacks.session_id = runs.session_id
        AND feedbacks.is_root = runs.is_root
        AND feedbacks.start_time = runs.start_time
        AND feedbacks.run_id = runs.id
        GROUP BY reference_example_id
    )

    SELECT
        session_id,
        groupArrayIf(reference_example_id, scores.score > bs.score) AS improved_examples,
        groupArrayIf(reference_example_id, scores.score < bs.score) AS regressed_examples
    FROM scores
    INNER JOIN baseline_scores bs
        ON scores.reference_example_id = bs.reference_example_id
    GROUP BY session_id
    ORDER BY session_id ASC
    LIMIT {{limit}} OFFSET {{offset}}
    SETTINGS max_threads = {settings.FETCH_FEEDBACK_DELTA_MAX_THREADS}
"""
    if query_params.comparative_experiment_id:
        global_params["comparative_experiment_id"] = (
            query_params.comparative_experiment_id
        )
    if is_feedback_configs_fetch_enabled(auth):
        feedback, is_lower_score_better = await asyncio.gather(
            _fetch_delta_ch(ch_feedback_query, global_params),
            _fetch_is_lower_pg(tenant_id, query_params.feedback_key),
        )
    else:
        feedback, is_lower_score_better = await asyncio.gather(
            _fetch_delta_ch(ch_feedback_query, global_params),
            _fetch_is_lower_ch(tenant_id, query_params.feedback_key),
        )

    feedback_deltas = {
        row["session_id"]: {
            "improved_examples": row["regressed_examples"]
            if is_lower_score_better is True
            else row["improved_examples"],
            "regressed_examples": row["improved_examples"]
            if is_lower_score_better is True
            else row["regressed_examples"],
        }
        for row in feedback
    }
    return (
        schemas.SessionFeedbackDelta(feedback_deltas=feedback_deltas),
        len(feedback_deltas),
    )


@retry_clickhouse_read
async def hydrate_runs(
    auth: AuthInfo,
    runs: list[dict[str, Any]],
    select: list[schemas.RunSelect],
    clickhouse_client_type: ClickhouseClient = ClickhouseClient.USER_QUERIES,
) -> list[dict[str, Any]]:
    """
    Hydrate runs with additional fields specified in the `select` parameter.
    """

    # Extract run IDs and tenant/session information
    run_ids = [run["id"] for run in runs]

    start_times = list(set([datetime.fromisoformat(run["start_time"]) for run in runs]))

    sorting_key_params = {
        # runs guaranteed to have same tenant
        "tenant_id": auth.tenant_id,
        "session_ids": list(set([run["session_id"] for run in runs])),
        "is_roots": list(set([run["is_root"] for run in runs])),
        "start_times": start_times,
        "ids": list(set(run_ids)),
    }

    min_start_time = min(start_times)
    min_start_time = min_start_time.replace(
        microsecond=min_start_time.microsecond // 1000 * 1000
    )
    min_start_time = min_start_time - timedelta(
        milliseconds=settings.TOKEN_STATS_START_TIME_BUFFER_MS
    )
    str_min_start_time = min_start_time.strftime("%Y-%m-%d %H:%M:%S.%f")
    max_start_time = max(start_times)
    max_start_time = max_start_time.replace(
        microsecond=max_start_time.microsecond // 1000 * 1000 + 999
    )
    max_start_time = max_start_time + timedelta(
        milliseconds=settings.TOKEN_STATS_START_TIME_BUFFER_MS
    )
    str_max_start_time = max_start_time.strftime("%Y-%m-%d %H:%M:%S.%f")

    # Fetch additional data based on the `select` parameter
    async with clickhouse_client(clickhouse_client_type) as ch, asyncpg_pool() as pg:
        # Create tasks for fetching data in parallel
        in_dataset_task = (
            asyncio.create_task(
                pg.fetch(
                    """SELECT DISTINCT source_run_id
               FROM examples_log
               WHERE source_run_id = ANY($1)
               AND examples_log.inputs IS NOT NULL""",
                    run_ids,
                )
            )
            if schemas.RunSelect.in_dataset in select
            else empty()
        )

        last_queued_at_task = (
            asyncio.create_task(
                pg.fetch(
                    """SELECT run_id, MAX(added_at) AS last_queued_at
               FROM (
                   SELECT run_id, added_at
                   FROM annotation_queue_runs
                   WHERE annotation_queue_runs.run_id = ANY($1)
                   UNION ALL
                   SELECT run_id, added_at
                   FROM annotation_queue_runs_archive
                   WHERE annotation_queue_runs_archive.run_id = ANY($1)
               ) AS combined_runs
               GROUP BY run_id""",
                    run_ids,
                )
            )
            if schemas.RunSelect.last_queued_at in select
            else empty()
        )

        token_counts_task = (
            asyncio.create_task(
                ch.fetch(
                    "fetch_runs_token_counts",
                    """
            SELECT
                id,
                SUM(total_tokens) as total_tokens,
                SUM(completion_tokens) as completion_tokens,
                SUM(prompt_tokens) as prompt_tokens,
                SUM(total_cost) as total_cost,
                SUM(completion_cost) as completion_cost,
                SUM(prompt_cost) as prompt_cost,
                MIN(first_token_time) as first_token_time
            FROM runs_token_counts FINAL
            PREWHERE tenant_id = {tenant_id}
            AND session_id IN {session_ids}
            AND is_root IN {is_roots}
            AND start_time >= {min_start_time}
            AND start_time <= {max_start_time}
            AND id IN {ids}
            AND runs_token_counts.total_tokens < 4000000000
            GROUP BY id""",
                    params={
                        **sorting_key_params,
                        "min_start_time": str_min_start_time,
                        "max_start_time": str_max_start_time,
                    },
                )
            )
            if any(
                field in select
                for field in [
                    schemas.RunSelect.total_tokens,
                    schemas.RunSelect.completion_tokens,
                    schemas.RunSelect.prompt_tokens,
                    schemas.RunSelect.first_token_time,
                    schemas.RunSelect.total_cost,
                    schemas.RunSelect.completion_cost,
                    schemas.RunSelect.prompt_cost,
                ]
            )
            else empty()
        )

        # TODO: the replaceAll below is a temporary fix for square brackets getting interpreted strangely in feedback comments
        # Revert when we fix the ClickHouse client
        feedback_stats_task = (
            asyncio.create_task(
                ch.fetch(
                    "fetch_runs_feedback_stats",
                    """
            WITH session_stats AS (
            SELECT
                key,
                min(COALESCE(
                CASE
                    WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                    ELSE NULL
                END,
                score
                )) as session_min_score,
                max(COALESCE(
                CASE
                    WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                    ELSE NULL
                END,
                score
                )) as session_max_score
            FROM feedbacks_rmt FINAL
            PREWHERE tenant_id = {tenant_id}
            AND session_id IN {session_ids}
            AND is_root IN {is_roots}
            AND start_time >= {min_start_time}
            AND start_time <= {max_start_time}
            AND run_id IN {ids}
            GROUP BY key
            )
            SELECT
            run_id,
            mapKeys(avgMap(map(key, score))) as feedback_keys,
            mapValues(avgMap(map(key, COALESCE(
                CASE
                WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                ELSE NULL
                END,
                score
            )))) as feedback_avgs,
            mapValues(stddevPopMap(map(key, COALESCE(
                CASE
                WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
                ELSE NULL
                END,
                score
            )))) as feedback_stdevs,
            mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors,
            mapValues(countMap(map(key, score))) as feedback_counts,
            mapValues(sumMap(map(key, JSONExtractString(feedback_source, 'metadata', '__run', 'run_id') != ''))) as feedback_show_arrows,
            mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
            mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
            mapValues(anyMap(map(key, session_min_score))) as session_min_score,
            mapValues(anyMap(map(key, session_max_score))) as session_max_score,
            mapValues(groupArrayMap(map(key, replaceAll(replaceAll(comment, '[', '('), ']', ')')))) as feedback_comments
            FROM feedbacks_rmt FINAL
            JOIN session_stats ON feedbacks_rmt.key = session_stats.key
            PREWHERE tenant_id = {tenant_id}
            AND session_id IN {session_ids}
            AND is_root IN {is_roots}
            AND start_time >= {min_start_time}
            AND start_time <= {max_start_time}
            AND run_id IN {ids}
            GROUP BY run_id""",
                    params={
                        **sorting_key_params,
                        "min_start_time": str_min_start_time,
                        "max_start_time": str_max_start_time,
                    },
                )
            )
            if schemas.RunSelect.feedback_stats in select
            else empty()
        )

        # Gather results
        tasks = [
            in_dataset_task,
            last_queued_at_task,
            token_counts_task,
            feedback_stats_task,
        ]
        (
            in_dataset_result,
            last_queued_at_result,
            token_counts_result,
            feedback_stats_result,
        ) = await asyncio.gather(*tasks)

        # Process results
        in_dataset = (
            {str(r["source_run_id"]) for r in in_dataset_result}
            if in_dataset_result is not None
            else set()
        )

        last_queued_at = (
            {str(r["run_id"]): r["last_queued_at"] for r in last_queued_at_result}
            if last_queued_at_result is not None
            else {}
        )

        token_counts = (
            {str(tc["id"]): tc for tc in token_counts_result}
            if token_counts_result is not None
            else {}
        )

        feedback_stats = (
            {str(f["run_id"]): f for f in feedback_stats_result}
            if feedback_stats_result is not None
            else {}
        )

    mapped_runs = await asyncio.gather(
        *(
            arun_in_executor(
                map_run,
                auth,
                run,
                feedback_stats.get(run["id"]),
                token_counts.get(run["id"]),
                run["id"] in in_dataset,
                last_queued_at.get(run["id"]),
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                with_inputs_preview=True,
                with_outputs_preview=True,
            )
            for run in runs
        )
    )

    return mapped_runs
