import asyncio
import gc
import uuid
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import Awaitable, Callable, List, Optional
from uuid import UUID

import fastparquet
import orjson
import pandas as pd
import structlog
from fastapi import HTTPException
from lc_config.settings import shared_settings as settings
from lc_config.utils import arun_in_executor
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from redis.asyncio.lock import Lock
from s3fs import S3FileSystem

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.models.query_lang.parse import Comparator as C
from app.models.query_lang.parse import (
    Comparison,
    Operation,
    Operator,
    parse_as_filter_directive,
)
from app.models.query_lang.translate import SqlVisitorClickhouse, convert_datetime
from app.models.runs.attrs import RUN_ATTRIBUTES
from app.models.runs.fetch_ch import fetch_runs
from app.schemas import (
    BulkExportCompression,
    BulkExportDestinationS3Config,
    BulkExportDestinationS3Credentials,
    RunSelect,
)
from app.utils import gated_coro

logger = structlog.get_logger(__name__)

export_semaphore = asyncio.Semaphore(settings.ASYNC_WORKER_SEMAPHORE)

LARGE_RUN_KEYS = ["inputs", "outputs", "events", "error", "extra", "tags"]

# Define schema based on the run data structure
RUN_PQ_SCHEMA = {
    "id": "string",
    "tenant_id": "string",
    "name": "string",
    "start_time": "datetime64[us]",
    "end_time": "datetime64[us]",
    "extra": "string",
    "error": "string",
    "inputs": "string",
    "outputs": "string",
    "events": "string",
    "tags": "string",
    "run_type": "string",
    "is_root": "bool",
    "session_id": "string",
    "parent_run_id": "string",
    "trace_id": "string",
    "dotted_order": "string",
    "status": "string",
    "parent_run_ids": "string",
    "feedback_stats": "string",
    "reference_example_id": "string",
    "total_tokens": "int32",
    "prompt_tokens": "int32",
    "completion_tokens": "int32",
    "total_cost": "float64",
    "prompt_cost": "float64",
    "completion_cost": "float64",
    "first_token_time": "datetime64[us]",
    "trace_tier": "string",
}


@dataclass
class ExportResult:
    export_id: UUID
    export_path: str
    files: List[str]
    # This is the status the run should be changed to
    new_status: Optional[schemas.BulkExportRunStatus] = None


def _write_parquet_to_s3(
    group_data: list[dict],
    parquet_file_path: str,
    s3: S3FileSystem,
    compression: Optional[str] = "SNAPPY",
    estimated_payload_size: Optional[int] = None,
) -> str:
    logger.info(
        "Writing Parquet file to S3",
        parquet_file_path=parquet_file_path,
        rows_written=len(group_data),
        compression=compression,
        estimated_payload_size=estimated_payload_size,
    )
    df = pd.DataFrame(group_data)
    fastparquet.write(
        parquet_file_path, df, open_with=s3.open, compression=compression, stats=False
    )
    logger.info(
        "Wrote Parquet file to S3",
        parquet_file_path=parquet_file_path,
        rows_written=len(group_data),
        compression=compression,
        estimated_payload_size=estimated_payload_size,
    )
    return parquet_file_path


def _calculate_delta(count: int) -> int:
    if count <= settings.BULK_EXPORT_MEDIUM_PARTITION_TARGET_SIZE:
        return settings.BULK_EXPORT_MEDIUM_PARTITION_TARGET_TIME_DELTA
    return settings.BULK_EXPORT_LARGE_PARTITION_TARGET_TIME_DELTA


async def _calculate_partitions_for_data_counts(
    data_counts: list[tuple[datetime, datetime, int]],
) -> list[tuple[datetime, datetime]]:
    result_partitions = []
    for start_datetime, end_datetime, count in data_counts:
        if count <= settings.BULK_EXPORT_PARTITION_TARGET_SIZE:
            result_partitions.append((start_datetime, end_datetime))
        else:
            delta = _calculate_delta(count)
            current = start_datetime
            while current < end_datetime:
                next_start_time = min(current + timedelta(seconds=delta), end_datetime)
                result_partitions.append((current, next_start_time))
                current = next_start_time
    return result_partitions


def create_s3fs_connection(
    s3_creds: Optional[BulkExportDestinationS3Credentials],
    s3_config: BulkExportDestinationS3Config,
) -> S3FileSystem:
    s3_args: dict[str, str | dict] = {
        # Required until GCS supports this https://github.com/boto/boto3/issues/4392
        "config_kwargs": {
            "request_checksum_calculation": "when_required",
            "response_checksum_validation": "when_required",
        },
    }
    if s3_creds:
        s3_args["key"] = s3_creds.access_key_id
        s3_args["secret"] = s3_creds.secret_access_key
        if s3_creds.session_token:
            s3_args["token"] = s3_creds.session_token
    if s3_config.endpoint_url:
        s3_args["endpoint_url"] = s3_config.endpoint_url
    if s3_config.region:
        s3_args["client_kwargs"] = {"region_name": s3_config.region}

    logger.info(
        "Configuring s3 connection",
        endpoint_url=s3_config.endpoint_url,
        region=s3_config.region,
        bucket=s3_config.bucket_name,
        prefix=s3_config.prefix,
        access_key_id_last_4=f"****{s3_creds.access_key_id[-4:] if s3_creds else ''}",
        secret_access_key_last_4=f"****{s3_creds.secret_access_key[-4:] if s3_creds else ''}",
    )
    return S3FileSystem(**s3_args)


async def validate_bulk_export_destination_connection(
    config: dict, credentials: Optional[BulkExportDestinationS3Credentials]
) -> None:
    # Cloud environments require credentials
    if not settings.IS_SELF_HOSTED and not credentials:
        raise HTTPException(
            status_code=400,
            detail="S3 credentials are required.",
        )

    s3_config = schemas.BulkExportDestinationS3Config(**config)
    s3 = create_s3fs_connection(credentials, s3_config)

    test_filename = f"{uuid.uuid4()}.txt"
    s3_path = f"{s3_config.prefix}/tmp/{test_filename}"
    full_s3_path = f"{s3_config.bucket_name}/{s3_path}"
    logger.info(f"Attempting to write test file to S3 path: {full_s3_path}")

    try:
        # Use put to write the empty file to S3
        s3.touch(full_s3_path)
        try:
            s3.rm_file(full_s3_path)
        except Exception as e:
            # this is ok - may not have rm permission
            logger.warning(f"Error removing test file: {e}")
    except Exception as e:
        logger.error(f"S3 destination validation failed: {type(e).__name__}: {str(e)}")
        raise HTTPException(
            status_code=400, detail=f"Failed to validate S3 destination: {str(e)}"
        )


def get_where_clause(
    tenant_id: str,
    session_id: str,
    export_start_time: str,
    export_end_time: str,
    filter: str | None,
) -> tuple[str, dict]:
    where_clause = Operation(
        operator=Operator.AND,
        arguments=[
            Comparison(C.EQ, "tenant_id", str(tenant_id)),
            Comparison(C.EQ, "session_id", str(session_id)),
            Comparison(C.GTE, "start_time", convert_datetime(export_start_time)),
            Comparison(C.LT, "start_time", convert_datetime(export_end_time)),
        ],
    )

    if filter:
        filter_directive = parse_as_filter_directive(filter)
        where_clause.arguments.append(filter_directive)

    from_join_where, from_join_where_params, _, _, _ = where_clause.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs",
        )
    )

    return from_join_where, from_join_where_params


def validate_bulk_export_params(
    auth: AuthInfo, payload: schemas.BulkExportCreate
) -> None:
    try:
        get_where_clause(
            tenant_id=str(auth.tenant_id),
            session_id=str(payload.session_id),
            export_start_time=convert_datetime(payload.start_time),
            export_end_time=convert_datetime(payload.end_time),
            filter=payload.filter,
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


async def calculate_partitions(
    tenant_id: UUID,
    session_id: UUID,
    export_start_time: datetime,
    export_end_time: datetime,
    filter: str | None,
) -> list[tuple[datetime, datetime]]:
    async with clickhouse_client(
        ClickhouseClient(settings.BULK_EXPORT_CLICKHOUSE_CLIENT)
    ) as ch:
        # Get the where clause for the query
        from_join_where, from_join_where_params = get_where_clause(
            tenant_id=str(tenant_id),
            session_id=str(session_id),
            export_start_time=convert_datetime(export_start_time),
            export_end_time=convert_datetime(export_end_time),
            filter=filter,
        )

        # Query the number of runs that match per day
        partitions_select = f"""
        SELECT toStartOfInterval(start_time, INTERVAL 1 DAY), count(1)
        {from_join_where}
        GROUP BY 1
        ORDER BY 1 ASC
        SETTINGS max_memory_usage = 5_000_000_000
        """
        runs = await ch.fetch(
            "fetch_partition_sizes",
            partitions_select,
            params=from_join_where_params,
        )

        data_counts: list[tuple[datetime, datetime, int]] = []
        for run in runs:
            # Datetimes are UTC in CH but need to be timezone aware
            start = run[0].replace(tzinfo=timezone.utc)
            end = min(start + timedelta(days=1), export_end_time)
            data_counts.append((start, end, run[1]))
        result = await _calculate_partitions_for_data_counts(data_counts)
        logger.info("Calculated partitions", partitions=result, data_counts=data_counts)
        # update initial partition time if needed as partitions are bucketed to start of day
        if result and result[0][0] < export_start_time:
            result[0] = (export_start_time, result[0][1])
        return result


async def _extend_lock_if_present(
    lock: Optional[Lock],
) -> None:
    if lock:
        await lock.extend(settings.DATA_EXPORT_LOCK_TIMEOUT_SEC)


def _prepare_run(run: dict) -> dict:
    result = {
        "id": str(run["id"]),
        "tenant_id": str(run["tenant_id"]),
        "name": run["name"],
        "start_time": run["start_time"],
        "end_time": run["end_time"],
        "extra": orjson.dumps(run["extra"]).decode("utf-8"),
        "error": run["error"],
        "inputs": orjson.dumps(run["inputs"] if run["inputs"] else {}).decode("utf-8"),
        "outputs": orjson.dumps(run["outputs"] if run["outputs"] else {}).decode(
            "utf-8"
        ),
        "events": orjson.dumps(run["events"] if run["events"] else []).decode("utf-8"),
        "tags": orjson.dumps(run["tags"] if run["tags"] else []).decode("utf-8"),
        "run_type": run["run_type"],
        "is_root": run["is_root"],
        "session_id": str(run["session_id"]),
        "parent_run_id": str(run["parent_run_id"]) if run["parent_run_id"] else None,
        "trace_id": str(run["trace_id"]),
        "dotted_order": run["dotted_order"],
        "status": run["status"],
        "parent_run_ids": orjson.dumps(
            run["parent_run_ids"] if run["parent_run_ids"] else []
        ).decode("utf-8"),
        "feedback_stats": orjson.dumps(run["feedback_stats"]).decode("utf-8")
        if run["feedback_stats"]
        else None,
        "reference_example_id": str(run["reference_example_id"])
        if run["reference_example_id"]
        else None,
        "total_tokens": run["total_tokens"],
        "prompt_tokens": run["prompt_tokens"],
        "completion_tokens": run["completion_tokens"],
        "total_cost": run["total_cost"],
        "prompt_cost": run["prompt_cost"],
        "completion_cost": run["completion_cost"],
        "first_token_time": run["first_token_time"],
        "trace_tier": run["trace_tier"],
    }
    # clean up memory
    for key in LARGE_RUN_KEYS:
        if key in result:
            del run[key]
    return result


def _estimated_payload_size(group_data: list[dict]) -> int:
    """Estimate large items from the payload"""
    keys_to_sum = LARGE_RUN_KEYS
    return sum(
        sum(
            len(row.get(key, ""))
            for key in keys_to_sum
            if key in row and row[key] is not None
        )
        for row in group_data
    )


async def export_partition(
    auth: AuthInfo,
    s3_config: schemas.BulkExportDestinationS3Config,
    s3_creds: Optional[schemas.BulkExportDestinationS3Credentials],
    export_id: UUID,
    run_id: UUID,
    tenant_id: UUID,
    session_id: UUID,
    export_start_time: datetime,
    export_end_time: datetime,
    filter: str | None,
    compression: BulkExportCompression = BulkExportCompression.SNAPPY,
    lock: Optional[Lock] = None,
    latest_progress: Optional[schemas.BulkExportRunProgress] = None,
    # Function to report progress: (auth, export_id, run_id, progress)
    report_progress_fn: Optional[
        Callable[[AuthInfo, UUID, UUID, schemas.BulkExportRunProgress], Awaitable[None]]
    ] = None,
    # This function is checked between exporting files and halts execution if it returns a status: (auth, export_id)
    should_update_status_fn: Optional[
        Callable[[AuthInfo, UUID], Awaitable[Optional[schemas.BulkExportRunStatus]]]
    ] = None,
) -> ExportResult:
    """Export a partition of data for a tenant."""
    compression_val = (
        None if compression == BulkExportCompression.NONE else compression.value.upper()
    )
    export_path = f"{s3_config.prefix}/export_id={str(export_id)}/tenant_id={str(tenant_id)}/session_id={str(session_id)}/runs"
    s3_full_dest = f"{s3_config.bucket_name}/{export_path}/year={{year}}/month={{month}}/day={{day}}/{{filename}}.parquet"

    if latest_progress:
        current_cursor = latest_progress.latest_cursor
        rows_exported = latest_progress.rows_written
        parquet_files = latest_progress.exported_files
    else:
        current_cursor = None
        rows_exported = 0
        parquet_files = []

    # short-circuit depending on bulk export status
    if should_update_status_fn and (
        new_status := await should_update_status_fn(auth, export_id)
    ):
        return ExportResult(
            export_id, export_path, sorted(parquet_files), new_status=new_status
        )

    if (
        latest_progress
        and latest_progress.latest_cursor is None
        and latest_progress.rows_written > 0
    ):
        logger.info(
            "Got progress but no cursor, assuming that run is finished, skipping export",
            export_id=export_id,
            latest_progress=latest_progress,
        )
        return ExportResult(export_id, export_path, sorted(parquet_files))

    select = [
        RunSelect.id,
        RunSelect.name,
        RunSelect.start_time,
        RunSelect.end_time,
        RunSelect.extra,
        RunSelect.error,
        RunSelect.inputs,
        RunSelect.outputs,
        RunSelect.events,
        RunSelect.tags,
        RunSelect.run_type,
        RunSelect.session_id,
        RunSelect.parent_run_id,
        RunSelect.trace_id,
        RunSelect.dotted_order,
        RunSelect.status,
        RunSelect.parent_run_ids,
        RunSelect.feedback_stats,
        RunSelect.reference_example_id,
        RunSelect.total_tokens,
        RunSelect.prompt_tokens,
        RunSelect.completion_tokens,
        RunSelect.total_cost,
        RunSelect.prompt_cost,
        RunSelect.completion_cost,
        RunSelect.first_token_time,
        RunSelect.trace_tier,
    ]
    more_rows = True
    data = defaultdict(list)

    s3 = create_s3fs_connection(s3_creds, s3_config)
    target_size_kb = settings.DATA_EXPORT_TARGET_SIZE_KB
    batch_size = settings.DATA_EXPORT_START_BATCH_SIZE
    max_batch_size = settings.DATA_EXPORT_MAX_BATCH_SIZE
    recalculate_at = 10
    while more_rows:
        logger.info(
            "Fetching runs for export",
            cursor=current_cursor,
            start_time=export_start_time,
            end_time=export_end_time,
        )
        runs_for_window = await fetch_runs(
            auth,
            schemas.BodyParamsForRunSchema.model_construct(
                session=[session_id],
                select=select,
                cursor=current_cursor,
                limit=settings.DATA_EXPORT_RUN_LIMIT,
                order=schemas.RunDateOrder.asc,
                filter=filter,
            ),
            additional_sql_where=f"AND start_time >= '{convert_datetime(export_start_time)}' AND start_time < '{convert_datetime(export_end_time)}'",
            skip_expensive_enabled=False,
            include_timeout=False,
            max_threads=settings.CLICKHOUSE_DATA_EXPORT_MAX_THREADS,
            clickhouse_client_type=ClickhouseClient(
                settings.BULK_EXPORT_CLICKHOUSE_CLIENT
            ),
            query_name_postfix="_export",
        )
        logger.info("Fetched runs for export", runs_count=len(runs_for_window["runs"]))
        more_rows = runs_for_window["cursors"]["next"] is not None
        current_cursor = runs_for_window["cursors"]["next"]
        if should_update_status_fn and (
            new_status := await should_update_status_fn(auth, export_id)
        ):
            return ExportResult(
                export_id, export_path, sorted(parquet_files), new_status=new_status
            )
        await _extend_lock_if_present(lock)

        # Convert runs to dictionary and group by start_time
        prepared_runs = await asyncio.gather(
            *(
                gated_coro(
                    arun_in_executor(_prepare_run, run),
                    export_semaphore,
                )
                for run in runs_for_window["runs"]
            )
        )
        # clear out for memory
        del runs_for_window
        for row in prepared_runs:
            start_time = row["start_time"]
            year = start_time.year
            month = start_time.month
            day = start_time.day
            data[(year, month, day)].append(row)

        # clear out for memory
        del prepared_runs

        logger.info("Appended rows for export")
        if data:
            # Write to Parquet file in S3, grouped by year/month/day if larger than batch size
            for (year, month, day), group_data in data.items():
                # short-circuit depending on bulk export status
                if should_update_status_fn and (
                    new_status := await should_update_status_fn(auth, export_id)
                ):
                    return ExportResult(
                        export_id,
                        export_path,
                        sorted(parquet_files),
                        new_status=new_status,
                    )
                await _extend_lock_if_present(lock)

                payload_size = _estimated_payload_size(group_data)
                if (
                    len(group_data) >= batch_size
                    or payload_size
                    >= settings.DATA_EXPORT_MAX_BATCH_PAYLOAD_SIZE_KB * 1024
                ):
                    parquet_file_path = s3_full_dest.format(
                        year=year, month=month, day=day, filename=uuid.uuid4()
                    )
                    pq_file = await arun_in_executor(
                        _write_parquet_to_s3,
                        group_data,
                        parquet_file_path,
                        s3,
                        compression_val,
                        estimated_payload_size=payload_size,
                    )
                    partition_length = len(group_data)
                    rows_exported += partition_length
                    # clear out as it's been processed
                    group_data.clear()
                    parquet_files.append(pq_file)
                    if report_progress_fn:
                        await report_progress_fn(
                            auth,
                            export_id,
                            run_id,
                            schemas.BulkExportRunProgress(
                                rows_written=rows_exported,
                                export_path=export_path,
                                latest_cursor=current_cursor,
                                exported_files=parquet_files,
                            ),
                        )
                    if settings.DATA_EXPORT_FORCE_GC:
                        # experimental - force gc collection
                        logger.info("Forcing garbage collection")
                        gc.collect()
                        logger.info("Done with garbage collection")
                    if (
                        len(parquet_files) == 1
                        or len(parquet_files) % recalculate_at == 0
                    ):
                        try:
                            # estimate batch size based on written file size
                            file_info = s3.info(pq_file)
                            file_size_bytes = file_info["size"]
                            if file_size_bytes > 0 and partition_length > 0:
                                # Calculate target size based on number of runs and file size
                                bytes_per_run = file_size_bytes / partition_length
                                batch_size = min(
                                    max_batch_size,
                                    max(
                                        settings.DATA_EXPORT_RUN_LIMIT,
                                        int(target_size_kb * 1024 / bytes_per_run),
                                    ),
                                )
                                logger.info(
                                    "Calculated new batch size",
                                    file_size_bytes=file_size_bytes,
                                    group_data_length=partition_length,
                                    bytes_per_run=bytes_per_run,
                                    target_size_kb=target_size_kb,
                                    calculated_batch_size=batch_size,
                                )
                        except PermissionError as e:
                            logger.info(
                                "Failed to get file size, skipping batch size calculation",
                                error=e,
                            )
                else:
                    logger.info(
                        "Not writing Parquet file to S3 due to smaller size",
                        rows_written=len(group_data),
                        batch_size=batch_size,
                        estimated_payload_size=payload_size,
                    )

    # write any remaining items
    for (year, month, day), group_data in data.items():
        # short-circuit depending on bulk export status
        if should_update_status_fn and (
            new_status := await should_update_status_fn(auth, export_id)
        ):
            return ExportResult(
                export_id, export_path, sorted(parquet_files), new_status=new_status
            )
        await _extend_lock_if_present(lock)
        if len(group_data) > 0:
            logger.info(
                "Writing final partition batch to S3", rows_written=len(group_data)
            )
            parquet_file_path = s3_full_dest.format(
                year=year, month=month, day=day, filename=uuid.uuid4()
            )
            pq_file = await arun_in_executor(
                _write_parquet_to_s3,
                group_data,
                parquet_file_path,
                s3,
                compression_val,
            )
            parquet_files.append(pq_file)
            rows_exported += len(group_data)
            if report_progress_fn:
                await report_progress_fn(
                    auth,
                    export_id,
                    run_id,
                    schemas.BulkExportRunProgress(
                        rows_written=rows_exported,
                        export_path=export_path,
                        latest_cursor=current_cursor,
                        exported_files=parquet_files,
                    ),
                )
            group_data.clear()

    return ExportResult(export_id, export_path, sorted(parquet_files))
