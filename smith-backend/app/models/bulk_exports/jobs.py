import asyncio
from datetime import datetime, timedelta, timezone
from typing import List, Optional
from uuid import UUID

import structlog
from fastapi import HTT<PERSON>Exception
from lc_config.settings import shared_settings
from lc_database import redis
from lc_database.database import asyncpg_conn
from saq import job as saq_job

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.api.auth.verify import internal_auth_request
from app.config import settings
from app.memoize import redis_cache
from app.models.bulk_exports import crud as bulk_exports_crud
from app.models.bulk_exports import export as bulk_exports

logger = structlog.getLogger(__name__)

SAQ_FAIL_STATUS_TO_RUN_STATUS = {
    saq_job.Status.FAILED: schemas.BulkExportRunStatus.FAILED,
    saq_job.Status.ABORTED: schemas.BulkExportRunStatus.FAILED,
}

PARTITION_JOB_TIMEOUT = 4 * 3600  # 4 hours
ORCHESTRATE_JOB_TIMEOUT = 30  # 30 seconds
EXPORT_ERROR_MAPPING = {
    "access denied": "The blob store credentials or bucket are not valid",
    "bucket is not valid": "The specified blob store bucket is not valid",
    "bucket does not exist": "The specified blob store bucket is not valid",
    "key id you provided does not exist": "The blob store credentials provided are not valid",
    "invalid endpoint": "The endpoint_url provided is invalid",
}


@redis_cache(60)
async def _get_auth_for_tenant_cached_export(tenant_id: UUID) -> AuthInfo:
    return await internal_auth_request(tenant_id)


def run_partition_job_key(run_id: UUID) -> str:
    return f"run_bulk_export_partition:{run_id}"


def orchestrate_bulk_export_job_key(bulk_export_id: UUID) -> str:
    return f"orchestrate_bulk_export:{bulk_export_id}"


async def sync_job_to_run(
    auth: AuthInfo,
    queue: redis.AsyncQueue,
    bulk_export: schemas.BulkExport,
    job: saq_job.Job,
    run: schemas.BulkExportRun,
) -> List[schemas.BulkExportRun]:
    """Update the run status and retries based on the SAQ job info."""

    if job.error:
        logger.warning(
            f"Detected SAQ job error for SAQ status '{job.status}': '{job.error}'",
            bulk_export_run_id=str(run.id),
        )

    update_payload = schemas.BulkExportRunUpdateBatchItem(
        id=run.id,
        retry_number=job.attempts,
    )
    # Fail the run if a non-retryable error occurred ('swept' is retryable)
    if job.status in saq_job.UNSUCCESSFUL_TERMINAL_STATUSES and job.error != "swept":
        update_payload.status = schemas.BulkExportRunStatus.FAILED
    elif job.error == "swept" and job.status == saq_job.Status.ABORTED:
        # In this case job is aborted and needs to be re-enqueued
        retries = settings.BULK_EXPORT_JOB_RETRIES - job.attempts + 1
        logger.info(
            "Detected swept bulk export run aborted job, re-enqueuing",
            bulk_export_run_id=run.id,
            bulk_export_id=bulk_export.id,
            tenant_id=auth.tenant_id,
            retries=retries,
        )
        new_job = await _enqueue_partition(
            queue,
            auth.tenant_id,
            bulk_export,
            run,
            retries=retries,
        )
        if not new_job:
            logger.error(
                "Failed to re-enqueue bulk export run job",
                bulk_export_run_id=run.id,
            )
    return await bulk_exports_crud.update_bulk_export_runs_batch(
        auth,
        schemas.BulkExportRunUpdateBatch(
            bulk_export_id=run.bulk_export_id, updates=[update_payload]
        ),
    )


async def _enqueue_partition(
    queue: redis.AsyncQueue,
    tenant_id: UUID,
    bulk_export: schemas.BulkExport,
    run: schemas.BulkExportRun,
    retries: int,
) -> saq_job.Job:
    return await queue.enqueue(
        job_or_func="run_bulk_export_partition",
        # job params
        key=run_partition_job_key(run.id),
        retries=retries,
        retry_delay=settings.BULK_EXPORT_JOB_RETRY_DELAY_SEC,
        retry_backoff=False,
        timeout=PARTITION_JOB_TIMEOUT,
        # func params
        tenant_id=tenant_id,
        bulk_export_id=bulk_export.id,
        run_id=run.id,
    )


async def should_update_status(
    auth: AuthInfo, export_id: UUID
) -> Optional[schemas.BulkExportRunStatus]:
    export = await bulk_exports_crud.get_bulk_export(auth, export_id)
    if export.status in bulk_exports_crud.BULK_EXPORT_PROPAGATE_STATUSES:
        return bulk_exports_crud.BULK_EXPORT_PROPAGATE_STATUSES[export.status]
    return None


async def report_progress(
    auth: AuthInfo,
    export_id: UUID,
    run_id: UUID,
    progress: schemas.BulkExportRunProgress,
) -> None:
    logger.info(f"Reporting progress: {progress}")
    await bulk_exports_crud.update_bulk_export_run_progress(
        auth, export_id, run_id, progress
    )


async def orchestrate_bulk_export(
    tenant_id: UUID,
    bulk_export_id: UUID,
    # Optional override for testing
    override_timeout: Optional[int] = None,
) -> None:
    #   1. Generate partitions for the bulk export
    #   2. Create the bulk export runs records for the partitions
    #   3. Kick off the bulk export runs jobs
    #   4. Update the bulk export status based on the status of the runs
    #   5. Notify when the bulk export is complete
    #   6. Manage validation, concurrency, and retries
    structlog.contextvars.bind_contextvars(
        tenant_id=str(tenant_id),
        bulk_export_id=str(bulk_export_id),
    )
    lock_key = f"data_export:{str(tenant_id)}:{str(bulk_export_id)}"
    async with (
        redis.renewable_lock(lock_key, 10) as lock,
        redis.async_queue(shared_settings.EXPORT_QUEUE) as queue,
    ):
        if not lock:
            raise ValueError("Failed to acquire lock")

        # Check that bulk exports are enabled
        auth = await _get_auth_for_tenant_cached_export(tenant_id)
        bulk_exports_crud.validate_bulk_exports_allowed(auth)

        # Bulk export must be running
        bulk_export = await bulk_exports_crud.get_bulk_export(auth, bulk_export_id)
        try:
            bulk_export_destination = (
                await bulk_exports_crud.get_bulk_export_destination(
                    auth, bulk_export.bulk_export_destination_id
                )
            )
        except HTTPException as e:
            if e.status_code == 404:
                logger.warning(
                    "Destination not found for bulk export",
                    bulk_export_id=bulk_export_id,
                    bulk_export_destination_id=bulk_export.bulk_export_destination_id,
                    tenant_id=tenant_id,
                )
                await bulk_exports_crud.update_bulk_export_internal(
                    auth,
                    bulk_export_id,
                    schemas.BulkExportUpdateInternal(
                        status=schemas.BulkExportStatus.FAILED,
                    ),
                )
                return
            raise
        structlog.contextvars.bind_contextvars(
            bulk_export_destination_id=str(bulk_export_destination.id),
            bulk_export_status=bulk_export.status.value,
        )

        all_runs = await bulk_exports_crud.list_bulk_export_runs(auth, bulk_export_id)
        num_by_status = {
            status.value: sum(1 for run in all_runs if run.status == status)
            for status in schemas.BulkExportRunStatus
        }
        logger.info(f"Run statuses: {num_by_status}")

        match bulk_export.status:
            case schemas.BulkExportStatus.CREATED:
                # Halt if too many other bulk exports are running for this workspace
                workspace_bulk_exports = await bulk_exports_crud.list_bulk_exports(auth)
                other_running_bulk_exports = [
                    be
                    for be in workspace_bulk_exports
                    if be.status == schemas.BulkExportStatus.RUNNING
                    and be.id != bulk_export.id
                ]
                if (
                    len(other_running_bulk_exports)
                    >= settings.MAX_CONCURRENT_BULK_EXPORTS_PER_WS
                ):
                    logger.info(
                        f"Skipping bulk export orchestration because {len(other_running_bulk_exports)} bulk exports are running for this workspace",
                    )
                    return
                if all_runs:
                    logger.info(
                        "Skipping bulk export orchestration and setting to Running because runs already exist",
                    )
                    await bulk_exports_crud.update_bulk_export_internal(
                        auth,
                        bulk_export_id,
                        schemas.BulkExportUpdateInternal(
                            status=schemas.BulkExportStatus.RUNNING,
                        ),
                    )
                    return

                partitions = await bulk_exports.calculate_partitions(
                    tenant_id,
                    bulk_export.session_id,
                    bulk_export.start_time,
                    bulk_export.end_time,
                    bulk_export.filter,
                )
                if not partitions:
                    logger.info("No partitions to export, setting to Completed")
                    await bulk_exports_crud.update_bulk_export_internal(
                        auth,
                        bulk_export_id,
                        schemas.BulkExportUpdateInternal(
                            status=schemas.BulkExportStatus.COMPLETED,
                            finished_at=datetime.now(tz=timezone.utc),
                        ),
                    )
                    return
                batch_payload = schemas.BulkExportRunCreateBatch(
                    bulk_export_id=bulk_export.id,
                    creates=[
                        schemas.BulkExportRunCreateBatchItem(
                            metadata=schemas.BulkExportRunMetadata(
                                prefix=bulk_export_destination.config.prefix,
                                start_time=partition[0],
                                end_time=partition[1],
                            ),
                        )
                        for partition in partitions
                    ],
                )
                created_runs = await bulk_exports_crud.create_bulk_export_runs_batch(
                    auth, batch_payload
                )
                sorted_runs = sorted(
                    created_runs, key=lambda run: run.metadata.start_time
                )
                runs_to_enqueue = sorted_runs[
                    : shared_settings.BULK_EXPORT_MAX_CONCURRENT_RUNS
                ]
                initial_enqueued_runs: List[schemas.BulkExportRun] = []
                for run in runs_to_enqueue:
                    job = await _enqueue_partition(
                        queue,
                        tenant_id,
                        bulk_export,
                        run,
                        settings.BULK_EXPORT_JOB_RETRIES,
                    )
                    if not job:
                        logger.error(
                            "Failed to enqueue bulk export run job",
                            bulk_export_run_id=run.id,
                        )
                    else:
                        initial_enqueued_runs.append(run)
                logger.info(
                    f"Enqueued runs: {[run.id for run in initial_enqueued_runs]}",
                )
                await bulk_exports_crud.update_bulk_export_internal(
                    auth,
                    bulk_export_id,
                    schemas.BulkExportUpdateInternal(
                        status=schemas.BulkExportStatus.RUNNING,
                    ),
                )
            case schemas.BulkExportStatus.RUNNING:
                # Currently running export, so runs records already exist.

                # Complete the export if all runs are completed
                if all(
                    run.status == schemas.BulkExportRunStatus.COMPLETED
                    for run in all_runs
                ):
                    await bulk_exports_crud.update_bulk_export_internal(
                        auth,
                        bulk_export_id,
                        schemas.BulkExportUpdateInternal(
                            status=schemas.BulkExportStatus.COMPLETED,
                            finished_at=datetime.now(tz=timezone.utc),
                        ),
                    )
                    return

                # Check for timeout
                workflow_timeout = (
                    override_timeout
                    if override_timeout is not None
                    else settings.BULK_EXPORT_WORKFLOW_TIMEOUT_SEC
                )
                if datetime.now(tz=timezone.utc) - bulk_export.created_at > timedelta(
                    seconds=workflow_timeout
                ):
                    # This means the job is stuck from a workflow perspective (has taken longer than our defined timeout)
                    logger.info(
                        f"Bulk export job has taken longer than {workflow_timeout}s, setting to {schemas.BulkExportStatus.TIMEDOUT.value}",
                        bulk_export_id=bulk_export_id,
                        bulk_export_created_at=bulk_export.created_at,
                        bulk_export_timeout=workflow_timeout,
                    )
                    await bulk_exports_crud.update_bulk_export_internal(
                        auth,
                        bulk_export_id,
                        schemas.BulkExportUpdateInternal(
                            status=schemas.BulkExportStatus.TIMEDOUT,
                        ),
                    )
                    return

                # Sync SAQ status to the DB
                running = [
                    run
                    for run in all_runs
                    if run.status == schemas.BulkExportRunStatus.RUNNING
                ]
                for run in running:
                    job = await queue.job(run_partition_job_key(run.id))
                    if job:
                        logger.debug(
                            f"Found SAQ job for running export run with {job.status=}, {job.retries=}, {job.error=}",
                            bulk_export_run_id=run.id,
                        )
                        updated_runs = await sync_job_to_run(
                            auth, queue, bulk_export, job, run
                        )
                        updated_run = updated_runs[0]
                        if updated_run.status == schemas.BulkExportRunStatus.FAILED:
                            logger.warning("Run failed, setting bulk export to failed")
                            await bulk_exports_crud.update_bulk_export_internal(
                                auth,
                                bulk_export_id,
                                schemas.BulkExportUpdateInternal(
                                    status=schemas.BulkExportStatus.FAILED,
                                ),
                            )
                            return

                # Kick off more runs if necessary
                pending = [
                    run
                    for run in all_runs
                    if run.status == schemas.BulkExportRunStatus.CREATED
                ]
                if (
                    len(running) < shared_settings.BULK_EXPORT_MAX_CONCURRENT_RUNS
                    and len(pending) > 0
                ):
                    num_to_enqueue = max(
                        min(
                            shared_settings.BULK_EXPORT_MAX_CONCURRENT_RUNS
                            - len(running),
                            len(pending),
                        ),
                        0,
                    )
                    if num_to_enqueue > 0:
                        sorted_runs = sorted(
                            pending, key=lambda run: run.metadata.start_time
                        )
                        runs_to_enqueue = sorted_runs[:num_to_enqueue]
                        addl_enqueued_runs: List[schemas.BulkExportRun] = []
                        for run in runs_to_enqueue:
                            job = await _enqueue_partition(
                                queue,
                                tenant_id,
                                bulk_export,
                                run,
                                settings.BULK_EXPORT_JOB_RETRIES,
                            )
                            if not job:
                                logger.error(
                                    "Failed to enqueue bulk export run job",
                                    bulk_export_run_id=run.id,
                                )
                            else:
                                addl_enqueued_runs.append(run)
                        logger.info(
                            f"Enqueued additional runs: {[run.id for run in addl_enqueued_runs]}",
                        )
            case _:
                logger.info(
                    "Skipping bulk export orchestration because it is not in a running or pending state",
                    bulk_export_status=bulk_export.status.value,
                )
                return


async def run_bulk_export_partition(
    tenant_id: UUID, bulk_export_id: UUID, run_id: UUID
) -> None:
    # This executes the actual bulk data export for a single partition
    structlog.contextvars.bind_contextvars(
        tenant_id=str(tenant_id),
        bulk_export_id=str(bulk_export_id),
        bulk_export_run_id=str(run_id),
    )
    lock_key = f"data_export:{str(tenant_id)}:{str(bulk_export_id)}:{str(run_id)}"
    async with redis.renewable_lock(
        lock_key, settings.DATA_EXPORT_LOCK_TIMEOUT_SEC
    ) as lock:
        if not lock:
            raise ValueError("Failed to acquire lock")
        auth = await _get_auth_for_tenant_cached_export(tenant_id)
        bulk_exports_crud.validate_bulk_exports_allowed(auth)

        try:
            bulk_export = await bulk_exports_crud.get_bulk_export(auth, bulk_export_id)
            destination = await bulk_exports_crud.get_bulk_export_destination_decrypted(
                auth, bulk_export.bulk_export_destination_id
            )
            credentials = destination.decrypted_credentials
            run = await bulk_exports_crud.get_bulk_export_run(
                auth, bulk_export_id, run_id
            )
        except HTTPException as e:
            if e.status_code == 404:
                logger.warning(
                    "Could not find bulk export, destination, or run, so halting.",
                    bulk_export_id=bulk_export_id,
                    bulk_export_run_id=run_id,
                )
                return

        structlog.contextvars.bind_contextvars(
            bulk_export_run_status=run.status.value,
        )
        # Update the run to running if needed. This is the standard case.
        # Skip the update if the run is already running, as this can happen if the job is retried
        # e.g. if it's interrupted by a deployment.
        if run.status != schemas.BulkExportRunStatus.RUNNING:
            running_payload = schemas.BulkExportRunUpdateBatch(
                bulk_export_id=bulk_export_id,
                updates=[
                    schemas.BulkExportRunUpdateBatchItem(
                        id=run.id,
                        status=schemas.BulkExportRunStatus.RUNNING,
                    )
                ],
            )
            await bulk_exports_crud.update_bulk_export_runs_batch(
                auth,
                running_payload,
            )
            logger.info("Updated run to running")
        else:
            logger.info("Run already running")

        try:
            result = await bulk_exports.export_partition(
                auth,
                destination.config,
                credentials,
                bulk_export_id,
                run_id,
                tenant_id,
                bulk_export.session_id,
                run.metadata.start_time,
                run.metadata.end_time,
                bulk_export.filter,
                compression=bulk_export.compression,
                lock=lock,
                latest_progress=run.metadata.result,
                should_update_status_fn=should_update_status,
                report_progress_fn=report_progress,
            )
            logger.info(f"Export partition result: {result}")
            if result.new_status:
                logger.info(
                    f"Setting run to {result.new_status} because detected corresponding bulk export status"
                )
                update_status_payload = schemas.BulkExportRunUpdateBatch(
                    bulk_export_id=bulk_export_id,
                    updates=[
                        schemas.BulkExportRunUpdateBatchItem(
                            id=run.id,
                            status=result.new_status,
                        )
                    ],
                )
                await bulk_exports_crud.update_bulk_export_runs_batch(
                    auth,
                    update_status_payload,
                )
                return

            completed_payload = schemas.BulkExportRunUpdateBatch(
                bulk_export_id=bulk_export_id,
                updates=[
                    schemas.BulkExportRunUpdateBatchItem(
                        id=run.id,
                        status=schemas.BulkExportRunStatus.COMPLETED,
                        finished_at=datetime.now(tz=timezone.utc),
                    )
                ],
            )
            await bulk_exports_crud.update_bulk_export_runs_batch(
                auth,
                completed_payload,
            )
            logger.info("Updated run to completed")
        except (OSError, PermissionError, FileNotFoundError, ValueError) as e:
            mapped_error = next(
                (
                    value
                    for key, value in EXPORT_ERROR_MAPPING.items()
                    if key in str(e).lower()
                ),
                None,
            )
            if mapped_error:
                failed_payload = schemas.BulkExportRunUpdateBatch(
                    bulk_export_id=bulk_export_id,
                    updates=[
                        schemas.BulkExportRunUpdateBatchItem(
                            id=run.id,
                            errors={
                                **(run.errors or {}),
                                f"retry_{run.retry_number}": mapped_error,
                            },
                        )
                    ],
                )
                await bulk_exports_crud.update_bulk_export_runs_batch(
                    auth,
                    failed_payload,
                )
            raise e


async def cron_schedule_bulk_exports() -> None:
    # Trigger orchestration for all bulk exports that are not done
    async with (
        asyncpg_conn() as db,
        redis.async_queue(shared_settings.ADHOC_QUEUE) as queue,
    ):
        bulk_exports = await bulk_exports_crud.list_bulk_exports_for_statuses(
            db,
            [
                schemas.BulkExportStatus.CREATED,
                schemas.BulkExportStatus.RUNNING,
            ],
        )
        for bulk_export in bulk_exports:
            job = await queue.enqueue(
                "orchestrate_bulk_export",
                # job params
                key=orchestrate_bulk_export_job_key(bulk_export.id),
                retries=0,
                retry_backoff=False,
                timeout=ORCHESTRATE_JOB_TIMEOUT,
                # func params
                tenant_id=bulk_export.tenant_id,
                bulk_export_id=bulk_export.id,
            )
            # Sleep for short time to stagger the jobs
            await asyncio.sleep(0.5)
            if not job:
                logger.error(
                    "Failed to enqueue bulk export orchestration job",
                    bulk_export_id=bulk_export.id,
                )
