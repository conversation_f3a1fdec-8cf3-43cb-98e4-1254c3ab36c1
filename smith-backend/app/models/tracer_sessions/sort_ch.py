from uuid import UUID

from fastapi import HTTPException
from lc_config.settings import shared_settings as settings
from lc_database.clickhouse import ClickhouseClient, clickhouse_client

from app.api.auth.schemas import AuthInfo, ShareDatasetInfo
from app.models.feedback.utils import normalize_feedback_key
from app.models.query_lang.parse import Comparator as C
from app.models.query_lang.parse import (
    Comparison,
    Operation,
    Operator,
)
from app.models.query_lang.translate import SqlVisitorClickhouse
from app.models.runs.attrs import RUN_ATTRIBUTES
from app.retry import retry_clickhouse_read
from app.schemas import FilterQueryParamsForTracerSessionSchema, SessionSortableColumns


@retry_clickhouse_read
async def get_sorted_session_ids_from_ch(
    auth: AuthInfo | ShareDatasetInfo,
    query_params: FilterQueryParamsForTracerSessionSchema,
    ch_session_clause: str | None = None,
    ch_session_params: dict | None = None,
) -> list[UUID]:
    """
    Query Clickhouse to get sorted session ids when sort by parameters are CH specific.
    """
    use_aggregated = (
        (
            settings.STATS_USE_SORTED_SESSION_AGGREGATION
            or str(auth.tenant_id) in settings.STATS_AGGREGATION_TENANT_IDS
        )
        and not query_params.sort_by_feedback_key
        and query_params.sort_by != SessionSortableColumns.FEEDBACK
        and not query_params.dataset_version
    )

    where = Operation(
        operator=Operator.AND,
        arguments=[
            Comparison(C.EQ, "is_root", True),
            Comparison(C.EQ, "is_trace_expired", False),
        ]
        if not use_aggregated
        else [Comparison(C.EQ, "is_root", True)],
    )
    args = where.arguments

    if isinstance(auth, AuthInfo):
        args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
    elif isinstance(auth, ShareDatasetInfo):
        args.append(Comparison(C.EQ, "tenant_id", auth.tenant_id))
        args.append(Comparison(C.EQ, "reference_dataset_id", auth.dataset_id))

    if query_params.reference_dataset:
        args.append(
            Comparison(C.IN, "reference_dataset_id", query_params.reference_dataset)
        )
    if query_params.reference_free is not None:
        args.append(
            Comparison(
                C.EXISTS, "reference_dataset_id", not query_params.reference_free
            )
        )

    if query_params.sort_by_feedback_key is not None:
        sort_by_feedback_key = normalize_feedback_key(query_params.sort_by_feedback_key)
        # check to see if the feedback key being sorted by is in the ch_session_params
        if ch_session_params:
            feedbacks_in_key = next(
                key
                for key in ch_session_params.keys()
                if key.startswith("feedback_keys") and key.endswith("in")
            )
            feedback_values_filter_on = ch_session_params[feedbacks_in_key]
            if sort_by_feedback_key not in feedback_values_filter_on:
                raise HTTPException(
                    status_code=400,
                    detail=f"Feedback key {sort_by_feedback_key} not in the feedback keys filter",
                )

        args.append(Comparison(C.EQ, "feedback_key", query_params.sort_by_feedback_key))
    if query_params.dataset_version:
        args.append(Comparison(C.EQ, "dataset_version", query_params.dataset_version))

    sql_from_join_where, params, _, _, _ = where.accept(
        SqlVisitorClickhouse(
            attributes=RUN_ATTRIBUTES,
            main_table="runs"
            if not use_aggregated
            else "runs_sessions_agg_hourly runs",
            main_table_suffix="FINAL"
            if query_params.sort_by != SessionSortableColumns.LAST_RUN_START_TIME
            and query_params.sort_by != SessionSortableColumns.ERROR_RATE
            else "",
            sql_join_projection_columns={"feedbacks_rmt": ["score"]},
        )
    )

    params.update(ch_session_params or {})

    if use_aggregated:
        if query_params.sort_by == SessionSortableColumns.LAST_RUN_START_TIME:
            order_by = "maxMerge(last_run_start_time)"
        elif query_params.sort_by == SessionSortableColumns.LATENCY_P50:
            order_by = "quantileMerge(0.5)(latency_ptiles)"
        elif query_params.sort_by == SessionSortableColumns.LATENCY_P99:
            order_by = "quantileMerge(0.99)(latency_ptiles)"
        elif query_params.sort_by == SessionSortableColumns.ERROR_RATE:
            order_by = "countMerge(error_run_count) / countMerge(run_count)"
        else:
            raise NotImplementedError(
                f"Sorting by {query_params.sort_by} not implemented."
            )
    else:
        if query_params.sort_by == SessionSortableColumns.LAST_RUN_START_TIME:
            order_by = "max(runs.start_time)"
        elif query_params.sort_by == SessionSortableColumns.LATENCY_P50:
            order_by = "quantile(0.5)(date_diff('ms', start_time, end_time, 'UTC'))"
        elif query_params.sort_by == SessionSortableColumns.LATENCY_P99:
            order_by = "quantile(0.99)(date_diff('ms', start_time, end_time, 'UTC'))"
        elif query_params.sort_by == SessionSortableColumns.ERROR_RATE:
            order_by = "uniqIf(id, status = 'error') / uniq(id)"
        elif query_params.sort_by == SessionSortableColumns.FEEDBACK:
            order_by = "avg(score)"
        else:
            raise NotImplementedError(
                f"Sorting by {query_params.sort_by} not implemented."
            )

    query = f"""
    SELECT runs.session_id
    {sql_from_join_where}
    {"AND runs.is_deleted = 0" if not use_aggregated else ""}
    {"AND runs.session_id IN " + f"({ch_session_clause})" if ch_session_clause else ""}
    GROUP BY 1
    ORDER BY {order_by} {"DESC" if query_params.sort_by_desc else "ASC"}, runs.session_id {"DESC" if query_params.sort_by_desc else "ASC"}
    LIMIT {query_params.limit + 1} OFFSET {query_params.offset}"""

    async with clickhouse_client(ClickhouseClient.USER_QUERIES) as ch:
        rows = await ch.fetch("fetch_sorted_session_ids", query, params=params)
    return [row["session_id"] for row in rows]
