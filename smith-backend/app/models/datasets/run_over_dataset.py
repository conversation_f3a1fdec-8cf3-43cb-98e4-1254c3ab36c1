import asyncio
import base64
import datetime
import time
from typing import Any, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dict, Set
from uuid import UUID, uuid4

import httpx
import structlog
from fastapi import HTTPException
from langchain_core.runnables.utils import Output
from lc_config.service_communication_settings import ServiceName
from lc_config.settings import shared_settings
from lc_config.settings import shared_settings as settings
from lc_config.utils import arun_in_executor
from lc_database import redis
from lc_database.database import asyncpg_conn
from lc_database.s3_client import get_signed_url_go
from lc_database.service_client import (
    get_internal_tenant_scoped_service_client,
)
from starlette.responses import StreamingResponse

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.crud import update_tracer_session
from app.models import tracer_sessions
from app.models.datasets.schema import (
    PlaygroundRunOverDatasetBatchRequestSchema,
    PlaygroundRunOverDatasetRequestSchema,
)
from app.models.datasets.utils import is_supported_mime_type
from app.models.datasets.utils.parse_sse_events import (
    get_completed_runs_from_stream_event,
)
from app.models.datasets.utils.should_run_evals_inline import should_run_evals_inline
from app.models.datasets.utils.sse import SSEDecoder, aiter_lines_raw
from app.models.examples.fetch import fetch_examples_json
from app.models.runs.rule_application import custom_code, online_eval
from app.models.runs.rules import trigger_rules_by_dataset, validate_and_decrypt_rule

logger = structlog.get_logger(__name__)


class DatasetRunManager:
    def __init__(self, auth: AuthInfo, dataset_id: UUID):
        self.auth = auth
        self.max_runtime = settings.MAX_RUNTIME_OF_TRIGGER_RUN_RULES_PLAYGROUND_SEC
        self.poll_interval = settings.POLL_INTERVAL_FOR_RUNS_PERSISTED_PLAYGROUND
        self.rule_trigger_interval = (
            settings.MIN_SECS_BETWEEN_TRIGGER_RUN_RULES_PLAYGROUND
        )
        self.runs: Dict[UUID, Dict] = {}
        self.pending_runs: Set[UUID] = set()
        self.example_ids: Set[UUID] = set()
        self.last_rule_trigger_time = 0
        self.processing_lock = asyncio.Lock()
        self.pending_lock = asyncio.Lock()
        self.is_processing = False
        self.start_time: float | None = None
        self.dataset_id = dataset_id
        self.processing_task: asyncio.Task | None = None

    async def add_runs(self, run_ids: Set[UUID], example_ids: Set[UUID]):
        async with self.pending_lock:
            self.pending_runs.update(run_ids)
            self.example_ids.update(example_ids)
            for run_id in run_ids:
                self.runs[run_id] = {"persisted": False}

        async with self.processing_lock:
            if not self.is_processing:
                self.is_processing = True
                if self.start_time is None:
                    self.start_time = time.time()
                if time.time() - self.start_time < self.max_runtime:
                    self.processing_task = asyncio.create_task(self._process_runs())
                    self.processing_task.add_done_callback(self._clear_processing_task)

    def _get_lock_key(self):
        example_ids = sorted([str(e) for e in self.example_ids])
        return f"{settings.TRIGGER_RUN_RULES_LOCK_PREFIX}{';'.join(example_ids)}"

    async def _process_runs(self):
        async with redis.renewable_lock(
            self._get_lock_key(), settings.TRIGGER_RUN_RULES_LOCK_TIMEOUT_SEC
        ) as lock:
            if not lock:
                return
            try:
                while True:
                    # Take a snapshot of the pending runs in case new runs are added while processing
                    async with self.pending_lock:
                        pending_runs_snapshot = set(self.pending_runs)
                        runs_snapshot = {
                            run_id: self.runs[run_id]
                            for run_id in pending_runs_snapshot
                        }

                    if not pending_runs_snapshot:
                        break

                    if time.time() - self.start_time > self.max_runtime:
                        break

                    current_time = time.time()

                    need_to_trigger_rules = (
                        current_time - self.last_rule_trigger_time
                        >= self.rule_trigger_interval
                        and any(info["persisted"] for info in runs_snapshot.values())
                    )

                    if need_to_trigger_rules:
                        persisted_runs_to_trigger = {
                            run_id
                            for run_id, info in runs_snapshot.items()
                            if info["persisted"]
                        }
                        if persisted_runs_to_trigger:
                            self.last_rule_trigger_time = current_time
                            async with self.pending_lock:
                                for run_id in persisted_runs_to_trigger:
                                    self.pending_runs.discard(run_id)
                                    self.runs.pop(run_id, None)
                            await trigger_rules_by_dataset(
                                self.auth, self.dataset_id, enforce_user_id=False
                            )

                    runs_not_persisted = {
                        run_id
                        for run_id, info in runs_snapshot.items()
                        if not info["persisted"]
                    }

                    if runs_not_persisted:
                        persisted_runs = await self._check_runs_persisted(
                            runs_not_persisted
                        )
                        if persisted_runs:
                            async with self.pending_lock:
                                for run_id in persisted_runs:
                                    if run_id in self.runs:
                                        self.runs[run_id]["persisted"] = True

                    await asyncio.sleep(self.poll_interval)
            except Exception as e:
                logger.error(
                    "Failed to process runs in DatasetRunManager",
                    error=e,
                )
                raise e
            finally:
                async with self.processing_lock:
                    self.is_processing = False

    async def _check_runs_persisted(self, run_ids: Set[UUID]) -> Set[UUID]:
        try:
            async with (
                redis.aredis_routed_pool(
                    str(self.auth.tenant_id), redis.RedisOperation.READ
                ) as aredis,
                aredis.pipeline() as pipe,
            ):
                # dual-write: readonly
                for run_id in run_ids:
                    pipe.hget(
                        f"smith:runs:pending:{str(self.auth.tenant_id)}:{str(run_id)}",
                        "post_done_ch",
                    )
                results = await pipe.execute()
            return {run_id for run_id, result in zip(run_ids, results) if result}
        except Exception:
            return set()

    def _clear_processing_task(self, task: asyncio.Task):
        try:
            task.result()
        except Exception as e:
            logger.error(
                "Failed to trigger rule execution in DatasetRunManager", error=e
            )
        finally:
            self.processing_task = None


async def create_dataset_runs_manager(
    auth_dict: dict[str, Any], dataset_id: str, run_ids: Set[str], example_ids: Set[str]
) -> DatasetRunManager:
    auth: AuthInfo = await arun_in_executor(AuthInfo.model_validate, auth_dict)
    manager = DatasetRunManager(auth, UUID(dataset_id))
    run_ids_uuid = {UUID(run_id) for run_id in run_ids}
    example_ids_uuid = {UUID(example_id) for example_id in example_ids}
    await manager.add_runs(run_ids_uuid, example_ids_uuid)
    return manager


async def create_dataset_runs_manager_background_task(
    auth: AuthInfo, dataset_id: UUID, run_ids: Set[UUID], example_ids: Set[UUID]
) -> None:
    async with redis.async_queue(settings.RUN_RULES_QUEUE) as queue:
        await queue.enqueue(
            "apply_dataset_manager",
            auth_dict=auth.model_dump(),
            dataset_id=str(dataset_id),
            run_ids=[str(run_id) for run_id in run_ids],
            example_ids=[str(example_id) for example_id in example_ids],
            **{**redis.DEFAULT_JOB_KWARGS, "retries": 0},
        )


async def create_playground_experiment(
    auth: AuthInfo,
    request: PlaygroundRunOverDatasetRequestSchema,
    runner_context: str = "langsmith_ui",
) -> schemas.TracerSessionWithoutVirtualFields:
    async with asyncpg_conn() as db:
        uniq_id = str(uuid4())[:8]
        project_name = f"{request.project_name}::{uniq_id}"

        max_retries = 10
        for index in range(1, max_retries + 1):
            project_name = f"{project_name}{'' if index == 1 else f' ({index})'}"
            try:
                create_req = schemas.TracerSessionCreate(
                    name=project_name,
                    reference_dataset_id=request.dataset_id,
                )
                extra: dict[str, Any] = {
                    "metadata": {"__ls_runner": runner_context},
                }
                if request.evaluator_rules is not None:
                    rules_exist = await db.fetch(
                        """
                        SELECT id FROM run_rules 
                        WHERE id = ANY($1) AND tenant_id = $2
                        """,
                        [str(rule) for rule in request.evaluator_rules],
                        str(auth.tenant_id),
                    )
                    existing_rule_ids = [str(row["id"]) for row in rules_exist]
                    extra["evaluator_info"] = {"selected_rules": existing_rule_ids}
                create_req.extra = extra
                session = await tracer_sessions.create.create_tracer_session(
                    db,
                    auth,
                    create_req,
                    upsert=False,
                )
                return session
            except HTTPException as e:
                if e.status_code != 409 or index == max_retries:
                    raise e
                # Continue to next iteration if conflict and not at max retries

    raise HTTPException(
        status_code=500, detail="Failed to create experiment after maximum retries"
    )


async def create_fetch_and_execute_examples_background_task(
    request: PlaygroundRunOverDatasetRequestSchema,
    auth: AuthInfo,
    session: schemas.TracerSessionWithoutVirtualFields,
    offset: int = 0,
    poll_rules: bool = False,
) -> None:
    request_dict = request.model_dump()
    auth_dict = auth.model_dump()
    session_dict = session.model_dump()
    async with redis.async_queue(settings.ADHOC_QUEUE) as queue:
        await queue.enqueue(
            "fetch_and_execute_examples",
            request_dict=request_dict,
            auth_dict=auth_dict,
            session_dict=session_dict,
            offset=offset,
            poll_rules=poll_rules,
            **{**redis.DEFAULT_JOB_KWARGS, "retries": 0},
        )


async def create_run_rules_background_task(
    runs: list[dict],
    auth: AuthInfo,
    rule_ids: list[str],
) -> None:
    auth_dict = auth.model_dump()
    async with redis.async_queue(settings.ADHOC_QUEUE) as queue:
        await queue.enqueue(
            "execute_playground_rules",
            runs=runs,
            auth_dict=auth_dict,
            rule_ids=rule_ids,
            **{**redis.DEFAULT_JOB_KWARGS, "retries": 0},
        )


async def execute_playground_rules_serializable(
    runs: list[dict],
    auth_dict: Dict[str, Any],
    rule_ids: list[str],
) -> None:
    auth: AuthInfo = await arun_in_executor(AuthInfo.model_validate, auth_dict)
    await execute_playground_rules(runs, auth, rule_ids)


async def execute_playground_rules(
    runs: list[dict],
    auth: AuthInfo,
    rule_ids: list[str],
) -> None:
    async with asyncpg_conn() as db:
        rule_dicts = await db.fetch(
            """
            select 
                run_rules.*, 
                llm_evaluators.commit_hash_or_tag as evaluator_commit_hash_or_tag,
                llm_evaluators.variable_mapping as evaluator_variable_mapping,
                hub_repos.repo_handle as evaluator_prompt_handle
            FROM run_rules
            LEFT JOIN llm_evaluators
                ON llm_evaluators.evaluator_id = run_rules.evaluator_id
            LEFT JOIN hub_repos 
                ON llm_evaluators.prompt_id = hub_repos.id
                AND hub_repos.tenant_id = $2
            WHERE
                run_rules.id = ANY($1)
                AND run_rules.tenant_id = $2       
            """,
            rule_ids,
            auth.tenant_id,
        )
    rules = await asyncio.gather(
        *[validate_and_decrypt_rule(dict(row)) for row in rule_dicts]
    )

    eval_sem = asyncio.Semaphore(settings.ASYNCPG_POOL_MAX_SIZE)
    code_eval_sem = asyncio.Semaphore(settings.ASYNCPG_POOL_MAX_SIZE)
    for run in runs:
        # change start time and end time from string to datetime
        run["start_time"] = datetime.datetime.fromisoformat(run["start_time"])
        run["end_time"] = (
            datetime.datetime.fromisoformat(run["end_time"])
            if run["end_time"]
            else None
        )

    async def _apply_rule(rule: schemas.RunRulesSchema) -> None:
        if rule.evaluators or rule.evaluator_id:
            async with eval_sem:
                await online_eval.OnlineEvaluatorRuleApplier.apply(
                    rule,
                    auth,
                    runs,
                    "",  # we don't actually use start/end time in online evalution applier
                    "",
                )
        elif rule.code_evaluators:
            async with code_eval_sem:
                await custom_code.CustomCodeRuleApplier.apply(
                    rule,
                    auth,
                    runs,
                    "",  # for custom code we do use start time but only for rule logs, which we don't need in playground
                    "",
                )

    results = await asyncio.gather(
        *(_apply_rule(rule) for rule in rules), return_exceptions=True
    )
    logger_coros = []
    for result, rule in zip(results, rules):
        if isinstance(result, Exception):
            logger_coros.append(
                logger.aerror(
                    "Error executing playground evals",
                    tenant_id=str(auth.tenant_id),
                    org_id=str(auth.organization_id),
                    rule_id=str(rule.id),
                    rule_display_name=rule.display_name,
                    runs_batch=runs,
                    exc_info=result,
                )
            )
            # TODO: when #9772 merges, use the same feedback key parsing logic to add errored feedback to the runs
            # so that there is some visual feedback to the user
    if logger_coros:
        await asyncio.gather(*logger_coros)


async def _evaluate_batch_of_runs(
    auth: AuthInfo,
    batch_runs: list[dict],
    evaluator_rules: list[UUID],
) -> None:
    if batch_runs and evaluator_rules:
        await create_run_rules_background_task(
            batch_runs,
            auth,
            [str(rule) for rule in evaluator_rules],
        )


# Run the first x rules synchronously, and the rest in the background.
# Where x = settings.MAX_PLAYGROUND_SYNC_EVALS
async def _evaluate_batch_of_runs_directly_first_x_rules(
    auth: AuthInfo,
    batch_runs: list[dict],
    evaluator_rules: list[UUID],
) -> None:
    if batch_runs and evaluator_rules:
        if _should_run_evals_sync(auth):
            evaluator_rules_str = [str(rule) for rule in evaluator_rules]
            coros = []
            coros.append(
                execute_playground_rules(
                    batch_runs,
                    auth,
                    evaluator_rules_str[: settings.MAX_PLAYGROUND_SYNC_EVALS],
                )
            )
            if len(evaluator_rules) > settings.MAX_PLAYGROUND_SYNC_EVALS:
                coros.append(
                    _evaluate_batch_of_runs(
                        auth,
                        batch_runs,
                        evaluator_rules[settings.MAX_PLAYGROUND_SYNC_EVALS :],
                    )
                )
            await asyncio.gather(*coros)
        else:
            await _evaluate_batch_of_runs(
                auth,
                batch_runs,
                evaluator_rules,
            )


async def fetch_and_execute_examples_serializable(
    request_dict: Dict[str, Any],
    auth_dict: Dict[str, Any],
    session_dict: Dict[str, Any],
    offset: int = 0,
    poll_rules: bool = False,
) -> Output:
    auth: AuthInfo = await arun_in_executor(AuthInfo.model_validate, auth_dict)
    session = await arun_in_executor(
        schemas.TracerSessionWithoutVirtualFields.model_validate, session_dict
    )
    request = await arun_in_executor(
        PlaygroundRunOverDatasetRequestSchema.model_validate, request_dict
    )
    return await fetch_and_execute_examples(
        request,
        auth,
        session,
        offset=offset,
        poll_rules=poll_rules,
    )


async def _format_attachment_urls(
    attachments: dict | None,
) -> dict[str, dict]:
    new_attachments: dict[str, dict] = {}

    if attachments:
        async with httpx.AsyncClient(timeout=10) as aclient:
            for k, v in attachments.items():
                if isinstance(v, str):
                    v = {"storage_url": v}
                elif not isinstance(v, dict):
                    continue
                mime_type = v.get("mime_type")
                # We only allow image and PDF attachments to be used in the playground
                if not mime_type or is_supported_mime_type(mime_type):
                    try:
                        # We avoid downloading the full content of a presigned url if it is not an image/pdf/audio
                        # (necessary because older examples won't have mime_type)
                        async with aclient.stream(
                            "GET", get_signed_url_go(v["storage_url"])
                        ) as response:
                            content_type = response.headers.get("Content-Type")
                            content_type_supported = (
                                content_type and is_supported_mime_type(content_type)
                            )

                            if content_type_supported:
                                content = await response.aread()
                                attachment_key = k.removeprefix("attachment.").replace(
                                    ".", "_"
                                )
                                new_attachments[attachment_key] = {
                                    "data": (
                                        f"data:{content_type};base64,"
                                        if not content_type.startswith("audio/")
                                        else ""
                                    )
                                    + base64.b64encode(content).decode("utf-8"),
                                    "mime_type": content_type,
                                }
                    except Exception as e:
                        await logger.aerror(
                            "Failed to fetch attachment", error=e, attachment=v
                        )

    return new_attachments


def _should_run_evals_sync(auth: AuthInfo) -> bool:
    return (
        auth.tenant_config.organization_config.playground_evaluator_strategy == "sync"
    )


def _should_run_evals_in_background(auth: AuthInfo) -> bool:
    return (
        auth.tenant_config.organization_config.playground_evaluator_strategy
        == "background"
    )


async def fetch_and_execute_examples(
    request: PlaygroundRunOverDatasetBatchRequestSchema,
    auth: AuthInfo,
    session: schemas.TracerSessionWithoutVirtualFields,
    offset: int = 0,
    poll_rules: bool = False,
) -> Output:
    if request.batch_size is not None and (
        request.batch_size * request.repetitions
        > shared_settings.PLAYGROUND_RUN_OVER_DATASET_MAX_SYNC_EXAMPLES
    ):
        raise HTTPException(
            status_code=400,
            detail=f"Batch size must be less than {shared_settings.PLAYGROUND_RUN_OVER_DATASET_MAX_SYNC_EXAMPLES}",
        )
    batch_size = (
        request.batch_size
        or shared_settings.PLAYGROUND_RUN_OVER_DATASET_EXAMPLE_BATCH_SIZE
    )
    # Fetch examples
    async with asyncpg_conn() as db:
        as_json = await fetch_examples_json(
            db,
            auth,
            schemas.FilterQueryParamsForExampleSchema(
                dataset=request.dataset_id,
                splits=request.dataset_splits,
                offset=offset,
                limit=batch_size + 1,
            ),
        )
        examples_all = [schemas.Example.model_validate_json(row) for row in as_json]
        examples_batch = examples_all[:batch_size]
        examples_batch = examples_batch * request.repetitions
        examples_batch_run_ids = [uuid4() for _ in range(len(examples_batch))]
    # Call batch in playground service
    async with get_internal_tenant_scoped_service_client(
        ServiceName.PLAYGROUND, auth.tenant_id
    ) as playground_client:
        response = await playground_client.post(
            "/internal/playground/examples/batch",
            json={
                **request.model_dump(
                    exclude={"dataset_id", "repetitions", "evaluator_rules"}
                ),
                "examples": [
                    {
                        "id": str(e.id),
                        "inputs": e.inputs,
                        "run_id": str(run_id),
                        "attachments": await _format_attachment_urls(e.attachment_urls),
                    }
                    for e, run_id in zip(examples_batch, examples_batch_run_ids)
                ],
                "project_name": session.name,
                "project_id": str(session.id),
            },
            headers=auth.to_headers(),
        )
        response.raise_for_status()
        runs_to_return = response.json()
    if should_run_evals_inline(auth) and request.evaluator_rules:
        # poll_rules is only true on the first batch, in which case we should evaluate up to 20 runs within the endpoint.
        # Otherwise, we should evaluate in the background.
        if poll_rules:
            runs_to_evaluate_directly = []
            runs_to_evaluate_in_background = []
            if _should_run_evals_in_background(auth):
                runs_to_evaluate_in_background = runs_to_return
            elif request.repetitions == 1:
                runs_to_evaluate_directly = runs_to_return
            else:
                # Go through the runs and take ONE run for each repetition. i.e. we should evaluate 1 run for each unique reference_example_id
                # and execute it synchronously, and the rest in the background.
                visited_reference_example_ids = set()
                for run in runs_to_return:
                    if (
                        run["reference_example_id"] not in visited_reference_example_ids
                        and _should_run_evals_sync(auth)
                    ):  # if we're not running evals synchronously, then we should add all runs to runs_to_evaluate_in_background
                        runs_to_evaluate_directly.append(run)
                        visited_reference_example_ids.add(run["reference_example_id"])
                    else:
                        runs_to_evaluate_in_background.append(run)
            coros = []
            coros.append(
                _evaluate_batch_of_runs_directly_first_x_rules(
                    auth,
                    runs_to_evaluate_directly,
                    request.evaluator_rules,
                )
            )
            coros.append(
                _evaluate_batch_of_runs(
                    auth,
                    runs_to_evaluate_in_background,
                    request.evaluator_rules,
                )
            )
            await asyncio.gather(*coros)
        else:
            await _evaluate_batch_of_runs(
                auth,
                runs_to_return,
                request.evaluator_rules,
            )
    elif poll_rules and not should_run_evals_inline(auth):
        await create_dataset_runs_manager_background_task(
            auth,
            request.dataset_id,
            set(examples_batch_run_ids),
            {e.id for e in examples_batch},
        )
    if len(examples_all) > batch_size:
        await create_fetch_and_execute_examples_background_task(
            request,
            auth,
            session,
            offset=offset + batch_size,
        )
    else:
        await update_tracer_session(
            auth,
            session.id,
            schemas.TracerSessionUpdate(end_time=datetime.datetime.now()),
        )
    return runs_to_return


async def stream_dataset_response(
    request: PlaygroundRunOverDatasetRequestSchema,
    auth: AuthInfo,
    session: schemas.TracerSessionWithoutVirtualFields,
) -> StreamingResponse:
    if request.repetitions > 1:
        raise HTTPException(
            status_code=400, detail="Repetitions are not supported when streaming"
        )
    async with asyncpg_conn() as db:
        as_json = await fetch_examples_json(
            db,
            auth,
            schemas.FilterQueryParamsForExampleSchema(
                dataset=request.dataset_id,
                splits=request.dataset_splits,
                offset=0,
                limit=shared_settings.PLAYGROUND_RUN_OVER_DATASET_NUM_STREAMING + 1,
            ),
        )
        examples = [schemas.Example.model_validate_json(row) for row in as_json]
        # Process the first batch of examples and stream the results back.
        first_batch = examples[
            : shared_settings.PLAYGROUND_RUN_OVER_DATASET_NUM_STREAMING
        ]
        first_batch_run_ids = [uuid4() for _ in range(len(first_batch))]

        if not should_run_evals_inline(auth):
            await create_dataset_runs_manager_background_task(
                auth,
                request.dataset_id,
                set(first_batch_run_ids),
                {e.id for e in first_batch},
            )

        has_more_examples = (
            len(examples) > shared_settings.PLAYGROUND_RUN_OVER_DATASET_NUM_STREAMING
        )

        # Initialize a list to collect run objects
        runs_to_return: list[dict] = []
        # Add a lock to protect the runs_to_return list during batched processing
        runs_lock = asyncio.Lock()
        # Set to track evaluation tasks
        eval_tasks: set[asyncio.Task] = set()

        # Function to clean up completed tasks
        def task_done_callback(task: asyncio.Task) -> None:
            eval_tasks.discard(task)
            try:
                if exc := task.exception():
                    logger.error("Background evaluation task failed", exc_info=exc)
            except asyncio.CancelledError:
                pass

        # Call playground service and stream the results
        async def stream_playground_results() -> AsyncGenerator[bytes, None]:
            try:
                async with get_internal_tenant_scoped_service_client(
                    ServiceName.PLAYGROUND, auth.tenant_id
                ) as playground_client:
                    stream_response = await playground_client.stream(
                        "POST",
                        "/internal/playground/examples/stream",
                        json={
                            **request.model_dump(
                                exclude={"dataset_id", "evaluator_rules"}
                            ),
                            "examples": [
                                {
                                    "id": str(e.id),
                                    "inputs": e.inputs,
                                    "run_id": str(run_id),
                                    "attachments": await _format_attachment_urls(
                                        e.attachment_urls
                                    ),
                                }
                                for e, run_id in zip(first_batch, first_batch_run_ids)
                            ],
                            "project_name": session.name,
                            "project_id": str(session.id),
                        },
                        headers=auth.to_headers(),
                    )

                    async with stream_response as stream:
                        stream.raise_for_status()
                        # Stream and collect the response
                        decoder = SSEDecoder()
                        async for line in aiter_lines_raw(stream):
                            # `line` is a bytearray, so we need to convert it to bytes before yielding. Also the lines don't have newlines, so it would stream to the client as one massive line, which can't be parsed by the FE.
                            yield bytes(line) + b"\n"
                            # We send up a final "replace" operation from the playground service in order to allow us to run evals over the full run without needing to build up the message object manually like we do on the frontend.
                            # But the frontend already does this as it receives each chunk, so we don't need to yield this chunk to the client: IF its a replace operation, and the run has an end_time, skip yielding.
                            if should_run_evals_inline(auth):
                                sse = decoder.decode(line=line.rstrip(b"\n"))
                                if sse is None:
                                    continue
                                new_runs = get_completed_runs_from_stream_event(sse)
                                batch_to_process = []
                                if new_runs:
                                    # Process runs in batches
                                    async with runs_lock:
                                        runs_to_return.extend(new_runs)
                                        # If we've reached the batch size, process these runs
                                        if (
                                            len(runs_to_return)
                                            >= shared_settings.PLAYGROUND_STREAM_EVALUATOR_BATCH_SIZE
                                        ):
                                            batch_to_process = runs_to_return.copy()
                                            runs_to_return.clear()

                                    # Process as an async task
                                    if batch_to_process and request.evaluator_rules:
                                        task = asyncio.create_task(
                                            _evaluate_batch_of_runs_directly_first_x_rules(
                                                auth,
                                                batch_to_process,
                                                request.evaluator_rules,
                                            )
                                        )
                                        eval_tasks.add(task)
                                        task.add_done_callback(task_done_callback)
            finally:
                # Create background task to execute any remaining runs at the end
                if request.evaluator_rules and should_run_evals_inline(auth):
                    async with runs_lock:
                        remaining_runs = runs_to_return.copy()
                        runs_to_return.clear()

                    if remaining_runs:
                        task = asyncio.create_task(
                            _evaluate_batch_of_runs_directly_first_x_rules(
                                auth,
                                remaining_runs,
                                request.evaluator_rules,
                            )
                        )
                        eval_tasks.add(task)
                        task.add_done_callback(task_done_callback)

                # Wait for all evaluation tasks to complete
                if eval_tasks:
                    await asyncio.gather(*eval_tasks, return_exceptions=True)

                # Create background task after streaming if there are more examples
                if has_more_examples:
                    await create_fetch_and_execute_examples_background_task(
                        request,
                        auth,
                        session,
                        offset=shared_settings.PLAYGROUND_RUN_OVER_DATASET_NUM_STREAMING,
                    )
                else:
                    await update_tracer_session(
                        auth,
                        session.id,
                        schemas.TracerSessionUpdate(end_time=datetime.datetime.now()),
                    )

        # Stream the response directly to the client
        return StreamingResponse(
            stream_playground_results(),
            media_type="text/event-stream",
        )
