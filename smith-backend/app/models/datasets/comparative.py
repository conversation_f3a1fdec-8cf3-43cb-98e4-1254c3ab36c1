from datetime import timezone
from typing import Any, Dict, <PERSON>, <PERSON><PERSON>, Union
from uuid import UUID

import asyncpg
import or<PERSON><PERSON>
from fastapi import HTT<PERSON>Exception
from lc_config.settings import shared_settings as settings
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.database import asyncpg_conn, asyncpg_pool, kwargs_to_pgpos

from app import schemas
from app.api.auth.schemas import AuthInfo, ShareDatasetInfo


def _build_comparative_experiments_filters(
    auth: AuthInfo | ShareDatasetInfo,
    query_params: schemas.QueryParamsForComparativeExperimentsSchema,
) -> Tuple[List[str], Dict[str, Any]]:
    filters: List[str] = []
    params: Dict[str, Any] = {}

    if isinstance(auth, ShareDatasetInfo):
        filters.append("comparative_experiment.reference_dataset_id = $dataset_id")
        filters.append("tracer_session.reference_dataset_id = $dataset_id")
        params["dataset_id"] = auth.dataset_id
    else:
        if query_params.reference_dataset_id is None:
            raise HTTPException(
                status_code=400,
                detail="reference_dataset_id is required",
            )
        filters.append("comparative_experiment.tenant_id = $tenant_id")
        filters.append("tracer_session.tenant_id = $tenant_id")
        params["tenant_id"] = auth.tenant_id
        filters.append("comparative_experiment.reference_dataset_id = $dataset_id")
        filters.append("tracer_session.reference_dataset_id = $dataset_id")
        params["dataset_id"] = query_params.reference_dataset_id

    if query_params.name:
        filters.append("comparative_experiment.name = $name")
        params["name"] = query_params.name
    if query_params.name_contains:
        filters.append(
            """EXISTS (
            SELECT 1 FROM tracer_session ts
            JOIN tracer_session_comparative_experiments tsce ON ts.id = tsce.tracer_session_id
            WHERE tsce.comparative_experiment_id = comparative_experiment.id
            AND ts.name ILIKE $name_contains
        )"""
        )
        params["name_contains"] = f"%{query_params.name_contains}%"
    if query_params.id:
        filters.append("comparative_experiment.id = ANY($id)")
        params["id"] = query_params.id

    return filters, params


async def get_comparative_experiments(
    auth: AuthInfo | ShareDatasetInfo,
    query_params: schemas.QueryParamsForComparativeExperimentsSchema,
) -> Tuple[
    List[Union[schemas.ComparativeExperiment, schemas.PublicComparativeExperiment]], int
]:
    """Get comparative experiments."""
    filters, params = _build_comparative_experiments_filters(auth, query_params)

    # Adjusted query to fetch required fields without aggregation
    query = f"""SELECT
                    comparative_experiment.*,
                    json_agg(
                        json_build_object(
                            'session_id', tracer_session.id,
                            'session_name', tracer_session.name,
                            'index', tracer_session_comparative_experiments.index
                        ) ORDER BY tracer_session_comparative_experiments.index
                    ) AS sessions
                FROM comparative_experiment
                JOIN tracer_session_comparative_experiments
                    ON comparative_experiment.id = tracer_session_comparative_experiments.comparative_experiment_id
                JOIN tracer_session
                    ON tracer_session_comparative_experiments.tracer_session_id = tracer_session.id
                WHERE {" AND ".join(filters)}
                GROUP BY comparative_experiment.id
                ORDER BY {query_params.sort_by.value} {"DESC" if query_params.sort_by_desc else "ASC"}, comparative_experiment.name
                LIMIT {query_params.limit + 1} OFFSET {query_params.offset}"""

    total_sql, total_args = kwargs_to_pgpos(query, params)
    async with asyncpg_pool() as pool:
        rows = await pool.fetch(total_sql, *total_args)

    experiments = []
    for row in rows:
        experiment = {
            "id": row["id"],
            "name": row["name"],
            "description": row["description"],
            "created_at": row["created_at"],
            "modified_at": row["modified_at"],
            "extra": row["extra"],
            "experiments_info": [],
            "feedback_stats": {},
        }
        if isinstance(auth, AuthInfo):
            experiment.update(
                {
                    "tenant_id": row["tenant_id"],
                    "reference_dataset_id": row["reference_dataset_id"],
                }
            )

        sessions = orjson.loads(row["sessions"])
        for session in sessions:
            experiment["experiments_info"].append(
                {
                    "id": session["session_id"],
                    "name": session["session_name"],
                }
            )

        experiments.append(experiment)

    # Extract tracer session ids from the experiments
    session_ids = set()
    for exp in experiments:
        for session in exp["experiments_info"]:
            session_ids.add(session["id"])

    # Prepare ClickHouse query to fetch feedback aggregates
    if session_ids:
        clickhouse_query = f"""
            WITH feedbacks_cte AS (
                SELECT *
                FROM feedbacks_rmt FINAL
                WHERE {"tenant_id = {tenant_id} AND" if isinstance(auth, AuthInfo) else ""}
                is_root = 1
                AND session_id IN {{session_ids}}
                AND comparative_experiment_id IS NOT NULL
            )
            SELECT
                key,
                session_id,
                comparative_experiment_id,
                sum(score) as total_score
            FROM feedbacks_cte
            GROUP BY key, session_id, comparative_experiment_id
            SETTINGS max_threads = {settings.FETCH_COMP_FEEDBACKS_MAX_THREADS}
        """

        async with clickhouse_client(ClickhouseClient.USER_QUERIES) as ch:
            tenant_id_dict = (
                {"tenant_id": auth.tenant_id} if isinstance(auth, AuthInfo) else {}
            )
            session_ids_list = [str(id) for id in session_ids]
            feedback_results = await ch.fetch(
                "fetch_comparative_feedbacks",
                clickhouse_query,
                params={"session_ids": session_ids_list, **tenant_id_dict},
            )

        # Organize feedback data by experiment and key
        for feedback in feedback_results:
            exp_id = feedback["comparative_experiment_id"]
            key = feedback["key"]
            session_id = feedback["session_id"]
            total_score = feedback["total_score"]

            # Find the corresponding experiment by id
            for exp in experiments:
                if exp["id"] == exp_id:
                    if key not in exp["feedback_stats"]:
                        exp["feedback_stats"][key] = {}
                    exp["feedback_stats"][key][session_id] = float(
                        total_score if total_score is not None else 0
                    )
                    break

    total = len(experiments) + query_params.offset
    if isinstance(auth, ShareDatasetInfo):
        experiments_list = [
            schemas.PublicComparativeExperiment(**exp)
            for exp in experiments[: query_params.limit]
        ]
    else:
        experiments_list = [
            schemas.ComparativeExperiment(**exp)
            for exp in experiments[: query_params.limit]
        ]
    return experiments_list, total


async def create_comparative_experiment(
    auth: AuthInfo,
    comparative_experiment: schemas.ComparativeExperimentCreate,
) -> schemas.ComparativeExperimentBase:
    """Create a new comparative experiment."""
    if comparative_experiment.created_at:
        if not comparative_experiment.created_at.tzinfo:
            comparative_experiment.created_at = (
                comparative_experiment.created_at.replace(tzinfo=timezone.utc)
            )
    async with asyncpg_conn() as db:
        sessions = await db.fetch(
            "SELECT id, tenant_id, reference_dataset_id FROM tracer_session WHERE id = ANY($1) AND tenant_id = $2",
            comparative_experiment.experiment_ids,
            auth.tenant_id,
        )
        if not len(sessions) == len(comparative_experiment.experiment_ids):
            raise HTTPException(
                status_code=404,
                detail="Experiments not found.",
            )
        if not all(
            [
                session["reference_dataset_id"]
                == comparative_experiment.reference_dataset_id
                for session in sessions
            ]
        ):
            raise HTTPException(
                status_code=400,
                detail="All sessions must belong to the same dataset.",
            )

    experiment_create_query = """INSERT INTO comparative_experiment (id, name, description, tenant_id, reference_dataset_id, extra, created_at, modified_at)
    VALUES ($id, $name, $description, $tenant_id, $reference_dataset_id, $extra, $created_at, $modified_at)
    RETURNING *"""
    experiment_create_sql, experiment_create_args = kwargs_to_pgpos(
        experiment_create_query,
        {
            **comparative_experiment.model_dump(),
            "tenant_id": auth.tenant_id,
        },
    )

    association_table_query = """
    INSERT INTO tracer_session_comparative_experiments (tracer_session_id, comparative_experiment_id, index)
    VALUES ($1, $2, $3)
    """
    async with asyncpg_conn() as db, db.transaction():
        try:
            results = await db.fetchrow(experiment_create_sql, *experiment_create_args)
        except asyncpg.UniqueViolationError:
            raise HTTPException(
                status_code=409,
                detail="Comparative Experiment with this name already exists.",
            )
        params = [
            (experiment, comparative_experiment.id, index)
            for index, experiment in enumerate(comparative_experiment.experiment_ids)
        ]
        await db.executemany(association_table_query, params)
    return schemas.ComparativeExperimentBase(**results)


async def delete_comparative_experiment(
    auth: AuthInfo,
    comparative_experiment_id: UUID,
) -> None:
    """Create a new comparative experiment."""
    async with asyncpg_conn() as db:
        comparative_experiment = await db.fetchval(
            "SELECT id FROM comparative_experiment WHERE id = $1 AND tenant_id = $2",
            comparative_experiment_id,
            auth.tenant_id,
        )
        if comparative_experiment is None:
            raise HTTPException(
                status_code=404,
                detail="Comparative experiment not found.",
            )

        await db.execute(
            "DELETE FROM comparative_experiment WHERE id = $1 AND tenant_id = $2",
            comparative_experiment_id,
            auth.tenant_id,
        )
