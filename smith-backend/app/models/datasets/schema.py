from typing import Any, Dict, List, Optional
from uuid import UUID

from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel, Field


class PlaygroundRequestSchema(BaseModel):
    manifest: Any
    secrets: Dict[str, str]
    run_id: Optional[str] = None
    repo_id: Optional[str] = None
    tools: Optional[List[Any]] = None
    tool_choice: Optional[str] = None
    parallel_tool_calls: Optional[bool] = None
    options: RunnableConfig
    project_name: Optional[str] = None
    repo_handle: Optional[str] = None
    owner: Optional[str] = None
    commit: Optional[str] = None
    evaluator_rules: Optional[List[UUID]] = None
    requests_per_second: Optional[int] = None
    use_or_fallback_to_workspace_secrets: bool = False


class PlaygroundRunOverDatasetRequestSchema(PlaygroundRequestSchema):
    project_name: str
    dataset_id: UUID
    dataset_splits: Optional[list[str]] = None
    repetitions: int = Field(default=1, ge=1, le=30)


class PlaygroundRunOverDatasetBatchRequestSchema(PlaygroundRunOverDatasetRequestSchema):
    batch_size: int | None = Field(default=None, ge=1, le=100)


class StudioRunOverDatasetRequestSchema(BaseModel):
    project_name: str
    dataset_id: UUID
    evaluator_rules: Optional[list[UUID]] = None
