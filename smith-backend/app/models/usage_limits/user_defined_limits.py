import uuid
from datetime import datetime, timezone
from enum import Enum

import or<PERSON><PERSON>
import structlog
from fastapi import HTTPException
from lc_database.database import asyncpg_conn
from pydantic import BaseModel, Field

from app import config, schemas
from app.api.auth.schemas import AuthInfo, BaseAuthInfo, OrgAuthInfo
from app.memoize import redis_cache
from app.utils import start_of_next_month_timestamp

logger = structlog.get_logger(__name__)


def usage_limit_unique_traces_per_month_hll_key(tenant_id: uuid.UUID) -> str:
    current_month = datetime.now(timezone.utc).strftime("%Y-%m")
    return f"smith:runs:usage_limit_unique_run_ids_hll:{current_month}:{tenant_id}"


def usage_limit_unique_longlived_traces_per_month_hll_key(tenant_id: uuid.UUID) -> str:
    current_month = datetime.now(timezone.utc).strftime("%Y-%m")
    return f"smith:runs:usage_limit_unique_longlived_run_ids_hll:{current_month}:{tenant_id}"


class UsageLimitDuration(str, Enum):
    """Duration of usage limit."""

    MONTH = "month"

    def get_expiry(self) -> int:
        if self == UsageLimitDuration.MONTH:
            return int(start_of_next_month_timestamp())
        else:
            raise ValueError(f"Unknown duration {self.value}")


class UsageLimitUnit(str, Enum):
    """Unit of usage limit."""

    TRACES = "traces"
    LONGLIVED_TRACES = "longlived_traces"

    def get_all_limits(self) -> list["UsageLimitType"]:
        if self == UsageLimitUnit.TRACES:
            return [UsageLimitType.MONTHLY_TRACES]
        elif self == UsageLimitUnit.LONGLIVED_TRACES:
            return [UsageLimitType.MONTHLY_LONGLIVED_TRACES]
        else:
            raise ValueError(f"Unknown limit unit {self.value}")


class UsageLimitType(str, Enum):
    """Type of usage limit."""

    MONTHLY_TRACES = "monthly_traces"
    MONTHLY_LONGLIVED_TRACES = "monthly_longlived_traces"

    def get_key_for_tenant(self, tenant_id: uuid.UUID) -> str:
        if self == UsageLimitType.MONTHLY_TRACES:
            # Unfortunately there was a bug in the original code where only
            # added to the hll for monthly traces when a usage limit was added,
            # which could have been midmonth.
            #
            # Instead, we will add to the hll for the current month that was already
            # in place. This works, becuase this is already being calculated for all
            # tenants, so we can still get the accurate number of traces
            #
            # This is a bit of a hack, but we know it won't double count traces,
            # since the hll is already used for tracking unique traces.
            #
            # TODO: later we should separate out these hlls
            return usage_limit_unique_traces_per_month_hll_key(tenant_id)
        elif self == UsageLimitType.MONTHLY_LONGLIVED_TRACES:
            return usage_limit_unique_longlived_traces_per_month_hll_key(tenant_id)
        else:
            raise ValueError(f"Unknown limit type {self.value}")

    def mark_seen_events_in_pipe(
        self, event_ids: set[uuid.UUID], pipe, tenant_id: uuid.UUID
    ) -> None:
        key = self.get_key_for_tenant(tenant_id)
        expiry = self.get_duration().get_expiry()

        pipe.pfadd(key, *(orjson.dumps(event_id) for event_id in event_ids))
        pipe.expireat(key, expiry)

    def get_unit(self):
        if self == UsageLimitType.MONTHLY_TRACES:
            return UsageLimitUnit.TRACES
        elif self == UsageLimitType.MONTHLY_LONGLIVED_TRACES:
            return UsageLimitUnit.LONGLIVED_TRACES
        else:
            raise ValueError(f"Unknown limit type {self.value}")

    def get_duration(self):
        if self == UsageLimitType.MONTHLY_TRACES:
            return UsageLimitDuration.MONTH
        elif self == UsageLimitType.MONTHLY_LONGLIVED_TRACES:
            return UsageLimitDuration.MONTH
        else:
            raise ValueError(f"Unknown limit type {self.value}")

    def to_tenant_usage_limit_type(self) -> schemas.TenantUsageLimitType:
        if self == UsageLimitType.MONTHLY_TRACES:
            return schemas.TenantUsageLimitType.user_defined_monthly_traces
        elif self == UsageLimitType.MONTHLY_LONGLIVED_TRACES:
            return schemas.TenantUsageLimitType.user_defined_monthly_longlived_traces
        else:
            return schemas.TenantUsageLimitType.user_defined_unknown


class UpsertUsageLimit(BaseModel):
    """Request body for creating or updating a usage limit."""

    limit_type: UsageLimitType
    limit_value: int
    id: uuid.UUID = Field(default_factory=lambda: uuid.uuid4())


class UsageLimit(UpsertUsageLimit):
    """Usage limit model."""

    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    def get_reject_key(self) -> str:
        return f"smith:user_defined_usage_limit_reject:{self.limit_type.value}:{self.tenant_id}"

    def get_current_level_from_pipe(self, pipe) -> None:
        pipe.pfcount(self.limit_type.get_key_for_tenant(self.tenant_id))

    def check_reject_set_from_pipe(self, pipe) -> None:
        pipe.exists(self.get_reject_key())

    def _get_reject_set_expiry(self) -> int:
        return config.settings.USER_DEFINED_USAGE_LIMIT_REJECT_EXPIRY_SEC

    def throw_limit(self) -> None:
        raise HTTPException(
            status_code=429,
            detail=f"Usage limit {self.limit_type.value} of {self.limit_value} exceeded",
        )

    async def check_level_with_pipe(self, current_level: int, pipe) -> None:
        expiry = self._get_reject_set_expiry()

        # -1 means unlimited
        if self.limit_value != -1 and current_level >= self.limit_value:
            await logger.awarn(
                "Usage Limit Exceeded",
                metadata={
                    "tenant_id": str(self.tenant_id),
                    "limit": self.limit_type.value,
                    "limit_value": self.limit_value,
                    "current_value": current_level,
                },
            )

            pipe.set(self.get_reject_key(), "1", ex=expiry, nx=True)
            self.throw_limit()


@redis_cache(60)
async def list_user_defined_usage_limits(auth: BaseAuthInfo) -> list[UsageLimit]:
    return await list_user_defined_usage_limits_uncached(auth)


async def list_user_defined_usage_limits_uncached(
    auth: BaseAuthInfo,
) -> list[UsageLimit]:
    async with asyncpg_conn() as conn:
        rows = await conn.fetch(
            """
            SELECT * FROM per_tenant_usage_limits
            WHERE tenant_id = $1
            """,
            auth.tenant_id,
        )
        return [UsageLimit(**row) for row in rows]


async def list_org_user_defined_usage_limits_uncached(
    auth: OrgAuthInfo,
) -> list[UsageLimit]:
    async with asyncpg_conn() as conn:
        rows = await conn.fetch(
            """
            SELECT per_tenant_usage_limits.*
            FROM per_tenant_usage_limits
            JOIN tenants ON per_tenant_usage_limits.tenant_id = tenants.id
            WHERE tenants.organization_id = $1
            ORDER BY per_tenant_usage_limits.created_at
            """,
            auth.organization_id,
        )
        return [UsageLimit(**row) for row in rows]


async def upsert_user_defined_usage_limit(
    auth: AuthInfo, usage_limit: UpsertUsageLimit
) -> UsageLimit:
    async with asyncpg_conn() as db, db.transaction():
        row = await db.fetchrow(
            """
            INSERT INTO per_tenant_usage_limits (tenant_id, limit_type, limit_value, id, created_at, updated_at)
            VALUES ($1, $2, $3, $4, NOW(), NOW())
            ON CONFLICT (tenant_id, limit_type)
            DO UPDATE SET
                limit_value = $3,
                updated_at = NOW()
            RETURNING *
            """,
            auth.tenant_id,
            usage_limit.limit_type,
            usage_limit.limit_value,
            usage_limit.id,
        )
        return UsageLimit(**row)


async def delete_user_defined_usage_limit(auth: AuthInfo, limit_id: uuid.UUID) -> None:
    async with asyncpg_conn() as db, db.transaction():
        deleted_row = await db.fetchrow(
            """
            DELETE FROM per_tenant_usage_limits
            WHERE tenant_id = $1 AND id = $2
            RETURNING *
            """,
            auth.tenant_id,
            limit_id,
        )
        if not deleted_row:
            raise HTTPException(status_code=404, detail="Usage limit not found")
