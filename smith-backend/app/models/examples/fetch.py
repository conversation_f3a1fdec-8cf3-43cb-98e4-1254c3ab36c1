import datetime
import json
import uuid
from collections import defaultdict
from typing import Any, Dict, Literal, Optional, Tuple, Union

import asyncpg
import orjson
from fastapi import HTTPException
from lc_database import database
from lc_database.s3_client import get_signed_url_go

from app import schemas
from app.api.auth import AuthInfo
from app.api.auth.schemas import BaseAuthInfo, ShareDatasetInfo
from app.models.datasets.sql import select_splits_coalesce_stmt
from app.models.examples import sql as examples_sql
from app.models.examples.attrs import get_example_attributes
from app.models.query_lang.parse import (
    Operation,
    Operator,
    parse_as_filter_directive_examples,
)
from app.models.query_lang.translate import SqlVisitorPostgres


def _parse_filter(
    filter: str, table: Literal["examples_log", "examples_tagged"]
) -> Tuple[str, dict]:
    filter_directive = Operation(operator=Operator.AND, arguments=[])
    filter_directive.arguments.append(parse_as_filter_directive_examples(filter))
    filter_sql, filter_params, _, _, _ = filter_directive.accept(
        SqlVisitorPostgres(
            attributes=get_example_attributes(table),
            main_table=table,
        )
    )
    conditions = filter_sql.split("WHERE")[1].strip()
    return conditions, filter_params


def _build_example_filters(
    auth: AuthInfo,
    query_params: schemas.FilterQueryParamsForExampleSchema,
) -> Tuple[str, str, dict]:
    filters = []
    params: Dict[str, Any] = {"tenant_id": auth.tenant_id}

    if query_params.filter is not None and query_params.metadata is not None:
        raise HTTPException(
            status_code=400, detail="filter and metadata cannot be used together"
        )
    if query_params.dataset is not None:
        filters.append("dataset_id = $dataset_id")
        params["dataset_id"] = query_params.dataset
    if query_params.id is not None:
        filters.append("examples_log.id = ANY($id_list)")
        params["id_list"] = query_params.id
    dataset_where_clause = (
        "WHERE tenant_id = $tenant_id AND examples_log.inputs is not null"
    )
    if query_params.metadata is not None:
        dataset_where_clause += " AND examples_log.metadata @> $metadata"
        try:
            params["metadata"] = orjson.loads(query_params.metadata)
        except orjson.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid metadata JSON")
    if query_params.full_text_contains is not None:
        for i, term in enumerate(query_params.full_text_contains):
            params[f"term_{i}"] = f"%{term}%"
            filters.append(
                f"(examples_log.inputs::text ILIKE ${f'term_{i}'} OR examples_log.outputs::text ILIKE ${f'term_{i}'})"
            )
    if query_params.splits is not None:
        params["splits"] = query_params.splits
        filters.append(
            f"{select_splits_coalesce_stmt.format(table_name='examples_log')} && $splits"
        )
    if query_params.filter is not None:
        conditions, filter_params = _parse_filter(query_params.filter, "examples_log")
        dataset_where_clause += f" AND {conditions}"
        params.update(filter_params)

    where_clause = (" AND " + " AND ".join(filters)) if filters else ""
    return dataset_where_clause, where_clause, params


def _build_public_example_filters(
    auth: ShareDatasetInfo,
    query_params: schemas.FilterQueryParamsForExampleSchema,
) -> Tuple[str, dict]:
    filters = ["dataset_id = $dataset_id"]
    params: Dict[str, Any] = {"dataset_id": auth.dataset_id}

    if query_params.filter is not None and query_params.metadata is not None:
        raise HTTPException(
            status_code=400, detail="filter and metadata cannot be used together"
        )

    if query_params.id:
        filters.append("examples_log.id = ANY($id_list)")
        params["id_list"] = query_params.id
    if query_params.metadata:
        filters.append("metadata @> $metadata")
        try:
            params["metadata"] = query_params.metadata
        except orjson.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid metadata JSON")
    if query_params.filter is not None:
        conditions, filter_params = _parse_filter(query_params.filter, "examples_log")
        filters.append(conditions)
        params.update(filter_params)
    if query_params.full_text_contains:
        for i, term in enumerate(query_params.full_text_contains):
            params[f"term_{i}"] = f"%{term}%"
            filters.append(
                f"(examples_log.inputs::text ILIKE ${f'term_{i}'} OR examples_log.outputs::text ILIKE ${f'term_{i}'})"
            )
    if query_params.splits is not None:
        params["splits"] = query_params.splits
        filters.append(
            f"{select_splits_coalesce_stmt.format(table_name='examples_log')} && $splits"
        )

    return " AND " + " AND ".join(filters), params


def _format_query(
    examples_filters: Optional[str] = None,
    dataset_filters: Optional[str] = None,
    select: tuple[schemas.ExampleSelect, ...] | None = None,
    ignore_unselected: bool = False,
) -> str:
    select = select or tuple(field for field in schemas.ExampleSelect)
    projections = f"""json_build_object({
        ", ".join(
            f"'{field.value}', {examples_sql.by_timestamp_projections[field]}"
            if field in select
            else f"'{field.value}', NULL"
            for field in schemas.ExampleSelect
            if not ignore_unselected or field in select
        )
    })"""
    return examples_sql.select_examples_sql.format(
        projections=projections,
        examples_filters=examples_filters or "",
        dataset_filters=dataset_filters or "",
    )


def populate_attachment_urls_correct_format(
    attachment_urls: dict[str, Any],
) -> dict[str, dict[str, str | None]]:
    return_dict: dict[str, dict[str, str | None]] = defaultdict(dict)
    for key, value in attachment_urls.items():
        if isinstance(value, str):
            return_dict[key]["storage_url"] = value
            return_dict[key]["presigned_url"] = get_signed_url_go(value)
            return_dict[key]["mime_type"] = None
        elif isinstance(value, dict) and "presigned_url" not in value:
            return_dict[key]["storage_url"] = value["storage_url"]
            return_dict[key]["presigned_url"] = get_signed_url_go(value["storage_url"])
            return_dict[key]["mime_type"] = value["mime_type"]
    return return_dict


async def fetch_example(
    db: asyncpg.Connection,
    auth: AuthInfo,
    example_id: uuid.UUID,
    as_of: Union[datetime.datetime, str],
) -> schemas.Example:
    if isinstance(as_of, str):
        return await fetch_example_by_tag(db, auth, example_id, as_of)

    params = {
        "example_id": example_id,
        "tenant_id": auth.tenant_id,
        "modified_at": as_of,
    }
    query = examples_sql.select_examples_sql.format(
        projections=", ".join(
            f"{examples_sql.by_timestamp_projections[select_field]} as {select_field.value}"
            for select_field in schemas.ExampleSelect
        ),
        examples_filters="AND examples_log.id = $example_id",
        dataset_filters="WHERE examples_log.inputs is not null AND tenant_id = $tenant_id",
    )

    sql_query = database.kwargs_to_pgpos(query, params)
    example_row = await db.fetchrow(sql_query.sql, *sql_query.args)
    auth.exists(example_row)

    example_row = dict(example_row)
    if "attachment_urls" in example_row and example_row["attachment_urls"]:
        example_row["attachment_urls"] = populate_attachment_urls_correct_format(
            example_row["attachment_urls"]
        )

    return schemas.Example.model_validate(example_row)


async def fetch_examples(
    db: asyncpg.Connection,
    auth: AuthInfo | ShareDatasetInfo,
    query_params: schemas.FilterQueryParamsForExampleSchema,
) -> list[schemas.Example]:
    as_json = await fetch_examples_json(db, auth, query_params)
    return [schemas.Example.model_validate_json(row) for row in as_json]


async def fetch_examples_json(
    db: asyncpg.Connection,
    auth: AuthInfo | ShareDatasetInfo,
    query_params: schemas.FilterQueryParamsForExampleSchema,
    descending: bool = True,
) -> list[str]:
    if isinstance(auth, ShareDatasetInfo):
        select: tuple[schemas.ExampleSelect, ...] = tuple(
            s for s in query_params.select if s != schemas.ExampleSelect.source_run_id
        )
    else:
        select = query_params.select

    dataset_id = (
        auth.dataset_id if isinstance(auth, ShareDatasetInfo) else query_params.dataset
    )
    if dataset_id:
        dataset = await db.fetchval(
            "SELECT id FROM dataset WHERE id = $1 AND tenant_id = $2",
            dataset_id,
            auth.tenant_id,
        )
        if not dataset:
            raise HTTPException(
                status_code=404, detail=f"dataset {dataset_id} not found"
            )

    if isinstance(query_params.as_of, str):
        return await _fetch_examples_by_tag(
            db,
            auth,
            tag=query_params.as_of,
            dataset_id=auth.dataset_id
            if isinstance(auth, ShareDatasetInfo)
            else query_params.dataset,
            example_ids=query_params.id,
            metadata=query_params.metadata,
            full_text_contains=query_params.full_text_contains,
            limit=query_params.limit + 1,
            offset=query_params.offset,
            splits=query_params.splits,
            descending=descending,
            order=schemas.ExampleListOrder.recent
            if isinstance(auth, ShareDatasetInfo)
            else query_params.order,
            random_seed=None
            if isinstance(auth, ShareDatasetInfo)
            else query_params.random_seed,
            select=select,
            filter=query_params.filter,
            ignore_unselected=query_params.ignore_unselected,
        )

    if isinstance(auth, AuthInfo):
        dataset_where_clause, where_clause, params = _build_example_filters(
            auth, query_params
        )
    elif isinstance(auth, ShareDatasetInfo):
        dataset_where_clause = "WHERE examples_log.inputs is not null"
        where_clause, params = _build_public_example_filters(auth, query_params)
    else:
        raise ValueError(f"Invalid auth type: {type(auth)}")

    params["modified_at"] = query_params.as_of
    offset_clause = ""
    if query_params.offset > 0:
        params["offset"] = query_params.offset
        offset_clause = "OFFSET $offset"
    limit_clause = " "
    if query_params.limit > 0:
        params["limit"] = query_params.limit + 1
        limit_clause = " LIMIT $limit"

    order_by = "DESC" if descending else "ASC"
    order_col = ""
    if query_params.order == schemas.ExampleListOrder.recent:
        order_col = "examples_log.modified_at"
    elif query_params.order == schemas.ExampleListOrder.recently_created:
        order_col = "examples_log.created_at"
    elif query_params.order == schemas.ExampleListOrder.random:
        order_col = "random()"
    else:
        raise ValueError(f"Invalid order: {query_params.order}")
    query = f"""
    {
        _format_query(
            select=select,
            examples_filters=where_clause,
            dataset_filters=dataset_where_clause,
            ignore_unselected=query_params.ignore_unselected,
        )
    }
    ORDER BY {order_col} {order_by}, examples_log.id
    {offset_clause}{limit_clause};
    """.strip()

    sql_query = database.kwargs_to_pgpos(query, params)
    rows = await db.fetch(sql_query.sql, *sql_query.args)
    return [row[0] for row in rows]


async def count_examples_json(
    db: asyncpg.Connection,
    auth: AuthInfo | ShareDatasetInfo,
    query_params: schemas.FilterQueryParamsForCountExampleSchema,
) -> int:
    if isinstance(query_params.as_of, str):
        return await count_examples_by_tag(
            db,
            auth,
            tag=query_params.as_of,
            dataset_id=auth.dataset_id
            if isinstance(auth, ShareDatasetInfo)
            else query_params.dataset,
            example_ids=query_params.id,
            metadata=query_params.metadata,
            full_text_contains=query_params.full_text_contains,
            splits=query_params.splits,
            filter=query_params.filter,
        )

    if isinstance(auth, AuthInfo):
        dataset_where_clause, where_clause, params = _build_example_filters(
            auth, query_params
        )
    elif isinstance(auth, ShareDatasetInfo):
        dataset_where_clause = "WHERE examples_log.inputs is not null"
        where_clause, params = _build_public_example_filters(auth, query_params)
    else:
        raise ValueError(f"Invalid auth type: {type(auth)}")

    params["modified_at"] = query_params.as_of
    count_projections = """json_build_object('total_count', COUNT(*))"""
    count_query = f"""{
        examples_sql.select_examples_sql.format(
            projections=count_projections,
            examples_filters=where_clause or "",
            dataset_filters=dataset_where_clause or "",
        )
    };""".strip()

    sql_count_query = database.kwargs_to_pgpos(count_query, params)
    count_row = await db.fetch(sql_count_query.sql, *sql_count_query.args)
    total_count = (
        json.loads(count_row[0]["json_build_object"])["total_count"] if count_row else 0
    )
    return total_count


async def fetch_example_by_tag(
    db: asyncpg.Connection,
    auth: AuthInfo,
    example_id: uuid.UUID,
    tag: str,
) -> schemas.Example:
    projections = ", ".join(
        f"{examples_sql.by_tag_projections[select_field]} as {select_field.value}"
        for select_field in schemas.ExampleSelect
    )
    example = await db.fetchrow(
        examples_sql.select_examples_by_tag_and_id_sql.format(
            projections=projections, dataset_filter=""
        ),
        [example_id],
        tag,
        auth.tenant_id,
        1,
        0,
    )
    auth.exists(example)

    example = dict(example)
    if "attachment_urls" in example and example["attachment_urls"]:
        example["attachment_urls"] = populate_attachment_urls_correct_format(
            example["attachment_urls"]
        )

    return schemas.Example.model_validate(example)


async def _fetch_examples_by_tag_helper(
    db: asyncpg.Connection,
    *,
    dataset_id: uuid.UUID | None,
    example_ids: list[uuid.UUID] | None,
    filter: str | None = None,
    metadata: str | None,
    args_list: list,
    kwargs: Dict[str, Any],
    full_text_contains: list[str] | None,
    splits: list[str] | None,
    projections: str,
    endpoint_type: str = "fetch",
    descending: bool | None = None,
    order: schemas.ExampleListOrder | None = None,
    random_seed: float | None = None,
) -> list[str]:
    sql_template = (
        examples_sql.select_examples_by_tag_and_id_sql
        if example_ids
        else examples_sql.select_examples_by_tag_and_dataset_sql
    )
    if endpoint_type == "count":
        # No need to order and don't want to limit when counting.
        sql_template = sql_template.split("ORDER")[0].split("LIMIT")[0]

    if example_ids:
        if dataset_id:
            args_list.append(dataset_id)
            dataset_filter = f"AND examples_tagged.dataset_id = ${len(args_list)}"
        else:
            dataset_filter = ""

        sql = sql_template.format(
            projections=projections, dataset_filter=dataset_filter
        )
        rows = await db.fetch(sql, *args_list)
        if len(rows) == 0:
            raise HTTPException(status_code=404, detail="Examples not found")
    elif dataset_id:
        if filter is not None and metadata is not None:
            raise HTTPException(
                status_code=400, detail="filter and metadata cannot be used together"
            )
        example_filter = ""
        kwargs.update({"dataset_id": dataset_id})
        if metadata is not None:
            example_filter += " AND examples_tagged.metadata @> $metadata"
            try:
                kwargs["metadata"] = orjson.loads(metadata)
            except orjson.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid metadata JSON")
        if filter is not None:
            conditions, filter_params = _parse_filter(filter, "examples_tagged")
            example_filter += f" AND {conditions}"
            kwargs.update(filter_params)
        if full_text_contains is not None:
            for i, term in enumerate(full_text_contains):
                kwargs[f"term_{i}"] = f"%{term}%"
                example_filter += f" AND (examples_tagged.inputs::text ILIKE ${f'term_{i}'} OR examples_tagged.outputs::text ILIKE ${f'term_{i}'})"
        if splits is not None:
            kwargs["splits"] = splits
            example_filter += f" AND {select_splits_coalesce_stmt.format(table_name='examples_tagged')} && $splits"

        if endpoint_type == "count":
            sql_with_kwargs = sql_template.format(
                projections=projections,
                example_filter=example_filter,
            )
            sql, args_list = database.kwargs_to_pgpos(sql_with_kwargs, kwargs)
            row = await db.fetchrow(sql, *args_list)
            rows = [row] if row else []
        else:  # endpoint_type == 'fetch'
            if order == schemas.ExampleListOrder.recent:
                order_col = "examples_tagged.modified_at"
            elif order == schemas.ExampleListOrder.recently_created:
                order_col = "examples_tagged.created_at"
            elif order == schemas.ExampleListOrder.random:
                order_col = "random()"
                if random_seed is not None:
                    tx = db.transaction()
                    await tx.start()
                    await db.execute("SELECT setseed($1)", random_seed)
            else:
                raise ValueError(f"Invalid order: {order}")
            # if dataset is provided, use the dataset_sql_string, otherwise use the across_datasets_sql_string to search across all datasets
            try:
                sql_with_kwargs = sql_template.format(
                    projections=projections,
                    direction="DESC" if descending else "ASC",
                    order=order_col,
                    example_filter=example_filter,
                )
                sql, args_list = database.kwargs_to_pgpos(sql_with_kwargs, kwargs)
                rows = await db.fetch(sql, *args_list)
            finally:
                if order == schemas.ExampleListOrder.random and random_seed is not None:
                    await tx.rollback()
    else:
        raise HTTPException(
            status_code=400,
            detail="Either dataset_id or id is required when as_of is a tag.",
        )

    # Return the first col of each row, which is a json object with all the main example info.
    return [row[0] for row in rows]


async def _fetch_examples_by_tag(
    db: asyncpg.Connection,
    auth: BaseAuthInfo,
    *,
    tag: str,
    dataset_id: uuid.UUID | None,
    example_ids: list[uuid.UUID] | None,
    metadata: str | None,
    full_text_contains: list[str] | None,
    splits: list[str] | None,
    limit: int,
    offset: int,
    descending: bool,
    order: schemas.ExampleListOrder,
    random_seed: float | None,
    select: tuple[schemas.ExampleSelect, ...],
    ignore_unselected: bool = False,
    filter: str | None = None,
) -> list[str]:
    return await _fetch_examples_by_tag_helper(
        db,
        dataset_id=dataset_id,
        example_ids=example_ids,
        filter=filter,
        metadata=metadata,
        full_text_contains=full_text_contains,
        splits=splits,
        descending=descending,
        order=order,
        random_seed=random_seed,
        args_list=[example_ids, tag, auth.tenant_id, limit, offset],
        kwargs={
            "tag": tag,
            "tenant_id": auth.tenant_id,
            "limit": limit,
            "offset": offset,
        },
        projections=f"""json_build_object({
            ", ".join(
                f"'{field.value}', {examples_sql.by_tag_projections[field]}"
                if field in select
                else f"'{field.value}', NULL"
                for field in schemas.ExampleSelect
                if not ignore_unselected or field in select
            )
        })""",
    )


async def count_examples_by_tag(
    db: asyncpg.Connection,
    auth: BaseAuthInfo,
    *,
    tag: str,
    dataset_id: uuid.UUID | None,
    example_ids: list[uuid.UUID] | None,
    metadata: str | None,
    full_text_contains: list[str] | None,
    splits: list[str] | None,
    filter: str | None = None,
) -> int:
    rows = await _fetch_examples_by_tag_helper(
        db,
        dataset_id=dataset_id,
        example_ids=example_ids,
        filter=filter,
        metadata=metadata,
        full_text_contains=full_text_contains,
        splits=splits,
        args_list=[example_ids, tag, auth.tenant_id],
        kwargs={
            "tag": tag,
            "tenant_id": auth.tenant_id,
        },
        projections="json_build_object('total_count', COUNT(*))",
        endpoint_type="count",
    )

    try:
        count = json.loads(rows[0])["total_count"]
    except Exception:
        raise HTTPException(status_code=404, detail="Examples not found")
    if not count and example_ids and dataset_id:
        raise HTTPException(
            status_code=404, detail="Specified examples do not exist in this dataset."
        )
    return count
