import datetime
import itertools
import random
import tempfile
import uuid
from collections import defaultdict
from contextlib import nullcontext
from typing import Any, <PERSON><PERSON><PERSON>, NamedTuple, Optional, Sequence, Tuple, TypeVar, cast

import asyncpg
import orjson
import pandas as pd
import structlog
from fastapi import HTTPException
from langchain_core.prompts import Chat<PERSON>rompt<PERSON><PERSON>plate
from langchain_openai import ChatOpenAI
from lc_database import database

import app.models as models
from app import schemas
from app.api.auth import AuthInfo
from app.models.examples.fetch import (
    fetch_example,
    fetch_examples,
)
from app.models.examples.sourcerun import populate_example_from_run
from app.models.examples.sql import (
    select_examples_by_tag_and_dataset_sql,
    select_examples_sql,
)
from app.models.examples.validate import (
    CombinedExampleValidationError,
    ExampleValidationError,
    validate_example,
)
from app.utils import arun_in_executor

logger = structlog.getLogger(__name__)

# Postgres has a 16-bit parameter index limit (32767).
# Each “parameter” in the query consumes two parameter slots in extended‐query mode
# (one for the type and one for the value).
_PG_FETCH_PARAMS_CHUNK_SIZE = 32767 // 2


class InsertAndValidateResponse(NamedTuple):
    examples_json: list[str]
    blobs_to_copy: dict[str, list[Tuple[str, str]]]
    example_ids_to_ignore: set[str]
    failed_validation_examples: dict[
        uuid.UUID, ExampleValidationError | CombinedExampleValidationError
    ]


async def _insert_batch_with_retry(
    auth: AuthInfo,
    db: asyncpg.Connection,
    db_examples_to_add: list[dict],
    failed_validation_examples: dict[
        uuid.UUID, ExampleValidationError | CombinedExampleValidationError
    ],
    ignore_conflicts: bool,
    return_ids: bool,
    create_new_transaction_per_batch: bool,
    is_unbatched_retry: bool = False,
) -> list[dict]:
    full_object_return = """
        'id', id, 
        'dataset_id', dataset_id,
        'created_at', created_at,
        'modified_at', modified_at,
        'inputs', inputs,
        'outputs', outputs,
        'source_run_id', source_run_id,
        'metadata', metadata,
        'name', '',
        'attachment_urls', attachment_urls
    """

    id_only_return = "'id', id"

    query = f"""
        WITH inserted_examples as (
            INSERT INTO examples_log (id, created_at, modified_at, dataset_id, inputs, outputs, source_run_id, metadata, attachment_urls)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            {"ON CONFLICT DO NOTHING" if ignore_conflicts else ""}
            RETURNING json_build_object({full_object_return if not return_ids else id_only_return}) as json_resp
        )
        SELECT * 
        FROM inserted_examples 
        {"LIMIT 100" if not return_ids else ""};
    """

    # Convert your example dicts to the parameter tuples once
    param_list = [
        (
            db_example["id"],
            db_example["created_at"],
            db_example["modified_at"],
            db_example["dataset_id"],
            db_example.get("inputs"),
            db_example.get("outputs"),
            db_example.get("source_run_id"),
            db_example.get("metadata"),
            db_example.get("attachment_urls"),
        )
        for db_example in db_examples_to_add
    ]

    results = []
    try:
        async with (
            db.transaction() if create_new_transaction_per_batch else nullcontext()
        ):
            # Process in chunks
            for start_idx in range(0, len(param_list), _PG_FETCH_PARAMS_CHUNK_SIZE):
                chunk = param_list[start_idx : start_idx + _PG_FETCH_PARAMS_CHUNK_SIZE]
                # The asyncpg “fetchmany” call below still uses all chunked parameters
                part_results = await db.fetchmany(query, chunk)
                results.extend(part_results)
        return results

    except asyncpg.exceptions.InvalidTextRepresentationError as e:
        await logger.aexception(
            f"Invalid JSON sent to bulk example endpoint. error: {e}",
            tenant_id=auth.tenant_id,
        )
        raise HTTPException(
            status_code=400,
            detail="Invalid JSON.",
        )
    except asyncpg.UniqueViolationError as e:
        # Should only happen if ignore_conflicts is False
        raise HTTPException(
            status_code=409,
            detail="Example(s) with the same ID and modified_at time already exist(s).",
        ) from e
    except asyncpg.ProgramLimitExceededError:
        if is_unbatched_retry:
            failed_validation_examples[
                uuid.UUID(_convert_uuid(db_examples_to_add[0]["id"]))
            ] = ExampleValidationError("Example size too large (> 256 MiB).")
            return []
        # retry unbatched
        try:
            examples = []
            for ex in db_examples_to_add:
                examples_subset = await _insert_batch_with_retry(
                    auth,
                    db,
                    [ex],
                    failed_validation_examples,
                    ignore_conflicts,
                    return_ids,
                    create_new_transaction_per_batch,
                    is_unbatched_retry=True,
                )
                examples += examples_subset
            return examples
        except Exception as e:
            raise e
    except asyncpg.exceptions.RaiseError as e:
        if str(e) == "Dataset ID mismatch":
            raise HTTPException(
                status_code=409,
                detail="The uploaded example ids already exist in a different dataset.",
            )
        else:
            raise e


async def update_example_raw(
    db: asyncpg.Connection,
    auth: AuthInfo,
    example_id: uuid.UUID,
    example_update: schemas.ExampleUpdate,
    full_overwrite: bool = False,
    coerce_modified_at: datetime.datetime | None = None,
    existing_attachments: Optional[dict[str, dict[str, str | None]]] = None,
) -> dict:
    """Insert a new row into the examples_log table."""
    params = {
        **example_update.model_dump(),
        "id": example_id,
        "tenant_id": auth.tenant_id,
        "coerce_modified_at": coerce_modified_at,
    }
    metadata = params.get("metadata")
    del params["split"]
    if example_update.split is not None:
        if metadata is None:
            existing_example = await fetch_example(db, auth, example_id, "latest")
            metadata = existing_example.metadata or {}
        if isinstance(example_update.split, str):
            metadata["dataset_split"] = [example_update.split]
        else:
            metadata["dataset_split"] = example_update.split
        params["metadata"] = metadata

    # Handle attachment operations if present
    attachments_operations = params.pop("attachments_operations", None)
    if attachments_operations and existing_attachments:
        # Create new attachment_urls with updated names
        new_attachments = {}
        if "rename" in attachments_operations:
            for old_name, new_name in attachments_operations["rename"].items():
                if "attachment." + old_name in existing_attachments:
                    new_attachments["attachment." + new_name] = existing_attachments[
                        "attachment." + old_name
                    ]
                else:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Cannot rename attachment {old_name} to {new_name} because it does not exist.",
                    )
        if "retain" in attachments_operations:
            for name in attachments_operations["retain"]:
                if "attachment." + name in existing_attachments:
                    new_attachments["attachment." + name] = existing_attachments[
                        "attachment." + name
                    ]
                else:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Cannot retain attachment {name} because it does not exist.",
                    )
        params["attachment_urls"] = new_attachments
    else:
        params["attachment_urls"] = None

    inputs_outputs = (
        "$inputs, $outputs"
        if full_overwrite
        else "COALESCE($inputs, inputs), COALESCE($outputs, outputs)"
    )
    raw_sql = f"""
INSERT INTO examples_log (id, modified_at, created_at, dataset_id, inputs, outputs, attachment_urls, source_run_id, metadata)
    SELECT $id, COALESCE($coerce_modified_at, now()), created_at, COALESCE($dataset_id, dataset_id), {inputs_outputs}, COALESCE($attachment_urls, attachment_urls), source_run_id, COALESCE($metadata, metadata)
FROM examples_log
WHERE
    id = $id AND
    modified_at = (SELECT max(examples_log.modified_at) FROM examples_log JOIN dataset ON dataset.id = examples_log.dataset_id WHERE examples_log.id = $id AND dataset.tenant_id = $tenant_id)
    AND EXISTS (
        SELECT 1 FROM dataset
        WHERE dataset.id = examples_log.dataset_id AND dataset.tenant_id = $tenant_id
    )
    AND ($dataset_id IS NULL OR EXISTS (
        SELECT 1 FROM dataset
        WHERE dataset.id = $dataset_id AND dataset.tenant_id = $tenant_id
    ))
RETURNING *;
"""
    sql_query = database.kwargs_to_pgpos(raw_sql, params)
    try:
        result = await db.fetchrow(sql_query.sql, *sql_query.args)
        if not result:
            raise HTTPException(
                status_code=404,
                detail="Example not found.",
            )
        return result
    except asyncpg.UniqueViolationError as e:
        raise HTTPException(
            status_code=409,
            detail="Example with this ID and modified_at time already exists.",
        ) from e
    except asyncpg.exceptions.RaiseError as e:
        if str(e) == "Dataset ID mismatch":
            raise HTTPException(
                status_code=409,
                detail="Cannot change an example's dataset.",
            )
        else:
            raise e


async def update_example(
    db: asyncpg.Connection,
    auth: AuthInfo,
    example_id: uuid.UUID,
    example_update: schemas.ExampleUpdate,
) -> schemas.Example:
    example = await fetch_example(db, auth, example_id, "latest")
    if (
        example_update.dataset_id is not None
        and example_update.dataset_id != example.dataset_id
    ):
        raise HTTPException(
            status_code=400,
            detail="Cannot change an example's dataset.",
        )
    dataset = await models.datasets.fetch.fetch_dataset(db, auth, example.dataset_id)

    if dataset.data_type is not None:
        try:
            validate_example(
                dataset.data_type,
                example_update,
                dataset.inputs_schema_definition,
                dataset.outputs_schema_definition,
                transformations=dataset.transformations,
                full_overwrite=False,
            )
        except ExampleValidationError as e:
            raise HTTPException(
                status_code=400, detail=f"Failed example validation: {str(e)}"
            )
        except CombinedExampleValidationError as e:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "Failed example validation",
                    "input_errors": [str(err) for err in e.input_errors],
                    "output_errors": [str(err) for err in e.output_errors],
                },
            )

    result = await update_example_raw(
        db,
        auth,
        example_id,
        example_update,
        existing_attachments={
            name: {k: v for k, v in info.items() if k in ["mime_type", "storage_url"]}
            for name, info in example.attachment_urls.items()
        }
        if example.attachment_urls
        else None,
    )
    result_dict = {**result}
    inputs = result_dict.pop("inputs", None) or {}
    return schemas.Example(**result_dict, inputs=inputs, run_count=0, name="")


def _populate_mime_type_and_storage_url(
    attachment_urls: dict[str, Any],
) -> dict[str, dict[str, str | None]]:
    return_dict: dict[str, dict[str, str | None]] = defaultdict(dict)
    for key, value in attachment_urls.items():
        if isinstance(value, str):
            return_dict[key]["storage_url"] = value
            return_dict[key]["mime_type"] = None
        elif isinstance(value, dict) and "presigned_url" not in value:
            return_dict[key]["storage_url"] = value["storage_url"]
            return_dict[key]["mime_type"] = value["mime_type"]
    return return_dict


async def update_examples(
    db: asyncpg.Connection,
    auth: AuthInfo,
    example_updates: list[schemas.ExampleUpdateWithID],
    full_overwrite: bool = False,
    coerce_modified_at: datetime.datetime | None = None,
) -> list[schemas.Example]:
    if len(example_updates) == 0:
        return []

    examples = await fetch_examples(
        db,
        auth,
        schemas.FilterQueryParamsForExampleSchema(id=[e.id for e in example_updates]),
    )
    dataset_id = examples[0].dataset_id
    for example in examples:
        if example.dataset_id != dataset_id:
            raise HTTPException(
                status_code=400,
                detail="All examples must belong to the same dataset",
            )

    dataset = await models.datasets.fetch.fetch_dataset(db, auth, example.dataset_id)
    if dataset.data_type is not None:
        for example_update in example_updates:
            if (
                example_update.dataset_id is not None
                and example_update.dataset_id != dataset.id
            ):
                raise HTTPException(
                    status_code=400,
                    detail="Cannot change an example's dataset",
                )
            try:
                validate_example(
                    dataset.data_type,
                    example_update,
                    dataset.inputs_schema_definition,
                    dataset.outputs_schema_definition,
                    transformations=dataset.transformations,
                    full_overwrite=False,
                )
            except ExampleValidationError as e:
                raise HTTPException(
                    status_code=400, detail=f"Failed example validation: {str(e)}"
                )
            except CombinedExampleValidationError as e:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "message": "Failed example validation",
                        "input_errors": [str(err) for err in e.input_errors],
                        "output_errors": [str(err) for err in e.output_errors],
                    },
                )

    results = []
    id_to_attachment_urls = {
        e.id: {
            name: {k: v for k, v in info.items()}
            for name, info in _populate_mime_type_and_storage_url(
                e.attachment_urls
            ).items()
        }
        if e.attachment_urls
        else None
        for e in examples
    }
    for example_update in example_updates:
        updated = await update_example_raw(
            db,
            auth,
            example_update.id,
            example_update,
            full_overwrite=full_overwrite,
            coerce_modified_at=coerce_modified_at,
            existing_attachments=id_to_attachment_urls.get(example_update.id),
        )
        results.append(updated)
    return_examples = []
    for result in results:
        result_dict = {**result}
        inputs = result_dict.pop("inputs", None) or {}
        return_examples.append(
            schemas.Example(**result_dict, inputs=inputs, run_count=0, name="")
        )
    return return_examples


async def insert_and_validate_examples(
    db: asyncpg.Connection,
    auth: AuthInfo,
    examples: list[dict],
    example_run_pairs: list[Tuple[dict, dict]],
    dataset: schemas.Dataset,
    ignore_conflicts: bool,
    corrections_dict: dict[str, dict[str, Any]] | None = None,
    return_ids: bool = False,
    fail_on_validation_error: bool = True,
    create_new_transaction_per_batch: bool = False,
) -> InsertAndValidateResponse:
    # maps example_id to a list of tuples (src, dest)
    blobs_to_copy: dict[str, list[Tuple[str, str]]] = {}
    if example_run_pairs:
        for example, run in example_run_pairs:
            await populate_example_from_run(
                example, run, dataset, auth.tenant_id, blobs_to_copy, corrections_dict
            )

    db_examples = []
    failed_validation_examples = {}
    for example in examples:
        if dataset.data_type is not None:
            try:
                updated_example = validate_example(
                    dataset.data_type,
                    example,
                    dataset.inputs_schema_definition,
                    dataset.outputs_schema_definition,
                    transformations=dataset.transformations,
                )
            except (ExampleValidationError, CombinedExampleValidationError) as e:
                if fail_on_validation_error:
                    if isinstance(e, CombinedExampleValidationError):
                        raise HTTPException(
                            status_code=400,
                            detail={
                                "message": "Failed example validation",
                                "input_errors": [str(err) for err in e.input_errors],
                                "output_errors": [str(err) for err in e.output_errors],
                            },
                        )
                    raise HTTPException(
                        status_code=400,
                        detail=f"Failed example validation: {str(e)}",
                    )
                else:
                    failed_validation_examples[
                        uuid.UUID(_convert_uuid(example["id"]))
                    ] = e
                    continue

            if example.get("attachment_urls"):
                updated_example["attachment_urls"] = example["attachment_urls"]
            example = cast(dict, updated_example)

        _prepare_example_for_insert(example, auth.tenant_id)

        if not example.get("modified_at"):
            example["modified_at"] = example["created_at"]

        db_examples.append(example)

    if not db_examples:
        return InsertAndValidateResponse(
            examples_json=[],
            blobs_to_copy={},
            example_ids_to_ignore=set(),
            failed_validation_examples=failed_validation_examples,
        )

    db_examples_to_add = []
    example_ids_to_ignore = set()

    if ignore_conflicts and db_examples:
        # Instead of building one massive query:
        # Build param_pairs for chunked approach
        param_pairs = [(e["id"], e["modified_at"]) for e in db_examples]
        example_ids_to_ignore = await _fetch_existing_ids_to_ignore_in_chunks(
            db,
            dataset.id,
            param_pairs,
            chunk_size=_PG_FETCH_PARAMS_CHUNK_SIZE // 2,  # 2 params per query
        )

        await logger.ainfo(
            "Ignoring existing examples in bulk create",
            example_ids_to_ignore=example_ids_to_ignore,
        )

        db_examples_to_add = [
            e
            for e in db_examples
            if _convert_uuid(e["id"]) not in example_ids_to_ignore
        ]
    else:
        db_examples_to_add = db_examples

    # NOTE: Before using the executemany API, we used to call into our fetch endpoint
    #       to read back the examples. This had a built in limit of 100 examples per
    #       request. We are keeping this limit for now so that we don't fail on sending
    #       back enormous requests.
    #
    # TODO (jakerachleff): make SDK call into an endpoint that returns only IDs / can handle > 100 examples
    examples = await _insert_batch_with_retry(
        auth,
        db,
        db_examples_to_add,
        failed_validation_examples,
        ignore_conflicts,
        create_new_transaction_per_batch,
        return_ids,
    )
    return InsertAndValidateResponse(
        examples_json=[e["json_resp"] for e in examples],
        blobs_to_copy=blobs_to_copy,
        example_ids_to_ignore=example_ids_to_ignore,
        failed_validation_examples=failed_validation_examples,
    )


def _convert_uuid(x: str | uuid.UUID) -> str:
    if isinstance(x, uuid.UUID):
        return str(x)

    if isinstance(x, str):
        return str(uuid.UUID(x))


def _prepare_example_for_insert(example: dict, tenant_id: uuid.UUID):
    """
    Augments the incoming example to be ready to enter the database.

    Updates the example in place, does not return a new value
    """
    params = example

    # Default to use the base split
    split = params.get("split") or "base"
    params["metadata"] = params.get("metadata") or {}
    if params.get("split") or "dataset_split" not in params["metadata"]:
        params["metadata"]["dataset_split"] = (
            [split] if isinstance(split, str) else split
        )

    params["id"] = params.get("id") or uuid.uuid4()
    params["tenant_id"] = tenant_id

    created_at = params.get("created_at")

    if isinstance(created_at, str):
        created_at = datetime.datetime.fromisoformat(created_at)

    if not created_at:
        created_at = datetime.datetime.now(tz=datetime.timezone.utc)
    elif created_at.tzinfo is None:
        created_at = created_at.replace(tzinfo=datetime.timezone.utc)

    params["created_at"] = created_at

    params["source_run_id"] = params.get("source_run_id") or None
    params["metadata"] = params.get("metadata") or None
    params["attachment_urls"] = params.get("attachment_urls") or None


async def clone_examples(
    target_dataset_id: uuid.UUID,
    source_dataset_id: uuid.UUID,
    example_ids: list[uuid.UUID],
    auth: AuthInfo,
    as_of: schemas.AsOfType | None = None,
) -> list[schemas.Example]:
    """Clone examples from one dataset to another."""
    params: dict[str, Any] = {
        "dataset_id": source_dataset_id,
        "new_dataset_id": target_dataset_id,
        "tenant_id": auth.tenant_id,
    }
    and_clauses = []
    and_clauses.append("dataset_id = $dataset_id")
    and_clauses.append("dataset.tenant_id = $tenant_id")
    datasets_filter_clause = "AND " + " AND ".join(and_clauses)

    if as_of is None:
        as_of = "latest"

    select_stmt = ""
    projections = """
        gen_random_uuid(),
        $new_dataset_id, 
        CURRENT_TIMESTAMP, 
        CURRENT_TIMESTAMP, 
        inputs, 
        outputs, 
        source_run_id, 
        metadata
    """
    if isinstance(as_of, datetime.datetime):
        examples_filter_clause = ""
        if len(example_ids) > 0:
            params["example_ids"] = example_ids
            examples_filter_clause = " AND examples_log.id = ANY($example_ids)"
        params["modified_at"] = as_of
        select_stmt = select_examples_sql.format(
            projections=projections,
            examples_filters=examples_filter_clause,
            dataset_filters=datasets_filter_clause,
        )
    else:
        examples_filter_clause = ""
        if len(example_ids) > 0:
            params["example_ids"] = example_ids
            examples_filter_clause = " AND examples_tagged.id = ANY($example_ids)"
        params["tag"] = as_of
        select_stmt = select_examples_by_tag_and_dataset_sql.format(
            projections=projections,
            example_filter=examples_filter_clause,
            order="modified_at",
            direction="DESC",
        ).split("ORDER BY")[0]

    raw_sql = f"""
INSERT INTO examples_log (id, dataset_id, created_at, modified_at, inputs, outputs, source_run_id, metadata)
    {select_stmt}
    AND EXISTS (
        SELECT 1 FROM dataset WHERE id = $new_dataset_id AND tenant_id = $tenant_id
    )
    AND inputs IS NOT NULL
    RETURNING *;
"""
    async with database.asyncpg_conn() as db:
        sql, args = database.kwargs_to_pgpos(raw_sql, params)
        result = await db.fetch(sql, *args)
    return [schemas.Example(**row, run_count=0, name="") for row in result]


T = TypeVar("T")


def chunked(seq: Sequence[T], size: int) -> Iterator[Tuple[T, ...]]:
    it = iter(seq)
    return iter(lambda: tuple(itertools.islice(it, size)), ())


def read_csv(
    file: tempfile.SpooledTemporaryFile,
    input_keys: Sequence[str],
    output_keys: Optional[Sequence[str]],
) -> pd.DataFrame:
    """Read a csv file. Only in its own function for ease of use with arun_in_executor."""
    return pd.read_csv(
        file,
        usecols=list(input_keys) + list(output_keys or []),
        na_filter=False,
        engine="c",
    )


async def get_examples_from_csv(
    auth: AuthInfo,
    dataset_id: uuid.UUID,
    file: tempfile.SpooledTemporaryFile,
    input_keys: Sequence[str],
    output_keys: Optional[Sequence[str]],
    data_type: schemas.DataType,
    inputs_schema_definition: dict | None = None,
    outputs_schema_definition: dict | None = None,
) -> list[dict[str, Any]]:
    """Create a dataset from the given csv file by creating examples."""
    df = await arun_in_executor(
        read_csv,
        file,
        input_keys,
        output_keys,
    )
    examples = []
    await logger.awarn("Read CSV File", num_rows=len(df), first_row=df.iloc[0])
    for _, row in df.iterrows():
        if data_type == schemas.DataType.chat:
            if len(input_keys) == 0:
                inputs = {}
            elif len(input_keys) == 1:
                inputs = {"input": row[input_keys[0]]}
            elif len(input_keys) == 2:
                if "functions" in input_keys:
                    fn_idx = input_keys.index("functions")
                    inputs = {
                        "functions": row[input_keys[fn_idx]],
                        "input": row[input_keys[1 - fn_idx]],
                    }
                else:
                    raise HTTPException(
                        status_code=400,
                        detail="Invalid input keys for chat dataset. Expected 'input' and optional 'functions' keys."
                        f" Got {input_keys}",
                    )
            else:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid input keys for chat dataset. Expected 'input' and optional 'functions' keys."
                    f" Got {input_keys}",
                )
            outputs = {"output": row[output_keys[0]]} if output_keys else {}
            if isinstance(inputs["input"], str):
                inputs["input"] = orjson.loads(inputs["input"])
            if "functions" in inputs and isinstance(inputs["functions"], str):
                inputs["functions"] = orjson.loads(inputs["functions"])
            if isinstance(outputs["output"], str):
                outputs["output"] = orjson.loads(outputs["output"])
        elif data_type == schemas.DataType.llm:
            inputs = {"input": row[input_keys[0]]} if len(input_keys) > 0 else {}
            outputs = (
                {"output": row[output_keys[0]]}
                if output_keys and len(output_keys) > 0
                else {}
            )
        else:
            inputs = {key: row[key] for key in input_keys}
            outputs = {key: row[key] for key in output_keys} if output_keys else {}
        example = schemas.ExampleCreate(
            id=uuid.uuid4(),
            dataset_id=dataset_id,
            inputs=inputs,
            outputs=outputs,
        )
        try:
            validated_example = validate_example(
                data_type,
                example,
                inputs_schema_definition,
                outputs_schema_definition,
            )
        except ExampleValidationError as e:
            raise HTTPException(
                status_code=400, detail=f"Failed example validation: {str(e)}"
            )
        except CombinedExampleValidationError as e:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "Failed example validation",
                    "input_errors": [str(err) for err in e.input_errors],
                    "output_errors": [str(err) for err in e.output_errors],
                },
            )
        examples.append(
            validated_example
            if isinstance(validated_example, dict)
            else validated_example.model_dump()
        )
    return examples


async def create_examples_from_csv(
    db: asyncpg.Connection,
    auth: AuthInfo,
    dataset_id: uuid.UUID,
    file: tempfile.SpooledTemporaryFile,
    input_keys: Sequence[str],
    output_keys: Optional[Sequence[str]],
    example_ids: Optional[Sequence[uuid.UUID]] = None,
) -> list[dict[str, Any]]:
    """Create a dataset from the given csv file by creating examples."""
    dataset = await db.fetchrow(
        "select id, data_type from dataset where id = $1 and tenant_id = $2 for update",
        dataset_id,
        auth.tenant_id,
    )

    if not dataset:
        raise HTTPException(
            status_code=404,
            detail="Dataset not found.",
        )

    # Seek to the beginning of the file, in case this is a retry
    file.seek(0)

    examples = await get_examples_from_csv(
        auth,
        dataset_id,
        file,
        input_keys,
        output_keys,
        dataset["data_type"],
        dataset.get("inputs_schema_definition"),
        dataset.get("outputs_schema_definition"),
    )
    # TODO: Remove once we delete the SQLAlchemy code
    if example_ids:
        for i, example in enumerate(examples):
            example["id"] = example_ids[i]

    if len(examples) == 0:
        raise HTTPException(
            status_code=400,
            detail="Must provide at least one example.",
        )

    for chunk in chunked(examples, 10000):
        await db.executemany(
            """
            INSERT INTO examples_log (id, dataset_id, inputs, outputs, created_at, modified_at)
            VALUES ($1, $2, $3, $4, now(), now())
            ON CONFLICT DO NOTHING
            """,
            [
                (
                    example["id"],
                    dataset_id,
                    example["inputs"],
                    example["outputs"],
                )
                for example in chunk
            ],
        )

    return examples


async def generate_synthetic_examples(
    auth: AuthInfo,
    dataset_id: uuid.UUID,
    example_ids: list[uuid.UUID] | None,
    num_examples: int,
):
    """Generate synthetic examples based on the provided examples."""

    # Check that this tenant_id owns this dataset
    async with database.asyncpg_conn() as db:
        dataset = await db.fetchrow(
            "SELECT * FROM dataset WHERE id = $1 AND tenant_id = $2",
            dataset_id,
            auth.tenant_id,
        )
        auth.exists(dataset)

        if dataset["inputs_schema_definition"] and dataset["outputs_schema_definition"]:
            inputs_schema_definition = dataset["inputs_schema_definition"]
            outputs_schema_definition = dataset["outputs_schema_definition"]
        else:
            raise HTTPException(
                status_code=400,
                detail="Dataset must have inputs and outputs schema definition.",
            )

        # Fetch 5 random examples
        if example_ids is None:
            examples = await fetch_examples(
                db,
                auth,
                schemas.FilterQueryParamsForExampleSchema(
                    dataset=dataset_id, limit=5, random_seed=random.random()
                ),
            )
        else:
            examples = await fetch_examples(
                db,
                auth,
                schemas.FilterQueryParamsForExampleSchema(
                    id=example_ids, dataset=dataset_id
                ),
            )

        # Example schema
        example_schema = {
            "type": "object",
            "properties": {
                "inputs": inputs_schema_definition,
                "outputs": outputs_schema_definition,
            },
            "required": ["inputs", "outputs"],
        }

        # Generated examples schema
        generated_examples_json_schema = {
            "name": "generate_examples",
            "description": f"Generate a dataset of input-output pairs based on the examples provided. Generate exactly {num_examples} pairs.",
            "parameters": {
                "type": "object",
                "properties": {
                    "input_output_pairs": {
                        "type": "array",
                        "items": example_schema,
                        "minItems": num_examples,
                        "maxItems": num_examples,
                        "uniqueItems": True,
                    }
                },
                "required": ["input_output_pairs"],
            },
        }

        reference_examples = []
        for example in examples:
            reference_examples.append(
                {"inputs": example.inputs, "outputs": example.outputs}
            )

        from app.models.tenants.secrets import list_secrets_as_dict

        secrets = await list_secrets_as_dict(auth)
        if "OPENAI_API_KEY" not in secrets:
            raise HTTPException(
                status_code=404,
                detail="OPENAI_API_KEY not found.",
            )

        try:
            model = ChatOpenAI(model="gpt-4o-mini", api_key=secrets["OPENAI_API_KEY"])
            structured_llm = model.with_structured_output(
                generated_examples_json_schema
            )
            prompt_template = ChatPromptTemplate.from_template(
                """
                Generate a new DATASET of unique input-output PAIRS.
                Ensure that none of the generated PAIRS are duplicates of the PROVIDED EXAMPLES.

                Generate EXACTLY the following number of PAIRS: {{num_examples}}.

                Use the following PROVIDED EXAMPLES as a reference:
                {{#examples}}
                Input: {{{inputs}}}
                Output: {{{outputs}}}
                {{/examples}}
                """,
                template_format="mustache",
            )
            chain = prompt_template | structured_llm

            prev: list = []
            id = -1
            yield [{"op": "add", "path": "", "value": {"examples": prev}}]

            async for chunk in chain.astream(
                {"examples": reference_examples, "num_examples": num_examples}
            ):
                if "input_output_pairs" in chunk and chunk["input_output_pairs"]:
                    if prev and len(prev) < len(chunk["input_output_pairs"]):
                        id += 1
                        prev[-1]["id"] = id
                        yield [{"op": "add", "path": "/examples/-", "value": prev[-1]}]
                    prev = chunk["input_output_pairs"]

            if prev:
                id += 1
                prev[-1]["id"] = id
                yield [{"op": "add", "path": "/examples/-", "value": prev[-1]}]

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to generate examples with error: {str(e)}",
            )


async def _fetch_existing_ids_to_ignore_in_chunks(
    db: asyncpg.Connection,
    dataset_id: uuid.UUID,
    param_pairs: list[tuple[str, datetime.datetime]],
    chunk_size: int,
) -> set[str]:
    """
    Fetch existing example IDs in chunks so you don't exceed Postgres's ~32k parameter limit.

    param_pairs: list of (id, modified_at)
    chunk_size: how many rows to handle per query chunk
    """
    existing_ids: set = set()

    for start_idx in range(0, len(param_pairs), chunk_size):
        chunk = param_pairs[start_idx : start_idx + chunk_size]

        # For each chunk, build the WHERE clause:
        # (id = $1 AND modified_at = $2) OR (id = $3 AND modified_at = $4) ...
        conds = []
        chunk_params: list = []
        param_index = 1
        for ex_id, updated_at in chunk:
            conds.append(f"(id = ${param_index} AND modified_at = ${param_index + 1})")
            param_index += 2
            chunk_params.append(ex_id)
            chunk_params.append(updated_at)

        conds_str = " OR ".join(conds)

        # After all pairs, add dataset_id as the last parameter
        chunk_params.append(dataset_id)
        dataset_param_index = param_index
        query = f"""
            SELECT DISTINCT id
            FROM examples_log
            WHERE ({conds_str})
            AND dataset_id = ${dataset_param_index}
        """

        # Execute the chunked query
        rows = await db.fetch(query, *chunk_params)
        existing_ids.update(str(r["id"]) for r in rows)

    return existing_ids
