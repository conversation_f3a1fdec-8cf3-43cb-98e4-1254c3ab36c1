from app.schemas import ExampleSelect

by_timestamp_projections = {
    ExampleSelect.id: "examples_log.id",
    ExampleSelect.created_at: "examples_log.created_at",
    ExampleSelect.modified_at: "examples_log.modified_at",
    ExampleSelect.inputs: "examples_log.inputs",
    ExampleSelect.outputs: "examples_log.outputs",
    ExampleSelect.dataset_id: "examples_log.dataset_id",
    ExampleSelect.metadata: "examples_log.metadata",
    ExampleSelect.source_run_id: "examples_log.source_run_id",
    ExampleSelect.name_: "concat('#', left(examples_log.id::text, 4), ' @ ', dataset.name)",
    ExampleSelect.attachment_urls: "examples_log.attachment_urls",
}

select_examples_sql = """
SELECT
    {projections}
FROM (
  SELECT
        examples_log.id,
        max(examples_log.modified_at) as modified_at
    FROM examples_log
    WHERE examples_log.modified_at <= $modified_at
    {examples_filters}
    GROUP BY 1
) as latest
INNER JOIN examples_log
    ON latest.id = examples_log.id and latest.modified_at = examples_log.modified_at
INNER JOIN dataset
    ON examples_log.dataset_id = dataset.id
{dataset_filters}
"""

by_tag_projections = {
    ExampleSelect.id: "examples_tagged.id",
    ExampleSelect.created_at: "examples_tagged.created_at",
    ExampleSelect.modified_at: "examples_tagged.modified_at",
    ExampleSelect.inputs: "examples_tagged.inputs",
    ExampleSelect.outputs: "examples_tagged.outputs",
    ExampleSelect.dataset_id: "examples_tagged.dataset_id",
    ExampleSelect.metadata: "examples_tagged.metadata",
    ExampleSelect.source_run_id: "examples_tagged.source_run_id",
    ExampleSelect.name_: "concat('#', left(examples_tagged.id::text, 4), ' @ ', dataset.name)",
    ExampleSelect.attachment_urls: "examples_tagged.attachment_urls",
}

select_examples_by_tag_and_id_sql = """
SELECT
    {projections}
FROM examples_tagged
INNER JOIN dataset ON examples_tagged.dataset_id = dataset.id
WHERE examples_tagged.id = ANY($1) and examples_tagged.tag = $2 and dataset.tenant_id = $3 {dataset_filter}
LIMIT $4 OFFSET $5
"""

select_examples_by_tag_and_dataset_sql = """
SELECT
    {projections}
FROM examples_tagged
INNER JOIN dataset ON examples_tagged.dataset_id = dataset.id
WHERE examples_tagged.dataset_id = $dataset_id and examples_tagged.tag = $tag and dataset.tenant_id = $tenant_id {example_filter}
ORDER BY {order} {direction}, examples_tagged.id
LIMIT $limit OFFSET $offset
"""
