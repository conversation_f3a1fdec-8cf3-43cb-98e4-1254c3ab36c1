import asyncio
import uuid
from collections import deque
from typing import Literal, cast

import structlog
from fastapi import HTTPException
from jsonschema import Draft7Validator, SchemaError, exceptions, validate
from lc_database import database
from lc_logging import trace as trace_utils
from referencing import Registry, Resource

import app.schemas as schemas
from app import config
from app.api.auth.schemas import AuthInfo
from app.models.datasets import fetch
from app.models.examples import transform as examples_transform
from app.models.examples.sourcerun import (
    populate_example_from_run,
    resolve_runs_for_examples,
)
from app.utils import gated_coro

logger = structlog.get_logger(__name__)

# Note: this is populated at the end of the package definition
_registry_w_custom_objects: Registry = Registry()
_default_registry: Registry = Registry()

SchemaType = Literal["openai-message", "openai-tool"]


class ExampleValidationError(Exception):
    def __init__(
        self, message: str, *, type_: Literal["inputs", "outputs"] | None = None
    ):
        super().__init__(message)
        self.message = message
        self.type = type_

    def __str__(self):
        return self.message


class CombinedExampleValidationError(Exception):
    def __init__(self, errors: list[ExampleValidationError]):
        super().__init__(errors)
        self.errors = errors

    @property
    def input_errors(self) -> list[ExampleValidationError]:
        return [e for e in self.errors if e.type == "inputs"]

    @property
    def output_errors(self) -> list[ExampleValidationError]:
        return [e for e in self.errors if e.type == "outputs"]


def _get_registry(use_chat_input_registry: bool) -> Registry:
    return (
        _default_registry if not use_chat_input_registry else _registry_w_custom_objects
    )


def validate_dataset_schema_and_transformations(
    field: str,
    schema: dict,
    transformations: list[schemas.DatasetTransformation] | None = None,
) -> None:
    if field not in ["inputs", "outputs"]:
        raise ValueError("Field must be 'inputs' or 'outputs'")

    if any(t.path[0] != field for t in transformations or []):
        raise ValueError("Transformation field does not match the schema field")
    try:
        # TODO: fail early if the schema has bad references. Currently appears that
        # Draft7Validator.check_schema does not catch this, even if passing in the
        # right resolver variable.
        Draft7Validator.check_schema(schema=schema)
    except SchemaError:
        raise HTTPException(
            status_code=400, detail="Invalid {field} schema definition: {e}"
        )

    if transformations:
        examples_transform.validate_transformations_for_schema(
            field, schema, transformations
        )


def _stringify_error_path(path: deque[str | int]) -> str:
    return ".".join([str(p) if isinstance(p, int) else p for p in path])


@trace_utils.wrap(
    service="validation",
    resource="validate_and_transform_example_json",
    swallow_exceptions=lambda e: isinstance(e, ExampleValidationError),
)
def validate_and_transform_example_json(
    field: Literal["inputs", "outputs"],
    example: dict,
    schema: dict,
    depth: int = 0,
    ff_w_chat_support=False,
    transformations: list[schemas.DatasetTransformation] | None = None,
    extra: dict | None = None,
    validation_errors: list | None = None,
) -> tuple[dict, list]:
    extra = extra or {}
    validation_errors = validation_errors if validation_errors is not None else []
    registry = _get_registry(ff_w_chat_support)

    if any(t.path[0] != field for t in transformations or []):
        raise ValueError("Transformation field does not match the example field")

    if ff_w_chat_support and transformations:
        examples_transform.apply_on_validation_start(example, extra, transformations)

    if depth > 5:
        raise ExampleValidationError(
            f'Could not identify a field within "{field}" that matches the dataset schema. Please edit your example and try again.',
            type_=field,
        )
    try:
        validate(
            instance=example,
            schema=schema,
            registry=registry,
        )

    except exceptions.ValidationError as e:
        logger.info(
            "Schema validation error for example",
            error_msg=e.message,
            path=_stringify_error_path(e.path),
        )
        coerced_example = None
        if ff_w_chat_support and transformations:
            try:
                coerced_example = examples_transform.apply_on_validation_error(
                    list(e.path), field, example, schema, registry, transformations
                )
            except examples_transform.TransformationError:
                logger.info(
                    "Could not transform example, throwing original validation error",
                    example_id=example.get("id"),
                )

                path_message = (
                    f"at path {_stringify_error_path(e.path)}" if e.path else "at root"
                )
                add_validation_error(
                    ExampleValidationError(
                        f"Example {field} does not match the dataset schema: {e.message} {path_message}.",
                        type_=field,
                    ),
                    validation_errors,
                )

        if coerced_example:
            logger.info(
                "Coerced schema for example",
                error_msg=e.message,
                path=_stringify_error_path(e.path),
            )
            # Recurse with the coerced example and increased depth
            example, validation_errors = validate_and_transform_example_json(
                field,
                coerced_example,
                schema,
                depth + 1,
                ff_w_chat_support,
                transformations,
                extra,
                validation_errors,
            )
        else:
            path_message = (
                f"at path {_stringify_error_path(e.path)}" if e.path else "at root"
            )
            add_validation_error(
                ExampleValidationError(
                    f"Example {field} does not match the dataset schema: {e.message} {path_message}.",
                    type_=field,
                ),
                validation_errors,
            )

    except exceptions._WrappedReferencingError as e:
        add_validation_error(
            ExampleValidationError(
                f"Undefined reference in {field} schema: {e}", type_=field
            ),
            validation_errors,
        )

    if (
        ff_w_chat_support
        and transformations
        and len(cast(list, validation_errors)) == 0
    ):
        example = examples_transform.apply_on_validation_success(
            example, schema, transformations
        )

    return example, cast(list, validation_errors)


async def _fetch_dataset_coro(dataset_id: uuid.UUID, auth: AuthInfo):
    async with database.asyncpg_conn() as db:
        return await fetch.fetch_dataset(db, auth, dataset_id)


async def validate_example_from_api(
    auth: AuthInfo,
    example: dict,
) -> schemas.ExampleValidationResult | dict:
    """
    Validate a *NEW* example against a dataset's schema definition.
    """
    return (await validate_examples_from_api(auth, [example]))[0]


async def validate_examples_from_api(
    auth: AuthInfo, examples: list[dict]
) -> list[schemas.ExampleValidationResult]:
    """
    Validate a list of *NEW* example against a dataset's schema definition.
    """
    if not examples:
        raise HTTPException(
            status_code=400, detail="Must provide at least one example."
        )
    dataset_id = examples[0]["dataset_id"]
    for example in examples:
        if example["dataset_id"] != dataset_id:
            raise HTTPException(
                status_code=400,
                detail="All examples must belong to the same dataset",
            )

    dataset, example_run_pairs = await asyncio.gather(
        _fetch_dataset_coro(dataset_id, auth),
        resolve_runs_for_examples(auth, examples),
    )
    if not dataset:
        raise HTTPException(status_code=404, detail="Dataset not found.")

    # Populate relevant examples with their source run input/outputs.
    if example_run_pairs:
        sem = asyncio.Semaphore(config.settings.BLOB_STORAGE_FETCH_SEMAPHORE)
        await asyncio.gather(
            *[
                gated_coro(
                    populate_example_from_run(
                        example, run, dataset, auth.tenant_id, blobs_to_copy={}
                    ),
                    sem,
                )
                for example, run in example_run_pairs
            ]
        )

    validated_examples = []
    validation_errors = []
    for i, example in enumerate(examples):
        try:
            validated_example = validate_example(
                dataset.data_type,
                example,
                dataset.inputs_schema_definition,
                dataset.outputs_schema_definition,
                full_overwrite=False,
                transformations=dataset.transformations,
            )
        except (ExampleValidationError, CombinedExampleValidationError) as e:
            err = (
                e
                if isinstance(e, CombinedExampleValidationError)
                else CombinedExampleValidationError(errors=[e])
            )
            validation_errors.append((i, example.get("id"), (err)))
        else:
            validated_examples.append(
                schemas.ExampleValidationResult(**validated_example)
            )

    if validation_errors:
        raise HTTPException(
            status_code=400,
            detail={
                "message": "Example validation failed",
                "examples": [
                    {
                        "example_id": str(id) if id else None,
                        "example_index": idx,
                        "input_errors": [str(err) for err in e.input_errors],
                        "output_errors": [str(err) for err in e.output_errors],
                    }
                    for idx, id, e in validation_errors
                ],
            },
        )
    else:
        return validated_examples


def add_validation_error(new_error: Exception, error_list: list[Exception]) -> None:
    if isinstance(new_error, ExampleValidationError):
        if not any(str(err) == str(new_error) for err in error_list):
            error_list.append(new_error)
    else:
        raise new_error


def validate_example(
    data_type: schemas.DataType,
    example: schemas.ExampleCreate | schemas.ExampleUpdate | dict,
    inputs_schema_definition: dict | None = None,
    outputs_schema_definition: dict | None = None,
    full_overwrite: bool = True,
    transformations: list[schemas.DatasetTransformation] | None = None,
) -> schemas.ExampleCreate | schemas.ExampleBase | schemas.ExampleUpdate | dict:
    inputs = example.get("inputs") if isinstance(example, dict) else example.inputs
    outputs = example.get("outputs") if isinstance(example, dict) else example.outputs

    validation_errors: list = []

    if not isinstance(example, schemas.ExampleCreate):
        attachments_operations = (
            example.get("attachments_operations")
            if isinstance(example, dict)
            else example.attachments_operations
        )
    else:
        attachments_operations = None

    if attachments_operations is not None:
        retains = (
            attachments_operations.get("retain")
            if isinstance(attachments_operations, dict)
            else attachments_operations.retain
        )
        renames = (
            attachments_operations.get("rename")
            if isinstance(attachments_operations, dict)
            else attachments_operations.rename
        )
        if retains is not None:
            for retain in retains:
                if renames is not None and retain in renames:
                    raise ExampleValidationError(
                        f"Cannot retain and rename the same attachment {retain}",
                        type_="inputs",
                    )

                if renames is not None and retain in renames.values():
                    raise ExampleValidationError(
                        f"Cannot retain attachment called {retain} and also rename another attachment to {retain}",
                        type_="inputs",
                    )

    should_validate_inputs = inputs is not None or full_overwrite
    if not isinstance(inputs, dict) and should_validate_inputs:
        add_validation_error(
            ExampleValidationError(
                "Example inputs must be a dictionary.", type_="inputs"
            ),
            validation_errors,
        )
    if outputs is not None and not isinstance(outputs, dict):
        add_validation_error(
            ExampleValidationError(
                "Example outputs must be a dictionary.", type_="outputs"
            ),
            validation_errors,
        )
    if validation_errors:
        raise CombinedExampleValidationError(errors=validation_errors)

    assert outputs is None or isinstance(outputs, dict)

    if data_type == schemas.DataType.llm:
        if should_validate_inputs:
            assert isinstance(inputs, dict)
            if inputs is None or inputs.keys() != {"input"}:
                add_validation_error(
                    ExampleValidationError(
                        "LLM Example inputs must have a single key 'input'.",
                        type_="inputs",
                    ),
                    validation_errors,
                )
            elif not isinstance(inputs["input"], str):
                add_validation_error(
                    ExampleValidationError(
                        "LLM Example inputs.input must be a string.", type_="inputs"
                    ),
                    validation_errors,
                )
        if outputs is not None:
            if outputs.keys() != {"output"}:
                add_validation_error(
                    ExampleValidationError(
                        "LLM Example outputs must have a single key 'output'.",
                        type_="outputs",
                    ),
                    validation_errors,
                )
            elif not isinstance(outputs["output"], str):
                add_validation_error(
                    ExampleValidationError(
                        "LLM Example outputs.output must be a string.", type_="outputs"
                    ),
                    validation_errors,
                )

    elif data_type == schemas.DataType.chat:
        if should_validate_inputs:
            assert isinstance(inputs, dict)
            if not inputs or not inputs.keys() <= {"input", "functions"}:
                add_validation_error(
                    ExampleValidationError(
                        "Chat Example inputs must have a single key 'input' or two keys 'input' and 'functions'."
                        f" Got keys: {inputs.keys() if inputs else {}}",
                        type_="inputs",
                    ),
                    validation_errors,
                )
            elif (
                not isinstance(inputs["input"], list)
                or len(inputs["input"]) == 0
                or not all(isinstance(x, dict) for x in inputs["input"])
                or not all(set(x.keys()) == {"type", "data"} for x in inputs["input"])
            ):
                add_validation_error(
                    ExampleValidationError(
                        "Chat Example inputs.input must be a list of dictionaries with keys 'type' and 'data'.",
                        type_="inputs",
                    ),
                    validation_errors,
                )
            elif (
                "functions" in inputs
                and inputs["functions"] is not None
                and (
                    inputs["functions"]
                    and not isinstance(inputs["functions"], list)
                    or not all(isinstance(x, dict) for x in inputs["functions"])
                    or not all(
                        set(x.keys()) == {"name", "description", "parameters"}
                        for x in inputs["functions"]
                    )
                )
            ):
                add_validation_error(
                    ExampleValidationError(
                        "Chat Example inputs.functions must be a list of dictionaries with keys 'name', 'description', and 'parameters'.",
                        type_="inputs",
                    ),
                    validation_errors,
                )
        if outputs is not None and outputs.keys() != {"output"}:
            add_validation_error(
                ExampleValidationError(
                    "Chat Example outputs must have a single key 'output'.",
                    type_="outputs",
                ),
                validation_errors,
            )
        elif outputs is not None and (
            not isinstance(outputs["output"], dict)
            or set(outputs["output"].keys()) != {"type", "data"}
        ):
            add_validation_error(
                ExampleValidationError(
                    "Chat Example outputs.output must a dictionary with keys 'type' and 'data'.",
                    type_="outputs",
                ),
                validation_errors,
            )

    elif data_type == schemas.DataType.kv:
        should_validate_output_schema = outputs is not None or full_overwrite

        # We only set the __source_run_extra field in creation, not in update
        transform_extra = {}
        if isinstance(example, dict) and "__source_run_extra" in example:
            transform_extra["__source_run_extra"] = example["__source_run_extra"]

        if inputs_schema_definition is not None and should_validate_inputs:
            inputs, validation_errors = validate_and_transform_example_json(
                "inputs",
                inputs if inputs else {},
                inputs_schema_definition,
                ff_w_chat_support=True,
                transformations=[
                    t for t in transformations or [] if t.path[0] == "inputs"
                ],
                extra=transform_extra,
                validation_errors=validation_errors,
            )
        if outputs_schema_definition is not None and should_validate_output_schema:
            outputs, validation_errors = validate_and_transform_example_json(
                "outputs",
                outputs if outputs else {},
                outputs_schema_definition,
                ff_w_chat_support=True,
                transformations=[
                    t for t in transformations or [] if t.path[0] == "outputs"
                ],
                extra=transform_extra,
                validation_errors=validation_errors,
            )

        if isinstance(example, dict):
            example["inputs"] = inputs
            example["outputs"] = outputs
        else:
            example.inputs = inputs
            example.outputs = outputs

    if validation_errors:
        raise CombinedExampleValidationError(errors=validation_errors)
    return example


def _get_tool_definition_format(version: str) -> dict:
    if version == "v1":
        return {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "description": {"type": "string"},
                "parameters": {"type": "object", "additionalProperties": True},
            },
            "required": ["name", "description", "parameters"],
            "additionalProperties": False,
        }
    else:
        raise HTTPException(
            status_code=400, detail=f"Unsupported tool format version: {version}"
        )


def get_tool_format(version: str) -> dict:
    """
    Note that the tool formatting is a work in progress. Significant changes are to be expected in the future.
    """
    if version == "v1":
        return {
            "$id": f"/public/schemas/{version}/tooldef.json",
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "properties": {
                "type": {"type": "string", "enum": ["function"]},
                "function": _get_tool_definition_format(version),
            },
            "required": ["type", "function"],
            "additionalProperties": False,
        }
    else:
        raise HTTPException(
            status_code=400, detail=f"Unsupported tool format version: {version}"
        )


def get_message_format(version: str) -> dict:
    if version == "v1":
        # V1 is built from https://platform.openai.com/docs/api-reference/chat/create#chat-create-messages
        # as of 2024-10-15
        return {
            "$id": f"/public/schemas/{version}/message.json",
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "properties": {
                "role": {
                    "type": "string",
                    "enum": [
                        "system",
                        "user",
                        "human",
                        "assistant",
                        "ai",
                        "tool",
                        "function",
                        "developer",
                    ],
                    "description": "The role of the message sender. It can be 'system', 'developer', 'user', 'human', 'tool', 'function', 'ai', or 'assistant'.",
                },
                "content": {
                    "oneOf": [
                        {
                            "type": "string",
                            "description": "Text content, as a simple string.",
                        },
                        {
                            "type": "array",
                            "description": "An array of content parts, which can be multiple 'text' types or one 'refusal' type.",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "type": {
                                        "type": "string",
                                        "description": "The type of the content part. It can be either 'text' or 'refusal'.",
                                    },
                                    "content": {
                                        "type": "string",
                                        "description": "The actual content of the part.",
                                    },
                                },
                                "required": ["type"],
                            },
                        },
                    ],
                },
                "refusal": {
                    "type": "string",
                    "description": "The refusal message by the assistant.",
                },
                "tool_calls": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "string",
                                "description": "The id of the tool call.",
                            },
                            "type": {
                                "type": "string",
                                "description": "The type of the tool.",
                            },
                            "function": {
                                "type": "object",
                                "properties": {
                                    "name": {
                                        "type": "string",
                                        "description": "The name of the function.",
                                    },
                                    "arguments": {
                                        "type": "string",
                                        "description": "The arguments for the function.",
                                    },
                                },
                                "required": ["name", "arguments"],
                                "description": "The function that the model called.",
                            },
                            # TODO: Support deprecated function_call field
                        },
                    },
                },
                "tool_call_id": {
                    "type": "string",
                    "description": "Tool call that this message is responding to.",
                },
                "function_call": {
                    "type": ["object", "null"],
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "The name of the function being called.",
                        },
                        "arguments": {
                            "type": "string",
                            "description": "Arguments to pass to the function.",
                        },
                    },
                    "required": ["name", "arguments"],
                    "description": "Only present when role is 'assistant' and the assistant is invoking a function.",
                },
                "name": {
                    "type": "string",
                    "description": "The name of the message sender.",
                },
            },
            "required": ["role", "content"],
            "additionalProperties": False,
        }
    else:
        raise HTTPException(
            status_code=400, detail=f"Unsupported message schema version: {version}"
        )


for tool_version in ["v1"]:
    tool_format = get_tool_format(tool_version)
    _registry_w_custom_objects = (
        Resource.from_contents(tool_format) @ _registry_w_custom_objects
    )
for message_version in ["v1"]:
    message_format = get_message_format(message_version)

    _registry_w_custom_objects = (
        Resource.from_contents(message_format) @ _registry_w_custom_objects
    )
