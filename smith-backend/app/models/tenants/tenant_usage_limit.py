import logging

from lc_database import redis

import app.config
from app import schemas
from app.api.auth.schemas import AuthInfo
from app.models.runs import usage_limits
from app.models.usage_limits import user_defined_limits
from app.schemas import TenantUsageLimitInfo

logger = logging.getLogger(__name__)


async def get_tenant_usage_limit_info(auth: AuthInfo) -> TenantUsageLimitInfo:
    if app.config.settings.IS_SELF_HOSTED:
        return TenantUsageLimitInfo(in_reject_set=False)

    user_defined_usage_limits = (
        await user_defined_limits.list_user_defined_usage_limits(auth)
    )
    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(
        str(auth.tenant_id)
    )

    async with (
        redis.aredis_routed_pool(
            str(auth.tenant_id), redis.RedisOperation.READ
        ) as aredis,
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
    ):
        pipe.get(
            usage_limits.usage_limit_events_ingested_per_minute_counter_key(
                auth.tenant_id
            )
        )
        pipe.get(
            usage_limits.usage_limit_events_ingested_per_hour_counter_key(
                auth.tenant_id
            )
        )
        pipe.get(
            usage_limits.usage_limit_payload_size_per_hour_counter_key(auth.tenant_id)
        )

        pipe.pfcount(
            user_defined_limits.usage_limit_unique_traces_per_month_hll_key(
                auth.tenant_id
            )
        )

        for limit in user_defined_usage_limits:
            limit.get_current_level_from_pipe(pipe)

        (
            events_ingested_in_min,
            events_ingested_in_hour,
            payload_size_ingested_in_hour,
            total_traces_in_month,
            *user_defined_counts,
        ) = await pipe.execute()

        # -1 means unlimited
        if (
            payload_size_ingested_in_hour is not None
            and auth.tenant_config.max_hourly_tracing_bytes != -1
            and int(payload_size_ingested_in_hour)
            >= auth.tenant_config.max_hourly_tracing_bytes
        ):
            return TenantUsageLimitInfo(
                in_reject_set=True,
                usage_limit_type=schemas.TenantUsageLimitType.payload_size,
                tenant_limit=auth.tenant_config.max_hourly_tracing_bytes,
            )

        if (
            events_ingested_in_hour is not None
            and auth.tenant_config.max_hourly_tracing_requests != -1
            and int(events_ingested_in_hour)
            >= auth.tenant_config.max_hourly_tracing_requests
        ):
            return TenantUsageLimitInfo(
                in_reject_set=True,
                usage_limit_type=schemas.TenantUsageLimitType.events_ingested_per_hour,
                tenant_limit=auth.tenant_config.max_hourly_tracing_requests,
            )

        if (
            total_traces_in_month is not None
            and auth.tenant_config.max_monthly_total_unique_traces != -1
            and int(total_traces_in_month)
            >= auth.tenant_config.max_monthly_total_unique_traces
        ):
            return TenantUsageLimitInfo(
                in_reject_set=True,
                usage_limit_type=schemas.TenantUsageLimitType.total_unique_traces,
                tenant_limit=auth.tenant_config.max_monthly_total_unique_traces,
            )

        if (
            events_ingested_in_min is not None
            and auth.tenant_config.max_events_ingested_per_minute != -1
            and int(events_ingested_in_min)
            >= auth.tenant_config.max_events_ingested_per_minute
        ):
            return TenantUsageLimitInfo(
                in_reject_set=True,
                usage_limit_type=schemas.TenantUsageLimitType.events_ingested_per_minute,
                tenant_limit=auth.tenant_config.max_events_ingested_per_minute,
            )

        for limit, current_level in zip(user_defined_usage_limits, user_defined_counts):
            if limit.limit_value != -1 and current_level >= limit.limit_value:
                return TenantUsageLimitInfo(
                    in_reject_set=True,
                    usage_limit_type=limit.limit_type.to_tenant_usage_limit_type(),
                    tenant_limit=limit.limit_value,
                )

        return TenantUsageLimitInfo(in_reject_set=False)
