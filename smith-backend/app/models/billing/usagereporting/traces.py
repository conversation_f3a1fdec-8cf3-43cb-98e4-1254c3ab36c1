import abc
import datetime
import uuid
from typing import List

import asyncpg
import structlog
from lc_config.settings import shared_settings
from lc_database import clickhouse, redis
from lc_database.database import asyncpg_conn
from redis.exceptions import LockNotOwnedError

from app import config
from app.models.billing.usagereporting.transactions import (
    MetronomeTransaction,
    MetronomeTransactionReporter,
    SelfHostedTransactionReporter,
    UsageReportingStatus,
)
from app.models.constants import CH_INSERT_TIME
from app.schemas import (
    TraceTransaction,
    TraceTransactionSource,
)
from app.schemas import (
    TraceTransactionWithOrg as TraceTransactionWithOrgModel,
)

logger = structlog.getLogger(__name__)


class TraceTransactionWithOrg(TraceTransactionWithOrgModel, MetronomeTransaction):
    def get_properties(self) -> dict:
        properties = {
            "tenant_id": str(self.tenant_id),
            "project_id": str(self.session_id),
            "count": self.trace_count,
        }
        # Add organization_id for self-hosted customers as metadata
        if self.self_hosted_customer_id:
            properties["organization_id"] = str(self.organization_id)
        return properties

    def get_org_id(self) -> uuid.UUID:
        return self.organization_id

    def get_transaction_id(self) -> uuid.UUID:
        return self.id

    def get_status(self) -> UsageReportingStatus:
        return UsageReportingStatus(self.status)

    def get_num_failed_send_attempts(self) -> int:
        return self.num_failed_send_attempts

    def get_event_timestamp(self) -> datetime.datetime:
        return self.start_interval_time

    def get_event_type(self) -> str:
        if self.transaction_type == "all_traces":
            return "langsmith_traces"
        elif self.transaction_type == "longlived_traces":
            return "langsmith_longlived_traces"
        else:
            raise ValueError(f"Unknown transaction type: {self.transaction_type}")

    def get_self_hosted_customer_id(self) -> uuid.UUID | None:
        return self.self_hosted_customer_id

    def to_json(self) -> dict:
        return {
            "id": str(self.id),
            "tenant_id": str(self.tenant_id),
            "session_id": str(self.session_id),
            "trace_count": self.trace_count,
            "start_insertion_time": self.start_insertion_time.isoformat(),
            "end_insertion_time": self.end_insertion_time.isoformat(),
            "start_interval_time": self.start_interval_time.isoformat(),
            "end_interval_time": self.end_interval_time.isoformat(),
            "status": self.status,
            "num_failed_send_attempts": self.num_failed_send_attempts,
            "transaction_type": self.transaction_type,
            "organization_id": str(self.organization_id),
            "source": self.source,
            "self_hosted_customer_id": str(self.self_hosted_customer_id)
            if self.self_hosted_customer_id
            else None,
        }

    @classmethod
    def get_status_colname(cls) -> str:
        return "status"

    @classmethod
    def get_failed_attempts_colname(cls) -> str:
        return "num_failed_send_attempts"

    @classmethod
    def get_table_name(cls) -> str:
        return "trace_count_transactions"

    @classmethod
    def get_transaction_id_colname(cls) -> str:
        return "id"


class TraceTransactionProcessor(abc.ABC):
    @classmethod
    @abc.abstractmethod
    def _get_redis_lock_name(cls, start_interval_time: datetime.datetime) -> str:
        pass

    @classmethod
    @abc.abstractmethod
    def get_transaction_type(cls) -> str:
        pass

    @classmethod
    @abc.abstractmethod
    def get_clickhouse_transaction_table_name(cls) -> str:
        pass

    @classmethod
    @abc.abstractmethod
    def _get_post_processing_status(cls) -> UsageReportingStatus:
        pass

    @classmethod
    async def process_trace_transactions(cls) -> None:
        """
        Creates transaction records in postgres that can be sent downstream to
        metronome. This is meant to be the source of truth on how we charge our
        customers.

        TRANSACTION INTERVALS:
        We aggregate traces in intervals to lower the number of records we are
        required to send to metronome / save space. For example, if the interval is
        30s, then the first interval of the day would be from 00:00:00 to 00:00:30.
        We may not have usage for the full interval, in which case we will record
        the usage starting from the first trace in the interval, up to the last
        trace in the interval.

        We make the assumption that we have recorded _all_ usage up until the last
        recorded trace in the interval, and will never attempt to process a new
        transaction prior to that time.

        We will process the last complete interval over
        `TRANSACTION_PROCESSING_DELAY_SEC` in the past.

        HANDLING FEATURE FLAGS:
        We record transactions for standard retention traces for all tenants in both cloud and self-hosted.
        We mark long-lived only as 'should_not_report' if LONGLIVED_TRACE_PROCESSING_ENABLED is set to False
        (this is the case in self-hosted). These records MUST NOT be sent to metronome.
        When our LONGLIVED_TRACE_PROCESSING_ENABLED flag is
        set to true, we will process any new long-lived transaction for all tenants.
        """
        delay_delta = datetime.timedelta(
            seconds=shared_settings.TRANSACTION_PROCESSING_DELAY_SEC
        )
        end_time = datetime.datetime.now(datetime.timezone.utc) - delay_delta

        last_complete_interval_end_ts = (
            end_time.timestamp()
            // shared_settings.TRANSACTION_INTERVAL_SEC
            * shared_settings.TRANSACTION_INTERVAL_SEC
        )
        last_complete_interval_end = datetime.datetime.fromtimestamp(
            last_complete_interval_end_ts, datetime.timezone.utc
        )

        async with asyncpg_conn() as db:
            query = f"""
                SELECT
                    max(upper(insertion_time_range))
                FROM trace_count_transactions
                WHERE transaction_type = '{cls.get_transaction_type()}'
                    AND source = '{TraceTransactionSource.local.value}'
            """
            first_interval_start = await db.fetchval(query)

        # Handle first run
        if not first_interval_start:
            first_interval_start = datetime.datetime.now(
                datetime.timezone.utc
            ) - datetime.timedelta(hours=2)

        logger_metadata = {
            "first_interval_start": first_interval_start.isoformat(),
            "last_complete_interval_end": last_complete_interval_end.isoformat(),
        }
        if first_interval_start > last_complete_interval_end:
            await logger.ainfo(
                "No transactions to process",
                metadata=logger_metadata,
            )
            return

        async with redis.aredis_pool() as aredis:
            # dual-write: lock, leave on original
            lock = aredis.lock(
                cls._get_redis_lock_name(first_interval_start),
                timeout=1,
                blocking=False,
            )
            if not await lock.acquire():
                await logger.ainfo(
                    "Transaction processing already in progress for interval, skipping...",
                    first_interval_start.isoformat(),
                    metadata=logger_metadata,
                )
                return

        try:
            await logger.ainfo(
                "Lock Acquired. Processing transactions",
                metadata=logger_metadata,
            )
            async with clickhouse.clickhouse_client(
                clickhouse.ClickhouseClient.USER_HEAVY_WORKLOADS_SLOW
            ) as ch:
                ch_table_name = cls.get_clickhouse_transaction_table_name()
                query = (
                    """
                    SELECT
                        tenant_id,
                        session_id,
                        generateUUIDv4() as transaction_id,
                        greatest(
                            toStartOfInterval(inserted_at, INTERVAL {TRANSACTION_INTERVAL_SEC} SECOND),
                            toDateTime64({start}, 6)
                        ) as start_interval_time,
                        min(inserted_at) as start_insertion_time,
                        max(inserted_at) as end_insertion_time,
                        start_interval_time + INTERVAL {TRANSACTION_INTERVAL_SEC} SECOND as end_interval_time,
                        COUNT(*) as num_traces
                    """
                    + f"FROM {ch_table_name} FINAL"
                    + """
                    WHERE
                        -- needs to be exclusive to match the lower bound condition
                        -- we enforce in transaction reporting
                        inserted_at > {start}
                        AND inserted_at <= {end}
                    GROUP BY tenant_id, session_id, start_interval_time
                """
                )
                rows = await ch.fetch(
                    f"fetch_{ch_table_name}_interval_{shared_settings.TRANSACTION_INTERVAL_SEC}_sec",
                    query,
                    params={
                        "start": first_interval_start.strftime(CH_INSERT_TIME),
                        "end": last_complete_interval_end.strftime(CH_INSERT_TIME),
                        "TRANSACTION_INTERVAL_SEC": shared_settings.TRANSACTION_INTERVAL_SEC,
                    },
                )

            transactions_to_create = [
                TraceTransaction(
                    id=row["transaction_id"],
                    tenant_id=row["tenant_id"],
                    session_id=row["session_id"],
                    trace_count=row["num_traces"],
                    start_insertion_time=row["start_insertion_time"].replace(
                        tzinfo=datetime.timezone.utc
                    ),
                    end_insertion_time=row["end_insertion_time"].replace(
                        tzinfo=datetime.timezone.utc
                    ),
                    start_interval_time=row["start_interval_time"].replace(
                        tzinfo=datetime.timezone.utc
                    ),
                    end_interval_time=row["end_interval_time"].replace(
                        tzinfo=datetime.timezone.utc
                    ),
                    status=cls._get_post_processing_status(),
                    num_failed_send_attempts=0,
                    transaction_type=cls.get_transaction_type(),
                    source=TraceTransactionSource.local.value,
                )
                for row in rows
            ]

            async with asyncpg_conn() as db, db.transaction():
                query = """
                    INSERT INTO trace_count_transactions (
                        id,
                        tenant_id,
                        session_id,
                        trace_count,
                        insertion_time_range,
                        interval,
                        insertion_time_range_start,
                        insertion_time_range_end,
                        interval_start,
                        interval_end,
                        status,
                        num_failed_send_attempts,
                        transaction_type,
                        organization_id,
                        source
                    )
                    SELECT
                        $1,
                        $2,
                        $3,
                        $4,
                        tstzrange($5, $6, '[]'),
                        tstzrange($7, $8, '(]'),
                        $5,
                        $6,
                        $7,
                        $8,
                        $9,
                        $10,
                        $11,
                        t.organization_id,
                        $12
                    FROM tenants t
                    WHERE t.id = $2
                """
                await db.executemany(
                    query,
                    [
                        (
                            txn.id,
                            txn.tenant_id,
                            txn.session_id,
                            txn.trace_count,
                            txn.start_insertion_time,
                            txn.end_insertion_time,
                            txn.start_interval_time,
                            txn.end_interval_time,
                            txn.status,
                            txn.num_failed_send_attempts,
                            txn.transaction_type,
                            txn.source,
                        )
                        for txn in transactions_to_create
                    ],
                )

            await logger.ainfo(
                "Transaction processing complete",
                metadata={
                    **logger_metadata,
                    "transactions_processed": len(transactions_to_create),
                },
            )

        finally:
            try:
                async with redis.aredis_pool() as aredis:
                    # dual-write: lock, leave on original
                    await lock.release()
            except LockNotOwnedError as e:
                await logger.awarn(
                    "Lock was not owned, but we tried to release it", exc_info=e
                )
                pass


class AllTracesTraceProcessor(TraceTransactionProcessor):
    TRACES_REDIS_LOCK_PREFIX = "trace_count_transactions_lock:"

    @classmethod
    def _get_redis_lock_name(cls, start_interval_time: datetime.datetime) -> str:
        return cls.TRACES_REDIS_LOCK_PREFIX + start_interval_time.isoformat()

    @classmethod
    def get_transaction_type(cls) -> str:
        return "all_traces"

    @classmethod
    def get_clickhouse_transaction_table_name(cls) -> str:
        return "billable_traces"

    @classmethod
    def _get_post_processing_status(cls) -> UsageReportingStatus:
        return UsageReportingStatus.PENDING


class LonglivedTracesTraceProcessor(TraceTransactionProcessor):
    LONGLIVED_REDIS_LOCK_PREFIX = "longlived_trace_count_transactions_lock:"

    @classmethod
    def _get_redis_lock_name(cls, start_interval_time: datetime.datetime) -> str:
        return cls.LONGLIVED_REDIS_LOCK_PREFIX + start_interval_time.isoformat()

    @classmethod
    def get_transaction_type(cls) -> str:
        return "longlived_traces"

    @classmethod
    def get_clickhouse_transaction_table_name(cls) -> str:
        return "billable_longlived_traces"

    @classmethod
    def _get_post_processing_status(cls) -> UsageReportingStatus:
        if config.settings.LONGLIVED_TRACE_PROCESSING_ENABLED:
            return UsageReportingStatus.PENDING
        else:
            return UsageReportingStatus.SHOULD_NOT_REPORT


class TraceTransactionReporter(MetronomeTransactionReporter[TraceTransactionWithOrg]):
    @classmethod
    def _get_initial_status(cls) -> UsageReportingStatus:
        return UsageReportingStatus.PENDING

    @classmethod
    async def _check_metronome_customerless_transactions(
        cls,
        db: asyncpg.Connection,
        status: UsageReportingStatus,
        source: TraceTransactionSource,
    ) -> None:
        # For monitoring purposes, we want to record how much data we will send
        # to metronome for orgs that don't have a metronome customer id.
        #
        # Metronome does support this, since we send data to an ingest_alias
        # (the organization_id) which can be later associated with a customer
        # when it is created. However, this may lead to data inconsistencies
        # if the metronome customer is never created and associated with the
        # organization.
        #
        # TODO: create a cron job to ensure that all orgs have valid customer
        #       ids associated in metronome
        if source == TraceTransactionSource.local:
            tenants_with_usage_but_no_metronome_customer = await db.fetch(
                f"""
                WITH trace_counts AS (
                    SELECT
                        SUM(trace_count) as total_traces,
                        tenant_id,
                        organization_id
                    FROM
                        trace_count_transactions
                    WHERE
                        status = '{status.value}'
                        AND organization_id IS NOT NULL
                        AND source = '{source.value}'
                    GROUP BY tenant_id, organization_id
                )
                SELECT tc.*, o.metronome_customer_id
                FROM trace_counts tc
                JOIN organizations o ON o.id = tc.organization_id
                WHERE o.metronome_customer_id IS NULL
                """
            )
            if len(tenants_with_usage_but_no_metronome_customer) > 0:
                tenants_without_usage = [
                    str(row["tenant_id"])
                    for row in tenants_with_usage_but_no_metronome_customer
                ]
                orgs_for_tenants = [
                    str(row["organization_id"])
                    for row in tenants_with_usage_but_no_metronome_customer
                ]
                counts_for_tenants = [
                    row["total_traces"]
                    for row in tenants_with_usage_but_no_metronome_customer
                ]

                await logger.awarn(
                    "Found transactions for orgs without a metronome customer id",
                    metadata={
                        "tenants": tenants_without_usage,
                        "orgs_for_tenants": orgs_for_tenants,
                        "trace_counts": counts_for_tenants,
                        "source": source.value,
                    },
                )
        elif source == TraceTransactionSource.remote_self_hosted:
            tenants_with_usage_but_no_metronome_customer = await db.fetch(
                f"""
                WITH trace_counts AS (
                    SELECT
                        SUM(trace_count) as total_traces,
                        tenant_id,
                        sl.self_hosted_customer_id
                    FROM
                        trace_count_transactions
                        JOIN self_hosted_licenses sl on sl.id = trace_count_transactions.self_hosted_license_id
                    WHERE
                        status = '{status.value}'
                        AND self_hosted_license_id IS NOT NULL
                        AND source = '{source.value}'
                    GROUP BY tenant_id, sl.self_hosted_customer_id
                )
                SELECT tc.*, sc.id as self_hosted_customer_id
                FROM trace_counts tc
                JOIN self_hosted_customers sc ON sc.id = tc.self_hosted_customer_id
                WHERE sc.metronome_customer_id IS NULL
                """
            )
            if len(tenants_with_usage_but_no_metronome_customer) > 0:
                tenants_without_usage = [
                    str(row["tenant_id"])
                    for row in tenants_with_usage_but_no_metronome_customer
                ]
                orgs_for_tenants = [
                    str(row["organization_id"])
                    for row in tenants_with_usage_but_no_metronome_customer
                ]
                customers_for_tenants = [
                    str(row["self_hosted_customer_id"])
                    for row in tenants_with_usage_but_no_metronome_customer
                ]
                counts_for_tenants = [
                    row["total_traces"]
                    for row in tenants_with_usage_but_no_metronome_customer
                ]

                await logger.awarn(
                    "Found self-hosted transactions for customers without a metronome customer id",
                    metadata={
                        "tenants": tenants_without_usage,
                        "orgs_for_tenants": orgs_for_tenants,
                        "trace_counts": counts_for_tenants,
                        "source": source.value,
                        "customers": customers_for_tenants,
                    },
                )
        else:
            raise ValueError(f"Unknown source: {source}")

    @classmethod
    async def _fetch_reportable_transactions(
        cls,
        status: UsageReportingStatus,
        _: datetime.datetime
        | None = None,  # Not needed because we don't limit the trace transactions fetching
    ) -> tuple[List[TraceTransactionWithOrg], bool]:
        async with asyncpg_conn() as db:
            for source in TraceTransactionSource:
                await cls._check_metronome_customerless_transactions(
                    db,
                    status,
                    source,
                )

            rows = await db.fetch(
                f"""
                SELECT
                    t.id as transaction_id,
                    t.tenant_id,
                    t.session_id,
                    t.trace_count,
                    t.interval_start as start_interval_time,
                    t.interval_end as end_interval_time,
                    t.insertion_time_range_start as start_insertion_time,
                    t.insertion_time_range_end as end_insertion_time,
                    t.status,
                    t.num_failed_send_attempts,
                    t.transaction_type,
                    t.organization_id,
                    t.source,
                    sl.self_hosted_customer_id as self_hosted_customer_id
                FROM
                    trace_count_transactions t
                LEFT JOIN self_hosted_licenses sl ON sl.id = t.self_hosted_license_id
                WHERE status = '{status.value}'
                ORDER BY start_interval_time
                """
            )
            return [
                TraceTransactionWithOrg(
                    id=row["transaction_id"],
                    tenant_id=row["tenant_id"],
                    session_id=row["session_id"],
                    trace_count=row["trace_count"],
                    start_insertion_time=row["start_insertion_time"],
                    end_insertion_time=row["end_insertion_time"],
                    start_interval_time=row["start_interval_time"],
                    end_interval_time=row["end_interval_time"],
                    status=row["status"],
                    num_failed_send_attempts=row["num_failed_send_attempts"],
                    transaction_type=row["transaction_type"],
                    organization_id=row["organization_id"],
                    source=row["source"],
                    self_hosted_customer_id=row.get("self_hosted_customer_id", None),
                )
                for row in rows
            ], False


class SelfHostedTraceTransactionReporter(
    SelfHostedTransactionReporter[TraceTransactionWithOrg]
):
    @classmethod
    def _get_initial_status(cls) -> UsageReportingStatus:
        return UsageReportingStatus.PENDING

    @classmethod
    async def _fetch_reportable_transactions(
        cls,
        status: UsageReportingStatus,
        _: datetime.datetime
        | None = None,  # Not needed because we don't limit the trace transactions fetching
    ) -> tuple[List[TraceTransactionWithOrg], bool]:
        async with asyncpg_conn() as db:
            rows = await db.fetch(
                f"""
                SELECT
                    id as transaction_id,
                    tenant_id,
                    session_id,
                    trace_count,
                    interval_start as start_interval_time,
                    interval_end as end_interval_time,
                    insertion_time_range_start as start_insertion_time,
                    insertion_time_range_end as end_insertion_time,
                    status,
                    num_failed_send_attempts,
                    transaction_type,
                    organization_id,
                    source
                FROM
                    trace_count_transactions
                WHERE
                    status = '{status.value}'
                ORDER BY start_interval_time
                """
            )

            return [
                TraceTransactionWithOrg(
                    id=row["transaction_id"],
                    tenant_id=row["tenant_id"],
                    session_id=row["session_id"],
                    trace_count=row["trace_count"],
                    start_insertion_time=row["start_insertion_time"],
                    end_insertion_time=row["end_insertion_time"],
                    start_interval_time=row["start_interval_time"],
                    end_interval_time=row["end_interval_time"],
                    status=row["status"],
                    num_failed_send_attempts=row["num_failed_send_attempts"],
                    transaction_type=row["transaction_type"],
                    # Some old trace count transactions may not have an organization_id
                    organization_id=row["organization_id"]
                    or uuid.UUID("00000000-0000-0000-0000-000000000000"),
                    source=row["source"],
                )
                for row in rows
            ], False
