import asyncio
import uuid
from collections import defaultdict
from typing import List, NamedT<PERSON><PERSON>, Union, cast
from urllib.parse import quote

import asyncpg
import asyncpg.transaction
import structlog
from fastapi import HTTPException
from lc_config.tenant_config import OrganizationConfig
from lc_database import database

from app import config, schemas
from app.api.auth import AuthInfo, TenantlessAuthInfo
from app.api.auth.basic_auth import generate_password, hash_password
from app.api.auth.schemas import OrganizationRoles, OrgAuthInfo
from app.config import settings
from app.models.identities.invites import (
    OrgInfo,
    WorkspaceInvitePayload,
    send_invite_email,
    send_workspaces_invite_email_batch,
)
from app.models.identities.seat_txn import (
    IDForSeatChangeOperation,
    SeatChangeOperation,
    get_seat_count_for_org,
    seat_event_modification_transaction,
)
from app.models.identities.users import (
    get_linked_login_methods_in_txn,
    get_provider_users_slim_bulk_in_txn,
)
from app.retry import retry_asyncpg

logger = structlog.get_logger(__name__)


class PendingIdentityCreateInTxn(NamedTuple):
    email: str
    read_only: bool
    role_id: uuid.UUID


class IdentityCreateInTxn(NamedTuple):
    ls_user_id: uuid.UUID
    user_id: uuid.UUID
    read_only: bool | None
    role_id: uuid.UUID


class UserCreateInTxn(NamedTuple):
    user_id: uuid.UUID
    email: str
    password: str
    hashed_password: str
    full_name: str | None


async def _send_invite_email_if_enabled(
    db: asyncpg.Connection, recipient_email: str, tenant_id: uuid.UUID
):
    if config.settings.POSTMARK_SERVER_TOKEN is not None:
        await send_invite_email(
            db,
            recipient_email,
            tenant_id,
        )


async def _fail_if_identity_is_org_admin_in_txn(
    db: asyncpg.Connection, identity_id: uuid.UUID, is_pending: bool
):
    # If multiple workspaces are enabled, prevent deletion of Org Admins
    # since they must be deleted at the org level.
    # This maintains the invariant that Org Admins are Admins in all workspaces.
    join_field = "email" if is_pending else "user_id"
    table = "pending_identities" if is_pending else "identities"

    org_role_name = await db.fetchval(
        f"""
        with workspace_identity as (
            select * from {table}
            where id = $1
            and access_scope = 'workspace'
        ),
        org_identity as (
            select oi.role_id from {table} oi join workspace_identity wi
            on oi.organization_id = wi.organization_id
                and oi.{join_field} = wi.{join_field}
                and oi.access_scope = 'organization'
        )
        select r.name from roles r cross join workspace_identity, org_identity
        where r.id = org_identity.role_id;
        """,
        identity_id,
    )
    if org_role_name == OrganizationRoles.ADMIN.value:
        raise HTTPException(
            status_code=409,
            detail="Cannot remove/uninvite an Organization Admin from a workspace. Please remove them from the organization instead.",
        )


async def _fail_if_last_org_admin_remaining_in_txn(
    auth: OrgAuthInfo, db: asyncpg.Connection, identity_id: uuid.UUID
):
    """Fail if this is the last organization admin in the organization"""
    result = await db.fetchrow(
        """
        WITH role_id_cte AS (
            SELECT id
            FROM roles
            WHERE name = $2
        ),
        org_admin_count AS (
            SELECT COUNT(*) AS count
            FROM identities
            WHERE organization_id = $1
            AND role_id = (SELECT id FROM role_id_cte)
            AND access_scope = 'organization'
        )
        SELECT
            (identities.role_id = role_id_cte.id) AS is_org_admin,
            org_admin_count.count
        FROM identities
        CROSS JOIN org_admin_count, role_id_cte
        WHERE identities.id = $3;
        """,
        auth.organization_id,
        OrganizationRoles.ADMIN.value,
        identity_id,
    )
    if result["is_org_admin"] and result["count"] == 1:
        raise HTTPException(
            status_code=409,
            detail="Cannot remove the last Organization Admin from an organization.",
        )


@retry_asyncpg
async def record_initial_seat_count(org_id: uuid.UUID) -> None:
    """Record the initial seat count for an organization."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(organization_id=org_id),
        SeatChangeOperation.INITIALIZE_SEATS,
    ):
        pass


@retry_asyncpg
async def record_seat_count_heartbeat(
    org_id: uuid.UUID, maybe_conn: asyncpg.Connection | None = None
) -> None:
    """Record a heartbeat event for an organization."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(organization_id=org_id),
        SeatChangeOperation.HEARTBEAT,
        maybe_conn=maybe_conn,
    ):
        pass


async def record_seat_count_heartbeats_batch(org_ids: list[uuid.UUID]) -> None:
    """Record heartbeat events for organizations on a single Postgres connection."""
    logger.info(
        "Recording seat count heartbeats for multiple organizations",
        num_orgs=len(org_ids),
    )
    async with database.asyncpg_conn() as db:
        for org_id in org_ids:
            await record_seat_count_heartbeat(org_id, maybe_conn=db)


@retry_asyncpg
async def get_org_members(
    auth: AuthInfo | OrgAuthInfo,
) -> schemas.OrganizationMembers:
    """Get all members of an organization, including pending members. If making changes, check get_tenant_members as well."""
    access_scope = schemas.AccessScope.organization
    if not auth.organization_id:
        raise HTTPException(
            status_code=400,
            detail="Organization must be set.",
        )

    query = """
    with

    members_cte as (
        select
            jsonb_agg(jsonb_build_object(
                'id', identities.id,
                'user_id', identities.user_id,
                'ls_user_id', identities.ls_user_id,
                'tenant_id', identities.tenant_id,
                'organization_id', identities.organization_id,
                'created_at', identities.created_at,
                'read_only', identities.read_only,
                'role_id', identities.role_id,
                'role_name', roles.display_name,
                'email', users.email,
                'full_name', users.full_name,
                'avatar_url', users.avatar_url,
                'access_scope', identities.access_scope,
                'tenant_ids', COALESCE((
                    select jsonb_agg(ordered_i.id)
                    from (
                        select t.id
                        from identities i
                        join tenants t on i.tenant_id = t.id
                        where i.ls_user_id = identities.ls_user_id
                        and i.organization_id = identities.organization_id
                        and i.access_scope = 'workspace'
                        and not t.is_deleted
                        order by t.display_name
                    ) as ordered_i
                ), '[]')
            ) order by identities.created_at desc, users.email asc) as members
        from identities
        inner join roles on roles.id = identities.role_id
        left join users on users.ls_user_id = identities.ls_user_id
        where identities.organization_id = $organization_id
        and identities.user_id is not null
        and identities.access_scope = $access_scope
    ),

    pending_cte as (
        select jsonb_agg(jsonb_build_object(
            'id', pending_identities.id,
            'tenant_id', pending_identities.tenant_id,
            'organization_id', pending_identities.organization_id,
            'created_at', pending_identities.created_at,
            'email', pending_identities.email,
            'read_only', pending_identities.read_only,
            'role_id', pending_identities.role_id,
            'role_name', roles.display_name,
            'access_scope', pending_identities.access_scope,
            'tenant_ids', COALESCE((
                select jsonb_agg(ordered_pi.id)
                from (
                    select t.id
                    from pending_identities pi
                    join tenants t on pi.tenant_id = t.id
                    where pi.email = pending_identities.email
                    and pi.organization_id = pending_identities.organization_id
                    and pi.access_scope = 'workspace'
                    and not t.is_deleted
                    order by t.display_name
                ) as ordered_pi
            ), '[]')
        ) order by pending_identities.created_at desc, pending_identities.email asc) as pending
        from pending_identities
        inner join roles on roles.id = pending_identities.role_id
        inner join organizations o on o.id = pending_identities.organization_id
        where pending_identities.organization_id = $organization_id
        and pending_identities.access_scope = $access_scope
        and not o.sso_only
    )

    select
        organizations.id as organization_id,
        coalesce(members_cte.members, '[]') as members,
        coalesce(pending_cte.pending, '[]') as pending
    from organizations
    cross join members_cte
    cross join pending_cte
    where organizations.id = $organization_id"""

    sql_params = {
        "organization_id": auth.organization_id,
        "access_scope": access_scope,
    }
    sql_query_pos = database.kwargs_to_pgpos(query, sql_params)

    async with database.asyncpg_conn() as db:
        result = await db.fetchrow(sql_query_pos.sql, *sql_query_pos.args)
        if not result:
            raise HTTPException(
                status_code=404,
                detail="Organization not found.",
            )
        response = schemas.OrganizationMembers(**result)
        provider_users = await get_provider_users_slim_bulk_in_txn(
            db, [cast(uuid.UUID, member.ls_user_id) for member in response.members]
        )
        provider_users_by_id = defaultdict(list)
        for provider_user in provider_users:
            provider_users_by_id[provider_user.ls_user_id].append(provider_user)
        for member in response.members:
            member.linked_login_methods = provider_users_by_id[member.ls_user_id]
    return response


@retry_asyncpg
async def get_active_org_members(
    auth: AuthInfo | OrgAuthInfo, params: schemas.ListMembersQueryParams
) -> tuple[list[schemas.OrgMemberIdentity], int]:
    """Get all active members of an organization. If making changes, check get_active_tenant_members as well."""
    if not auth.organization_id:
        raise HTTPException(
            status_code=400,
            detail="Organization must be set.",
        )
    access_scope = schemas.AccessScope.organization
    paginated_sql_params = {
        "limit": params.limit,
        "offset": params.offset,
    }
    base_sql_params = {
        "organization_id": auth.organization_id,
        "access_scope": access_scope,
    }

    where_clause = """
    where i.organization_id = $organization_id
        and i.user_id is not null
        and i.access_scope = $access_scope
    """

    # Optional filters
    if params.user_ids:
        base_sql_params["user_ids"] = params.user_ids
        where_clause += " and i.ls_user_id = ANY(SELECT ls_user_id from provider_users where provider_user_id = ANY($user_ids))"
    if params.ls_user_ids:
        base_sql_params["ls_user_ids"] = params.ls_user_ids
        where_clause += " and i.ls_user_id = ANY($ls_user_ids)"
    if params.emails:
        base_sql_params["emails"] = [email.lower() for email in params.emails]
        where_clause += " and lower(u.email) = ANY($emails)"

    total_query = f"""
        select count(*) from identities i
        left join users u on u.ls_user_id = i.ls_user_id
        {where_clause}
    """
    members_query = f"""
    with paged as (
        select i.*, u.email, u.full_name, u.avatar_url
        from identities i
        left join users u on u.ls_user_id = i.ls_user_id
        {where_clause}
        order by i.created_at desc, u.email asc
        OFFSET $offset LIMIT $limit
    )
    select
        paged.id,
        paged.user_id,
        paged.ls_user_id,
        paged.tenant_id,
        paged.organization_id,
        paged.created_at,
        paged.read_only,
        paged.role_id,
        roles.display_name as role_name,
        paged.email,
        paged.full_name,
        paged.avatar_url,
        paged.access_scope,
        COALESCE((
            select jsonb_agg(ordered_i.id)
            from (
                select t.id
                from identities i
                join tenants t on i.tenant_id = t.id
                where i.ls_user_id = paged.ls_user_id
                and i.organization_id = paged.organization_id
                and i.access_scope = 'workspace'
                and not t.is_deleted
                order by t.display_name
            ) as ordered_i
        ), '[]') as tenant_ids
    from paged
    inner join roles on roles.id = paged.role_id
    order by paged.created_at desc, paged.email asc
    """

    sql_query_pos = database.kwargs_to_pgpos(
        members_query, {**base_sql_params, **paginated_sql_params}
    )
    total_sql_query_pos = database.kwargs_to_pgpos(total_query, base_sql_params)

    async with database.asyncpg_pool() as pool, database.asyncpg_conn() as db:
        result, total = await asyncio.gather(
            db.fetch(sql_query_pos.sql, *sql_query_pos.args),
            pool.fetchval(total_sql_query_pos.sql, *total_sql_query_pos.args),
        )
        if not result:
            return [], 0
        response = [schemas.OrgMemberIdentity(**i) for i in result]
        provider_users = await get_provider_users_slim_bulk_in_txn(
            db, [cast(uuid.UUID, member.ls_user_id) for member in response]
        )
        provider_users_by_id = defaultdict(list)
        for provider_user in provider_users:
            provider_users_by_id[provider_user.ls_user_id].append(provider_user)
        for member in response:
            member.linked_login_methods = provider_users_by_id[member.ls_user_id]
    return response, total


@retry_asyncpg
async def get_pending_org_members(
    auth: AuthInfo | OrgAuthInfo, params: schemas.ListPendingMembersQueryParams
) -> tuple[list[schemas.OrgPendingIdentity], int]:
    """Get all pending members of an organization. If making changes, check get_pending_tenant_members and get_[org|tenant]_members as well."""
    if not auth.organization_id:
        raise HTTPException(
            status_code=400,
            detail="Organization must be set.",
        )
    access_scope = schemas.AccessScope.organization
    paginated_sql_params = {
        "limit": params.limit,
        "offset": params.offset,
    }
    base_sql_params = {
        "organization_id": auth.organization_id,
        "access_scope": access_scope,
    }

    where_clause = """
    where pi.organization_id = $organization_id
        and pi.access_scope = $access_scope
        and not o.sso_only
    """

    # Optional filters
    if params.emails:
        base_sql_params["emails"] = [email.lower() for email in params.emails]
        where_clause += " and lower(pi.email) = ANY($emails)"

    total_query = f"""
    select count(*) from pending_identities pi
        inner join organizations o on o.id = pi.organization_id
        {where_clause}
    """
    members_query = f"""
    with paged as (
        select pi.*
        from pending_identities pi
        inner join organizations o on o.id = pi.organization_id
        {where_clause}
        order by pi.created_at desc, pi.email asc
        OFFSET $offset LIMIT $limit
    )
    select
        paged.id,
        paged.tenant_id,
        paged.organization_id,
        paged.created_at,
        paged.email,
        paged.read_only,
        paged.role_id,
        roles.display_name as role_name,
        paged.access_scope,
        COALESCE((
            select jsonb_agg(ordered_pi.id)
            from (
                select t.id
                from pending_identities pi
                join tenants t on pi.tenant_id = t.id
                where pi.email = paged.email
                and pi.organization_id = paged.organization_id
                and pi.access_scope = 'workspace'
                and not t.is_deleted
                order by t.display_name
            ) as ordered_pi
        ), '[]') as tenant_ids
    from paged
    inner join roles on roles.id = paged.role_id
    order by paged.created_at desc, paged.email asc
    """

    sql_query_pos = database.kwargs_to_pgpos(
        members_query, {**base_sql_params, **paginated_sql_params}
    )

    total_sql_query_pos = database.kwargs_to_pgpos(total_query, base_sql_params)

    async with database.asyncpg_pool() as pool, database.asyncpg_conn() as db:
        result, total = await asyncio.gather(
            db.fetch(sql_query_pos.sql, *sql_query_pos.args),
            pool.fetchval(total_sql_query_pos.sql, *total_sql_query_pos.args),
        )
    return [schemas.OrgPendingIdentity(**i) for i in result], total


@retry_asyncpg
async def get_active_ws_members(
    auth: AuthInfo,
    params: schemas.ListMembersQueryParams,
) -> tuple[list[schemas.MemberIdentity], int]:
    """Get all active members of a workspace. If making changes, check get_active_org_members as well."""
    if not auth.tenant_id:
        raise HTTPException(
            status_code=400,
            detail="Tenant must be set.",
        )
    access_scope = schemas.AccessScope.workspace
    paginated_sql_params = {
        "limit": params.limit,
        "offset": params.offset,
    }
    base_sql_params = {
        "tenant_id": auth.tenant_id,
        "access_scope": access_scope,
    }

    where_clause = """
    where i.tenant_id = $tenant_id
        and i.user_id is not null
        and i.access_scope = $access_scope
    """

    # Optional filters
    total_join_clause = ""
    if params.user_ids:
        base_sql_params["user_ids"] = params.user_ids
        where_clause += " and i.ls_user_id = ANY(SELECT ls_user_id from provider_users where provider_user_id = ANY($user_ids))"
    if params.ls_user_ids:
        base_sql_params["ls_user_ids"] = params.ls_user_ids
        where_clause += " and i.ls_user_id = ANY($ls_user_ids)"
    if params.emails:
        base_sql_params["emails"] = [email.lower() for email in params.emails]
        where_clause += " and lower(u.email) = ANY($emails)"
        total_join_clause = "left join users u on u.ls_user_id = i.ls_user_id"

    total_query = f"""
    select count(*) from identities i
    {total_join_clause}
    {where_clause}
    """
    members_query = f"""
    with paged as (
        select i.*, u.email, u.full_name, u.avatar_url
        from identities i
        left join users u on u.ls_user_id = i.ls_user_id
        {where_clause}
        order by i.created_at desc, u.email asc
        OFFSET $offset LIMIT $limit
    )
    select
        paged.id,
        paged.user_id,
        paged.ls_user_id,
        paged.tenant_id,
        paged.organization_id,
        paged.created_at,
        paged.read_only,
        paged.role_id,
        roles.display_name as role_name,
        paged.email,
        paged.full_name,
        paged.avatar_url,
        paged.access_scope,
        parent_roles.id AS org_role_id,
        parent_roles.display_name AS org_role_name
    from paged
    inner join roles on roles.id = paged.role_id
    left join identities parent_identities ON parent_identities.id = paged.parent_identity_id
    left join roles parent_roles ON parent_roles.id = parent_identities.role_id
    order by paged.created_at desc, paged.email asc
    """
    sql_query_pos = database.kwargs_to_pgpos(
        members_query, {**base_sql_params, **paginated_sql_params}
    )
    total_sql_query_pos = database.kwargs_to_pgpos(total_query, base_sql_params)

    async with database.asyncpg_pool() as pool, database.asyncpg_conn() as db:
        result, total = await asyncio.gather(
            db.fetch(sql_query_pos.sql, *sql_query_pos.args),
            pool.fetchval(total_sql_query_pos.sql, *total_sql_query_pos.args),
        )
        if not result:
            return [], 0
        response = [schemas.MemberIdentity(**i) for i in result]
        provider_users = await get_provider_users_slim_bulk_in_txn(
            db, [cast(uuid.UUID, member.ls_user_id) for member in response]
        )
        provider_users_by_id = defaultdict(list)
        for provider_user in provider_users:
            provider_users_by_id[provider_user.ls_user_id].append(provider_user)
        for member in response:
            member.linked_login_methods = provider_users_by_id[member.ls_user_id]
    return response, total


@retry_asyncpg
async def get_pending_ws_members(
    auth: AuthInfo, params: schemas.ListMembersQueryParams
) -> tuple[list[schemas.PendingIdentity], int]:
    """Get all pending members of a workspace. If making changes, check get_pending_org_members as well."""
    if not auth.tenant_id:
        raise HTTPException(
            status_code=400,
            detail="Tenant must be set.",
        )
    access_scope = schemas.AccessScope.workspace
    paginated_sql_params = {
        "limit": params.limit,
        "offset": params.offset,
    }
    base_sql_params = {
        "tenant_id": auth.tenant_id,
        "access_scope": access_scope,
    }

    where_clause = """
    where pi.tenant_id = $tenant_id
        and pi.access_scope = $access_scope
        and not o.sso_only
    """

    # Optional filters
    if params.emails:
        base_sql_params["emails"] = [email.lower() for email in params.emails]
        where_clause += " and lower(pi.email) = ANY($emails)"

    total_query = f"""
    select count(*) from pending_identities pi
        inner join organizations o on o.id = pi.organization_id
        {where_clause}
    """
    members_query = f"""
    with paged as (
        select pi.*
        from pending_identities pi
        inner join organizations o on o.id = pi.organization_id
        {where_clause}
        order by pi.created_at desc, pi.email asc
        OFFSET $offset LIMIT $limit
    )
    select
        paged.id,
        paged.tenant_id,
        paged.organization_id,
        paged.created_at,
        paged.email,
        paged.read_only,
        paged.role_id,
        roles.display_name as role_name,
        paged.access_scope,
        parent_roles.id AS org_role_id,
        parent_roles.display_name AS org_role_name
    from paged
    inner join roles on roles.id = paged.role_id
    left join pending_identities parent_identities ON parent_identities.id = paged.parent_pending_identity_id
    left join roles parent_roles ON parent_roles.id = parent_identities.role_id
    order by paged.created_at desc, paged.email asc
    """
    sql_query_pos = database.kwargs_to_pgpos(
        members_query, {**base_sql_params, **paginated_sql_params}
    )
    total_sql_query_pos = database.kwargs_to_pgpos(total_query, base_sql_params)

    async with database.asyncpg_pool() as pool, database.asyncpg_conn() as db:
        result, total = await asyncio.gather(
            db.fetch(sql_query_pos.sql, *sql_query_pos.args),
            pool.fetchval(total_sql_query_pos.sql, *total_sql_query_pos.args),
        )
        if not result:
            return [], 0
    return [schemas.PendingIdentity(**i) for i in result], total


@retry_asyncpg
async def get_tenant_members(
    auth: AuthInfo,
) -> schemas.TenantMembers:
    """Get all members of a tenant, including pending members. If making changes, check get_org_members as well."""
    access_scope = schemas.AccessScope.workspace
    if not auth.tenant_id:
        raise HTTPException(
            status_code=400,
            detail="Tenant must be set.",
        )

    async with database.asyncpg_conn() as db:
        result = await db.fetchrow(
            """
    with

    members_cte as (
        select
            jsonb_agg(jsonb_build_object(
                'id', identities.id,
                'user_id', identities.user_id,
                'ls_user_id', identities.ls_user_id,
                'tenant_id', identities.tenant_id,
                'organization_id', identities.organization_id,
                'created_at', identities.created_at,
                'read_only', identities.read_only,
                'role_id', identities.role_id,
                'role_name', roles.display_name,
                'email', users.email,
                'full_name', users.full_name,
                'avatar_url', users.avatar_url,
                'access_scope', identities.access_scope,
                'org_role_id', parent_roles.id,
                'org_role_name', parent_roles.display_name
            ) order by identities.created_at desc) as members
        from identities
        inner join roles on roles.id = identities.role_id
        left join users on users.ls_user_id = identities.ls_user_id
        left join identities parent_identities ON parent_identities.id = identities.parent_identity_id
        left join roles parent_roles ON parent_roles.id = parent_identities.role_id
        where identities.tenant_id = $1
        and identities.ls_user_id is not null
        and identities.access_scope = $2
    ),

    pending_cte as (
        select jsonb_agg(jsonb_build_object(
            'id', pending_identities.id,
            'tenant_id', pending_identities.tenant_id,
            'organization_id', pending_identities.organization_id,
            'created_at', pending_identities.created_at,
            'email', pending_identities.email,
            'read_only', pending_identities.read_only,
            'role_id', pending_identities.role_id,
            'role_name', roles.display_name,
            'access_scope', pending_identities.access_scope,
            'org_role_id', parent_roles.id,
            'org_role_name', parent_roles.display_name
        ) order by pending_identities.created_at desc) as pending
        from pending_identities
        inner join roles on roles.id = pending_identities.role_id
        inner join organizations o on o.id = pending_identities.organization_id
        left join pending_identities parent_identities ON parent_identities.id = pending_identities.parent_pending_identity_id
        left join roles parent_roles ON parent_roles.id = parent_identities.role_id
        where pending_identities.tenant_id = $1
        and pending_identities.access_scope = $2
        and not o.sso_only
    )

    select
        tenants.id as tenant_id,
        coalesce(members_cte.members, '[]') as members,
        coalesce(pending_cte.pending, '[]') as pending
    from tenants
    cross join members_cte
    cross join pending_cte
    where tenants.id = $1""",
            auth.tenant_id,
            access_scope,
        )

        if not result:
            raise HTTPException(
                status_code=404,
                detail="Workspace not found.",
            )
        response = schemas.TenantMembers(**result)
        provider_users = await get_provider_users_slim_bulk_in_txn(
            db, [cast(uuid.UUID, member.ls_user_id) for member in response.members]
        )
        provider_users_by_id = defaultdict(list)
        for provider_user in provider_users:
            provider_users_by_id[provider_user.ls_user_id].append(provider_user)
        for member in response.members:
            member.linked_login_methods = provider_users_by_id[member.ls_user_id]
    return response


def _validate_seats_and_rbac(
    auth: AuthInfo | OrgAuthInfo,
    payload: Union[
        schemas.PendingIdentityCreateInternal,
        schemas.IdentityCreateInternal,
        schemas.OrgIdentityPatchInternal,
    ],
    org_level_invite: bool = False,
) -> None:
    resolved_config = (
        auth.tenant_config if isinstance(auth, AuthInfo) else auth.org_config
    )
    if not resolved_config.can_add_seats:
        raise HTTPException(
            status_code=400,
            detail="This tenant is unable to add users. Please ensure your plan includes seats.",
        )

    if auth.organization_is_personal:
        raise HTTPException(
            status_code=400,
            detail="Cannot add users to a personal organization.",
        )

    if org_level_invite:
        # This is an organization-level invite, so check against the organization's max_workspaces config
        if (
            payload.read_only or payload.role_id
        ) and resolved_config.max_workspaces == 1:
            raise HTTPException(
                status_code=400,
                detail="This organization is unable to set organization roles for users. Please ensure your plan includes multiple workspaces.",
            )

    else:
        # This is a workspace-level invite, so prevent RBAC usage at the workspace level if the tenant is not allowed to use RBAC
        if (
            (payload.read_only or payload.role_id)
            and not resolved_config.can_use_rbac
            and isinstance(auth, AuthInfo)
        ):
            raise HTTPException(
                status_code=400,
                detail="This tenant does not support role based access control (RBAC). Please ensure your plan includes RBAC.",
            )

    # Only prevent Org User vs Org Admin selection at the organization level if the organization cannot create more than 1 workspace.
    if (
        payload.read_only
        and isinstance(auth, OrgAuthInfo)
        and resolved_config.max_workspaces == 1
    ):
        raise HTTPException(
            status_code=400,
            detail="This organization is unable to add read-only users. Please ensure your plan includes multiple workspaces.",
        )
    if (
        payload.role_id
        and isinstance(auth, OrgAuthInfo)
        and resolved_config.max_workspaces == 1
    ):
        raise HTTPException(
            status_code=400,
            detail="This organization is unable to add roles to users. Please ensure your plan includes multiple workspaces.",
        )

    if payload.role_id and payload.read_only:
        raise HTTPException(
            status_code=400,
            detail="Cannot set both role_id and read_only.",
        )

    if isinstance(payload, schemas.PendingIdentityCreateInternal):
        # TODO: check if frontend sends workspace_ids in this case or ever hits this case
        if payload.workspace_ids and not resolved_config.max_workspaces > 1:
            raise HTTPException(
                status_code=400,
                detail="This organization is unable to use multiple workspaces. Please ensure your plan includes multiple workspaces.",
            )
        if payload.workspace_role_id and not resolved_config.can_use_rbac:
            raise HTTPException(
                status_code=400,
                detail="This organization is unable to add roles to users. Please ensure your plan includes RBAC.",
            )


def _validate_seats_and_rbac_batch(
    auth: OrgAuthInfo,
    payloads: List[schemas.PendingIdentityCreateInternal]
    | List[schemas.BasicAuthMemberCreate],
) -> None:
    if not auth.org_config.can_add_seats:
        raise HTTPException(
            status_code=400,
            detail="This organization is unable to add users. Please ensure your plan includes seats.",
        )

    if auth.organization_is_personal:
        raise HTTPException(
            status_code=400,
            detail="Cannot add users to a personal organization.",
        )

    for payload in payloads:
        if (
            payload.read_only or payload.role_id
        ) and auth.org_config.max_workspaces == 1:
            raise HTTPException(
                status_code=400,
                detail="This organization is unable to set organization roles for users. Please ensure your plan includes multiple workspaces.",
            )

        if payload.role_id and payload.read_only:
            raise HTTPException(
                status_code=400,
                detail="Cannot set both role_id and read_only.",
            )

        if (
            payload.workspace_ids
            and len(payload.workspace_ids) > 1
            and auth.org_config.max_workspaces == 1
        ):
            raise HTTPException(
                status_code=400,
                detail="This organization is unable to use multiple workspaces. Please ensure your plan includes multiple workspaces.",
            )
        if payload.workspace_role_id and not auth.org_config.can_use_rbac:
            raise HTTPException(
                status_code=400,
                detail="This organization is unable to add roles to users. Please ensure your plan includes RBAC.",
            )


async def _get_default_org_role(
    db: asyncpg.Connection,
    config: OrganizationConfig,
    payload: schemas.PendingIdentityCreateInternal,
) -> tuple[uuid.UUID, bool]:
    """Get the default role for an organization based on plan, ability to add workspaces, and inserted workspace role."""
    roles = await db.fetch(
        f"""
        SELECT
            (SELECT id FROM roles WHERE name = '{OrganizationRoles.ADMIN.value}') AS org_admin,
            (SELECT id FROM roles WHERE name = '{OrganizationRoles.USER.value}') AS org_user
        """,
    )
    org_admin_id = roles[0]["org_admin"]
    org_user_id = roles[0]["org_user"]
    org_role_to_insert = org_user_id
    # If single workspace, all users are org admins
    if config.max_workspaces == 1:
        org_role_to_insert = org_admin_id
    else:
        # In all other cases, we insert org user
        # (1) RBAC enabled: don't infer org role from workspace role (they can edit org role afterwards)
        # (2) RBAC disabled: always workspace admin, don't infer org role from workspace role
        logger.info(
            f"Defaulting to org user for plan with {config.can_use_rbac=}, {payload.role_id=}, {payload.read_only=},"
            f" and {config.max_workspaces=}"
        )

    return (org_role_to_insert, org_role_to_insert == org_user_id)


# TODO: remove after we deprecate read_only field
async def _temp_get_identites_payload(
    db: asyncpg.Connection,
    auth: AuthInfo | OrgAuthInfo,
    payload: schemas.PendingIdentityCreateInternal
    | schemas.IdentityCreateInternal
    | schemas.IdentityPatchInternal
    | schemas.BasicAuthMemberCreate,
    org_auth_workspace_scope: bool = False,
) -> tuple[uuid.UUID, bool]:
    if payload.role_id and payload.read_only:
        raise HTTPException(
            status_code=400,
            detail="Cannot set both role_id and read_only.",
        )

    access_scope = payload.access_scope
    if access_scope == schemas.AccessScope.organization:
        auth = cast(OrgAuthInfo, auth)
        if not auth.organization_id:
            raise HTTPException(
                status_code=400,
                detail="Organization must be set for organization scoped role.",
            )
    if access_scope == schemas.AccessScope.workspace and not org_auth_workspace_scope:
        auth = cast(AuthInfo, auth)
        if not auth.tenant_id:
            raise HTTPException(
                status_code=400,
                detail="Tenant must be set for workspace scoped role.",
            )

    if payload.role_id:
        role_name = await db.fetchval(
            """
            select name
            from roles
            where (organization_id = $1 or organization_id is null)
            and roles.id = $2
            and access_scope = $3
            """,
            auth.organization_id,
            payload.role_id,
            payload.access_scope,
        )

        if role_name is None:
            raise HTTPException(status_code=404, detail="Role not found.")

        role_name_to_compare = (
            OrganizationRoles.USER.value
            if access_scope == schemas.AccessScope.organization
            else "WORKSPACE_VIEWER"
        )
        return (payload.role_id, role_name == role_name_to_compare)

    # role_id is not set and workspace scoped, so we need to determine it based on the readonly field
    elif not payload.role_id and access_scope == schemas.AccessScope.workspace:
        role_name = (
            OrganizationRoles.USER.value
            if payload.read_only and access_scope == schemas.AccessScope.organization
            else OrganizationRoles.ADMIN.value
            if access_scope == schemas.AccessScope.organization
            else "WORKSPACE_VIEWER"
            if payload.read_only
            else "WORKSPACE_ADMIN"
        )
        read_only = payload.read_only or False
        role_id = await db.fetchval(
            """SELECT id FROM roles WHERE name = $1""",
            role_name,
        )

        return (role_id, read_only)

    # role_id is not set and organization scoped, so we need to determine it based on the default org role
    return await _get_default_org_role(db, auth.org_config, payload)


async def _create_pending_identity_for_tenant_in_txn(
    db: asyncpg.Connection,
    auth: AuthInfo | OrgAuthInfo,
    tenant_id: uuid.UUID,
    payload: schemas.PendingIdentityCreateInternal,
    send_invite_email: bool = True,
    org_auth_workspace_scope: bool = False,
) -> schemas.PendingIdentity:
    (role_id, read_only) = await _temp_get_identites_payload(
        db, auth, payload, org_auth_workspace_scope=org_auth_workspace_scope
    )

    result = await db.fetchrow(
        """
insert into pending_identities (tenant_id, email, read_only, organization_id, role_id, access_scope, parent_pending_identity_id)
values ($1, lower($2), $3, $4, $5, $6, (select id from pending_identities where email = lower($2) and organization_id = $4 and access_scope = 'organization'))
on conflict (tenant_id, email)
do update set tenant_id = excluded.tenant_id
returning *""",
        tenant_id,
        payload.email,
        read_only,
        auth.organization_id,
        role_id,
        schemas.AccessScope.workspace,
    )

    identity = schemas.PendingIdentity(**result)
    if send_invite_email:
        display_name = await db.fetchval(
            """
            select tenants.display_name
            from tenants
            where tenants.id = $1
            """,
            tenant_id,
        )
        await _send_invite_email_if_enabled(display_name, identity.email, tenant_id)

    return identity


async def _create_identity_for_workspace_in_txn(
    db: asyncpg.Connection,
    auth: AuthInfo | OrgAuthInfo,
    tenant_id: uuid.UUID,
    payload: schemas.IdentityCreateInternal,
    org_auth_workspace_scope: bool = False,
) -> schemas.PendingIdentity:
    (role_id, _) = await _temp_get_identites_payload(
        db, auth, payload, org_auth_workspace_scope=org_auth_workspace_scope
    )

    result = await db.fetchrow(
        """
        insert into identities (tenant_id, user_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
        values (
            $1,
            $2,
            $3,
            $4,
            $5,
            (select id from identities where ls_user_id = $6 and organization_id = $3 and access_scope = 'organization'),
            $6
        )
        on conflict (tenant_id, ls_user_id) where access_scope = 'workspace'
        do update set tenant_id = excluded.tenant_id
        returning *
        """,
        tenant_id,
        payload.user_id,
        auth.organization_id,
        role_id,
        schemas.AccessScope.workspace,
        payload.ls_user_id,
    )

    if not result:
        raise HTTPException(
            status_code=400,
            detail="Failed to create identity.",
        )

    return schemas.Identity(**result)


async def _create_pending_identity_for_org_in_txn(
    db: asyncpg.Connection,
    organization_id: uuid.UUID,
    max_identities: int,
    email: str,
    read_only: bool,
    role_id: uuid.UUID,
):
    seat_counts = await get_seat_count_for_org(organization_id, db)  # type: ignore[arg-type]
    total_seats = sum(s.count + s.count_pending for s in seat_counts)
    if total_seats >= max_identities:
        raise HTTPException(
            status_code=400,
            detail="This organization has reached its maximum number of members.",
        )

    insert_query = """
        insert into pending_identities (organization_id, email, read_only, role_id, access_scope)
        select
            $1,
            lower($2),
            $3,
            $4,
            'organization'
        from organizations
        where organizations.id = $1
        on conflict (organization_id, email) WHERE access_scope = 'organization'
        do update set organization_id = excluded.organization_id
        returning *
    """

    org_pending_identity = await db.fetchrow(
        insert_query,
        organization_id,
        email,
        read_only,
        role_id,
    )

    if org_pending_identity is None:
        logger.info(
            f"User with pending workspace identity {email} already has an org identity."
        )

    return schemas.PendingIdentity(**org_pending_identity)


async def _create_workspace_pending_identities_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
    payload: schemas.PendingIdentityCreateInternal,
) -> None:
    # if workspace_ids are provided, create pending identities for each workspace
    if payload.workspace_ids:
        # verify that the workspaces are part of the organization
        valid_workspaces = await db.fetchval(
            "SELECT COUNT(*) FROM tenants WHERE organization_id = $1 AND id = ANY($2)",
            auth.organization_id,
            payload.workspace_ids,
        )
        if valid_workspaces != len(payload.workspace_ids):
            raise HTTPException(
                status_code=400,
                detail="Cannot invite to workspaces in different organizations.",
            )

        # transform payload to workspace access scope & use the workspace role ID
        payload_workspace = schemas.PendingIdentityCreateInternal(
            email=payload.email,
            read_only=payload.read_only,
            role_id=payload.workspace_role_id,
            access_scope=schemas.AccessScope.workspace,
        )
        for workspace_id in payload.workspace_ids:
            # pass org_auth_workspace_scope=True to avoid checking tenant_id on AuthInfo, which isn't needed
            await _create_pending_identity_for_tenant_in_txn(
                db,
                auth,
                workspace_id,
                payload_workspace,
                send_invite_email=False,
                org_auth_workspace_scope=True,
            )


async def _create_workspace_identity_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
    payload: schemas.BasicAuthMemberCreate | schemas.WorkspaceIdentityCreateInternal,
) -> None:
    # if workspace_ids are provided, create identities for each workspace
    if payload.workspace_ids:
        # verify that the workspaces are part of the organization
        valid_workspaces = await db.fetchval(
            "SELECT COUNT(*) FROM tenants WHERE organization_id = $1 AND id = ANY($2)",
            auth.organization_id,
            payload.workspace_ids,
        )
        if valid_workspaces != len(payload.workspace_ids):
            raise HTTPException(
                status_code=400,
                detail="Cannot invite to workspaces in different organizations.",
            )

        # transform payload to workspace access scope & use the workspace role ID
        payload_workspace = schemas.IdentityCreateInternal(
            user_id=payload.user_id,
            ls_user_id=payload.ls_user_id,
            role_id=payload.workspace_role_id,
            access_scope=schemas.AccessScope.workspace,
        )
        for workspace_id in payload.workspace_ids:
            # pass org_auth_workspace_scope=True to avoid checking tenant_id on AuthInfo, which isn't needed
            await _create_identity_for_workspace_in_txn(
                db,
                auth,
                workspace_id,
                payload_workspace,
                org_auth_workspace_scope=True,
            )


async def _create_pending_identities_for_org_in_txn(
    db: asyncpg.Connection,
    organization_id: uuid.UUID,
    max_identities: int,
    payloads: List[PendingIdentityCreateInTxn],
) -> List[schemas.PendingIdentity]:
    seat_counts = await get_seat_count_for_org(organization_id, db)
    total_seats = sum(s.count + s.count_pending for s in seat_counts)
    new_count = total_seats + len(payloads)
    if new_count > max_identities:
        raise HTTPException(
            status_code=400,
            detail=f"This batch invite would bring the organization over its maximum number of members: {new_count}/{max_identities}.",
        )

    # Noop and return record on conflict
    insert_query = """
        WITH data AS (
            SELECT
                unnest($2::text[]) AS email,
                unnest($3::boolean[]) AS read_only,
                unnest($4::uuid[]) AS role_id
        )
        INSERT INTO pending_identities (organization_id, email, read_only, role_id, access_scope)
        SELECT
            $1,
            lower(email),
            read_only,
            role_id,
            'organization'
        FROM data
        ON CONFLICT (organization_id, email) WHERE access_scope = 'organization'
        DO UPDATE SET organization_id = excluded.organization_id
        RETURNING *;
    """

    results = await db.fetch(
        insert_query,
        organization_id,
        [payload.email for payload in payloads],
        [payload.read_only for payload in payloads],
        [payload.role_id for payload in payloads],
    )

    if not results:
        raise HTTPException(
            status_code=400,
            detail="Failed to create pending identities.",
        )

    return [schemas.PendingIdentity(**result) for result in results]


async def _create_users_for_org_in_txn(
    db: asyncpg.Connection,
    organization_id: uuid.UUID,
    max_identities: int,
    payloads: List[IdentityCreateInTxn],
) -> List[schemas.UserWithPassword]:
    seat_counts = await get_seat_count_for_org(organization_id, db)
    total_seats = sum(s.count + s.count_pending for s in seat_counts)
    new_count = total_seats + len(payloads)
    if new_count > max_identities:
        raise HTTPException(
            status_code=400,
            detail=f"This batch invite would bring the organization over its maximum number of members: {new_count}/{max_identities}.",
        )

    # Noop and return record on conflict
    insert_query = """
        WITH data AS (
            SELECT
                unnest($2::text[]) AS email,
                unnest($3::boolean[]) AS read_only,
                unnest($4::uuid[]) AS role_id
        )
        INSERT INTO pending_identities (organization_id, email, read_only, role_id, access_scope)
        SELECT
            $1,
            lower(email),
            read_only,
            role_id,
            'organization'
        FROM data
        ON CONFLICT (organization_id, email) WHERE access_scope = 'organization'
        DO UPDATE SET organization_id = excluded.organization_id
        RETURNING *;
    """

    results = await db.fetch(
        insert_query,
        organization_id,
        [payload.user_id for payload in payloads],
        [payload.read_only for payload in payloads],
        [payload.role_id for payload in payloads],
    )

    if not results:
        raise HTTPException(
            status_code=400,
            detail="Failed to create pending identities.",
        )

    return [schemas.PendingIdentity(**result) for result in results]


async def create_identities_for_org_in_txn(
    db: asyncpg.Connection,
    organization_id: uuid.UUID,
    max_identities: int,
    payloads: List[IdentityCreateInTxn],
) -> List[schemas.Identity]:
    seat_counts = await get_seat_count_for_org(organization_id, db)
    total_seats = sum(s.count + s.count_pending for s in seat_counts)
    new_count = total_seats + len(payloads)
    if new_count > max_identities:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot bring the organization over its maximum number of members: {new_count}/{max_identities}.",
        )

    # Noop and return record on conflict
    insert_query = """
        WITH data AS (
            SELECT
                unnest($2::uuid[]) AS user_id,
                unnest($3::uuid[]) AS role_id,
                unnest($4::uuid[]) AS ls_user_id
        )
        INSERT INTO identities (organization_id, user_id, role_id, ls_user_id, access_scope)
        SELECT
            $1,
            user_id,
            role_id,
            ls_user_id,
            'organization'
        FROM data
        ON CONFLICT (organization_id, ls_user_id) WHERE access_scope = 'organization'
        DO UPDATE SET organization_id = excluded.organization_id
        RETURNING *;
    """

    results = await db.fetch(
        insert_query,
        organization_id,
        [payload.user_id for payload in payloads],
        [payload.role_id for payload in payloads],
        [payload.ls_user_id for payload in payloads],
    )

    if not results:
        raise HTTPException(
            status_code=400,
            detail="Failed to create identities.",
        )

    return [schemas.Identity(**result) for result in results]


async def _create_basic_auth_users_in_txn_unsafe(
    db: asyncpg.Connection,
    payloads: List[UserCreateInTxn],
) -> List[schemas.UserWithPassword]:
    # Noop and return record on conflict
    insert_query = """
        WITH data AS (
            SELECT
                unnest($1::uuid[]) AS user_id,
                unnest($2::text[]) AS email,
                unnest($3::text[]) AS full_name,
                unnest($4::text[]) AS hashed_password
        ),
        inserted_users AS (
            INSERT INTO users (id, email, full_name, hashed_password)
            SELECT
                user_id,
                lower(email),
                full_name,
                hashed_password
            FROM data
            ON CONFLICT (id)
            DO UPDATE SET
                email = excluded.email,
                full_name = excluded.full_name,
                hashed_password = excluded.hashed_password
            RETURNING *
        ),
        inserted_provider_users AS (
            INSERT INTO provider_users (provider, ls_user_id, email, full_name, hashed_password)
            SELECT
                'email',
                ls_user_id,
                lower(email),
                full_name,
                hashed_password
            FROM inserted_users
            RETURNING *
        )
        SELECT
            iu.*,
            pu.provider
        FROM inserted_users iu
        LEFT JOIN inserted_provider_users pu ON iu.ls_user_id = pu.ls_user_id;
    """

    results = await db.fetch(
        insert_query,
        [payload.user_id for payload in payloads],
        [payload.email for payload in payloads],
        [payload.full_name for payload in payloads],
        [payload.hashed_password for payload in payloads],
    )

    if not results:
        raise HTTPException(
            status_code=400,
            detail="Failed to create users.",
        )

    users_with_passwords = [schemas.UserWithPassword(**result) for result in results]
    passwords_by_user_id = {payload.user_id: payload.password for payload in payloads}
    for user in users_with_passwords:
        user.password = passwords_by_user_id[user.id]

    return users_with_passwords


def _check_invites_payloads(
    payloads: List[schemas.PendingIdentityCreateInternal]
    | List[schemas.BasicAuthMemberCreate],
) -> None:
    if len(payloads) > 500:
        # Due to PostMark batch email limit
        raise HTTPException(
            status_code=400,
            detail="Batch invite limit is 500.",
        )

    if len(set([p.email.lower() for p in payloads])) != len(payloads):
        raise HTTPException(
            status_code=400,
            detail="Duplicate emails are not allowed.",
        )


async def _invite_users(
    auth: OrgAuthInfo,
    payloads: List[schemas.PendingIdentityCreateInternal],
) -> List[schemas.PendingIdentity]:
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(organization_id=auth.organization_id),
        SeatChangeOperation.INVITE,
    ) as db:
        # Check identities and pending for existing users with these emails
        query = """
            SELECT emails.email,
                (SELECT COUNT(*)
                FROM identities
                WHERE ls_user_id = (SELECT ls_user_id FROM users WHERE lower(email) = lower(emails.email))
                AND organization_id = $2
                AND access_scope = $3) AS num_members,
                (SELECT COUNT(*)
                FROM pending_identities
                WHERE lower(email) = lower(emails.email)
                AND organization_id = $2
                AND access_scope = $3) AS num_pending
            FROM unnest($1::text[]) AS emails(email)
        """

        emails = [p.email.lower() for p in payloads]
        existing_identities = await db.fetch(
            query,
            emails,
            auth.organization_id,
            schemas.AccessScope.organization,
        )

        for existing in existing_identities:
            if existing["num_members"] > 0:
                raise HTTPException(
                    status_code=409,
                    detail=f"A user with email {existing['email']} already exists.",
                )
            if existing["num_pending"] > 0:
                raise HTTPException(
                    status_code=409,
                    detail=f"A pending invite with email {existing['email']} already exists.",
                )

        pending_identities = await create_pending_identities_batch_in_txn(
            db,
            auth,
            payloads,
        )

    if config.settings.POSTMARK_SERVER_TOKEN is not None:
        # Send all workspaces if the user is an org admin
        async with database.asyncpg_conn() as db:
            invite_info = await db.fetchrow(
                """
                WITH workspaces AS (
                    SELECT id, display_name
                    FROM tenants
                        WHERE organization_id = $1
                    ORDER BY display_name ASC
                )
                SELECT
                    (SELECT display_name FROM organizations WHERE id = $1) AS organization_name,
                    (SELECT id from roles where name = $2) as org_admin_role_id,
                    ARRAY(SELECT id FROM workspaces) AS workspace_ids,
                    ARRAY(SELECT display_name FROM workspaces) AS workspace_names;
                """,
                auth.organization_id,
                OrganizationRoles.ADMIN.value,
            )
        if not invite_info or not invite_info["organization_name"]:
            # This should never happen, but we check just in case the function above changes
            raise HTTPException(
                status_code=404,
                detail="Organization not found.",
            )
        all_workspaces = [
            {"id": id, "display_name": name}
            for id, name in zip(
                invite_info["workspace_ids"], invite_info["workspace_names"]
            )
        ]
        email_payloads: List[WorkspaceInvitePayload] = []
        for payload in payloads:
            workspace_names = (
                invite_info.get("workspace_names", [])
                if payload.role_id == invite_info["org_admin_role_id"]
                else [
                    ws["display_name"]
                    for ws in all_workspaces
                    if ws["id"] in (payload.workspace_ids or [])
                ]
            )
            url = (
                config.settings.LANGSMITH_URL
                + "/accept-invite?org_id="
                + str(auth.organization_id)
                + "&email="
                + quote(payload.email, safe="")
            )

            email_payloads.append(
                WorkspaceInvitePayload(
                    org=OrgInfo(
                        display_name=invite_info["organization_name"],
                    ),
                    workspaces=workspace_names,
                    email=payload.email,
                    url=url,
                )
            )
        await send_workspaces_invite_email_batch(
            email_payloads,
        )

    return pending_identities


async def _add_basic_auth_users_unsafe(
    auth: OrgAuthInfo,
    payloads: List[schemas.BasicAuthMemberCreate],
) -> List[schemas.UserWithPassword]:
    """Assumes that the payloads have been validated."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(organization_id=auth.organization_id),
        SeatChangeOperation.ADD_TO_ORGANIZATION,
    ) as db:
        # Check for existing users with these emails
        query = """
            SELECT emails.email,
                (SELECT COUNT(*)
                FROM users
                WHERE lower(email) = lower(emails.email)) AS num_members
            FROM unnest($1::text[]) AS emails(email)
        """

        emails = [p.email.lower() for p in payloads]
        existing_users = await db.fetch(
            query,
            emails,
        )

        for existing in existing_users:
            if existing["num_members"] > 0:
                raise HTTPException(
                    status_code=409,
                    detail=f"A user with email {existing['email']} already exists.",
                )

        users = await _create_users_and_identities_batch_in_txn(
            db,
            auth,
            payloads,
        )

        return users


@retry_asyncpg
async def create_pending_identities_batch_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
    payloads: List[schemas.PendingIdentityCreateInternal],
) -> List[schemas.PendingIdentity]:
    """Create multiple pending identities."""
    _validate_seats_and_rbac_batch(auth, payloads)

    # insert organization pending identity
    pending_creates: List[PendingIdentityCreateInTxn] = []
    for payload in payloads:
        (role_id, read_only) = await _temp_get_identites_payload(db, auth, payload)
        pending_creates.append(
            PendingIdentityCreateInTxn(
                email=payload.email, read_only=read_only, role_id=role_id
            )
        )
    identities_to_return = await _create_pending_identities_for_org_in_txn(
        db,
        auth.organization_id,
        auth.org_config.max_identities,
        pending_creates,
    )

    for payload in payloads:
        await _create_workspace_pending_identities_in_txn(db, auth, payload)

    # Add the org admins as pending to all workspaces in this org
    for pending_identity in identities_to_return:
        await ensure_specific_org_admin_pending_identities_in_txn(
            db, pending_identity.id
        )

    return identities_to_return


@retry_asyncpg
async def create_basic_auth_users_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
    payloads: List[schemas.BasicAuthMemberCreate],
) -> List[schemas.UserWithPassword]:
    """Create multiple users and corresponding active identities."""
    _validate_seats_and_rbac_batch(auth, payloads)

    # insert users
    user_creates: List[UserCreateInTxn] = []
    user_ids_by_email = {}
    for payload in payloads:
        user_id = payload.user_id or uuid.uuid4()
        user_ids_by_email[payload.email] = user_id
        password = payload.password or generate_password()
        hashed_password = hash_password(password)
        user_creates.append(
            UserCreateInTxn(
                user_id=user_id,
                email=payload.email,
                password=password,
                hashed_password=hashed_password,
                full_name=payload.full_name,
            )
        )
    return await _create_basic_auth_users_in_txn_unsafe(db, user_creates)


async def create_basic_auth_user_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
    payload: schemas.BasicAuthMemberCreate,
) -> schemas.UserWithPassword:
    return (await create_basic_auth_users_in_txn(db, auth, [payload]))[0]


@retry_asyncpg
async def create_identities_for_basic_auth_users_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
    payloads: List[schemas.BasicAuthMemberCreate],
    users: List[schemas.UserWithPassword],
) -> List[schemas.UserWithPassword]:
    users_by_email = {user.email: user for user in users}

    # insert organization identities
    identity_creates: List[IdentityCreateInTxn] = []
    for payload in payloads:
        (role_id, _) = await _temp_get_identites_payload(
            db,
            auth,
            schemas.IdentityCreateInternal(
                user_id=users_by_email[payload.email].id,
                ls_user_id=users_by_email[payload.email].ls_user_id,
                role_id=payload.role_id,
                access_scope=schemas.AccessScope.organization,
            ),
        )
        identity_creates.append(
            IdentityCreateInTxn(
                user_id=users_by_email[payload.email].id,
                ls_user_id=users_by_email[payload.email].ls_user_id,
                read_only=None,
                role_id=role_id,
            )
        )
    org_identities = await create_identities_for_org_in_txn(
        db,
        auth.organization_id,
        auth.org_config.max_identities,
        identity_creates,
    )

    for payload in payloads:
        payload_with_user_ids = payload.model_copy(
            update={
                "user_id": users_by_email[payload.email].id,
                "ls_user_id": users_by_email[payload.email].ls_user_id,
            },
        )
        await _create_workspace_identity_in_txn(db, auth, payload_with_user_ids)

    # Add any org admins to all workspaces in this org
    for identity in org_identities:
        await ensure_org_admin_identities_in_txn(db, identity.id)

    return users


async def create_identity_for_basic_auth_user_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
    payload: schemas.BasicAuthMemberCreate,
    user: schemas.UserWithPassword,
) -> schemas.UserWithPassword:
    return (
        await create_identities_for_basic_auth_users_in_txn(db, auth, [payload], [user])
    )[0]


@retry_asyncpg
async def _create_users_and_identities_batch_in_txn(
    db: asyncpg.Connection,
    auth: OrgAuthInfo,
    payloads: List[schemas.BasicAuthMemberCreate],
) -> List[schemas.UserWithPassword]:
    """Create multiple users and corresponding active identities."""
    users_to_return = await create_basic_auth_users_in_txn(db, auth, payloads)

    return await create_identities_for_basic_auth_users_in_txn(
        db,
        auth,
        payloads,
        users_to_return,
    )


@retry_asyncpg
async def invite_user_to_org(
    auth: OrgAuthInfo, payload: schemas.PendingIdentityCreateInternal
) -> schemas.PendingIdentity:
    if payload.access_scope != schemas.AccessScope.organization:
        raise HTTPException(
            status_code=400,
            detail="Access scope must be 'organization' for inviting to an org.",
        )

    return (await invite_users_to_org(auth, [payload]))[0]


@retry_asyncpg
async def invite_users_to_org_and_workspace(
    org_auth: OrgAuthInfo,
    auth: AuthInfo,
    payloads: List[schemas.PendingIdentityCreateInternal],
) -> List[schemas.PendingIdentity]:
    """Invite users to the authed workspace and the organization as an Organization User."""
    if not config.settings.FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED:
        raise HTTPException(
            status_code=400,
            detail="Inviting users to an organization at the workspace level is not enabled.",
        )

    if org_auth.sso_only:
        raise HTTPException(
            status_code=403,
            detail="Cannot invite users to an SSO only organization.",
        )

    _check_invites_payloads(payloads)

    if any(p.workspace_ids for p in payloads):
        raise HTTPException(
            status_code=400,
            detail="Cannot invite to specific workspaces at the workspace level.",
        )

    if any(p.role_id for p in payloads):
        raise HTTPException(
            status_code=400,
            detail="Cannot set organization role at the workspace level.",
        )

    async with database.asyncpg_conn() as db:
        org_user_role_id = await db.fetchval(
            "SELECT id FROM roles WHERE name = $1", OrganizationRoles.USER.value
        )

    for payload in payloads:
        payload.workspace_ids = [auth.tenant_id]
        payload.role_id = org_user_role_id

    return await _invite_users(org_auth, payloads)


@retry_asyncpg
async def invite_users_to_org(
    auth: OrgAuthInfo, payloads: List[schemas.PendingIdentityCreateInternal]
) -> List[schemas.PendingIdentity]:
    """Invited users to the organization and specified workspaces."""
    if settings.BASIC_AUTH_ENABLED:
        raise NotImplementedError("Not implemented for basic auth.")
    if auth.sso_only:
        raise HTTPException(
            status_code=403,
            detail="Cannot invite users to an SSO only organization.",
        )

    _check_invites_payloads(payloads)
    return await _invite_users(auth, payloads)


@retry_asyncpg
async def create_workspace_identity(
    auth: AuthInfo,
    payload: schemas.IdentityCreateInternal,
) -> schemas.Identity:
    """Add workspace identity directly. Only succeeds if the user has an existing org identity."""
    _validate_seats_and_rbac(auth, payload)
    if (
        auth.tenant_id is None
        or not payload.access_scope == schemas.AccessScope.workspace
    ):
        raise HTTPException(
            status_code=400,
            detail="Workspace must be set to invite to a workspace.",
        )

    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(tenant_id=auth.tenant_id),
        SeatChangeOperation.ADD_TO_TENANT,
    ) as db:
        (role_id, read_only) = await _temp_get_identites_payload(db, auth, payload)
        organization_id = await db.fetchval(
            "select organization_id from tenants where id = $1", auth.tenant_id
        )
        if not organization_id:
            raise HTTPException(
                status_code=404,
                detail="Tenant's organization not found.",
            )
        # using org_identity_id is preferred because user_id matches a particular login method that may not be present
        if payload.org_identity_id:
            org_identity = await db.fetchrow(
                "select * from identities where id = $1 and organization_id = $2 and access_scope = 'organization'",
                payload.org_identity_id,
                organization_id,
            )
        elif payload.user_id:
            linked_login_methods = await get_linked_login_methods_in_txn(
                db, [payload.user_id]
            )
            if linked_login_methods:
                org_identity = await db.fetchrow(
                    "select * from identities where ls_user_id = $1 and organization_id = $2 and access_scope = 'organization'",
                    linked_login_methods[0].ls_user_id,
                    organization_id,
                )
            else:
                # Fallback to user_id if no linked login methods
                org_identity = await db.fetchrow(
                    "select * from identities where user_id = $1 and organization_id = $2 and access_scope = 'organization'",
                    payload.user_id,
                    organization_id,
                )
        else:
            raise HTTPException(
                status_code=400,
                detail="Must provide either org_identity_id or user_id.",
            )
        if not org_identity:
            raise HTTPException(
                status_code=404,
                detail="User does not have an organization identity.",
            )

        result = await db.fetchrow(
            """
            insert into identities (tenant_id, read_only, user_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
                select $1, $2, $3, $4, $5, $6, $7, $8
                from tenants where tenants.id = $1
                on conflict (tenant_id, ls_user_id) where access_scope = 'workspace'
                do update set tenant_id = excluded.tenant_id
                returning *
            """,
            auth.tenant_id,
            read_only,
            org_identity["user_id"],
            organization_id,
            role_id,
            payload.access_scope,
            org_identity["id"],
            org_identity["ls_user_id"],
        )
        if result is None:
            raise HTTPException(
                status_code=500,
                detail="Failed to create workspace identity.",
            )
    return schemas.Identity(**result)


@retry_asyncpg
async def delete_pending_identity_for_current_user(
    auth: TenantlessAuthInfo | OrgAuthInfo, tenant_id: uuid.UUID
) -> None:
    """Delete a pending identity."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(tenant_id=tenant_id),
        SeatChangeOperation.UNINVITE,
    ) as db:
        result = await db.fetchrow(
            """
    delete from pending_identities
    where tenant_id = $1 and email = lower($2)
    returning *""",
            tenant_id,
            auth.user_email,
        )

    if result is None:
        raise HTTPException(
            status_code=404,
            detail="Pending identity not found.",
        )


@retry_asyncpg
async def delete_identity_for_current_org(
    auth: OrgAuthInfo, identity_id: uuid.UUID
) -> None:
    """Delete an organization identity and associated resources, including the users record if in basic auth."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(organization_id=auth.organization_id),
        SeatChangeOperation.REMOVE_FROM_ORGANIZATION,
    ) as db:
        # check if user exists and is not the caller
        query = """
        SELECT u.* FROM identities i
            INNER JOIN users u ON i.ls_user_id = u.ls_user_id
            WHERE i.id = $1 AND i.access_scope = 'organization'
            AND i.id != (
                SELECT id FROM identities
                WHERE ls_user_id = $2
                    AND access_scope = 'organization'
                    AND organization_id = $3
            )
        """
        user_record = await db.fetchrow(
            query,
            identity_id,
            auth.ls_user_id,
            auth.organization_id,
        )
        if not user_record:
            logger.error(
                f"Identity {identity_id} not found or is the caller with LS user ID {auth.ls_user_id}"
            )
            raise HTTPException(
                status_code=404,
                detail="User not found.",
            )
        await _fail_if_last_org_admin_remaining_in_txn(auth, db, identity_id)
        email = user_record["email"]

        query = """
            -- delete this user's pending identities
            WITH deleted_user_pending_identities AS (
                DELETE FROM pending_identities
                WHERE email = lower($2)
                    AND organization_id = $3
                RETURNING *
            ),
            -- delete the specified organization identity
            deleted_org_identity AS (
                DELETE FROM identities
                WHERE id = $1
                    AND organization_id = $3
                    AND access_scope = 'organization'
                RETURNING *
            ),
            -- delete any workspace identities the user has
            deleted_workspace_identities AS (
                DELETE FROM identities
                WHERE user_id = (SELECT id from users where email = lower($2))
                    AND organization_id = $3
                    AND access_scope = 'workspace'
                    AND tenant_id in
                    (SELECT id FROM tenants WHERE organization_id = $3)
                RETURNING id
            ),
            deleted_api_keys AS (
                DELETE FROM api_keys
                WHERE identity_id in (SELECT id FROM deleted_workspace_identities)
                RETURNING *
            )
            SELECT * FROM deleted_org_identity
        """

        deleted_identities = await db.fetch(
            query,
            identity_id,
            email,
            auth.organization_id,
        )

        if deleted_identities is None:
            raise HTTPException(
                status_code=404,
                detail="User not found.",
            )

        # If SAML SSO is enabled, remove the SSO login method for this user if one exists
        if auth.org_config.can_use_saml_sso:
            deleted_provider_user = await db.fetchrow(
                """
                DELETE FROM provider_users
                WHERE ls_user_id = $1
                AND provider = $2
                AND saml_provider_id = (
                    SELECT provider_id FROM saml_providers WHERE organization_id = $3
                )
                RETURNING *
                """,
                deleted_identities[0]["ls_user_id"],
                schemas.AuthProvider.supabase_sso.value,
                auth.organization_id,
            )
            if deleted_provider_user:
                logger.info(f"Deleted SAML SSO login method: {deleted_provider_user=}")

        if settings.BASIC_AUTH_ENABLED:
            await db.fetch(
                """
                DELETE FROM users
                WHERE id = $1
                """,
                user_record["id"],
            )

    logger.info(f"Removed user {email} ({identity_id=}) from {auth.organization_id=}")


@retry_asyncpg
async def delete_pending_org_identity_for_current_user(
    auth: TenantlessAuthInfo, organization_id: uuid.UUID
) -> None:
    """Delete a pending org identity."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(organization_id=organization_id),
        SeatChangeOperation.UNINVITE,
    ) as db:
        result = await db.fetchrow(
            """
            with deleted_org_pending_identity as (
                delete from pending_identities
                where organization_id = $1
                    and email = lower($2)
                    and access_scope = 'organization'
                returning *
            ),
            deleted_workspace_pending_identities as (
                delete from pending_identities
                where email = (select email from deleted_org_pending_identity)
                    and organization_id = $1
                    and access_scope = 'workspace'
            )
            select * from deleted_org_pending_identity
            """,
            organization_id,
            auth.user_email,
        )

    if result is None:
        raise HTTPException(
            status_code=404,
            detail="Pending identity not found.",
        )


@retry_asyncpg
async def delete_pending_identity_for_current_tenant(
    auth: AuthInfo, id: uuid.UUID
) -> None:
    """Delete a pending identity."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(tenant_id=auth.tenant_id),
        SeatChangeOperation.UNINVITE,
    ) as db:
        if auth.tenant_config.organization_config.max_workspaces == 1:
            raise HTTPException(
                status_code=400,
                detail="Unable to delete pending users at the workspace level in a single workspace organization. Please delete at the organization level.",
            )
        await _fail_if_identity_is_org_admin_in_txn(db, id, True)
        deleted_workspace_pending_identity = await db.fetchrow(
            """
    delete from pending_identities
    where
        id = $1
        and tenant_id = $2
        and access_scope = 'workspace'
    returning *""",
            id,
            auth.tenant_id,
        )

        if deleted_workspace_pending_identity is None:
            raise HTTPException(
                status_code=404,
                detail="Pending identity not found.",
            )


@retry_asyncpg
async def delete_pending_identity_for_current_org(
    auth: OrgAuthInfo, identity_id: uuid.UUID
) -> None:
    """Delete a pending organization identity, which also deletes pending workspace identities."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(organization_id=auth.organization_id),
        SeatChangeOperation.UNINVITE,
    ) as db:
        result = await db.fetchrow(
            """
            with deleted_org_pending_identity as (
                delete from pending_identities
                where id = $1
                    and organization_id = $2
                    and access_scope = 'organization'
                returning *
            ),
            deleted_workspace_pending_identities as (
                delete from pending_identities
                where email = (select email from deleted_org_pending_identity)
                    and organization_id = $2
                    and access_scope = 'workspace'
            )
            select * from deleted_org_pending_identity
            """,
            identity_id,
            auth.organization_id,
        )
    if result is None:
        raise HTTPException(
            status_code=404,
            detail="Pending identity not found.",
        )


@retry_asyncpg
async def delete_identity_for_current_tenant(auth: AuthInfo, id: uuid.UUID) -> None:
    """Delete an identity."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(tenant_id=auth.tenant_id),
        SeatChangeOperation.REMOVE_FROM_TENANT,
    ) as db:
        if auth.tenant_config.organization_config.max_workspaces == 1:
            raise HTTPException(
                status_code=400,
                detail="Unable to delete users at the workspace level in a single workspace organization. Please delete at the organization level.",
            )
        await _fail_if_identity_is_org_admin_in_txn(db, id, is_pending=False)
        workspace_identity_deletion_result = await db.fetchrow(
            """
            delete from identities
            where
                id = $1
                and tenant_id = $2
                and user_id != $3
                and access_scope = 'workspace'
            returning *
            """,
            id,
            auth.tenant_id,
            auth.user_id,
        )

        if workspace_identity_deletion_result is None:
            raise HTTPException(
                status_code=404,
                detail="Identity not found.",
            )

        org_config = auth.tenant_config.organization_config
        if org_config.max_workspaces == 1:
            deleted_identity_user_id = workspace_identity_deletion_result["user_id"]
            deleted_identity_organization_id = workspace_identity_deletion_result[
                "organization_id"
            ]
            if deleted_identity_organization_id != auth.organization_id:
                raise ValueError("Organization ID mismatch")

            await db.execute(
                """
        delete from identities
        where
            organization_id = $1
            and user_id = $2
            and user_id != $3
            and access_scope = 'organization'
                """,
                auth.organization_id,
                deleted_identity_user_id,
                auth.user_id,
            )


@retry_asyncpg
async def patch_identity_for_current_tenant(
    auth: AuthInfo, id: uuid.UUID, patch: schemas.IdentityPatchInternal
) -> None:
    """Patch an identity."""
    if patch.role_id and not auth.tenant_config.can_use_rbac:
        raise HTTPException(
            status_code=400,
            detail="This workspace is unable to add roles to users. Please ensure your plan includes RBAC.",
        )

    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(tenant_id=auth.tenant_id),
        SeatChangeOperation.UPDATE_IDENTITY,
    ) as db:
        (role_id, read_only) = await _temp_get_identites_payload(db, auth, patch)
        # use shared where clause to ensure no drift & same identity
        where_clause = "where id = $1 and tenant_id = $2 and user_id != $3 and access_scope = 'workspace'"
        previous_identity = await db.fetchrow(
            f"""
            select *
            from identities
            {where_clause}
            """,
            id,
            auth.tenant_id,
            auth.user_id,
        )
        result = await db.fetchrow(
            f"""
            update identities
            set read_only = $4, role_id = $5
            {where_clause}
            returning id
            """,
            id,
            auth.tenant_id,
            auth.user_id,
            read_only,
            role_id,
        )
        if result is None:
            raise HTTPException(
                status_code=404,
                detail="Identity not found.",
            )

        # Prevent updating the workspace role of an organization admin at the workspace level
        if previous_identity["role_id"] != role_id:
            parent_identity_role_check = await db.fetchval(
                "SELECT role_id FROM identities WHERE id = $1 and access_scope = 'organization' and role_id = (SELECT id FROM roles WHERE name = 'ORGANIZATION_ADMIN')",
                previous_identity["parent_identity_id"],
            )
            if parent_identity_role_check:
                raise HTTPException(
                    status_code=400,
                    detail="Cannot change the workspace role of an organization admin. Please update the organization role to Organization User first.",
                )


@retry_asyncpg
async def patch_organization_user(
    auth: OrgAuthInfo, identity_id: uuid.UUID, payload: schemas.OrgIdentityPatchInternal
) -> None:
    """Update a user's role in the organization."""
    _validate_seats_and_rbac(auth, payload)
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(organization_id=auth.organization_id),
        SeatChangeOperation.UPDATE_IDENTITY,
    ) as db:
        if settings.BASIC_AUTH_ENABLED:
            # Update either the password or full name for basic auth, if provided
            hashed_password = (
                hash_password(payload.password) if payload.password else None
            )

            query = """
            WITH updated_user AS (
                UPDATE users
                    SET hashed_password = coalesce($1, hashed_password),
                        full_name = coalesce($2, full_name),
                        updated_at = now()
                    WHERE id = (SELECT user_id from identities where id = $3)
                    RETURNING *
            ),
            updated_provider_users AS (
                UPDATE provider_users
                    SET hashed_password = coalesce($1, hashed_password),
                        full_name = coalesce($2, full_name),
                        updated_at = now()
                    WHERE ls_user_id = (SELECT ls_user_id from updated_user)
            )
            SELECT * FROM updated_user
            """

            user_record = await db.fetchrow(
                query,
                hashed_password,
                payload.full_name,
                identity_id,
            )

            assert auth.exists(user_record)
        if payload.role_id or payload.read_only is not None:
            await _fail_if_last_org_admin_remaining_in_txn(auth, db, identity_id)

            # Update the user's organization role, only allows setting to an organization role.
            result = await db.fetchrow(
                """
                update identities
                set read_only = CASE WHEN $4 = (select id from roles where name = 'ORGANIZATION_USER') THEN true ELSE false END,
                role_id = $4
                where id = $1
                    and organization_id = $2
                    and user_id != $3
                    and $4 in (select id from roles where access_scope = 'organization' and (organization_id = $2 or organization_id is null))
                    and access_scope = 'organization'
                returning id
                """,
                identity_id,
                auth.organization_id,
                auth.user_id,
                payload.role_id,
            )
            if result is None:
                raise HTTPException(
                    status_code=404,
                    detail="Identity or role not found.",
                )
            # If updating the user's role to Org Admin, add them to all workspaces
            await ensure_org_admin_identities_in_txn(db, identity_id)


@retry_asyncpg
async def patch_basic_auth_current_user(
    auth: OrgAuthInfo, payload: schemas.BasicAuthUserPatch
) -> None:
    """Update a user's name and/or password."""
    if not settings.BASIC_AUTH_ENABLED:
        raise HTTPException(
            status_code=400,
            detail="Basic auth is not enabled.",
        )
    hashed_password = hash_password(payload.password) if payload.password else None

    query = """
    WITH updated_user AS (
        UPDATE users
            SET hashed_password = coalesce($1, hashed_password),
                full_name = coalesce($2, full_name),
                updated_at = now()
            WHERE id = $3
            RETURNING *
    ),
    updated_provider_users AS (
        UPDATE provider_users
            SET hashed_password = coalesce($1, hashed_password),
                full_name = coalesce($2, full_name),
                updated_at = now()
            WHERE ls_user_id = (SELECT ls_user_id from updated_user)
    )
    SELECT * FROM updated_user
    """

    async with database.asyncpg_conn() as db:
        user_record = await db.fetchrow(
            query,
            hashed_password,
            payload.full_name,
            auth.user_id,
        )

    assert auth.exists(user_record)


async def claim_pending_organization_identity_in_txn(
    db: asyncpg.Connection,
    organization_id: uuid.UUID,
    email: str | None,
    user_id: uuid.UUID | None,
    ls_user_id: uuid.UUID | None,
) -> schemas.Identity | None:
    if user_id is None or ls_user_id is None:
        raise HTTPException(
            status_code=400,
            detail="User ID is required to claim a pending organization identity.",
        )
    try:
        pending_org_identity = await db.fetchrow(
            """
            SELECT read_only, organization_id, role_id, access_scope
            from pending_identities
            where organization_id = $1
            and email = lower($2)
            and access_scope = 'organization'
            """,
            organization_id,
            email,
        )
        if not pending_org_identity:
            logger.error(
                f"Pending organization identity not found for {email=} {organization_id}."
            )
            raise HTTPException(
                status_code=404,
                detail="Pending organization identity not found.",
            )
        result = await db.fetchrow(
            """
            insert into identities (read_only, user_id, organization_id, role_id, access_scope, ls_user_id)
            VALUES ($1, $2, $3, $4, $5, $6)
            on conflict (organization_id, ls_user_id) where access_scope = 'organization'
            do update set organization_id = excluded.organization_id
            returning *
            """,
            pending_org_identity["read_only"],
            user_id,
            pending_org_identity["organization_id"],
            pending_org_identity["role_id"],
            pending_org_identity["access_scope"],
            ls_user_id,
        )
    except asyncpg.ForeignKeyViolationError:
        logger.error(
            f"Unexpected identity integrity violation for {user_id=} ({ls_user_id=}) {organization_id=}"
        )
        raise HTTPException(
            status_code=400,
            detail="User is not a member of the organization.",
        )
    return schemas.Identity(**result) if result else None


@retry_asyncpg
async def claim_pending_org_identity(
    auth: TenantlessAuthInfo, organization_id: uuid.UUID
) -> schemas.Identity:
    """Claim a pending organization identity for the current user."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(organization_id=organization_id),
        SeatChangeOperation.CLAIM_INTO_ORGANIZATION,
    ) as db:
        result = await claim_pending_organization_identity_in_txn(
            db, organization_id, auth.user_email, auth.user_id, auth.ls_user_id
        )
        if result:
            parent_identity_id = result.id
        else:
            org_identity = await db.fetchrow(
                """
                SELECT * from identities
                    WHERE organization_id = $1
                    AND user_id = $2
                    AND access_scope = 'organization'
                """,
                organization_id,
                auth.user_id,
            )
            if not org_identity:
                raise HTTPException(
                    status_code=404,
                    detail="Organization identity not found.",
                )
            parent_identity_id = org_identity["id"]

        # Additionally claim any pending identities for workspaces in the organization
        _accepted_workspaces = await db.fetch(
            """
            WITH deleted AS (
                DELETE FROM pending_identities
                WHERE organization_id = $1
                AND email = lower($2)
                AND access_scope = 'workspace'
                RETURNING *
            )

            INSERT INTO identities (read_only, user_id, organization_id, tenant_id, role_id, access_scope, parent_identity_id, ls_user_id)
            SELECT read_only, $3, organization_id, tenant_id, role_id, access_scope, $4, $5
            FROM deleted
            ON CONFLICT (tenant_id, ls_user_id) WHERE access_scope = 'workspace'
            DO UPDATE SET tenant_id = EXCLUDED.tenant_id
            RETURNING *
            """,
            organization_id,
            auth.user_email,
            auth.user_id,
            parent_identity_id,
            auth.ls_user_id,
        )
        if _accepted_workspaces:
            logger.info(
                f"Claimed pending workspace identities in {organization_id=}: {_accepted_workspaces}"
            )

        # DELETE the pending org identity which will also cascade to pending workspace identities
        await db.execute(
            "DELETE FROM pending_identities WHERE email=lower($1) AND organization_id = $2 AND access_scope = 'organization'",
            auth.user_email,
            organization_id,
        )

    if result is None:
        raise HTTPException(
            status_code=404,
            detail="Pending identity not found.",
        )

    return result


@retry_asyncpg
async def claim_pending_identity(
    auth: TenantlessAuthInfo | OrgAuthInfo, tenant_id: uuid.UUID
) -> schemas.Identity:
    """Claim a pending identity for the current user."""
    async with seat_event_modification_transaction(
        IDForSeatChangeOperation(tenant_id=tenant_id),
        SeatChangeOperation.CLAIM_INTO_TENANT,
    ) as db:
        pending_identity_row = await db.fetchrow(
            """
            SELECT * from pending_identities
            where tenant_id = $1
            and email = lower($2)
            and access_scope = 'workspace'
            """,
            tenant_id,
            auth.user_email,
        )
        if pending_identity_row is None:
            raise HTTPException(
                status_code=404,
                detail="Pending identity not found.",
            )
        organization_id = pending_identity_row["organization_id"]

        # claim the pending org identity if it exists
        # TODO: consolidate the deletion of the pending_identities with org once we add existing org invites
        #       as there is a CASCADE on the identity one.
        claimed_org_identity = await claim_pending_organization_identity_in_txn(
            db, organization_id, auth.user_email, auth.user_id, auth.ls_user_id
        )
        if not claimed_org_identity:
            logger.info(f"User {auth.user_id=} does not have pending org identity.")

        # Create the identity directly if the user doesn't have an existing or newly claimed org identity.
        # This is only the case for users who were invited to a workspace before this feature was implemented.
        # TODO: update this to insert Organization User depending on plan
        org_identity = await db.fetchrow(
            """
            SELECT * from identities
                WHERE organization_id = (SELECT organization_id from tenants WHERE id = $1)
                AND user_id = $2
                AND access_scope = 'organization'
            """,
            tenant_id,
            auth.user_id,
        )

        # TODO: This should not be needed REMOVE THIS
        if not org_identity and not claimed_org_identity:
            # Uses the org admin role if they're a workspace admin, otherwise org user
            org_identity = await db.fetchrow(
                f"""
                INSERT INTO identities (organization_id, user_id, read_only, role_id, access_scope, ls_user_id)
                    SELECT
                        (SELECT organization_id from tenants WHERE id = $1),
                        $2,
                        $3,
                        CASE WHEN $4 = (SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN')
                            THEN (SELECT id FROM roles WHERE name = '{OrganizationRoles.ADMIN.value}')
                            ELSE (SELECT id FROM roles WHERE name = '{OrganizationRoles.USER.value}')
                        END,
                        'organization',
                        $5
                    returning *
                """,
                tenant_id,
                auth.user_id,
                pending_identity_row["read_only"],
                pending_identity_row["role_id"],
                auth.ls_user_id,
            )

        result = await db.fetchrow(
            """
    with

    pending_identities as (
        SELECT * from pending_identities
        where tenant_id = $1
            and email = lower($2)
            and access_scope = 'workspace'
    )

    insert into identities (tenant_id, read_only, user_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
    select tenant_id, read_only, $3, organization_id, role_id, access_scope, $4, $5

    from pending_identities
    on conflict (tenant_id, ls_user_id) where access_scope = 'workspace'
    do update set tenant_id = excluded.tenant_id
    returning *""",
            tenant_id,
            auth.user_email,
            auth.user_id,
            org_identity["id"],
            org_identity["ls_user_id"],
        )
        if result is None:
            raise HTTPException(
                status_code=404,
                detail="Pending identity not found.",
            )
        tenant_identity = schemas.Identity(**result)

        # DELETE the pending org identity which will also cascade to pending workspace identities
        await db.execute(
            "DELETE FROM pending_identities WHERE email=lower($1) AND organization_id = $2 AND access_scope = 'organization'",
            auth.user_email,
            organization_id,
        )

    return tenant_identity


@retry_asyncpg
async def add_basic_auth_users(
    auth: OrgAuthInfo,
    payloads: List[schemas.BasicAuthMemberCreate],
) -> List[schemas.UserWithPassword]:
    """
    This is only used for basic auth. It creates users and identities in the organization and specified workspace(s).
    Fails if user records with the same email already exist. Does not fail if identities already exist.
    """
    if not settings.BASIC_AUTH_ENABLED:
        raise HTTPException(
            status_code=400,
            detail="Basic auth is not enabled.",
        )

    _check_invites_payloads(payloads)

    return await _add_basic_auth_users_unsafe(
        auth,
        payloads,
    )


@retry_asyncpg
async def add_basic_auth_user(
    auth: OrgAuthInfo,
    payload: schemas.BasicAuthMemberCreate,
) -> schemas.UserWithPassword:
    return (await add_basic_auth_users(auth, [payload]))[0]


@retry_asyncpg
async def list_pending_tenants(
    auth: TenantlessAuthInfo,
) -> list[schemas.Tenant]:
    """List tenants that have pending identities for the current user."""
    async with database.asyncpg_conn() as db:
        results = await db.fetch(
            """
    select
        tenants.*,
        is_personal
    from pending_identities
    inner join tenants on tenants.id = pending_identities.tenant_id
    inner join organizations on tenants.organization_id = organizations.id
    where pending_identities.email = lower($1)""",
            auth.user_email,
        )
    return [schemas.Tenant.model_validate(r) for r in results]


@retry_asyncpg
async def list_pending_organization_invites(
    auth: TenantlessAuthInfo,
) -> list[schemas.OrganizationPGSchemaSlim]:
    """List organizations that have pending identities for the current user, omitting SSO only orgs."""
    async with database.asyncpg_conn() as db:
        results = await db.fetch(
            """
    select
        o.*
    from pending_identities
    inner join organizations o on o.id = pending_identities.organization_id
    where pending_identities.email = lower($1)
    and pending_identities.access_scope = 'organization'
    and not o.sso_only
    """,
            auth.user_email,
        )
    return [schemas.OrganizationPGSchemaSlim(**r) for r in results]


@retry_asyncpg
async def ensure_org_admin_identities_in_txn(
    db: asyncpg.Connection,
    identity_id: uuid.UUID,
) -> None:
    """
    Ensure that the specified org admin has workspace admin identity in all workspaces in the corresponding org,
    doing nothing if there is no org admin identity with the specified ID.
    """
    await db.execute(
        """
        WITH org_identity AS (
            SELECT id, organization_id, user_id, ls_user_id
            FROM identities
            WHERE id = $1
                AND access_scope = 'organization'
                AND role_id = (SELECT id FROM roles WHERE name = 'ORGANIZATION_ADMIN')
        )
        INSERT INTO identities (tenant_id, organization_id, user_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
        SELECT
            t.id,
            oi.organization_id,
            oi.user_id,
            false,
            (SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'),
            'workspace',
            oi.id,
            oi.ls_user_id
        FROM tenants t
        JOIN org_identity oi ON t.organization_id = oi.organization_id
        ON CONFLICT (tenant_id, ls_user_id) WHERE access_scope = 'workspace'
        DO UPDATE SET role_id = EXCLUDED.role_id, read_only = EXCLUDED.read_only;
        """,
        identity_id,
    )


@retry_asyncpg
async def ensure_specific_org_admin_pending_identities_in_txn(
    db: asyncpg.Connection,
    identity_id: uuid.UUID,
) -> None:
    """
    Ensure that the specified org admin has pending identities in all workspaces in the corresponding org,
    doing nothing if there is no pending org admin identity with the specified ID.
    """
    await db.execute(
        """
        WITH org_pending_admin AS (
            SELECT id, organization_id, email
            FROM pending_identities
            WHERE id = $1
                AND access_scope = 'organization'
                AND role_id = (SELECT id FROM roles WHERE name = 'ORGANIZATION_ADMIN')
        )
        INSERT INTO pending_identities (tenant_id, organization_id, email, read_only, role_id, access_scope, parent_pending_identity_id)
        SELECT
            t.id,
            oi.organization_id,
            oi.email,
            false,
            (SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'),
            'workspace',
            (SELECT id FROM pending_identities WHERE email = oi.email AND organization_id = oi.organization_id AND access_scope = 'organization')
        FROM tenants t
        JOIN org_pending_admin oi ON t.organization_id = oi.organization_id
        ON CONFLICT (tenant_id, email) DO NOTHING;
        """,
        identity_id,
    )


@retry_asyncpg
async def ensure_all_org_admin_identities_in_txn(
    db: asyncpg.Connection,
    tenant_id: uuid.UUID,
) -> None:
    """
    Ensure that all org admins have identities in the specified workspace,
    doing nothing if there are no org admin identities.
    """
    await db.execute(
        """
        WITH org_admins AS (
            SELECT id, organization_id, user_id, ls_user_id
            FROM identities
            WHERE organization_id = (SELECT organization_id FROM tenants where id = $1)
                AND access_scope = 'organization'
                AND role_id = (SELECT id FROM roles WHERE name = 'ORGANIZATION_ADMIN')
                AND user_id IS NOT NULL AND ls_user_id IS NOT NULL
        )
        INSERT INTO identities (tenant_id, organization_id, user_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
        SELECT
            $1,
            oi.organization_id,
            oi.user_id,
            false,
            (SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'),
            'workspace',
            oi.id,
            oi.ls_user_id
        FROM tenants t
        JOIN org_admins oi ON t.organization_id = oi.organization_id
        WHERE t.id = $1
        ON CONFLICT (tenant_id, ls_user_id) WHERE access_scope = 'workspace' AND ls_user_id IS NOT NULL DO NOTHING;
        """,
        tenant_id,
    )


@retry_asyncpg
async def ensure_all_org_admin_pending_identities_in_txn(
    db: asyncpg.Connection,
    tenant_id: uuid.UUID,
) -> None:
    """
    Ensure that the all org admins have pending identities in the specified workspace,
    doing nothing if there are no pending org admin identities.
    """
    await db.execute(
        """
        WITH org_pending_admins AS (
            SELECT organization_id, email
            FROM pending_identities
            WHERE organization_id = (SELECT organization_id FROM tenants where id = $1)
                AND access_scope = 'organization'
                AND role_id = (SELECT id FROM roles WHERE name = 'ORGANIZATION_ADMIN')
        )
        INSERT INTO pending_identities (tenant_id, organization_id, email, read_only, role_id, access_scope, parent_pending_identity_id)
        SELECT
            $1,
            oi.organization_id,
            oi.email,
            false,
            (SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'),
            'workspace',
            (SELECT id FROM pending_identities WHERE email = oi.email AND organization_id = oi.organization_id AND access_scope = 'organization')
        FROM tenants t
        JOIN org_pending_admins oi ON t.organization_id = oi.organization_id
        WHERE t.id = $1
        ON CONFLICT (tenant_id, email) DO NOTHING;
        """,
        tenant_id,
    )


@retry_asyncpg
async def provision_jit_sso_identities(
    db: asyncpg.Connection,
    ls_user_id: uuid.UUID,
    saml_provider_id: uuid.UUID,
    provider_user_id: uuid.UUID,
) -> None:
    """
    Provision just-in-time SSO identities for configured workspaces (if present) and organization.
    """
    # This import is here to avoid circular imports
    from app.models.organizations.config import get_org_config_cached

    result = await db.fetchrow(
        """
        SELECT sp.id, provider_id, organization_id, default_workspace_ids, default_workspace_role_id, o.jit_provisioning_enabled
        FROM saml_providers sp JOIN organizations o on sp.organization_id = o.id
        WHERE sp.provider_id = $1
        """,
        saml_provider_id,
    )
    if not result:
        raise HTTPException(
            status_code=404,
            detail="SSO settings not found.",
        )
    sso_settings = schemas.SSOProviderWithSettings(**result)
    organization_id = sso_settings.organization_id
    structlog.contextvars.bind_contextvars(
        saml_provider_id=saml_provider_id,
        provider_user_id=provider_user_id,
        ls_user_id=ls_user_id,
    )

    if not sso_settings.jit_provisioning_enabled:
        logger.info(
            "Skipping JIT provisioning because it is disabled for organization."
        )
        return

    # get organization config
    org_result = await db.fetchrow(
        "SELECT metronome_customer_id, config, is_personal FROM organizations WHERE id = $1",
        organization_id,
    )
    org_config, _ = await get_org_config_cached(
        OrganizationConfig.model_validate(org_result["config"]),
        str(organization_id),
        org_result["is_personal"],
        org_result["metronome_customer_id"],
    )

    org_user_role_id = await db.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.USER.value
    )

    existing_org_identity = await db.fetchrow(
        """
        SELECT * FROM identities
        WHERE ls_user_id = $1
            AND organization_id = $2
            AND access_scope = 'organization'
        """,
        ls_user_id,
        organization_id,
    )
    if not existing_org_identity:
        identity_creates = [
            IdentityCreateInTxn(
                ls_user_id=ls_user_id,
                user_id=provider_user_id,
                read_only=None,
                role_id=org_user_role_id,
            )
        ]

        await create_identities_for_org_in_txn(
            db,
            sso_settings.organization_id,
            org_config.max_identities,
            identity_creates,
        )

        if (
            sso_settings.default_workspace_role_id
            and sso_settings.default_workspace_ids
        ):
            payload = schemas.WorkspaceIdentityCreateInternal(
                ls_user_id=ls_user_id,
                user_id=provider_user_id,
                workspace_role_id=sso_settings.default_workspace_role_id,
                workspace_ids=sso_settings.default_workspace_ids,
            )
            auth = OrgAuthInfo(
                user_id=provider_user_id,
                # There is no actual user executing this and it shouldn't be read
                ls_user_id=None,
                organization_id=sso_settings.organization_id,
                org_config=org_config,
            )
            await _create_workspace_identity_in_txn(db, auth, payload)
