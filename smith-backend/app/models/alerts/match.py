import asyncio
from datetime import timed<PERSON><PERSON>, timezone
from typing import Optional
from uuid import UUID

import or<PERSON><PERSON>
import structlog
from lc_config.settings import shared_settings as settings
from lc_database import database, redis
from lc_database.curl import internal_platform_request
from lc_logging import trace as trace_utils

from app.api.auth.schemas import AuthInfo
from app.memoize import redis_cache
from app.models.alerts.models import (
    AlertAggregation,
    AlertAttributeName,
    AlertEntity,
    AlertMetricEntry,
    AlertRule,
)
from app.models.alerts.visitor import AlertEntityVisitor
from app.models.query_lang.parse import parse_as_filter_directive

logger = structlog.get_logger()

ONE_MINUTE_SECONDS = 60

ALERT_RULE_FIELDS = [
    "id",
    "session_id",
    "attribute",
    "aggregation",
    "filter",
    "denominator_filter",
]


@redis_cache(ttl=60)
async def get_session_alert_rules(
    tenant_id: UUID, session_id: UUID
) -> tuple[UUID, list[AlertRule]]:
    """Get alert rules for a session from redis and db."""
    async with database.asyncpg_conn() as db:
        query = f"""
        SELECT
            {", ".join(ALERT_RULE_FIELDS)}
        FROM alert_rules
        WHERE session_id = $1 AND tenant_id = $2
        """
        result = await db.fetch(query, session_id, tenant_id)

    # parse filters
    alert_rules = [AlertRule(**row) for row in result]
    for alert_rule in alert_rules:
        if alert_rule.attribute == AlertAttributeName.FEEDBACK_SCORE:
            continue
        # parse filters
        if alert_rule.filter is not None and alert_rule.filter != "":
            alert_rule.filter_directive = parse_as_filter_directive(alert_rule.filter)
        if (
            alert_rule.denominator_filter is not None
            and alert_rule.denominator_filter != ""
        ):
            alert_rule.denominator_filter_directive = parse_as_filter_directive(
                alert_rule.denominator_filter
            )

    return (session_id, alert_rules)


async def get_alert_rules_for_sessions(
    tenant_id: UUID, session_ids: list[UUID]
) -> dict[str, list[AlertRule]]:
    """Get alert rules for sessions from redis and db."""
    session_alert_rules_list = await asyncio.gather(
        *(get_session_alert_rules(tenant_id, session_id) for session_id in session_ids)
    )

    session_alert_rules = {
        str(session_id): session_alert_rules
        for (session_id, session_alert_rules) in session_alert_rules_list
    }

    return session_alert_rules


def create_run_count_metric(
    entity: AlertEntity, alert_rule: AlertRule
) -> Optional[AlertMetricEntry]:
    """Create a metric entry for run count if conditions are met."""
    if not entity.end_time:
        return None

    # check if the entity matches the filter
    matches_filter = True
    if alert_rule.filter_directive is not None:
        matches_filter = alert_rule.filter_directive.accept(
            AlertEntityVisitor(alert_entity=entity)
        )

    # if the aggregation is PCT, we need to check the denominator filter
    matches_denominator_filter = alert_rule.aggregation == AlertAggregation.PCT and (
        alert_rule.denominator_filter is None  # matches all runs
        or (
            alert_rule.denominator_filter_directive is not None
            and alert_rule.denominator_filter_directive.accept(
                AlertEntityVisitor(alert_entity=entity)
            )
        )  # matches some runs
    )

    # if the entity does not match the filter or denominator filter, return None
    if not matches_filter and not matches_denominator_filter:
        return None

    # create metric value
    metric_value = 1 if matches_filter else 0

    # create metric denominator value
    metric_denominator_value = (
        1
        if matches_denominator_filter
        else 0
        if alert_rule.aggregation == AlertAggregation.PCT
        else None
    )

    return AlertMetricEntry(
        session_id=entity.session_id,
        alert_rule_id=alert_rule.id,
        metric_value=metric_value,
        timestamp=entity.end_time,
        metric_denominator_value=metric_denominator_value,
    )


def create_error_count_metric(
    entity: AlertEntity, alert_rule: AlertRule
) -> Optional[AlertMetricEntry]:
    """Create a metric entry for error count if conditions are met."""
    if not entity.end_time:
        return None

    if (
        alert_rule.aggregation == AlertAggregation.PCT
        or entity.error
        or entity.status == "error"
    ) and entity.is_root:
        return AlertMetricEntry(
            session_id=entity.session_id,
            alert_rule_id=alert_rule.id,
            metric_value=1 if (entity.error or entity.status == "error") else 0,
            timestamp=entity.end_time,
            metric_denominator_value=1
            if alert_rule.aggregation == AlertAggregation.PCT
            else None,
        )
    return None


def create_latency_metric(
    entity: AlertEntity, alert_rule: AlertRule
) -> Optional[AlertMetricEntry]:
    """Create a metric entry for trace latency if conditions are met."""
    if entity.start_time is None or entity.end_time is None or not entity.is_root:
        return None

    if entity.latency is not None and entity.latency >= 0:
        return AlertMetricEntry(
            session_id=entity.session_id,
            alert_rule_id=alert_rule.id,
            metric_value=entity.latency,
            timestamp=entity.end_time,
        )
    return None


def create_run_latency_metric(
    entity: AlertEntity, alert_rule: AlertRule
) -> Optional[AlertMetricEntry]:
    """Create a metric entry for run latency if conditions are met."""
    if entity.start_time is None or entity.end_time is None:
        return None

    if (
        alert_rule.filter_directive is not None
        and not alert_rule.filter_directive.accept(
            AlertEntityVisitor(alert_entity=entity)
        )
    ):
        return None

    latency = (entity.end_time - entity.start_time).total_seconds()
    if latency >= 0:
        return AlertMetricEntry(
            session_id=entity.session_id,
            alert_rule_id=alert_rule.id,
            metric_value=latency,
            timestamp=entity.end_time,
        )
    return None


def create_feedback_score_metric(
    entity: AlertEntity, alert_rule: AlertRule
) -> Optional[AlertMetricEntry]:
    """Create a metric entry for feedback score if conditions are met."""
    if (
        entity.feedback_score is None
        or entity.feedback_key is None
        or alert_rule.filter is None
        or entity.feedback_key not in alert_rule.filter
    ):
        return None

    if entity.modified_at is None:
        logger.error(
            f"Feedback modified_at is None for feedback key {entity.feedback_key} for entity session {entity.session_id}"
        )
        return None

    return AlertMetricEntry(
        session_id=entity.session_id,
        alert_rule_id=alert_rule.id,
        metric_value=entity.feedback_score,
        timestamp=entity.modified_at,
    )


def check_entities_for_alert_rules(
    entities: list[AlertEntity], session_alert_rules: dict[str, list[AlertRule]]
) -> list[AlertEntity]:
    """Check entities against alert rules and generate metric entries for matching conditions."""
    queued_alert_metrics = []

    for entity in entities:
        alert_rules = session_alert_rules.get(str(entity.session_id), [])
        metrics = generate_metrics_for_entity(entity, alert_rules)
        queued_alert_metrics.extend(metrics)

    return queued_alert_metrics


def generate_metrics_for_entity(
    entity: AlertEntity, alert_rules: list[AlertRule]
) -> list[AlertMetricEntry]:
    """Generate metric entries for an entity based on matching alert rules."""
    metrics = []

    for alert_rule in alert_rules:
        metric = create_metric_if_applicable(entity, alert_rule)
        if metric:
            metrics.append(metric)

    return metrics


def create_metric_if_applicable(
    entity: AlertEntity, alert_rule: AlertRule
) -> Optional[AlertMetricEntry]:
    """Create a metric entry if the entity matches the alert rule's conditions."""
    if alert_rule.attribute == AlertAttributeName.ERROR_COUNT:  # legacy metric
        return create_error_count_metric(entity, alert_rule)
    elif alert_rule.attribute == AlertAttributeName.LATENCY:  # legacy metric
        return create_latency_metric(entity, alert_rule)
    elif alert_rule.attribute == AlertAttributeName.FEEDBACK_SCORE:
        return create_feedback_score_metric(entity, alert_rule)
    elif alert_rule.attribute == AlertAttributeName.RUN_COUNT:
        return create_run_count_metric(entity, alert_rule)
    elif alert_rule.attribute == AlertAttributeName.RUN_LATENCY:
        return create_run_latency_metric(entity, alert_rule)
    return None


async def add_metrics_to_alert_minute_entries(
    tenant_id: UUID, queued_alert_metrics: list[AlertMetricEntry]
):
    """Add metrics to alert minute entries in redis"""
    async with (
        redis.aredis_routed_pool(str(tenant_id), redis.RedisOperation.ALERTS) as aredis,
        aredis.pipeline() as pipe,
    ):
        for metric in queued_alert_metrics:
            pipe.exists(
                f"smith:alerts:metrics:{str(metric.alert_rule_id)}:{metric.timestamp.strftime('%Y-%m-%d-%H-%M')}"
            )
        metric_key_exists = await pipe.execute()

    queue_job_keys = set()
    async with (
        redis.aredis_routed_pool(str(tenant_id), redis.RedisOperation.ALERTS) as aredis,
        aredis.pipeline() as pipe,
    ):
        for i, metric in enumerate(queued_alert_metrics):
            minute_key = metric.timestamp.strftime("%Y-%m-%d-%H-%M")
            expiration_time = metric.timestamp.replace(
                second=0, microsecond=0
            ) + timedelta(minutes=settings.METRICS_RETENTION_MINUTES)
            numerator_key = (
                f"smith:alerts:metrics:{str(metric.alert_rule_id)}:{minute_key}"
            )
            denominator_key = f"smith:alerts:metrics:{str(metric.alert_rule_id)}:{minute_key}:denominator"
            if metric.metric_value is not None:
                logger.debug(
                    "Adding metric value",
                    metric_value=metric.metric_value,
                    numerator_key=numerator_key,
                )
                pipe.rpush(numerator_key, metric.metric_value)
            if metric.metric_denominator_value is not None:
                logger.debug(
                    "Adding metric denominator value",
                    metric_denominator_value=metric.metric_denominator_value,
                    denominator_key=denominator_key,
                )
                pipe.rpush(denominator_key, metric.metric_denominator_value)
            if metric.metric_value is not None and not metric_key_exists[i]:
                if settings.REDIS_USE_NX_IN_EXPIREAT:
                    pipe.expireat(numerator_key, expiration_time, nx=True)
                else:
                    pipe.expireat(numerator_key, expiration_time)
            if metric.metric_denominator_value is not None and not metric_key_exists[i]:
                if settings.REDIS_USE_NX_IN_EXPIREAT:
                    pipe.expireat(denominator_key, expiration_time, nx=True)
                else:
                    pipe.expireat(denominator_key, expiration_time)
            if not metric_key_exists[i] and (
                metric.metric_value is not None
                or metric.metric_denominator_value is not None
            ):
                queue_job_keys.add(
                    (
                        metric.session_id,
                        metric.alert_rule_id,
                        metric.timestamp.replace(second=0, microsecond=0),
                    )
                )
        results = await pipe.execute()
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Redis command failed with error: {result}")

    async with redis.async_queue(settings.ADHOC_QUEUE) as queue:
        for session_id, alert_rule_id, minute_ts in queue_job_keys:
            job_proc_time = minute_ts + timedelta(
                minutes=settings.AGGREGATE_ALERT_RULE_DELAY_MINUTES
            )
            job_proc_time = job_proc_time.replace(tzinfo=timezone.utc)
            minute_key = minute_ts.strftime("%Y-%m-%d-%H-%M")
            await queue.enqueue(
                "aggregate_and_check_alert_rule_worker",
                pipeline=None,
                tenant_id=str(tenant_id),
                session_id=str(session_id),
                alert_rule_id=str(alert_rule_id),
                minute_key=minute_key,
                scheduled=job_proc_time.timestamp(),
                **redis.DEFAULT_JOB_KWARGS,
            )
            logger.info(
                "Enqueued alert rule aggregation and check",
                tenant_id=str(tenant_id),
                session_id=str(session_id),
                alert_rule_id=str(alert_rule_id),
                minute_key=minute_key,
            )


@trace_utils.wrap(
    "saq-queue",
    "check_entities_and_add_metrics",
)
async def check_entities_and_add_metrics(auth: AuthInfo, entities: list[AlertEntity]):
    """Check entities against alert rules and add metrics to alert minute entries."""
    if (
        auth.tenant_config is None
        or auth.tenant_config.organization_config is None
        or not auth.tenant_config.organization_config.langsmith_alerts_poc_enabled
    ) and settings.LANGCHAIN_ENV not in ["local_test"]:
        return
    entity_sessions = list(set([entity.session_id for entity in entities]))
    alert_rules = await get_alert_rules_for_sessions(auth.tenant_id, entity_sessions)
    queued_alert_metrics = check_entities_for_alert_rules(entities, alert_rules)
    await add_metrics_to_alert_minute_entries(auth.tenant_id, queued_alert_metrics)


# queue worker function to aggreagate and check alert triggers
async def aggregate_and_check_alert_rule(
    tenant_id: str, session_id: str, alert_rule_id: str, minute_key: str
):
    """Aggregate and check alert rule worker function"""
    # acquire lock to ensure only once processing of an alert rule's minute worth of metrics
    async with redis.aredis_caching_pool() as aredis:
        agg_key = f"smith:alerts:aggregation_worker:{alert_rule_id}:{minute_key}"
        lock = aredis.lock(
            agg_key,
            timeout=settings.AGGREGATE_ALERT_RULE_LOCK_TIMEOUT_SECONDS,
        )
        if not await lock.acquire(blocking=False):
            logger.debug(f"Alert rule {agg_key} is already being aggregated, skipping")
            return

        # aggregate and check alert rule
        try:
            response = await internal_platform_request(
                "POST",
                "/internal/aggregate-alerts",
                headers={"X-Tenant-Id": tenant_id},
                jwt_payload={"tenant_id": tenant_id},
                body=orjson.dumps(
                    {
                        "session_id": session_id,
                        "alert_rule_id": alert_rule_id,
                        "minute_key": minute_key,
                    }
                ),
            )
            if response.code != 200:
                logger.error(
                    f"Failed to call into platform-backend to check alert rule {agg_key}",
                    status_code=response.code,
                    response=response.body.decode("utf-8"),
                )
            else:
                logger.info(
                    f"Successfully called into platform-backend to check alert rule {agg_key}",
                    status_code=response.code,
                    response=response.body.decode("utf-8"),
                )
        except Exception:
            logger.warning(f"Failed to aggregate and check alert rule {agg_key}")

        # release lock
        try:
            await lock.release()
        except Exception as e:
            logger.debug(f"Error releasing lock: {e}")
