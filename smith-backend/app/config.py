from typing import List, Self, Union
from uuid import UUID

from lc_config.settings import (
    ENV_FILE_PATH,
    SECRETS_DIR_PATH,
    SharedSettings,
    shared_settings,
)
from pydantic import AnyHttpUrl, field_validator, model_validator
from pydantic_core.core_schema import ValidationInfo


class Settings(SharedSettings):
    """Settings for the application."""

    PROJECT_NAME: str = "LangSmith"
    FF_WAITLIST_ENABLED: str = "false"
    POSTMARK_SERVER_TOKEN: str | None = None
    POSTMARK_FROM_EMAIL: str = "<EMAIL>"
    POSTMARK_INVITE_MEMBER_TEMPLATE_ID: str = "member-invited"
    POSTMARK_WORKSPACE_INVITE_TEMPLATE_ID: str = "workspace-invite"
    POSTMARK_SSO_EMAIL_VERIFICATION_TEMPLATE_ID: str = "sso-email-verification"
    POSTMARK_UNUSED_HOST_PROJECT_TEMPLATE_ID: str = "unused-host-project"
    POSTMARK_DELETED_HOST_PROJECT_TEMPLATE_ID: str = "deleted-host-project"
    POSTMARK_MESSAGE_STREAM: str = "smith-backend"
    LANGSMITH_URL: str = "https://smith.langchain.com"
    MARKETPLACE_URL: str = "https://agents.langchain.com"
    FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED: bool = False
    FF_USAGE_LIMITS_ENABLED: bool = False
    CLICKHOUSE_ASYNC_INSERT_WAIT_PCT_FLOAT: float = 1
    COMPRESS_MIN_SIZE_KB: int = 1  # 1KB
    SPOOL_MIN_SIZE_KB: int = 1000  # 1000KB
    FF_ORG_CREATION_DISABLED: bool = False

    FF_COMPARE_TRACE_ENABLED: bool = False
    MULTIPART_READ_TIMEOUT: int = 60  # 60 seconds
    MULTIPART_FIRST_READ_TIMEOUT: int = 60  # 60 seconds

    # For fetch runs queries returning more than this number of runs,
    # we skip any expensive queries (and may return less columns than requested)
    FETCH_RUNS_THRESHOLD_SKIP_EXPENSIVE: int = 1000

    # Only return / store to CH the first N characters of input/output for preview
    RUN_PREVIEW_IO_MAX_CHARS: int = 150

    # This is limited by the maximum desired Redis memory usage
    # Increase to keep items around for longer (more resilient to long outages)
    # Decrease to reduce max Redis memory usage (less resilient to long outages)
    REDIS_RUNS_EXPIRY_SECONDS: int = 60 * 60 * 12  # 12 hours
    # This is limited by the number of max desired connections to the database
    # Increasing writes more items in parallel, using more database connections
    WRITE_QUEUE_PARALLEL_PG_OPS: int = 5
    # This limit the number of max async simultaneous operations
    WRITE_QUEUE_PARALLEL_OPS: int = 10
    # This is the namespace used to create auth ids for write queue items
    # There should be no reason to change this
    WRITE_QUEUE_AUTH_NAMESPACE: UUID = UUID("6ba7b815-9dad-11d1-80b4-00c04fd430c8")
    # This is the maximum number of items to keep in the error log for each tenant
    # Older items are deleted when new items are added
    WRITE_QUEUE_TENANT_ERROR_LOG_LIMIT: int = 100

    DEFAULT_TOKENIZER: str = "cl100k_base"  # Configure default tokenizer

    # Hub commit cache settings
    HUB_COMMIT_MANIFEST_CACHE_TTL_SECONDS: int = 600  # 10 minutes
    HUB_COMMIT_LIST_CACHE_TTL_SECONDS: int = 600  # 10 minutes
    CACHE_HUB_COMMITS: bool = False  # Feature flag for hub commits caching

    # TODO: make use of this in the future to prevent allowing all origins
    # BACKEND_CORS_ORIGINS is a JSON-formatted list of origins
    # e.g: '["http://localhost", "http://localhost:4200", "http://localhost:3000", \
    # "http://localhost:8080"]'
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    # Basic auth fields
    BASIC_AUTH_ENABLED: bool = False  # Can only be True if AUTH_TYPE == 'mixed'
    BASIC_AUTH_JWT_SECRET: str | None = None
    INITIAL_ORG_ADMIN_EMAIL: str | None = None
    INITIAL_ORG_ADMIN_PASSWORD: str | None = None

    # Settings for running in mixed mode.
    # OAUTH_CUSTOM_OIDC_ENABLED is set dynamically based on the other settings.
    OAUTH_CLIENT_SECRET: str | None = None
    OAUTH_CUSTOM_OIDC_ENABLED: bool = False

    # Limit Settings
    RUNS_QUERY_LIMIT: int = 100

    # Online Evals Batch Settings
    ONLINE_EVALS_BATCH_SIZE: int = 20
    ONLINE_EVALS_MAX_CONCURRENCY: int = 5

    INFO_CACHE_MAX_AGE_SECONDS: int = 60

    @field_validator("OAUTH_CUSTOM_OIDC_ENABLED", mode="after")
    @classmethod
    def get_custom_oidc_enabled(cls, v: str, info: ValidationInfo):
        # Note that this must be in this class rather than lc_config's
        # in order to resolve LANGSMITH_URL correctly
        auth_type = info.data.get("AUTH_TYPE")
        return (
            auth_type == "mixed"
            and info.data.get("OAUTH_CLIENT_ID")
            and info.data.get("OAUTH_CLIENT_SECRET")
            and info.data.get("OAUTH_ISSUER_URL")
            and info.data.get("LANGSMITH_URL")
        )

    @model_validator(mode="after")
    def validate_model(self) -> Self:
        if self.BASIC_AUTH_ENABLED and not self.AUTH_TYPE == "mixed":
            raise ValueError("Basic auth is only allowed with 'mixed' AUTH_TYPE")
        if self.BASIC_AUTH_ENABLED and not self.BASIC_AUTH_JWT_SECRET:
            raise ValueError("For Basic Auth, BASIC_AUTH_JWT_SECRET must be set")
        return self

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    @field_validator("FF_WAITLIST_ENABLED")
    @classmethod
    def validate_ff_waitlist_enabled(cls, v: str) -> str:
        if v != "true" and v != "false":
            raise ValueError("FF_WAITLIST_ENABLED must be true or false")
        return v


LANGCHAIN_ENV = shared_settings.LANGCHAIN_ENV
settings = Settings(  # type: ignore
    LANGCHAIN_ENV=LANGCHAIN_ENV,
    _env_file=ENV_FILE_PATH,  # type: ignore
    _secrets_dir=SECRETS_DIR_PATH,  # type: ignore
)
