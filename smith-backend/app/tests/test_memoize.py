import asyncio
from unittest.mock import AsyncMock

import pytest

from app.memoize import execute_with_cache, redis_cache


async def test_redis_cache_empty_results():
    mock_function = AsyncMock(side_effect=[[], 1])

    @redis_cache(ttl=1)
    async def cache_empty_value():
        return await mock_function()

    assert await cache_empty_value() == []
    assert await cache_empty_value() == []

    assert mock_function.await_count == 1


async def test_redis_cache_dont_cache_empty_results():
    mock_function = AsyncMock(side_effect=[None, [], 1])

    @redis_cache(ttl=1, cache_empty=False)
    async def dont_cache_empty_value():
        return await mock_function()

    assert await dont_cache_empty_value() is None
    assert await dont_cache_empty_value() == []
    assert await dont_cache_empty_value() == 1
    assert await dont_cache_empty_value() == 1

    assert mock_function.await_count == 3


async def test_execute_with_cache():
    mock_function = AsyncMock(side_effect=[1, 2, 3])

    for _ in range(3):
        assert (
            await execute_with_cache(
                key="cache:key:1", func=mock_function, args=[], kwargs={}, ttl=5
            )
            == 1
        )

    assert mock_function.await_count == 1

    assert (
        await execute_with_cache(
            key="cache:key:2", func=mock_function, args=[], kwargs={}, ttl=5
        )
        == 2
    )


async def test_execute_with_0_ttl():
    mock_function = AsyncMock(side_effect=[0, 1, 2])

    @redis_cache(ttl=0)
    async def execute():
        return await mock_function()

    for i in range(3):
        res = await execute()
        assert res == i

    assert mock_function.await_count == 3


async def test_redis_cache_with_exceptions_while_calling_function():
    mock_function = AsyncMock(side_effect=[ValueError("Test exception"), 1, 1, 1])

    @redis_cache(ttl=1)
    async def caching_with_exception():
        return await mock_function()

    with pytest.raises(ValueError, match="Test exception"):
        await caching_with_exception()
    assert await caching_with_exception() == 1
    assert await caching_with_exception() == 1
    assert await caching_with_exception() == 1

    assert mock_function.await_count == 2


async def test_redis_cache_enabled_false():
    """Test that caching is disabled when enabled=False."""
    mock_function = AsyncMock(side_effect=[1, 2, 3])

    @redis_cache(ttl=300, enabled=False)
    async def disabled_cache():
        return await mock_function()

    # Each call should hit the function since caching is disabled
    assert await disabled_cache() == 1
    assert await disabled_cache() == 2
    assert await disabled_cache() == 3

    # Function should be called every time (no caching)
    assert mock_function.await_count == 3


async def test_redis_cache_enabled_true():
    """Test that caching works normally when enabled=True (explicit)."""
    call_count = 0

    async def test_function():
        nonlocal call_count
        call_count += 1
        return f"result_{call_count}"

    @redis_cache(ttl=300, enabled=True)
    async def enabled_cache():
        return await test_function()

    # First call hits function, subsequent calls use cache
    result1 = await enabled_cache()
    result2 = await enabled_cache()  # Cached
    result3 = await enabled_cache()  # Cached

    # All results should be the same (cached)
    assert result1 == result2 == result3
    # Function should only be called once (caching works)
    assert call_count == 1


async def test_redis_cache_enabled_callable():
    """Test that caching respects callable enabled parameter."""
    # Simple test: just verify that when enabled returns False, no caching occurs
    call_count = 0

    async def test_function():
        nonlocal call_count
        call_count += 1
        return f"result_{call_count}"

    # Test with enabled=False via callable
    @redis_cache(ttl=300, enabled=lambda: False)
    async def disabled_cache():
        return await test_function()

    # When disabled, should call function every time
    result1 = await disabled_cache()
    result2 = await disabled_cache()

    # Results should be different since caching is disabled
    assert result1 != result2
    assert call_count == 2  # Function called twice


async def test_redis_cache_enabled_default():
    """Test that default behavior (no enabled parameter) still works."""
    call_count = 0

    async def test_function():
        nonlocal call_count
        call_count += 1
        return f"result_{call_count}"

    @redis_cache(ttl=300)  # No enabled parameter - should default to True
    async def default_cache():
        return await test_function()

    # Should work like normal caching (enabled=True by default)
    result1 = await default_cache()
    result2 = await default_cache()  # Cached
    result3 = await default_cache()  # Cached

    # All results should be the same (cached)
    assert result1 == result2 == result3
    # Function should only be called once
    assert call_count == 1


async def test_redis_cache_with_version_key():
    """Test that version_func parameter works correctly."""
    call_count = 0

    async def test_function(db, owner: str, repo: str):
        nonlocal call_count
        call_count += 1
        return f"result_{call_count}_{owner}_{repo}"

    def mock_version_key(*args, **kwargs):
        owner = kwargs.get("owner", "default_owner")
        repo = kwargs.get("repo", "default_repo")
        return f"repo_v={hash(f'{owner}/{repo}')}"

    @redis_cache(ttl=300, version_func=mock_version_key, exclude_kwargs=["db"])
    async def cached_with_version():
        return await test_function("mock_db", owner="test_owner", repo="test_repo")

    # First call should hit function
    result1 = await cached_with_version()
    # Second call should use cache (same version key)
    result2 = await cached_with_version()

    # Results should be the same (cached)
    assert result1 == result2
    # Function should only be called once
    assert call_count == 1


async def test_redis_cache_version_key_missing_params():
    """Test that version_func handles missing parameters gracefully."""
    call_count = 0

    async def test_function(db, user_id: str):
        nonlocal call_count
        call_count += 1
        return f"result_{call_count}_{user_id}"

    def mock_version_key(*args, **kwargs):
        # This function looks for owner/repo but they don't exist
        owner = kwargs.get("owner")
        repo = kwargs.get("repo")
        if owner and repo:
            return f"repo_v={hash(f'{owner}/{repo}')}"
        return ""  # Return empty string when params missing

    @redis_cache(ttl=300, version_func=mock_version_key, exclude_kwargs=["db"])
    async def cached_without_repo_params():
        return await test_function("mock_db", user_id="123")

    # Should work normally even though version_func returns empty string
    result1 = await cached_without_repo_params()
    result2 = await cached_without_repo_params()  # Should be cached

    # Results should be the same (cached works without version suffix)
    assert result1 == result2
    # Function should only be called once
    assert call_count == 1


async def test_redis_cache_version_key_exception():
    """Test that version func exceptions are handled gracefully."""

    def failing_version_key(*args, **kwargs):
        raise ValueError("Version key failed")

    @redis_cache(ttl=60, version_func=failing_version_key)
    async def cached_func_with_failing_version_key(value: str) -> str:
        return f"result_{value}"

    # Should work despite version func failure
    result = await cached_func_with_failing_version_key("test")
    assert result == "result_test"


async def test_redis_cache_with_async_version_key():
    """Test that async version func functions work correctly."""

    async def async_version_key(*args, **kwargs) -> str:
        # Simulate async work
        await asyncio.sleep(0.01)
        value = kwargs.get("value") or args[0] if args else "default"
        return f"async_v={hash(value)}"

    @redis_cache(ttl=60, version_func=async_version_key)
    async def cached_func_with_async_version_key(value: str) -> str:
        return f"result_{value}"

    # First call should cache with async version func
    result1 = await cached_func_with_async_version_key("test")
    assert result1 == "result_test"

    # Second call should use cached result
    result2 = await cached_func_with_async_version_key("test")
    assert result2 == "result_test"

    # Different value should not use cache
    result3 = await cached_func_with_async_version_key("other")
    assert result3 == "result_other"


async def test_update_redis_cache_version_key():
    """Test that update_redis_cache_version_key works correctly."""
    from unittest.mock import AsyncMock, patch

    from app.memoize import update_redis_cache_version_key

    def mock_version_func(*args, **kwargs) -> str:
        owner = kwargs.get("owner", "test_owner")
        repo = kwargs.get("repo", "test_repo")
        return f"repo_v={hash(f'{owner}/{repo}')}"

    async def dummy_function(owner: str, repo: str) -> str:
        return f"result_{owner}_{repo}"

    # Mock Redis to avoid connection issues in tests
    with patch("app.memoize.aredis_caching_pool") as mock_pool:
        mock_redis = AsyncMock()
        mock_pool.return_value.__aenter__.return_value = mock_redis
        mock_redis.delete.return_value = None

        # Test clearing cache with version - should not error
        await update_redis_cache_version_key(
            dummy_function,
            mock_version_func,
            exclude_kwargs=["db"],
            owner="test_owner",
            repo="test_repo",
        )

        # Verify delete was called
        assert mock_redis.delete.call_count == 1
