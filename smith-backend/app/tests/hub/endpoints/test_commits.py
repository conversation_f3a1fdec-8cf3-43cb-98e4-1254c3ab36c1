import asyncio
import random
import string
import uuid

import asyncpg
import orj<PERSON>
import pytest
from httpx import AsyncClient

from app.api.auth import AuthInfo
from app.config import settings
from app.hub.crud.commits import _rewrite_ids
from app.tests.utils import fresh_tenant_client


async def test_commit_private_repo(
    http_tenant_one: AsyncClient,
    http_tenant_one_read_only: AsyncClient,
    private_repo_tenant_one: dict,
    manifest_one: dict,
    manifest_two: dict,
    manifest_not_implemented: dict,
    manifest_bad_namespace: dict,
) -> None:
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/"
    )
    assert response.status_code == 200
    assert response.json() == {"commits": [], "total": 0}

    # Create a commit with non-existing parent
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": "abcdefgh",
            "manifest": manifest_one,
        },
    )
    assert response.status_code == 409  # Should be 409 because parent doesn't exist

    # Create a commit with not implemented manifest
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": None,
            "manifest": manifest_not_implemented,
        },
    )
    assert response.status_code == 400

    # create a commit with bad namespace
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": None,
            "manifest": manifest_bad_namespace,
        },
    )
    assert response.status_code == 400

    # Create two first commits in parallel
    response_one, response_two = await asyncio.gather(
        http_tenant_one.post(
            f"/commits/{private_repo_tenant_one['full_name']}",
            json={
                "parent_commit": None,
                "manifest": manifest_one,
            },
        ),
        http_tenant_one.post(
            f"/commits/{private_repo_tenant_one['full_name']}",
            json={
                "parent_commit": None,
                "manifest": manifest_one,
            },
        ),
    )
    assert {response_one.status_code, response_two.status_code} == {
        200,
        409,
    }, "One of the commits should succeed, the other should fail"
    commit_1 = (
        response_one.json() if response_one.status_code == 200 else response_two.json()
    )
    assert commit_1["commit"]["repo_id"] == private_repo_tenant_one["id"]
    assert commit_1["commit"]["parent_id"] is None

    # Get the commit contents as tenant one
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/{commit_1['commit']['commit_hash']}"
    )
    assert response.status_code == 200
    assert response.json()["commit_hash"] == commit_1["commit"]["commit_hash"]
    commit_1_manifest = response.json()["manifest"]
    assert commit_1_manifest in [
        manifest_one,
        manifest_two,
    ], (
        "Parent manifest should be one of the two manifests, we don't know which one since the order is not guaranteed"
    )

    # Get the repo and check view and download count
    response = await http_tenant_one.get(
        f"/repos/{private_repo_tenant_one['full_name']}"
    )
    assert response.status_code == 200
    assert response.json()["repo"]["num_downloads"] == 1
    assert response.json()["repo"]["num_views"] == 0

    # List commits
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/"
    )
    assert response.status_code == 200
    assert response.json()["total"] == 1
    assert (
        response.json()["commits"][0]["commit_hash"]
        == commit_1["commit"]["commit_hash"]
    )
    assert response.json()["commits"][0]["num_downloads"] == 1
    assert response.json()["commits"][0]["num_views"] == 0

    # get the latest commit
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/latest"
    )
    assert response.status_code == 200
    assert response.json()["commit_hash"] == commit_1["commit"]["commit_hash"]

    # Get the repo and check download count
    response = await http_tenant_one.get(
        f"/repos/{private_repo_tenant_one['full_name']}"
    )
    assert response.status_code == 200
    assert response.json()["repo"]["num_downloads"] == 2
    assert response.json()["repo"]["num_views"] == 0
    assert (
        response.json()["repo"]["last_commit_hash"] == commit_1["commit"]["commit_hash"]
    )

    # Create new commit with parent
    commit_2_manifest = manifest_two
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": commit_1["commit"]["commit_hash"],
            "manifest": commit_2_manifest,
        },
    )
    assert response.status_code == 200
    commit_2 = response.json()
    assert commit_2["commit"]["repo_id"] == private_repo_tenant_one["id"]
    assert commit_2["commit"]["parent_id"] == commit_1["commit"]["id"]
    assert commit_2["commit"]["parent_commit_hash"] == commit_1["commit"]["commit_hash"]

    # List the commits
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/"
    )
    assert response.status_code == 200
    assert response.json()["total"] == 2
    assert (
        response.json()["commits"][0]["commit_hash"]
        == commit_2["commit"]["commit_hash"]
    )
    assert response.json()["commits"][0]["num_downloads"] == 0
    assert response.json()["commits"][0]["num_views"] == 0
    assert (
        response.json()["commits"][1]["commit_hash"]
        == commit_1["commit"]["commit_hash"]
    )
    assert response.json()["commits"][1]["num_downloads"] == 2
    assert response.json()["commits"][1]["num_views"] == 0

    # get the latest commit
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/latest"
    )
    assert response.status_code == 200
    assert response.json()["commit_hash"] == commit_2["commit"]["commit_hash"]

    # Get the second commit's contents as tenant one
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/{commit_2['commit']['commit_hash']}?is_view=true"
    )
    assert response.status_code == 200
    assert response.json()["commit_hash"] == commit_2["commit"]["commit_hash"]
    assert response.json()["manifest"] == commit_2_manifest

    # List the commits, updated download count
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/"
    )
    assert response.status_code == 200
    assert response.json()["total"] == 2
    assert (
        response.json()["commits"][0]["commit_hash"]
        == commit_2["commit"]["commit_hash"]
    )
    assert response.json()["commits"][0]["num_downloads"] == 1
    assert response.json()["commits"][0]["num_views"] == 1
    assert (
        response.json()["commits"][1]["commit_hash"]
        == commit_1["commit"]["commit_hash"]
    )
    assert response.json()["commits"][1]["num_downloads"] == 2
    assert response.json()["commits"][1]["num_views"] == 0

    # Get the repo and check download count
    response = await http_tenant_one.get(
        f"/repos/{private_repo_tenant_one['full_name']}"
    )
    assert response.status_code == 200
    assert response.json()["repo"]["num_downloads"] == 3
    assert response.json()["repo"]["num_views"] == 1
    assert (
        response.json()["repo"]["last_commit_hash"] == commit_2["commit"]["commit_hash"]
    )

    # Try to create another commit off of the first
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": commit_1["commit"]["commit_hash"],
            "manifest": manifest_one,
        },
    )
    assert response.status_code == 409

    # Try to create two commits off of the second
    response_one, response_two = await asyncio.gather(
        http_tenant_one.post(
            f"/commits/{private_repo_tenant_one['full_name']}",
            json={
                "parent_commit": commit_2["commit"]["commit_hash"],
                "manifest": manifest_one,
            },
        ),
        http_tenant_one.post(
            f"/commits/{private_repo_tenant_one['full_name']}",
            json={
                "parent_commit": commit_2["commit"]["commit_hash"],
                "manifest": manifest_one,
            },
        ),
    )
    assert {response_one.status_code, response_two.status_code} == {
        200,
        409,
    }, "One of the commits should succeed, the other should fail"
    response = response_one if response_one.status_code == 200 else response_two
    commit_3 = response.json()
    assert commit_3["commit"]["repo_id"] == private_repo_tenant_one["id"]
    assert commit_3["commit"]["parent_id"] == commit_2["commit"]["id"]
    assert commit_3["commit"]["parent_commit_hash"] == commit_2["commit"]["commit_hash"]

    # Try to commit MANIFEST again
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": commit_3["commit"]["commit_hash"],
            "manifest": manifest_one,
        },
    )
    # should fail because manifest is same
    assert response.status_code == 409

    # Try MANIFEST_TWO - should work
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": commit_3["commit"]["commit_hash"],
            "manifest": manifest_two,
        },
    )
    assert response.status_code == 200
    commit_4 = response.json()
    assert commit_4["commit"]["repo_id"] == private_repo_tenant_one["id"]
    assert commit_4["commit"]["parent_id"] == commit_3["commit"]["id"]
    assert commit_4["commit"]["parent_commit_hash"] == commit_3["commit"]["commit_hash"]

    if settings.AUTH_TYPE != "none":
        # Read only 403
        response = await http_tenant_one_read_only.post(
            f"/commits/{private_repo_tenant_one['full_name']}",
            json={
                "parent_commit": commit_3["commit"]["commit_hash"],
                "manifest": manifest_two,
            },
        )
        assert response.status_code == 403


# Test access as tenant 2
@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_commit_private_repo_tenant_two(
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    private_repo_tenant_one: dict,
) -> None:
    # Confirm accessible as tenant one
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/"
    )
    assert response.status_code == 200

    # List the commit contents as tenant two
    response = await http_tenant_two.get(
        f"/commits/{private_repo_tenant_one['full_name']}/"
    )
    assert response.status_code == 404


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("as_runnable_sequence", [False, True])
async def test_commit_playground_prompt(
    http_tenant_one: AsyncClient,
    private_repo_tenant_one: dict,
    manifest_playground_prompt: dict,
    as_runnable_sequence: bool,
) -> None:
    if as_runnable_sequence:
        manifest_playground_prompt["id"] = [
            "langchain",
            "schema",
            "runnable",
            "RunnableSequence",
        ]
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": None,
            "manifest": manifest_playground_prompt,
        },
    )
    assert response.status_code == 200, response.text
    commit_1 = response.json()
    assert commit_1["commit"]["repo_id"] == private_repo_tenant_one["id"]
    assert commit_1["commit"]["parent_id"] is None

    rewritten_manifest_playground_prompt = _rewrite_ids(manifest_playground_prompt)

    # Get the commit and include the model, we expect a runnable sequence
    get_response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/latest",
        params={"get_examples": True, "is_view": False, "include_model": True},
    )
    assert get_response.status_code == 200, get_response.text
    res_manifest = get_response.json()["manifest"]
    assert res_manifest["id"] == ["langchain", "schema", "runnable", "RunnableSequence"]
    assert (
        res_manifest["kwargs"]["first"]
        == rewritten_manifest_playground_prompt["kwargs"]["first"]
    )
    assert (
        res_manifest["kwargs"]["last"]
        == rewritten_manifest_playground_prompt["kwargs"]["last"]
    )

    # Get the commit and don't include the model, we expect just the prompt manifest
    get_response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/latest",
        params={"get_examples": True, "is_view": False, "include_model": False},
    )
    assert get_response.status_code == 200, get_response.text
    assert (
        get_response.json()["manifest"]
        == rewritten_manifest_playground_prompt["kwargs"]["first"]
    )


# Test commit examples
@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_commit_examples(
    http_tenant_one: AsyncClient,
    private_repo_tenant_one: dict,
    manifest_one: dict,
    manifest_two: dict,
    manifest_three: dict,
    tenant_one_run: uuid.UUID,
    tenant_two_run: uuid.UUID,
) -> None:
    # Test commit by tenant one with tenant one's run
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": None,
            "manifest": manifest_one,
            "example_run_ids": [str(tenant_one_run)],
        },
    )
    assert response.status_code == 200, response.text
    commit_1 = response.json()["commit"]

    # test commit by tenant one with tenant two's run (should fail)
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": response.json()["commit"]["commit_hash"],
            "manifest": manifest_one,
            "example_run_ids": [str(tenant_two_run)],
        },
    )
    assert response.status_code == 400

    # test commit by tenant one with tenant one's and tenant two's run (should fail)
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": commit_1["commit_hash"],
            "manifest": manifest_one,
            "example_run_ids": [str(tenant_one_run), str(tenant_two_run)],
        },
    )
    assert response.status_code == 400

    # Test commit by tenant one without example_run_ids field (should work)
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": commit_1["commit_hash"],
            "manifest": manifest_two,
        },
    )
    assert response.status_code == 200, response.text
    commit_2 = response.json()["commit"]

    # Test commit by tenant one with empty example_run_ids field (should work)
    response = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "parent_commit": commit_2["commit_hash"],
            "manifest": manifest_three,
            "example_run_ids": [],
        },
    )
    assert response.status_code == 200

    # test get endpoint get /commits/owner/repo with ?get_examples=true
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/{commit_1['commit_hash']}?get_examples=true"
    )
    assert response.status_code == 200 and response.json()["examples"][0]["id"] == str(
        tenant_one_run
    ), response.text

    # test get endpoint get /commits/owner/repo with empty query params, make sure we don't get example run ids
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/{commit_1['commit_hash']}"
    )
    assert response.status_code == 200 and response.json()["examples"] == [], (
        response.text
    )

    # test get endpoint get /commits/owner/repo with ?get_examples=false, make sure we don't get example run ids
    response = await http_tenant_one.get(
        f"/commits/{private_repo_tenant_one['full_name']}/{commit_1['commit_hash']}?get_examples=false"
    )
    assert response.status_code == 200 and response.json()["examples"] == [], (
        response.text
    )


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_get_public_repo_no_auth(
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client
        # Create a public and private repo
        public_handle = "".join(random.choices(string.ascii_lowercase, k=20))
        response = await aclient.post(
            "/repos/",
            json={"repo_handle": public_handle, "is_public": True, "tags": ["a", "b"]},
        )
        assert response.status_code == 200
        public_repo = response.json()["repo"]

        private_handle = "".join(random.choices(string.ascii_lowercase, k=20))
        response = await aclient.post(
            "/repos/",
            json={
                "repo_handle": private_handle,
                "is_public": False,
                "tags": ["a", "b"],
            },
        )
        assert response.status_code == 200
        private_repo = response.json()["repo"]
    # Get public commit as no auth
    response = await http_no_auth.get(f"/commits/{public_repo['full_name']}")
    assert response.status_code == 200

    # Try to get public commit without handle
    response = await http_no_auth.get(f"/commits/-/{public_repo['repo_handle']}")
    assert response.status_code == 400
    assert response.json()["detail"] == "No prompt owner specified"

    # Try to get private commit as no auth
    response = await http_no_auth.get(f"/commits/{private_repo['full_name']}/")
    assert response.status_code == 404


async def test_commit_field_ordering(
    http_tenant_one: AsyncClient,
) -> None:
    # Create a new repo
    response = await http_tenant_one.post(
        "/repos/",
        json={"repo_handle": "ordering", "is_public": False, "tags": ["a", "b"]},
    )
    assert response.status_code == 200
    repo = response.json()["repo"]
    full_name = repo["full_name"]

    # Create a commit with a manifest that has fields in a different order
    manifest: dict = {
        "lc": 1,
        "type": "constructor",
        "id": ["langsmith", "playground", "PromptPlayground"],
        "kwargs": {
            "first": {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain", "prompts", "chat", "ChatPromptTemplate"],
                "kwargs": {
                    "messages": [
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": [
                                "langchain_core",
                                "prompts",
                                "chat",
                                "SystemMessagePromptTemplate",
                            ],
                            "kwargs": {
                                "prompt": {
                                    "lc": 1,
                                    "type": "constructor",
                                    "id": [
                                        "langchain_core",
                                        "prompts",
                                        "prompt",
                                        "PromptTemplate",
                                    ],
                                    "kwargs": {
                                        "input_variables": [],
                                        "template_format": "f-string",
                                        "template": "You are a chatbot.",
                                    },
                                }
                            },
                        },
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": [
                                "langchain_core",
                                "prompts",
                                "chat",
                                "HumanMessagePromptTemplate",
                            ],
                            "kwargs": {
                                "prompt": {
                                    "lc": 1,
                                    "type": "constructor",
                                    "id": [
                                        "langchain_core",
                                        "prompts",
                                        "prompt",
                                        "PromptTemplate",
                                    ],
                                    "kwargs": {
                                        "input_variables": ["question"],
                                        "template_format": "f-string",
                                        "template": "{question}",
                                    },
                                }
                            },
                        },
                    ],
                    "input_variables": ["question"],
                },
            },
            "last": {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain", "schema", "runnable", "RunnableBinding"],
                "kwargs": {
                    "bound": {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "chat_models", "openai", "ChatOpenAI"],
                        "kwargs": {
                            "openai_api_key": {
                                "id": ["OPENAI_API_KEY"],
                                "lc": 1,
                                "type": "secret",
                            }
                        },
                    },
                    "kwargs": {
                        "tools": [
                            {
                                "type": "function",
                                "function": {
                                    "name": "test_ordering_args",
                                    "description": "test_args",
                                    "parameters": {
                                        "type": "object",
                                        "properties": {
                                            "a": {"type": "string"},
                                            "c": {"type": "string"},
                                            "b": {"type": "string"},
                                        },
                                        "required": ["a", "c", "b"],
                                    },
                                },
                            }
                        ]
                    },
                },
            },
        },
    }

    response = await http_tenant_one.post(
        f"/commits/{full_name}",
        json={
            "parent_commit": None,
            "manifest": manifest,
        },
    )
    assert response.status_code == 200, response.text
    commit_hash = response.json()["commit"]["commit_hash"]

    # Get the commit and check the manifest to check the fields in kwargs.last.kwargs.tools[0].function.parameters.properties are in the correct order
    response = await http_tenant_one.get(
        f"/commits/{full_name}/latest?include_model=true"
    )
    assert response.status_code == 200, response.text
    resp_manifest = response.json()["manifest"]

    assert list(
        resp_manifest["kwargs"]["last"]["kwargs"]["kwargs"]["tools"][0]["function"][
            "parameters"
        ]["properties"].keys()
    ) == ["a", "c", "b"]

    # Create another commit where the tool ordering is different to ensure it doesn't get blocked
    manifest["kwargs"]["last"]["kwargs"]["kwargs"]["tools"][0]["function"][
        "properties"
    ] = {
        "c": {"type": "string"},
        "b": {"type": "string"},
        "a": {"type": "string"},
    }

    response = await http_tenant_one.post(
        f"/commits/{full_name}",
        json={
            "parent_commit": commit_hash,
            "manifest": manifest,
        },
    )
    assert response.status_code == 200, response.text


async def test_hub_commit_inline_trigger(
    auth_tenant_one: AuthInfo,
    db_asyncpg: asyncpg.Connection,
    http_tenant_one: AsyncClient,
) -> None:
    # Create a new repo
    response = await http_tenant_one.post(
        "/repos/",
        json={"repo_handle": "inline_trigger", "is_public": False, "tags": ["a", "b"]},
    )
    assert response.status_code == 200
    repo = response.json()["repo"]
    repo_id = repo["id"]

    # Insert into the run_manifests table
    manifest_id = await db_asyncpg.fetchval(
        "INSERT INTO run_manifests (tenant_id, manifest, display_name) VALUES ($1, $2, $3) RETURNING id",
        auth_tenant_one.tenant_id,
        {"a": 1},
        "test",
    )

    # Create a commit
    hub_commit_id = await db_asyncpg.fetchval(
        "INSERT INTO hub_commits (repo_id, commit_hash, manifest_id) VALUES ($1, $2, $3) RETURNING id",
        repo_id,
        "test",
        manifest_id,
    )

    # Check that the trigger has been executed, manifest and manifest_sha should be set
    row = await db_asyncpg.fetchrow(
        "SELECT manifest, manifest_sha FROM hub_commits WHERE id = $1", hub_commit_id
    )
    assert orjson.loads(row["manifest"]) == {"a": 1}
    assert row["manifest_sha"] is not None


async def test_hub_commit_tags(
    http_tenant_one: AsyncClient, manifest_one: dict, manifest_two: dict
) -> None:
    prompt1 = "testing_commit_tags"

    # Create repo and initial commit
    await http_tenant_one.post(
        "/repos/", json={"repo_handle": prompt1, "is_public": False}
    )
    response = await http_tenant_one.post(
        f"/commits/-/{prompt1}",
        json={"parent_commit": None, "manifest": manifest_one, "example_run_ids": None},
    )
    commit_id_1 = response.json()["commit"]["id"]
    commit_hash_1 = response.json()["commit"]["commit_hash"]

    # Create and verify tag
    tag_name = "test_tag1"
    response = await http_tenant_one.post(
        f"/repos/-/{prompt1}/tags",
        json={"commit_id": str(commit_id_1), "tag_name": tag_name},
    )
    assert response.status_code == 200

    # Fetch by tag and latest, ensure they both point to the same commit
    for endpoint in [
        f"/commits/-/{prompt1}/{tag_name}",
        f"/commits/-/{prompt1}/latest",
        f"/commits/-/{prompt1}/{commit_hash_1[0:8]}",
    ]:
        response = await http_tenant_one.get(endpoint)
        assert (
            response.status_code == 200
            and response.json()["commit_hash"] == commit_hash_1
        )

    # Add second commit and ensure tag stays on original but latest has updated
    response = await http_tenant_one.post(
        f"/commits/-/{prompt1}",
        json={
            "parent_commit": commit_hash_1,
            "manifest": manifest_two,
            "example_run_ids": None,
        },
    )
    assert response.status_code == 200
    commit_hash_2 = response.json()["commit"]["commit_hash"]
    response = await http_tenant_one.get(f"/commits/-/{prompt1}/{tag_name}")
    assert (
        response.status_code == 200 and response.json()["commit_hash"] == commit_hash_1
    )
    response = await http_tenant_one.get(f"/commits/-/{prompt1}/latest")
    assert (
        response.status_code == 200 and response.json()["commit_hash"] == commit_hash_2
    )

    # Add a tag to the first commit that has the same name as the first 8 and first 7 characters of the second commit's hash
    response = await http_tenant_one.post(
        f"/repos/-/{prompt1}/tags",
        json={"commit_id": str(commit_id_1), "tag_name": commit_hash_2[0:8]},
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        f"/repos/-/{prompt1}/tags",
        json={"commit_id": str(commit_id_1), "tag_name": commit_hash_2[0:7]},
    )
    assert response.status_code == 200

    # Fetch by the first 8 of the commit hash and ensure we get the second commit since we're prioritizing commit hash over tag
    response = await http_tenant_one.get(f"/commits/-/{prompt1}/{commit_hash_2[0:8]}")
    assert response.status_code == 200
    assert response.json()["commit_hash"] == commit_hash_2

    # Fetch by the first 7 of the commit hash and ensure we get the first commit since in this case we only check for tags
    response = await http_tenant_one.get(f"/commits/-/{prompt1}/{commit_hash_2[0:7]}")
    assert response.status_code == 200
    assert response.json()["commit_hash"] == commit_hash_1

    # Test non-existent tag
    response = await http_tenant_one.get(f"/commits/-/{prompt1}/non_existent_tag")
    assert response.status_code == 404
