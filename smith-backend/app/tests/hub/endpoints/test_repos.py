import asyncio
from typing import Any

import asyncpg
import pytest
from httpx import AsyncClient

from app.config import settings
from app.tests.utils import fresh_tenant_client, random_lower_string


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_private_repo(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    aclient: AsyncClient,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient_main_tenant = authed_client.client
        repo_name = "test-repo-" + settings.AUTH_TYPE.replace("_", "-")

        # Create a repo as tenant one
        response = await aclient_main_tenant.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": False,
            },
        )
        assert response.status_code == 200
        body = response.json()
        assert body["repo"]["repo_handle"] == repo_name
        assert body["repo"]["is_public"] is False

        # Get repo as tenant one
        response = await aclient_main_tenant.get(f"/repos/{body['repo']['full_name']}")
        assert response.status_code == 200
        assert response.json() == body

        # Get repo as tenant one without handle
        response = await aclient_main_tenant.get(f"/repos/-/{repo_name}")
        assert response.status_code == 200
        assert response.json() == body

        # List repos as tenant one
        response = await aclient_main_tenant.get("/repos/")
        assert response.status_code == 200
        assert body["repo"] in response.json()["repos"]

        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client2:
            aclient_second_tenant = authed_client2.client

            # Get repo as tenant two
            response = await aclient_second_tenant.get(
                f"/repos/{body['repo']['full_name']}"
            )
            assert response.status_code == 404
            assert response.json() == {
                "detail": f"Repo {body['repo']['full_name']} not found"
            }

            # List repos as tenant two
            response = await aclient_second_tenant.get("/repos/")
            assert response.status_code == 200
            assert body["repo"] not in response.json()["repos"]

            # Get repo as public
            response = await aclient.get(f"/repos/{body['repo']['full_name']}")
            assert response.status_code == 404

            # Create another repo with same name as tenant one
            response = await aclient_main_tenant.post(
                "/repos/", json={"repo_handle": repo_name, "is_public": False}
            )
            assert response.status_code == 409


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_public_repo(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    aclient: AsyncClient,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient_main_tenant = authed_client.client
        repo_name = "test-repo-" + settings.AUTH_TYPE.replace("_", "-")

        # Create a repo as tenant one
        response = await aclient_main_tenant.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": True,
            },
        )
        assert response.status_code == 200
        body = response.json()
        assert body["repo"]["repo_handle"] == repo_name
        assert body["repo"]["is_public"] is True

        # Get repo as tenant one
        response = await aclient_main_tenant.get(f"/repos/{body['repo']['full_name']}")
        assert response.status_code == 200
        assert response.json() == body

        # List repos as tenant one
        response = await aclient_main_tenant.get("/repos/")
        assert response.status_code == 200
        assert body["repo"] in response.json()["repos"]

        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client_2:
            aclient_second_tenant = authed_client_2.client
            # Get repo as tenant two
            response = await aclient_second_tenant.get(
                f"/repos/{body['repo']['full_name']}"
            )
            assert response.status_code == 200
            assert response.json() == body

            # List repos as tenant two
            response = await aclient_second_tenant.get("/repos/")
            assert response.status_code == 200
            assert body["repo"] in response.json()["repos"]

            # Get repo as public
            response = await aclient.get(f"/repos/{body['repo']['full_name']}")
            assert response.status_code == 200
            response_json = response.json()
            assert response_json["repo"]["liked_by_auth_user"] is None
            # compare all keys in ["repo"] except "liked_by_auth_user"
            assert {
                k: v
                for k, v in response_json["repo"].items()
                if k != "liked_by_auth_user"
            } == {k: v for k, v in body["repo"].items() if k != "liked_by_auth_user"}

            # Create another repo with same name as tenant one
            response = await aclient_main_tenant.post(
                "/repos/", json={"repo_handle": repo_name, "is_public": False}
            )
            assert response.status_code == 409


@pytest.mark.skipif(settings.AUTH_TYPE != "supabase", reason="needs user_id")
async def test_repo_tenant_stats(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    repo_name = "test-repo-stats-" + settings.AUTH_TYPE.replace("_", "-")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        # Get tenant stats, store number of repos
        response = await aclient.get("/workspaces/current/stats")
        assert response.status_code == 200
        stats = response.json()
        repo_count = stats["repo_count"]

        assert isinstance(repo_count, int)

        # Create a private repo as tenant one
        response = await aclient.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": False,
            },
        )
        assert response.status_code == 200
        body = response.json()
        assert body["repo"]["repo_handle"] == repo_name
        assert body["repo"]["is_public"] is False

        # Get tenant stats, confirm 1 repo more
        response = await aclient.get("/workspaces/current/stats")
        assert response.status_code == 200
        stats = response.json()
        assert stats["repo_count"] == repo_count + 1

        # Create a private repo as tenant one
        response = await aclient.post(
            "/repos/",
            json={
                "repo_handle": repo_name + "-private",
                "is_public": False,
            },
        )
        assert response.status_code == 200

        # Get tenant stats, confirm another repo (+2 including public)
        response = await aclient.get("/workspaces/current/stats")
        assert response.status_code == 200
        stats = response.json()
        assert stats["repo_count"] == repo_count + 2


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_repo_tags(
    db_asyncpg: asyncpg.Connection,
    aclient: AsyncClient,
    use_api_key: bool,
) -> None:
    # only check tags a,b,c because we don't know what other tests have created

    tag_prefix = "test-repo-tags-" + settings.AUTH_TYPE.replace("_", "-")

    def filter_tags(tags: list[dict[str, Any]]) -> list[dict[str, Any]]:
        return [
            {"tag": tag["tag"], "count": tag["count"]}
            for tag in tags
            if tag["tag"].startswith(tag_prefix)
        ]

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, include_read_only=True
    ) as authed_client:
        aclient_main_tenant = authed_client.client
        aclient_main_tenant_read_only = authed_client.read_only_client
        assert aclient_main_tenant_read_only is not None

        repo_name = "test-repo-" + settings.AUTH_TYPE.replace("_", "-")

        # Create a public repo as tenant one
        response = await aclient_main_tenant.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": True,
            },
        )
        assert response.status_code == 200
        body = response.json()
        assert body["repo"]["repo_handle"] == repo_name
        assert body["repo"]["is_public"] is True

        public_repo_tenant_one = body["repo"]

        # Create a private repo as tenant one
        repo_name = "test-repo-private-" + settings.AUTH_TYPE.replace("_", "-")
        response = await aclient_main_tenant.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": False,
            },
        )
        assert response.status_code == 200
        body = response.json()
        assert body["repo"]["repo_handle"] == repo_name
        assert not body["repo"]["is_public"]

        private_repo_tenant_one = body["repo"]

        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client2:
            aclient_second_tenant = authed_client2.client

            # get tags as tenant one, should be empty
            response = await aclient_main_tenant.get("/repos/tags")
            assert response.status_code == 200
            assert filter_tags(response.json()["tags"]) == []

            # set tags on public repo to a,b
            response = await aclient_main_tenant.patch(
                f"/repos/{public_repo_tenant_one['full_name']}",
                json={"tags": [f"{tag_prefix}-a", f"{tag_prefix}-b"]},
            )
            assert response.status_code == 200

            # get tags as tenant one, should be a,b
            response = await aclient_main_tenant.get("/repos/tags")
            assert response.status_code == 200
            assert filter_tags(response.json()["tags"]) == [
                {"tag": f"{tag_prefix}-a", "count": 1},
                {"tag": f"{tag_prefix}-b", "count": 1},
            ]
            # get as tenant two, should be same (public repo)
            response = await aclient_second_tenant.get("/repos/tags")
            assert response.status_code == 200
            assert filter_tags(response.json()["tags"]) == [
                {"tag": f"{tag_prefix}-a", "count": 1},
                {"tag": f"{tag_prefix}-b", "count": 1},
            ]
            # get as public, should be same (public repo)
            response = await aclient.get("/repos/tags")
            assert response.status_code == 200
            assert filter_tags(response.json()["tags"]) == [
                {"tag": f"{tag_prefix}-a", "count": 1},
                {"tag": f"{tag_prefix}-b", "count": 1},
            ]

            # update private repo tags as tenant one
            response = await aclient_main_tenant.patch(
                f"/repos/{private_repo_tenant_one['full_name']}",
                json={"tags": [f"{tag_prefix}-a", f"{tag_prefix}-c"]},
            )
            assert response.status_code == 200

            # get tags as tenant one, should be a=2,b=1,c=1
            response = await aclient_main_tenant.get("/repos/tags")
            assert response.status_code == 200
            assert filter_tags(response.json()["tags"]) == [
                {"tag": f"{tag_prefix}-a", "count": 2},
                {"tag": f"{tag_prefix}-b", "count": 1},
                {"tag": f"{tag_prefix}-c", "count": 1},
            ]
            # get as tenant two, should still be a,b
            response = await aclient_second_tenant.get("/repos/tags")
            assert response.status_code == 200
            assert filter_tags(response.json()["tags"]) == [
                {"tag": f"{tag_prefix}-a", "count": 1},
                {"tag": f"{tag_prefix}-b", "count": 1},
            ]
            # get as public, should still be a,b
            response = await aclient.get("/repos/tags")
            assert response.status_code == 200
            assert filter_tags(response.json()["tags"]) == [
                {"tag": f"{tag_prefix}-a", "count": 1},
                {"tag": f"{tag_prefix}-b", "count": 1},
            ]

            # set repo to private
            response = await aclient_main_tenant.patch(
                f"/repos/{public_repo_tenant_one['full_name']}",
                json={"is_public": False},
            )
            assert response.status_code == 200
            assert response.json()["repo"]["is_public"] is False

            # set repo to public
            response = await aclient_main_tenant.patch(
                f"/repos/{public_repo_tenant_one['full_name']}",
                json={"is_public": True},
            )
            assert response.status_code == 200
            assert response.json()["repo"]["is_public"] is True

            # can't set to public if no repo share permission
            response = await aclient_main_tenant_read_only.patch(
                f"/repos/{public_repo_tenant_one['full_name']}",
                json={"is_public": True},
            )
            assert response.status_code == 403


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_fork_public_repo(
    db_asyncpg: asyncpg.Connection,
    aclient: AsyncClient,
    use_api_key: bool,
    manifest_one: dict,
    manifest_two: dict,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient_main_tenant = authed_client.client
        repo_name = "test-repo-" + settings.AUTH_TYPE.replace("_", "-")
        # Create a repo as tenant one
        response = await aclient_main_tenant.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": True,
            },
        )
        assert response.status_code == 200
        body = response.json()
        assert body["repo"]["repo_handle"] == repo_name
        assert body["repo"]["is_public"]
        public_repo_tenant_one = body["repo"]

        resp = await aclient_main_tenant.post(
            f"/commits/{public_repo_tenant_one['full_name']}",
            json={
                "manifest": manifest_two,
                "parent_commit": None,
            },
        )
        assert resp.status_code == 200
        public_repo_tenant_one_commit_1 = resp.json()["commit"]

        resp = await aclient_main_tenant.post(
            f"/commits/{public_repo_tenant_one['full_name']}",
            json={
                "manifest": manifest_one,
                "parent_commit": public_repo_tenant_one_commit_1["commit_hash"],
            },
        )
        assert resp.status_code == 200
        public_repo_tenant_one_commit_2 = resp.json()["commit"]

        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client2:
            aclient_second_tenant = authed_client2.client
            new_metadata = {
                "repo_handle": "forked-prompt-" + settings.AUTH_TYPE.replace("_", "-"),
                "description": "my desc",
                "tags": ["tag1", "tag2"],
                "is_public": True,
            }
            # Fork repo as tenant two
            response = await aclient_second_tenant.post(
                f"/repos/{public_repo_tenant_one['full_name']}/fork",
                json=new_metadata,
            )
            assert response.status_code == 200
            body = response.json()
            assert body["repo"]["repo_handle"] == new_metadata["repo_handle"]
            assert body["repo"]["is_public"] is True
            assert body["repo"]["num_commits"] == 2
            assert body["repo"]["tags"] == new_metadata["tags"]
            assert (
                body["repo"]["last_commit_hash"]
                == public_repo_tenant_one_commit_2["commit_hash"]
            )
            assert (
                body["repo"]["original_repo_full_name"]
                == public_repo_tenant_one["full_name"]
            )
            assert (
                body["repo"]["upstream_repo_full_name"]
                == public_repo_tenant_one["full_name"]
            )

            owner = public_repo_tenant_one["owner"]
            repo_handle = public_repo_tenant_one["repo_handle"]
            # List my forks as tenant two
            response = await aclient_second_tenant.get(
                f"/repos/?upstream_repo_owner={owner}&upstream_repo_handle={repo_handle}"
            )
            assert response.status_code == 200
            assert response.json()["total"] == 1
            assert response.json()["repos"][0]["full_name"] == body["repo"]["full_name"]

            # Get repo as tenant one
            response = await aclient_main_tenant.get(
                f"/repos/{body['repo']['full_name']}"
            )
            assert response.status_code == 200

            # Get repo as tenant two
            response = await aclient_second_tenant.get(
                f"/repos/{body['repo']['full_name']}"
            )
            assert response.status_code == 200
            assert response.json() == body

            # get commit and make sure equivalent
            response = await aclient_second_tenant.get(
                f"/commits/{body['repo']['full_name']}/{public_repo_tenant_one_commit_2['commit_hash']}"
            )
            assert response.status_code == 200
            assert response.json()["manifest"] == manifest_one

            # get commit 1 and make sure equivalent
            response = await aclient_second_tenant.get(
                f"/commits/{body['repo']['full_name']}/{public_repo_tenant_one_commit_1['commit_hash']}"
            )
            assert response.status_code == 200
            assert response.json()["manifest"] == manifest_two

            # get list of commits and make sure ls_user_id is the same
            original_prompt_commits = await aclient_main_tenant.get(
                f"/commits/{public_repo_tenant_one['full_name']}"
            )
            assert original_prompt_commits.status_code == 200
            original_prompt_commits = original_prompt_commits.json()["commits"]
            forked_prompt_commits = await aclient_second_tenant.get(
                f"/commits/{body['repo']['full_name']}"
            )
            assert response.status_code == 200
            forked_prompt_commits = forked_prompt_commits.json()["commits"]
            # loop through both and make sure the ls_user_id is the same
            for i in range(len(original_prompt_commits)):
                assert (
                    original_prompt_commits[i]["full_name"]
                    == forked_prompt_commits[i]["full_name"]
                )

            # fork the fork, test difference in upstream and original
            new_metadata = {
                "repo_handle": "forked-fork-prompt-"
                + settings.AUTH_TYPE.replace("_", "-"),
                "description": "my desc",
                "tags": ["tag1", "tag2"],
                "is_public": False,
            }
            # Fork repo as tenant one
            response = await aclient_main_tenant.post(
                f"/repos/{body['repo']['full_name']}/fork",
                json=new_metadata,
            )
            assert response.status_code == 200
            body2 = response.json()
            assert body2["repo"]["repo_handle"] == new_metadata["repo_handle"]
            assert body2["repo"]["is_public"] is False
            assert body2["repo"]["num_commits"] == 2
            assert body2["repo"]["tags"] == new_metadata["tags"]
            assert (
                body2["repo"]["last_commit_hash"]
                == public_repo_tenant_one_commit_2["commit_hash"]
            )
            assert (
                body2["repo"]["original_repo_full_name"]
                == public_repo_tenant_one["full_name"]
            )
            assert body2["repo"]["upstream_repo_full_name"] == body["repo"]["full_name"]

            # Get as tenant 2, make sure 404
            response = await aclient_second_tenant.get(
                f"/repos/{body2['repo']['full_name']}"
            )
            assert response.status_code == 404


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_fork_private_repo(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    aclient: AsyncClient,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient_main_tenant = authed_client.client
        repo_name = "test-fork-private" + settings.AUTH_TYPE.replace("_", "-")
        response = await aclient_main_tenant.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": False,
            },
        )
        assert response.status_code == 200
        body = response.json()
        assert body["repo"]["repo_handle"] == repo_name
        assert body["repo"]["is_public"] is False
        new_repo = body["repo"]

    new_metadata = {
        "repo_handle": "forked-private-prompt-" + settings.AUTH_TYPE.replace("_", "-"),
        "description": "my desc",
        "tags": ["tag1", "tag2"],
        "is_public": False,
    }
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client2:
        aclient_second_tenant = authed_client2.client
        # Fork private repo as tenant 2, should 404
        response = await aclient_second_tenant.post(
            f"/repos/{new_repo['full_name']}/fork",
            json=new_metadata,
        )
        assert response.status_code == 404


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_fork_private_repo_same_name(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    aclient: AsyncClient,
) -> None:
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_tenant_handle=False
    ) as authed_client:
        aclient_main_tenant = authed_client.client

        repo_name = "test-repo-no-handle" + settings.AUTH_TYPE.replace("_", "-")
        # create a private repo for tenant_two that has the same name as private_repo_tenant_one
        new_repo = await aclient_main_tenant.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": False,
            },
        )
        assert new_repo.status_code == 200

        async with fresh_tenant_client(
            db_asyncpg, use_api_key, set_tenant_handle=False
        ) as authed_client2:
            aclient_second_tenant = authed_client2.client
            auth_second_tenant = authed_client2.auth

            new_repo = await aclient_second_tenant.post(
                "/repos/",
                json={
                    "repo_handle": repo_name,
                    "is_public": False,
                },
            )
            assert new_repo.status_code == 200

            # fork tenant 2's repo
            response = await aclient_second_tenant.post(
                f"/repos/-/{repo_name}/fork",
                json={
                    "repo_handle": f"forked-{repo_name}",
                    "description": "blah blah blah",
                    "tags": ["tag1", "tag2"],
                    "is_public": False,
                },
            )
            assert response.status_code == 200

            # now get all of the forked repos for the og tenant 2 repo
            assert hasattr(auth_second_tenant, "tenant_id")
            response = await aclient_second_tenant.get(
                f"/repos/?upstream_repo_handle={repo_name}&tenant_id={auth_second_tenant.tenant_id}"
            )
            assert response.status_code == 200
            assert response.json()["total"] == 1


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_search_repos(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    repo_name = "test-repo-search-" + settings.AUTH_TYPE.replace("_", "-")
    repo_name2 = "another-test-" + settings.AUTH_TYPE.replace("_", "-")
    tag_1 = random_lower_string() + "-tag"
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        response = await aclient.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": False,
                "tags": [tag_1],
            },
        )
        assert response.status_code == 200

        response = await aclient.post(
            "/repos/",
            json={
                "repo_handle": repo_name2,
                "is_public": False,
            },
        )
        assert response.status_code == 200

        # Search for repo as tenant one
        # Should only match the "another-test" repo name
        response = await aclient.get(f"/repos/?query={repo_name2[0:4]}")
        assert response.status_code == 200
        assert response.json()["total"] == 1
        assert response.json()["repos"][0]["repo_handle"] == repo_name2

        # Search for repo as tenant one with query term "test" which is in both repo handles
        # Sort by updated_at since these two repos were most recently created
        response = await aclient.get(
            f"/repos/?query={repo_name[0:4]}&sort_field=updated_at&sort_direction=desc"
        )
        assert response.status_code == 200
        assert (
            response.json()["total"] >= 2
        )  # Should match both repos and likely more that were created before
        repo_handles = [repo["repo_handle"] for repo in response.json()["repos"]]
        assert repo_name in repo_handles
        assert repo_name2 in repo_handles

        # Search for repo as tenant one with matching tag for query term
        response = await aclient.get(f"/repos/?query={tag_1}")
        assert response.status_code == 200
        assert response.json()["total"] == 1
        repo_handles = [repo["repo_handle"] for repo in response.json()["repos"]]
        assert repo_name in repo_handles
        assert repo_name2 not in repo_handles


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_delete_repo(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client
        repo_name = "test-delete-repo-" + settings.AUTH_TYPE.replace("_", "-")
        response = await aclient.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": False,
            },
        )
        assert response.status_code == 200

        # Delete repo as tenant one
        response = await aclient.delete(f"/repos/-/{repo_name}")
        assert response.status_code == 200

        # Get repo as tenant one, should be 404
        response = await aclient.get(f"/repos/-/{repo_name}")
        assert response.status_code == 404


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_get_public_repo_no_auth(
    http_no_auth: AsyncClient,
    public_repo_tenant_one: dict,
    private_repo_tenant_one: dict,
) -> None:
    # Get public repo as no auth
    response = await http_no_auth.get(f"/repos/{public_repo_tenant_one['full_name']}")
    assert response.status_code == 200
    assert response.json()["repo"]["full_name"] == public_repo_tenant_one["full_name"]

    # Try to get public repo without handle
    response = await http_no_auth.get(
        f"/repos/-/{public_repo_tenant_one['repo_handle']}"
    )
    assert response.status_code == 400, response.detail == "No prompt owner specified"

    # Try to get private repo as no auth
    response = await http_no_auth.get(f"/repos/{private_repo_tenant_one['full_name']}")
    assert response.status_code == 404


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_repo_taggings(
    db_asyncpg: asyncpg.Connection,
    aclient: AsyncClient,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient_main_tenant = authed_client.client
        repo_name = "test-repo-" + settings.AUTH_TYPE.replace("_", "-")
        # Create a repo as tenant one
        response = await aclient_main_tenant.post(
            "/repos/",
            json={
                "repo_handle": repo_name,
                "is_public": True,
            },
        )
        assert response.status_code == 200
        body = response.json()
        assert body["repo"]["repo_handle"] == repo_name
        assert body["repo"]["is_public"]
        public_repo_tenant_one = body["repo"]

        key1 = random_lower_string()
        response = await aclient_main_tenant.post(
            "/workspaces/current/tag-keys",
            json={"key": key1},
        )

        assert response.status_code == 200
        key1_id = response.json()["id"]

        value1 = random_lower_string()
        response = await aclient_main_tenant.post(
            f"/workspaces/current/tag-keys/{key1_id}/tag-values",
            json={"value": value1},
        )

        assert response.status_code == 200
        value1_id = response.json()["id"]

        value2 = random_lower_string()
        response = await aclient_main_tenant.post(
            f"/workspaces/current/tag-keys/{key1_id}/tag-values",
            json={"value": value2},
        )

        assert response.status_code == 200
        value2_id = response.json()["id"]

        # create three repos concurrently
        repo_names = [random_lower_string() for _ in range(3)]
        _, _, repo3_name = repo_names

        responses = await asyncio.gather(
            *[
                aclient_main_tenant.post(
                    "/repos/",
                    json={
                        "repo_handle": repo_name,
                        "is_public": False,
                    },
                )
                for repo_name in repo_names
            ]
        )

        assert len(responses) == 3
        assert all(response.status_code == 200 for response in responses)
        repo1_id, repo2_id, repo3_id = [
            response.json()["repo"]["id"] for response in responses
        ]

        # tag the repos
        responses_taggings = await asyncio.gather(
            aclient_main_tenant.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "prompt",
                    "resource_id": repo1_id,
                    "tag_value_id": value1_id,
                },
            ),
            aclient_main_tenant.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "prompt",
                    "resource_id": repo1_id,
                    "tag_value_id": value2_id,
                },
            ),
            aclient_main_tenant.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "prompt",
                    "resource_id": repo2_id,
                    "tag_value_id": value1_id,
                },
            ),
        )

        assert all(response.status_code == 200 for response in responses_taggings)

        # fetch the prompts without any tag_value_id in the query
        response = await aclient_main_tenant.get(
            "/repos/",
        )
        assert response.status_code == 200
        assert len(response.json()["repos"]) >= 3
        assert any(repo["id"] == repo1_id for repo in response.json()["repos"])
        assert any(repo["id"] == repo2_id for repo in response.json()["repos"])
        assert any(repo["id"] == repo3_id for repo in response.json()["repos"])

        # fetch the prompts with tag_value_id
        response = await aclient_main_tenant.get(
            f"/repos/?tag_value_id={value1_id}",
        )
        assert response.status_code == 200
        assert len(response.json()["repos"]) == 2
        assert any(repo["id"] == repo1_id for repo in response.json()["repos"])
        assert any(repo["id"] == repo2_id for repo in response.json()["repos"])

        response = await aclient_main_tenant.get(
            f"/repos/?tag_value_id={value2_id}",
        )
        assert response.status_code == 200
        assert len(response.json()["repos"]) == 1
        assert any(repo["id"] == repo1_id for repo in response.json()["repos"])

        # fetch the prompts with tag_value_id and tag_value_id
        response = await aclient_main_tenant.get(
            f"/repos/?tag_value_id={value1_id}&tag_value_id={value2_id}",
        )
        assert response.status_code == 200
        assert len(response.json()["repos"]) == 1
        assert any(repo["id"] == repo1_id for repo in response.json()["repos"])

        # fetch only public repos with authed tenant
        response = await aclient_main_tenant.get(
            f"/repos/?tag_value_id={value1_id}&tag_value_id={value2_id}&is_public=true",
        )

        assert response.status_code == 200
        assert len(response.json()["repos"]) == 0

        # the tag_value_ids should have no effect on the query with no-auth
        response = await aclient.get(
            f"/repos/?tag_value_id={value1_id}&tag_value_id={value2_id}&is_public=true",
        )
        assert response.status_code == 200
        assert len(response.json()["repos"]) >= 1
        assert not any(repo["id"] == repo1_id for repo in response.json()["repos"])
        assert any(
            public_repo_tenant_one["id"] == repo["id"]
            for repo in response.json()["repos"]
        )
