from typing import Optional

import pytest
from httpx import AsyncClient

from app import config

pytestmark = pytest.mark.anyio


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def _create_tag(
    http_tenant: AsyncClient,
    tag_name: str,
    owner: str,
    repo_name: str,
    commit_id: str,
    repo_id: str,
    status_code: int = 200,
    error_msg: Optional[str] = None,
) -> None:
    response = await http_tenant.post(
        f"/repos/{owner}/{repo_name}/tags",
        json={
            "tag_name": tag_name,
            "commit_id": commit_id,
        },
    )

    assert response.status_code == status_code, (
        f"Expected status code {status_code}, got {response.status_code}"
    )
    if error_msg:
        response_detail = response.json()["detail"]
        assert (response_detail == error_msg) or (error_msg in response_detail), (
            f"Expected '{error_msg}' to match or be contained in '{response_detail}'"
        )
        return
    tag = response.json()
    assert tag["tag_name"] == tag_name
    assert tag["commit_id"] == commit_id
    assert tag["repo_id"] == repo_id


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_tag_crud(
    http_tenant_one: AsyncClient,
    private_repo_tenant_one: dict,
    private_repo_tenant_one_commit: dict,
    manifest_two: dict,
) -> None:
    """Test that a tag can be created."""
    # POST
    owner = private_repo_tenant_one["owner"]
    repo = private_repo_tenant_one["repo_handle"]

    # test error on non alphanumeric tag name
    await _create_tag(
        http_tenant_one,
        "tag_name_1!",
        owner,
        repo,
        private_repo_tenant_one_commit["id"],
        private_repo_tenant_one["id"],
        status_code=400,
        error_msg="Commit tag name must only contain letters, numbers, hyphens and underscores",
    )

    # get tags and assert there are 0 to make sure the tag wasn't created
    response = await http_tenant_one.get(f"/repos/{owner}/{repo}/tags")
    assert response.status_code == 200
    assert len(response.json()) == 0

    tag_name = "create_tag_1"
    await _create_tag(
        http_tenant_one,
        tag_name,
        owner,
        repo,
        private_repo_tenant_one_commit["id"],
        private_repo_tenant_one["id"],
    )

    # test error when tag already exists
    await _create_tag(
        http_tenant_one,
        tag_name,
        owner,
        repo,
        private_repo_tenant_one_commit["id"],
        private_repo_tenant_one["id"],
        status_code=409,
        error_msg=f"Tag {tag_name} already exists on commit",
    )

    # test error when commit does not exist
    nonexistent_commit_id = "00000000-0000-0000-0000-000000000000"
    await _create_tag(
        http_tenant_one,
        tag_name,
        owner,
        repo,
        nonexistent_commit_id,
        private_repo_tenant_one["id"],
        status_code=404,
        error_msg="Commit or repo not found",
    )

    # test error when repo does not exist
    nonexistent_repo = "nonexistent_repo"
    await _create_tag(
        http_tenant_one,
        tag_name,
        owner,
        nonexistent_repo,
        private_repo_tenant_one_commit["id"],
        private_repo_tenant_one["id"],
        status_code=404,
        error_msg="Commit or repo not found",
    )

    # GET

    # get tag
    response = await http_tenant_one.get(f"/repos/{owner}/{repo}/tags/{tag_name}")
    assert response.status_code == 200
    tag = response.json()
    assert tag["tag_name"] == tag_name
    assert tag["commit_id"] == private_repo_tenant_one_commit["id"]
    assert tag["repo_id"] == private_repo_tenant_one["id"]

    # test error when tag does not exist
    nonexistent_tag = "nonexistent_tag"
    response = await http_tenant_one.get(
        f"/repos/{owner}/{repo}/tags/{nonexistent_tag}"
    )
    assert response.status_code == 404
    assert response.json()["detail"] == f"Tag {nonexistent_tag} not found"

    # create tag 2
    tag_name2 = "create_tag_2"
    await _create_tag(
        http_tenant_one,
        tag_name2,
        owner,
        repo,
        private_repo_tenant_one_commit["id"],
        private_repo_tenant_one["id"],
    )

    # GET ALL

    # get all tags
    response = await http_tenant_one.get(f"/repos/{owner}/{repo}/tags")
    assert response.status_code == 200
    tags = response.json()
    assert len(tags) >= 2

    expected_tags = [tag_name, tag_name2]
    for expected_tag in expected_tags:
        tag = next((tag for tag in tags if tag["tag_name"] == expected_tag), None)
        assert tag is not None, f"Tag {expected_tag} not found"
        assert tag["commit_id"] == private_repo_tenant_one_commit["id"]
        assert tag["repo_id"] == private_repo_tenant_one["id"]

    # add new commit to repo
    response = await http_tenant_one.post(
        f"/commits/{owner}/{repo}",
        json={
            "manifest": manifest_two,
            "parent_commit": private_repo_tenant_one_commit["commit_hash"],
        },
    )
    assert response.status_code == 200
    new_commit_id = response.json()["commit"]["id"]

    # PATCH

    # update tag
    response = await http_tenant_one.patch(
        f"/repos/{owner}/{repo}/tags/{tag_name}",
        json={"commit_id": new_commit_id},
    )
    assert response.status_code == 200
    tag = response.json()
    assert tag["tag_name"] == tag_name
    assert tag["commit_id"] == new_commit_id
    assert tag["repo_id"] == private_repo_tenant_one["id"]

    # get tag and confirm it was updated
    response = await http_tenant_one.get(f"/repos/{owner}/{repo}/tags/{tag_name}")
    assert response.status_code == 200
    tag = response.json()
    assert tag["tag_name"] == tag_name
    assert tag["commit_id"] == new_commit_id
    assert tag["repo_id"] == private_repo_tenant_one["id"]

    # try to update to the same commit, expect 200
    response = await http_tenant_one.patch(
        f"/repos/{owner}/{repo}/tags/{tag_name}",
        json={"commit_id": new_commit_id},
    )
    assert response.status_code == 200

    # test error when tag does not exist
    nonexistent_tag = "nonexistent_tag"
    response = await http_tenant_one.patch(
        f"/repos/{owner}/{repo}/tags/{nonexistent_tag}",
        json={"commit_id": new_commit_id},
    )
    assert response.status_code == 404
    assert response.json()["detail"] == f"Tag {nonexistent_tag} not found"

    # DELETE

    response = await http_tenant_one.delete(f"/repos/{owner}/{repo}/tags/{tag_name}")
    assert response.status_code == 200

    # try to get tag, expect 404
    response = await http_tenant_one.get(f"/repos/{owner}/{repo}/tags/{tag_name}")
    assert response.status_code == 404
    assert response.json()["detail"] == f"Tag {tag_name} not found"

    # test error when tag does not exist
    nonexistent_tag = "nonexistent_tag"
    response = await http_tenant_one.delete(
        f"/repos/{owner}/{repo}/tags/{nonexistent_tag}"
    )
    assert response.status_code == 404
    assert response.json()["detail"] == f"Tag {nonexistent_tag} not found"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_tag_auth_crud(
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    private_repo_tenant_one: dict,
    private_repo_tenant_one_commit: dict,
    manifest_three: dict,
) -> None:
    # test that another tenant cannot create a tag
    tag_name = "create_tag_auth_1"
    owner = private_repo_tenant_one["owner"]
    repo = private_repo_tenant_one["repo_handle"]

    # create tag for tenant 1
    await _create_tag(
        http_tenant_one,
        tag_name,
        owner,
        repo,
        private_repo_tenant_one_commit["id"],
        private_repo_tenant_one["id"],
    )

    # POST

    # try to create tag for tenant 2 using tenant 1 prompt & commit
    await _create_tag(
        http_tenant_two,
        tag_name,
        owner,
        repo,
        private_repo_tenant_one_commit["id"],
        private_repo_tenant_one["id"],
        status_code=404,
        error_msg="Commit or repo not found",
    )

    # GET

    # test that another tenant cannot get the tag
    response = await http_tenant_two.get(f"/repos/{owner}/{repo}/tags/{tag_name}")
    assert response.status_code == 404

    # GET ALL

    # test that another tenant cannot get the tags
    response = await http_tenant_two.get(f"/repos/{owner}/{repo}/tags")
    assert response.status_code == 200
    assert len(response.json()) == 0

    # PATCH

    # add new commit to repo
    response = await http_tenant_one.post(
        f"/commits/{owner}/{repo}",
        json={
            "manifest": manifest_three,
            "parent_commit": private_repo_tenant_one_commit["commit_hash"],
        },
    )
    assert response.status_code == 200
    new_commit_id = response.json()["commit"]["id"]

    # test that another tenant cannot update the tag
    response = await http_tenant_two.patch(
        f"/repos/{owner}/{repo}/tags/{tag_name}",
        json={"commit_id": new_commit_id},
    )
    assert response.status_code == 404

    # DELETE
    response = await http_tenant_two.delete(f"/repos/{owner}/{repo}/tags/{tag_name}")
    assert response.status_code == 404
