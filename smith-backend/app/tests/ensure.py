from dataclasses import dataclass, field

from asyncpg import UniqueViolationError
from lc_database.database import asyncpg_conn

import app.config
from app.retry import retry_asyncpg
from app.schemas import AuthProvider, ProvisioningMethod


@dataclass
class DecodedUserMetadata:
    avatar_url: str | None = None


@dataclass
class DecodedUserInfo:
    sub: str
    id: str
    email: str
    full_name: str | None = None
    user_metadata: DecodedUserMetadata = field(default_factory=DecodedUserMetadata)
    provider: str | None = None
    saml_provider_id: str | None = None
    provisioning_method: str | None = None


@retry_asyncpg
async def ensure_user(user_info: DecodedUserInfo) -> dict | None:
    async with asyncpg_conn() as conn:
        # Migrate from uuid5(sub) to uuid5(email)
        if app.config.settings.AUTH_TYPE == "oauth":
            old_user = await conn.fetchrow(
                "SELECT id FROM users WHERE email = $1", user_info.email.lower()
            )
            if old_user and old_user["id"] != user_info.id:
                await conn.execute(
                    "UPDATE users SET id = $1 WHERE id = $2",
                    user_info.id,
                    old_user["id"],
                )
                await conn.execute(
                    "UPDATE identities SET user_id = $1 WHERE user_id = $2",
                    user_info.id,
                    old_user["id"],
                )
        provider = (
            AuthProvider.supabase_non_sso.value
            if app.config.settings.AUTH_TYPE == "supabase"
            else AuthProvider.email.value
            if app.config.settings.AUTH_TYPE == "mixed"
            else AuthProvider.oidc.value
            if app.config.settings.AUTH_TYPE == "oauth"
            else None
        )
        selected_provider = user_info.provider or provider
        provider_user_id_clause = (
            "NULL" if provider in [None, "email"] else "inserted_user.id"
        )
        conflict_clause = (
            "(ls_user_id, saml_provider_id)"
            if selected_provider == AuthProvider.supabase_sso
            else "(ls_user_id, provider_user_id) WHERE provider <> 'supabase:sso'"
        )
        provisioning_method = (
            user_info.provisioning_method
            if user_info.provisioning_method
            else ProvisioningMethod.saml_jit.value
            if selected_provider == AuthProvider.supabase_sso
            else None
        )
        try:
            result = await conn.fetchrow(
                f"""
                WITH inserted_user AS (
                    INSERT INTO users (id, email, full_name, avatar_url)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (id) DO UPDATE
                    SET
                        email = $2,
                        full_name = coalesce($3, users.full_name),
                        avatar_url = coalesce($4, users.avatar_url),
                        updated_at = NOW()
                    RETURNING *
                ),
                inserted_provider_user AS (
                    INSERT INTO provider_users (provider, ls_user_id, saml_provider_id, provider_user_id, email, full_name, provisioning_method)
                    SELECT
                        $5 as provider,
                        inserted_user.ls_user_id,
                        $6 as saml_provider_id,
                        {provider_user_id_clause} as provider_user_id,
                        $2 as email,
                        $3 as full_name,
                        $7 as provisioning_method
                    FROM inserted_user
                    ON CONFLICT {conflict_clause} DO UPDATE
                    SET email = coalesce($2, provider_users.email),
                        full_name = coalesce($3, provider_users.full_name),
                        updated_at = NOW()
                )
                SELECT * FROM inserted_user;
                """,
                user_info.id,
                user_info.email.lower(),
                user_info.full_name,
                user_info.user_metadata.avatar_url,
                user_info.provider or provider,
                user_info.saml_provider_id,
                provisioning_method,
            )
        except UniqueViolationError:
            # fetch the user if a parallel test created
            result = await conn.fetchrow(
                "SELECT * FROM users WHERE email = $1 OR id = $2",
                user_info.email.lower(),
                user_info.id,
            )
        return dict(result) if result else None
