"""Test correct functionality of api keys."""

from datetime import datetime, timedelta, timezone
from uuid import uuid4

import pytest
from asyncpg import Connection
from host.models import deployment_type, host_metadata_crud, projects
from httpx import AsyncClient

from app import config, schemas
from app.api.auth import AuthInfo
from app.api.auth.api_keys import verify_key_signature
from app.models.api_keys.crud import create_api_key


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_api_keys_crud(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
) -> None:
    api_key = await create_api_key(
        auth_tenant_one, schemas.APIKeyCreateRequest(description="test")
    )
    assert verify_key_signature(api_key.key)
    assert api_key.last_used_at is None
    assert api_key.expires_at is None

    # Verify you can create another API key, then delete it
    api_key_temp = await create_api_key(
        auth_tenant_one, schemas.APIKeyCreateRequest(description="test 2")
    )
    assert verify_key_signature(api_key_temp.key)
    res = await aclient.delete(
        f"/api-key/{api_key_temp.id}",
        headers=tenant_one_headers,
    )
    assert res.status_code == 200

    # Verify you can use the api key to authenticate
    get_response = await aclient.get("/sessions", headers={"X-Api-Key": api_key.key})
    assert get_response.status_code == 200

    # Ensure that an API key exists on list (we have no get API)
    get_response = await aclient.get(
        "/api-key",
        headers=tenant_one_headers,
    )
    key = next(key for key in get_response.json() if key["id"] == str(api_key.id))
    assert key is not None
    assert key["description"] == "test"
    assert key["last_used_at"]
    assert key["expires_at"] is None

    # Verify that you cant use an org:manage api
    post_response = await aclient.post(
        "/service-accounts",
        headers={"X-Api-Key": api_key.key},
        json={"name": "test"},
    )
    assert post_response.status_code == 403, post_response.text

    # Ensure that a client can directly delete the API key
    del_response = await aclient.delete(
        f"/api-key/{api_key.id}",
        headers=tenant_one_headers,
    )
    assert del_response.status_code == 200

    # Ensure that the API key was deleted
    get_response = await aclient.get(
        "/api-key",
        headers=tenant_one_headers,
    )
    assert get_response.status_code == 200
    assert str(api_key.id) not in [key["id"] for key in get_response.json()]


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_cannot_delete_host_project_api_key(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
) -> None:
    # Set up the host project to have a known API key
    name = "test-cannot-delete-host-project-api-key" + uuid4().hex
    host_project = (
        await host_metadata_crud.create_project_metadata(
            auth_tenant_one,
            host_metadata_crud.CreateProjectMetadataRequest(
                name=name,
                repo_url="https://github.com/langchain-ai/chat-langchain",
                repo_path="langgraph.json",
                repo_commit="main",
                platform=projects._get_default_project_platform(
                    image_source=deployment_type.ImageSourceId.github,
                    organization_id=auth_tenant_one.organization_id,
                    deployment_type=deployment_type.DeploymentType.prod,
                    tenant_id=auth_tenant_one.tenant_id,
                    remote_reconciled=False,
                    deployment_platform=None,
                    region=None,
                    aws_account_id=None,
                    public=None,
                ),
                created_by={},
                container_spec={},
                database_platform=deployment_type.DatabasePlatformId.gcp_cloud_sql,
                secrets={},
            ),
        )
    ).project_metadata

    api_key_id = host_project.api_key_id

    # Ensure that an API key exists on list (we have no get API)
    get_response = await aclient.get(
        "/api-key",
        headers=tenant_one_headers,
    )
    assert str(api_key_id) in [key["id"] for key in get_response.json()]
    key = next(key for key in get_response.json() if key["id"] == str(api_key_id))
    assert key is not None
    assert key["description"] == f"LangGraph Platform: {name}"

    # Ensure that a client can't directly delete the API key
    del_response = await aclient.delete(
        f"/api-key/{api_key_id}",
        headers=tenant_one_headers,
    )
    assert del_response.status_code == 409

    # Ensure that the API key was not deleted
    get_response = await aclient.get(
        "/api-key",
        headers=tenant_one_headers,
    )
    assert get_response.status_code == 200
    assert str(api_key_id) in [key["id"] for key in get_response.json()]


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_personal_access_token_crud(
    db_asyncpg: Connection,
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    tenant_one_read_only_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    # Create PAT
    post_response = await aclient.post(
        "/api-key/current",
        headers=tenant_one_headers,
        json={"description": "test"},
    )
    # Need a user to create a PAT
    if use_api_key:
        assert post_response.status_code == 401
        return
    pat = schemas.APIKeyCreateResponse(**post_response.json())
    assert verify_key_signature(pat.key)
    assert pat.last_used_at is None

    # Verify you can create another PAT, then delete it
    res = await aclient.post(
        "/api-key/current",
        headers=tenant_one_headers,
        json={"description": "test 2"},
    )
    assert res.status_code == 200
    pat_temp = schemas.APIKeyCreateResponse(**res.json())
    res = await aclient.delete(
        f"/api-key/current/{pat_temp.id}",
        headers=tenant_one_headers,
    )
    assert res.status_code == 200

    # Verify you can use the PAT to authenticate
    get_response = await aclient.get("/sessions", headers={"X-Api-Key": pat.key})
    assert get_response.status_code == 200

    # Ensure that a PAT exists on list (we have no get API)
    get_response = await aclient.get(
        "/api-key/current",
        headers=tenant_one_headers,
    )

    tokens = [key for key in get_response.json() if key["id"] == str(pat.id)]
    assert len(tokens) == 1
    token = tokens[0]
    assert token["description"] == "test"
    assert token["last_used_at"]

    pat_ls_user_id = await db_asyncpg.fetchval(
        "SELECT ls_user_id FROM api_keys WHERE id = $1", pat.id
    )
    user_ls_user_id = await db_asyncpg.fetchval(
        "SELECT ls_user_id FROM users WHERE id = $1", auth_tenant_one.user_id
    )
    assert user_ls_user_id == pat_ls_user_id

    # Verify another user cannot see your personal access tokens
    get_response = await aclient.get(
        "/api-key/current",
        headers=tenant_one_read_only_headers,
    )

    assert all(key["id"] != str(pat.id) for key in get_response.json())

    # Verify another user cannot delete your personal access tokens
    delete_response = await aclient.delete(
        f"/api-key/current/{pat.id}",
        headers=tenant_one_read_only_headers,
    )

    assert delete_response.status_code == 404

    # Verify cannot delete your pat with api keys endpoint
    delete_response = await aclient.delete(
        f"/api-key/{pat.id}",
        headers=tenant_one_headers,
    )
    assert delete_response.status_code == 404

    # Verify PAT doesn't show up in the list of all API keys
    get_response = await aclient.get(
        "/api-key",
        headers=tenant_one_headers,
    )
    assert get_response.status_code == 200
    assert pat.id not in {key["id"] for key in get_response.json()}

    # Verify PAT can be used to authenticate
    get_response = await aclient.get(
        "/sessions",
        headers={**tenant_one_headers, "X-Api-Key": pat.key},
    )
    assert get_response.status_code == 200

    # Verify another user cannot delete your personal access tokens
    res = await aclient.delete(
        f"/api-key/current/{pat.id}",
        headers=tenant_one_read_only_headers,
    )
    assert res.status_code == 404

    # Delete the PAT
    res = await aclient.delete(
        f"/api-key/current/{pat.id}",
        headers=tenant_one_headers,
    )
    assert res.status_code == 200

    # Ensure that there is one less pat after deletion
    get_response = await aclient.get(
        "/api-key/current",
        headers=tenant_one_headers,
    )
    assert all(key["id"] != str(pat.id) for key in get_response.json())


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_api_key_creation_with_expires_at(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
) -> None:
    """Test API key creation with various expires_at values."""

    # Test 1: No expires_at (should default to None)
    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={"description": "no expiry key"},
    )
    assert post_response.status_code == 200
    api_key_no_expiry = post_response.json()
    assert api_key_no_expiry["expires_at"] is None
    assert api_key_no_expiry["description"] == "no expiry key"

    # Test 2: Future expires_at
    future_date = (datetime.now(timezone.utc) + timedelta(days=30)).isoformat()
    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={"description": "future expiry key", "expires_at": future_date},
    )
    assert post_response.status_code == 200
    api_key_future = post_response.json()
    assert api_key_future["expires_at"] is not None
    assert api_key_future["description"] == "future expiry key"
    # Verify the returned date matches what we sent (within a few seconds)
    returned_date = datetime.fromisoformat(
        api_key_future["expires_at"].replace("Z", "+00:00")
    )
    sent_date = datetime.fromisoformat(future_date.replace("Z", "+00:00"))
    assert abs((returned_date - sent_date).total_seconds()) < 5

    # Test 3: Past expires_at (should still be created but immediately unusable)
    past_date = (datetime.now(timezone.utc) - timedelta(days=1)).isoformat()
    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={"description": "past expiry key", "expires_at": past_date},
    )
    assert post_response.status_code == 200
    api_key_past = post_response.json()
    assert api_key_past["expires_at"] is not None
    assert api_key_past["description"] == "past expiry key"

    # Test 4: Custom expires_at (1 hour from now)
    custom_date = (datetime.now(timezone.utc) + timedelta(hours=1)).isoformat()
    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={"description": "custom expiry key", "expires_at": custom_date},
    )
    assert post_response.status_code == 200
    api_key_custom = post_response.json()
    assert api_key_custom["expires_at"] is not None
    assert api_key_custom["description"] == "custom expiry key"

    # Clean up
    for key_id in [
        api_key_no_expiry["id"],
        api_key_future["id"],
        api_key_past["id"],
        api_key_custom["id"],
    ]:
        await aclient.delete(f"/api-key/{key_id}", headers=tenant_one_headers)


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_pat_creation_with_expires_at(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test Personal Access Token creation with various expires_at values."""

    # Skip if using API key auth (PATs require user auth)
    if use_api_key:
        pytest.skip("PATs require user authentication")

    # Test 1: No expires_at (should default to None)
    post_response = await aclient.post(
        "/api-key/current",
        headers=tenant_one_headers,
        json={"description": "no expiry PAT"},
    )
    assert post_response.status_code == 200
    pat_no_expiry = post_response.json()
    assert pat_no_expiry["expires_at"] is None
    assert pat_no_expiry["description"] == "no expiry PAT"

    # Test 2: Future expires_at
    future_date = (datetime.now(timezone.utc) + timedelta(days=30)).isoformat()
    post_response = await aclient.post(
        "/api-key/current",
        headers=tenant_one_headers,
        json={"description": "future expiry PAT", "expires_at": future_date},
    )
    assert post_response.status_code == 200
    pat_future = post_response.json()
    assert pat_future["expires_at"] is not None
    assert pat_future["description"] == "future expiry PAT"

    # Test 3: Past expires_at
    past_date = (datetime.now(timezone.utc) - timedelta(days=1)).isoformat()
    post_response = await aclient.post(
        "/api-key/current",
        headers=tenant_one_headers,
        json={"description": "past expiry PAT", "expires_at": past_date},
    )
    assert post_response.status_code == 200
    pat_past = post_response.json()
    assert pat_past["expires_at"] is not None
    assert pat_past["description"] == "past expiry PAT"

    # Clean up
    for pat_id in [pat_no_expiry["id"], pat_future["id"], pat_past["id"]]:
        await aclient.delete(f"/api-key/current/{pat_id}", headers=tenant_one_headers)


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_api_key_listing_includes_expires_at(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
) -> None:
    """Test that API key listing includes expires_at field."""

    # Create a key with expiry
    future_date = (datetime.now(timezone.utc) + timedelta(days=30)).isoformat()
    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={"description": "key with expiry", "expires_at": future_date},
    )
    assert post_response.status_code == 200
    created_key = post_response.json()

    # List keys and verify expires_at is included
    get_response = await aclient.get("/api-key", headers=tenant_one_headers)
    assert get_response.status_code == 200

    keys = get_response.json()
    found_key = next((key for key in keys if key["id"] == created_key["id"]), None)
    assert found_key is not None
    assert found_key["expires_at"] is not None
    assert found_key["description"] == "key with expiry"

    # Clean up
    await aclient.delete(f"/api-key/{created_key['id']}", headers=tenant_one_headers)


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_pat_listing_includes_expires_at(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test that PAT listing includes expires_at field."""

    # Skip if using API key auth (PATs require user auth)
    if use_api_key:
        pytest.skip("PATs require user authentication")

    # Create a PAT with expiry
    future_date = (datetime.now(timezone.utc) + timedelta(days=30)).isoformat()
    post_response = await aclient.post(
        "/api-key/current",
        headers=tenant_one_headers,
        json={"description": "PAT with expiry", "expires_at": future_date},
    )
    assert post_response.status_code == 200
    created_pat = post_response.json()

    # List PATs and verify expires_at is included
    get_response = await aclient.get("/api-key/current", headers=tenant_one_headers)
    assert get_response.status_code == 200

    pats = get_response.json()
    found_pat = next((pat for pat in pats if pat["id"] == created_pat["id"]), None)
    assert found_pat is not None
    assert found_pat["expires_at"] is not None
    assert found_pat["description"] == "PAT with expiry"

    # Clean up
    await aclient.delete(
        f"/api-key/current/{created_pat['id']}", headers=tenant_one_headers
    )


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_api_key_usage_enforcement_no_expiry(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test that API keys with no expires_at are always accepted."""

    # Skip if using API key auth (we need to test with user auth)
    if use_api_key:
        pytest.skip("Need user authentication for this test")

    # Create a key with no expires_at
    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={"description": "Key with no expiry"},
    )
    assert post_response.status_code == 200
    created_key = post_response.json()

    # Use the key for authentication
    api_key_headers = {
        "X-API-Key": created_key["key"],
        "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
        "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
    }

    # Make a request with the API key
    response = await aclient.get("/sessions", headers=api_key_headers)
    assert response.status_code == 200


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_api_key_usage_enforcement_future_expiry(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test that API keys with future expires_at are accepted."""

    # Skip if using API key auth (we need to test with user auth)
    if use_api_key:
        pytest.skip("Need user authentication for this test")

    # Create a key with future expiry (30 days)
    future_date = (datetime.now(timezone.utc) + timedelta(days=30)).isoformat()
    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={"description": "Key with future expiry", "expires_at": future_date},
    )
    assert post_response.status_code == 200
    created_key = post_response.json()

    # Use the key for authentication
    api_key_headers = {
        "X-API-Key": created_key["key"],
        "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
        "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
    }

    # Make a request with the API key
    response = await aclient.get("/sessions", headers=api_key_headers)
    assert response.status_code == 200


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_api_key_usage_enforcement_past_expiry(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test that API keys with past expires_at are rejected."""

    # Skip if using API key auth (we need to test with user auth)
    if use_api_key:
        pytest.skip("Need user authentication for this test")

    # Create a key with past expiry (1 day ago)
    past_date = (datetime.now(timezone.utc) - timedelta(days=1)).isoformat()
    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={"description": "Key with past expiry", "expires_at": past_date},
    )
    assert post_response.status_code == 200
    created_key = post_response.json()

    # Use the key for authentication
    api_key_headers = {
        "X-API-Key": created_key["key"],
        "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
        "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
    }

    # Make a request with the API key - should be rejected
    response = await aclient.get("/sessions", headers=api_key_headers)
    assert response.status_code in [401, 403]  # Unauthorized or Forbidden


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_api_key_usage_enforcement_exact_expiry(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test that API keys with expires_at exactly now are rejected."""

    # Skip if using API key auth (we need to test with user auth)
    if use_api_key:
        pytest.skip("Need user authentication for this test")

    # Create a key with expiry exactly now
    now_date = datetime.now(timezone.utc).isoformat()
    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={"description": "Key with exact expiry", "expires_at": now_date},
    )
    assert post_response.status_code == 200
    created_key = post_response.json()

    # Use the key for authentication
    api_key_headers = {
        "X-API-Key": created_key["key"],
        "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
        "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
    }

    # Make a request with the API key - should be rejected
    response = await aclient.get("/sessions", headers=api_key_headers)
    assert response.status_code in [401, 403]  # Unauthorized or Forbidden


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_api_key_usage_enforcement_custom_durations(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test API keys with various custom durations."""

    # Skip if using API key auth (we need to test with user auth)
    if use_api_key:
        pytest.skip("Need user authentication for this test")

    # Test various custom durations
    test_durations = [1, 7, 90, 365]  # 1 day, 1 week, 90 days, 1 year

    for days in test_durations:
        future_date = (datetime.now(timezone.utc) + timedelta(days=days)).isoformat()
        post_response = await aclient.post(
            "/api-key",
            headers=tenant_one_headers,
            json={
                "description": f"Key with {days} days expiry",
                "expires_at": future_date,
            },
        )
        assert post_response.status_code == 200
        created_key = post_response.json()

        # Use the key for authentication
        api_key_headers = {
            "X-API-Key": created_key["key"],
            "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
            "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
        }

        # Make a request with the API key
        response = await aclient.get("/sessions", headers=api_key_headers)
        assert response.status_code == 200


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_api_key_usage_enforcement_timezone_handling(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test that API keys handle timezone formats correctly."""

    # Skip if using API key auth (we need to test with user auth)
    if use_api_key:
        pytest.skip("Need user authentication for this test")

    # Test with ISO string without 'Z' (should be interpreted as UTC)
    future_date_no_z = (datetime.now(timezone.utc) + timedelta(days=30)).strftime(
        "%Y-%m-%dT%H:%M:%S.%f"
    )[:-3]

    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={
            "description": "Key with ISO date without Z",
            "expires_at": future_date_no_z,
        },
    )
    assert post_response.status_code == 200
    created_key = post_response.json()

    # Use the key for authentication
    api_key_headers = {
        "X-API-Key": created_key["key"],
        "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
        "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
    }

    # Make a request with the API key
    response = await aclient.get("/sessions", headers=api_key_headers)
    assert response.status_code == 200

    # Test with ISO string with 'Z'
    future_date_with_z = (datetime.now(timezone.utc) + timedelta(days=30)).isoformat()

    post_response = await aclient.post(
        "/api-key",
        headers=tenant_one_headers,
        json={
            "description": "Key with ISO date with Z",
            "expires_at": future_date_with_z,
        },
    )
    assert post_response.status_code == 200
    created_key = post_response.json()

    # Use the key for authentication
    api_key_headers = {
        "X-API-Key": created_key["key"],
        "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
        "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
    }

    # Make a request with the API key
    response = await aclient.get("/sessions", headers=api_key_headers)
    assert response.status_code == 200


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_pat_usage_enforcement_no_expiry(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test that PATs with no expires_at are always accepted."""

    # Skip if using API key auth (PATs require user auth)
    if use_api_key:
        pytest.skip("PATs require user authentication")

    # Create a PAT with no expires_at
    post_response = await aclient.post(
        "/api-key/current",
        headers=tenant_one_headers,
        json={"description": "PAT with no expiry"},
    )
    assert post_response.status_code == 200
    created_pat = post_response.json()

    # Use the PAT for authentication
    pat_headers = {
        "X-API-Key": created_pat["key"],
        "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
        "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
    }

    # Make a request with the PAT
    response = await aclient.get("/sessions", headers=pat_headers)
    assert response.status_code == 200


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_pat_usage_enforcement_future_expiry(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test that PATs with future expires_at are accepted."""

    # Skip if using API key auth (PATs require user auth)
    if use_api_key:
        pytest.skip("PATs require user authentication")

    # Create a PAT with future expiry (30 days)
    future_date = (datetime.now(timezone.utc) + timedelta(days=30)).isoformat()
    post_response = await aclient.post(
        "/api-key/current",
        headers=tenant_one_headers,
        json={"description": "PAT with future expiry", "expires_at": future_date},
    )
    assert post_response.status_code == 200
    created_pat = post_response.json()

    # Use the PAT for authentication
    pat_headers = {
        "X-API-Key": created_pat["key"],
        "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
        "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
    }

    # Make a request with the PAT
    response = await aclient.get("/sessions", headers=pat_headers)
    assert response.status_code == 200


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none",
    reason="no api keys/pats for auth none",
)
async def test_pat_usage_enforcement_past_expiry(
    aclient: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict[str, str],
    use_api_key: bool,
) -> None:
    """Test that PATs with past expires_at are rejected."""

    # Skip if using API key auth (PATs require user auth)
    if use_api_key:
        pytest.skip("PATs require user authentication")

    # Create a PAT with past expiry (1 day ago)
    past_date = (datetime.now(timezone.utc) - timedelta(days=1)).isoformat()
    post_response = await aclient.post(
        "/api-key/current",
        headers=tenant_one_headers,
        json={"description": "PAT with past expiry", "expires_at": past_date},
    )
    assert post_response.status_code == 200
    created_pat = post_response.json()

    # Use the PAT for authentication
    pat_headers = {
        "X-API-Key": created_pat["key"],
        "X-Organization-Id": tenant_one_headers["X-Organization-Id"],
        "X-Tenant-Id": tenant_one_headers["X-Tenant-Id"],
    }

    # Make a request with the PAT - should be rejected
    response = await aclient.get("/sessions", headers=pat_headers)
    assert response.status_code in [401, 403]  # Unauthorized or Forbidden
