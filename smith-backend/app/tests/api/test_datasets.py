"""Test correct functionality of dataset endpoints."""

import asyncio
import csv
import json
import math
import os
import tempfile
import time
import urllib.parse
from contextlib import asynccontextmanager
from datetime import datetime, timedelta, timezone
from io import String<PERSON>
from typing import Any, AsyncGenerator, Awaitable, Callable, cast
from unittest import mock
from unittest.mock import patch
from uuid import UUID, uuid4

import asyncpg
import pytest
from fastapi import HTTPException
from httpx import AsyncClient
from langchain_core.load import dumpd
from langchain_core.messages import AIMessage, ToolCall
from langchain_core.prompts import ChatPromptTemplate
from playground.models.playground.fake import (
    FakeMessagesListChatModel,
    FakeStreamingMessagesListChatModel,
)

from app import config, crud, schemas
from app.api.auth import AuthInfo
from app.config import settings
from app.models.datasets.schema import (
    PlaygroundRunOverDatasetBatchRequestSchema,
    PlaygroundRunOverDatasetRequestSchema,
)
from app.models.runs.ingest import (
    ROOT_S3_KEY,
)
from app.models.runs.utils import merge_run_infos
from app.tests.api.test_runs import post_runs
from app.tests.utils import (
    FreshTenantClient,
    create_chat_preset_dataset,
    fresh_tenant_client,
    random_lower_string,
    wait_for_runs_to_end,
)


async def _create_example(
    auth: AuthInfo,
    create_example: schemas.ExampleCreate,
) -> schemas.Example:
    """Helper function to create an example"""
    example_dict = create_example.model_dump()
    created_examples_json = await crud.create_examples(
        auth, [example_dict], ignore_conflicts=False
    )
    assert len(created_examples_json) == 1
    return schemas.Example.model_validate_json(created_examples_json[0])


async def _create_examples(
    auth: AuthInfo,
    create_examples: list[schemas.ExampleCreate],
) -> list[schemas.Example]:
    """Helper function to create examples"""
    example_dicts = [example.model_dump() for example in create_examples]
    created_examples_json = await crud.create_examples(auth, example_dicts)
    return [
        schemas.Example.model_validate_json(example)
        for example in created_examples_json
    ]


async def test_create_dataset(
    http_tenant_one: AsyncClient, http_tenant_one_read_only: AsyncClient
) -> None:
    """Test that a dataset can be created."""
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )

    assert response.status_code == 200

    if config.settings.AUTH_TYPE != "none":
        response = await http_tenant_one_read_only.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )

        assert response.status_code == 403


async def test_create_dataset_with_existing_name(http_tenant_one: AsyncClient) -> None:
    """Test that a dataset cannot be created with an existing name."""
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )

    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": response.json()["name"],
            "description": "test",
        },
    )

    assert response.status_code == 409


async def test_create_dataset_with_no_description(http_tenant_one: AsyncClient) -> None:
    """Test that a dataset can be created with no description."""
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": None,
        },
    )

    assert response.status_code == 200


async def test_read_dataset(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    http_tenant_one_read_only: AsyncClient,
) -> None:
    """Test that a dataset can be read."""
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
        ),
    )
    response = await http_tenant_one.get(f"/datasets/{dataset.id}")

    assert response.status_code == 200
    assert response.json()["name"] == dataset.name
    assert response.json()["description"] == dataset.description

    if config.settings.AUTH_TYPE != "none":
        response = await http_tenant_one_read_only.get(f"/datasets/{dataset.id}")

        assert response.status_code == 200
        assert response.json()["name"] == dataset.name
        assert response.json()["description"] == dataset.description


async def test_create_dataset_with_whitespace(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    http_tenant_one_read_only: AsyncClient,
) -> None:
    """Test that whitespace is trimmed from the dataset name during creation."""
    name = random_lower_string()
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=f"  {name}  ",
            description="test",
        ),
    )
    response = await http_tenant_one.get(f"/datasets/{dataset.id}")

    assert response.status_code == 200
    assert response.json()["name"] == name
    assert response.json()["description"] == dataset.description

    if config.settings.AUTH_TYPE != "none":
        response = await http_tenant_one_read_only.get(f"/datasets/{dataset.id}")

        assert response.status_code == 200
        assert response.json()["name"] == name
        assert response.json()["description"] == dataset.description


async def test_read_dataset_not_found(http_tenant_one: AsyncClient) -> None:
    """Test that a dataset cannot be read if it does not exist."""
    response = await http_tenant_one.get(f"/datasets/{str(uuid4())}")

    assert response.status_code == 404


async def test_read_dataset_by_name(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    http_tenant_one_read_only: AsyncClient,
) -> None:
    """Test that a dataset can be read by name."""
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
        ),
    )
    response = await http_tenant_one.get(f"/datasets?name={dataset.name}")

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == dataset.name
    assert response.json()[0]["description"] == dataset.description

    if config.settings.AUTH_TYPE != "none":
        response = await http_tenant_one_read_only.get(f"/datasets?name={dataset.name}")

        assert response.status_code == 200
        assert len(response.json()) == 1
        assert response.json()[0]["name"] == dataset.name
        assert response.json()[0]["description"] == dataset.description


async def test_read_dataset_by_name_contains(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that a dataset can be searched by name_contains."""
    dataset_name = random_lower_string()
    query = dataset_name[1:-1]
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=dataset_name,
            description="test",
        ),
    )

    response = await http_tenant_one.get(f"/datasets?name_contains={query}")

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == dataset.name
    assert response.json()[0]["description"] == dataset.description


async def test_read_dataset_by_metadata(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that a dataset can be searched by name_contains."""
    dataset_name = random_lower_string()
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=dataset_name,
            description="test",
            extra={
                "metadata": {
                    "foo": "bar",
                    "baz": "qux",
                }
            },
        ),
    )

    response = await http_tenant_one.get(
        f"/datasets?metadata={json.dumps({'foo': 'bar'})}"
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == dataset.name
    assert response.json()[0]["description"] == dataset.description

    response = await http_tenant_one.get(
        f"/datasets?metadata={json.dumps({'baz': 'qux'})}"
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == dataset.name
    assert response.json()[0]["description"] == dataset.description

    response = await http_tenant_one.get(
        f"/datasets?metadata={json.dumps({'baz': 'qux', 'foo': 'bar'})}"
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == dataset.name
    assert response.json()[0]["description"] == dataset.description

    response = await http_tenant_one.get(
        f"/datasets?metadata={json.dumps({'baz': 'qux', 'foo': 'bar', 'doesntexist': 'nope'})}"
    )

    assert response.status_code == 200
    assert len(response.json()) == 0


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_read_dataset_other_user(
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
) -> None:
    """Test that a dataset cannot be read by another user."""
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
            id=None,
        ),
    )

    with pytest.raises(HTTPException):
        await crud.get_dataset(
            auth_tenant_two,
            dataset.id,  # type: ignore
        )


async def test_read_datasets(http_tenant_one: AsyncClient) -> None:
    """Test that datasets can be read."""
    response = await http_tenant_one.get("/datasets")

    assert response.status_code == 200


async def test_update_datasets(
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that a dataset can be updated."""
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
        ),
    )

    new_name = random_lower_string()
    dataset_id = cast(UUID, dataset.id)

    response, _ = await crud.update_dataset(
        auth_tenant_one,
        dataset_id,
        schemas.DatasetUpdate(name=new_name),
    )
    assert response.example_count == 0
    assert response.session_count == 0
    assert response.last_session_start_time is None

    updated_dataset = await crud.get_dataset(auth_tenant_one, dataset_id)
    assert updated_dataset.name == new_name  # type: ignore
    assert updated_dataset.description == dataset.description  # type: ignore


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_update_datasets_other_user(
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
) -> None:
    """Test that a dataset cannot be updated by another user."""
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
            id=None,
        ),
    )

    with pytest.raises(HTTPException):
        await crud.update_dataset(
            auth_tenant_two,
            cast(UUID, dataset.id),
            schemas.DatasetUpdate(name=random_lower_string()),
        )


async def test_delete_dataset(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    http_tenant_one_read_only: AsyncClient,
) -> None:
    """Test that a dataset can be deleted."""
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
        ),
    )

    if config.settings.AUTH_TYPE != "none":
        response = await http_tenant_one_read_only.delete(f"/datasets/{dataset.id}")

        assert response.status_code == 403

    response = await http_tenant_one.delete(f"/datasets/{dataset.id}")

    assert response.status_code == 200

    with pytest.raises(HTTPException):
        await crud.get_dataset(auth_tenant_one, dataset.id)  # type: ignore


@pytest.mark.parametrize(
    "blob_storage_engine",
    [
        "Azure",
        "S3",
    ],
)
async def test_delete_dataset_deletes_sessions(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    blob_storage_engine: str,
) -> None:
    """Test that on dataset deletion, associated experiments/projects are also deleted if not Azure."""
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": "input_val_1", "input_2": "input_val_2"},
            "outputs": {"output_1": "output_val_1", "output_2": "output_val_2"},
            "dataset_id": str(dataset_id),
        },
    )
    assert response.status_code == 200
    example_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "reference_dataset_id": str(dataset_id),
            "start_time": "2023-05-05T05:13:24.571809",
        },
    )
    assert response.status_code == 200
    session_id = response.json()["id"]

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "reference_example_id": example_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202, response.text
    await wait_for_runs_to_end(auth_tenant_one, run_id)

    with patch.object(crud.settings, "BLOB_STORAGE_ENGINE", blob_storage_engine):
        # await crud.delete_dataset(auth_tenant_one, dataset_id)
        response = await http_tenant_one.delete(f"/datasets/{dataset_id}")
        assert response.status_code == 200
    if blob_storage_engine != "Azure":
        await wait_until_task_queue_empty()

    response = await http_tenant_one.get(f"/datasets/{dataset_id}")
    assert response.status_code == 404

    expected_status_code = 404 if blob_storage_engine != "Azure" else 200
    response = await http_tenant_one.get(f"/sessions/{session_id}")
    assert response.status_code == expected_status_code

    response = await http_tenant_one.get(f"/runs/{run_id}")
    assert response.status_code == expected_status_code


async def test_delete_dataset_cascading_deletes(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that a dataset can be deleted."""
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
        ),
    )
    example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={},
            dataset_id=UUID(str(dataset.id)),
        ),
    )

    response = await http_tenant_one.delete(f"/datasets/{dataset.id}")

    assert response.status_code == 200

    with pytest.raises(HTTPException):
        await crud.get_dataset(auth_tenant_one, dataset.id)  # type: ignore

    with pytest.raises(HTTPException):
        await crud.get_example(auth_tenant_one, example.id)  # type: ignore


async def test_download_dataset_sesssions_csv(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a dataset session can be downloaded."""
    with mock.patch(
        "app.config.settings.DOWNLOAD_EXPERIMENT_RESULTS_LIMIT",
        new=10,
    ):
        start_time = datetime.now(timezone.utc) + timedelta(days=1)
        end_time = start_time + timedelta(seconds=8)
        # Create a dataset
        response = await http_tenant_one.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert response.status_code == 200
        dataset_id = response.json()["id"]

        # Create 10 examples (this is the limit)
        examples = []
        example_ids = [uuid4() for _ in range(10)]
        for i in range(10):
            examples.append(
                {
                    "inputs": {"input": f"test input {i}"},
                    "outputs": {"output": f"test output {i}"},
                    "dataset_id": str(dataset_id),
                    "id": str(example_ids[i]),
                }
            )

        response = await http_tenant_one.post("/examples/bulk", json=examples)
        assert response.status_code == 200

        # Create a experiment session
        response = await http_tenant_one.post(
            "/sessions",
            json={
                "name": "csv_session_1",
                "reference_dataset_id": str(dataset_id),
            },
        )
        assert response.status_code == 200
        session_id = response.json()["id"]

        runs = []
        run_ids = []
        for example_id in example_ids:
            for i in range(2):
                run_id = uuid4()
                run_ids.append(run_id)
                runs.append(
                    {
                        "name": "Target",
                        "start_time": start_time.isoformat(),
                        "end_time": end_time.isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Target"},
                        "inputs": {"input": f"{str(example_id)} {i}"},
                        "outputs": {
                            "generations": [
                                [
                                    {
                                        "text": str(example_id),
                                        "generation_info": {
                                            "finish_reason": "stop",
                                            "logprobs": None,
                                        },
                                        "type": "ChatGeneration",
                                        "message": {
                                            "lc": 1,
                                            "type": "constructor",
                                            "id": [
                                                "langchain",
                                                "schema",
                                                "messages",
                                                "AIMessage",
                                            ],
                                            "kwargs": {
                                                "content": "In this morning's meeting, we successfully resolved all world conflicts.",
                                                "additional_kwargs": {"refusal": None},
                                                "response_metadata": {
                                                    "token_usage": {
                                                        "completion_tokens": i,
                                                        "prompt_tokens": i,
                                                        "total_tokens": 2 * i,
                                                        "completion_tokens_details": {
                                                            "reasoning_tokens": 0
                                                        },
                                                        "prompt_tokens_details": {
                                                            "cached_tokens": 0
                                                        },
                                                    },
                                                    "model_name": "gpt-4o-2024-08-06",
                                                    "system_fingerprint": "fp_45cf54deae",
                                                    "finish_reason": "stop",
                                                    "logprobs": None,
                                                },
                                                "type": "ai",
                                                "id": "run-ea314874-92ff-4eb5-8e18-277b07f4cef9-0",
                                                "usage_metadata": {
                                                    "input_tokens": 3 * i,
                                                    "output_tokens": i,
                                                    "total_tokens": 4 * i,
                                                    "input_token_details": {
                                                        "cache_read": 0
                                                    },
                                                    "output_token_details": {
                                                        "reasoning": 0
                                                    },
                                                },
                                                "tool_calls": [],
                                                "invalid_tool_calls": [],
                                            },
                                        },
                                    }
                                ]
                            ],
                            "llm_output": {
                                "token_usage": {
                                    "completion_tokens": 3 * i,
                                    "prompt_tokens": i,
                                    "total_tokens": 4 * i,
                                    "completion_tokens_details": {
                                        "reasoning_tokens": 0
                                    },
                                    "prompt_tokens_details": {"cached_tokens": 0},
                                },
                                "model_name": "gpt-4o-2024-08-06",
                                "system_fingerprint": "fp_45cf54deae",
                            },
                            "run": None,
                            "type": "LLMResult",
                        },
                        "session_id": str(session_id),
                        "reference_example_id": str(example_id),
                        "parent_run_id": None,
                        "run_type": "llm",
                        "id": str(run_id),
                        "trace_id": str(run_id),
                        "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%f')}Z{run_id}",
                        "total_tokens": 4 * i,
                    },
                )

        response = await http_tenant_one.post(
            "/runs/batch",
            json={
                "post": [
                    {k: v for k, v in run.items() if k != "total_tokens"}
                    for run in runs
                ],
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        feedbacks = []
        for run_id in run_ids[::2]:
            feedback = {
                "run_id": str(run_id),
                "key": "foo",
                "score": 1,
                "feedback_source": {"type": "api"},
            }
            response = await http_tenant_one.post(
                "/feedback",
                json=feedback,
            )
            assert response.status_code == 200
            feedbacks.append(feedback)
        # Download the session CSV
        response = await http_tenant_one.post(
            f"/datasets/{dataset_id}/runs?format=csv",
            json={"session_ids": [session_id]},
        )
        assert response.status_code == 200
        decoded_content = ""
        async for (
            chunk
        ) in response.aiter_text():  # Asynchronously iterate over the response chunks
            decoded_content += chunk

        csv_file = StringIO(decoded_content)
        reader = csv.reader(csv_file)
        header = next(reader)
        data = list(reader)
        assert header == [
            "id",
            "inputs",
            "reference_outputs",
            "rep_1_outputs",
            "rep_2_outputs",
            "rep_1_run",
            "rep_2_run",
            "rep_1_status",
            "rep_2_status",
            "rep_1_error",
            "rep_2_error",
            "rep_1_latency",
            "rep_2_latency",
            "rep_1_tokens",
            "rep_2_tokens",
            "rep_1_total_cost",
            "rep_2_total_cost",
            "rep_1_foo",
            "rep_2_foo",
        ]
        # Ensure feedback is kept. num cols will be (7+num_feedback)*reps*num_sessions+3
        assert len(data[0]) == (7 + 1) * 2 * 1 + 3
        count_of_ones = 0
        for row in data:
            if any(row[i] == "1.0" for i in range(len(row))):
                count_of_ones += 1
        assert count_of_ones == 10
        assert len(data) == 10

        # Add one more example
        new_example_id = uuid4()
        example_ids.append(new_example_id)
        new_example = {
            "inputs": {"input": "test input 1000"},
            "outputs": {"output": "test output 1000"},
            "dataset_id": str(dataset_id),
            "id": str(new_example_id),
        }
        examples.append(new_example)
        response = await http_tenant_one.post(
            "/examples",
            json=new_example,
        )
        assert response.status_code == 200

        # Create another session with 1 repetition
        response = await http_tenant_one.post(
            "/sessions",
            json={
                "name": "csv_session_2",
                "reference_dataset_id": str(dataset_id),
            },
        )
        assert response.status_code == 200
        session_id2 = response.json()["id"]

        runs_2 = []
        for i, example_id in enumerate(example_ids):
            run_id = uuid4()
            runs_2.append(
                {
                    "name": "Target",
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Target"},
                    "inputs": {"input": str(example_id)},
                    "outputs": {
                        "generations": [
                            [
                                {
                                    "text": str(example_id),
                                    "generation_info": {
                                        "finish_reason": "stop",
                                        "logprobs": None,
                                    },
                                    "type": "ChatGeneration",
                                    "message": {
                                        "lc": 1,
                                        "type": "constructor",
                                        "id": [
                                            "langchain",
                                            "schema",
                                            "messages",
                                            "AIMessage",
                                        ],
                                        "kwargs": {
                                            "content": "In this morning's meeting, we successfully resolved all world conflicts.",
                                            "additional_kwargs": {"refusal": None},
                                            "response_metadata": {
                                                "token_usage": {
                                                    "completion_tokens": i,
                                                    "prompt_tokens": i,
                                                    "total_tokens": 2 * i,
                                                    "completion_tokens_details": {
                                                        "reasoning_tokens": 0
                                                    },
                                                    "prompt_tokens_details": {
                                                        "cached_tokens": 0
                                                    },
                                                },
                                                "model_name": "gpt-4o-2024-08-06",
                                                "system_fingerprint": "fp_45cf54deae",
                                                "finish_reason": "stop",
                                                "logprobs": None,
                                            },
                                            "type": "ai",
                                            "id": "run-ea314874-92ff-4eb5-8e18-277b07f4cef9-0",
                                            "usage_metadata": {
                                                "input_tokens": i,
                                                "output_tokens": i,
                                                "total_tokens": 2 * i,
                                                "input_token_details": {
                                                    "cache_read": 0
                                                },
                                                "output_token_details": {
                                                    "reasoning": 0
                                                },
                                            },
                                            "tool_calls": [],
                                            "invalid_tool_calls": [],
                                        },
                                    },
                                }
                            ]
                        ],
                        "llm_output": {
                            "token_usage": {
                                "completion_tokens": i,
                                "prompt_tokens": i,
                                "total_tokens": 2 * i,
                                "completion_tokens_details": {"reasoning_tokens": 0},
                                "prompt_tokens_details": {"cached_tokens": 0},
                            },
                            "model_name": "gpt-4o-2024-08-06",
                            "system_fingerprint": "fp_45cf54deae",
                        },
                        "run": None,
                        "type": "LLMResult",
                    },
                    "session_id": str(session_id2),
                    "reference_example_id": str(example_id),
                    "parent_run_id": None,
                    "run_type": "llm",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%f')}Z{run_id}",
                    "total_tokens": 2 * i,
                },
            )

        response = await http_tenant_one.post(
            "/runs/batch",
            json={
                "post": [
                    {k: v for k, v in run.items() if k != "total_tokens"}
                    for run in runs_2
                ],
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        # Download the session CSV again
        response = await http_tenant_one.post(
            f"/datasets/{dataset_id}/runs?format=csv",
            json={"session_ids": [session_id2]},
        )
        assert response.status_code == 206
        decoded_content = ""
        async for (
            chunk
        ) in response.aiter_text():  # Asynchronously iterate over the response chunks
            decoded_content += chunk

        csv_file = StringIO(decoded_content)
        reader = csv.reader(csv_file)
        header = next(reader)
        assert header == [
            "id",
            "inputs",
            "reference_outputs",
            "outputs",
            "run",
            "status",
            "error",
            "latency",
            "tokens",
            "total_cost",
        ]
        data = list(reader)
        assert len(data) == 10
        assert len(data[0]) == 10

        # Download comparison CSV
        response = await http_tenant_one.post(
            f"/datasets/{dataset_id}/runs?format=csv",
            json={"session_ids": [session_id, session_id2]},
        )
        assert response.status_code == 206
        decoded_content = ""
        async for (
            chunk
        ) in response.aiter_text():  # Asynchronously iterate over the response chunks
            decoded_content += chunk

        csv_file = StringIO(decoded_content)
        reader = csv.reader(csv_file)
        header = next(reader)
        assert header == [
            "id",
            "inputs",
            "reference_outputs",
            "csv_session_1_rep_1_outputs",
            "csv_session_1_rep_2_outputs",
            "csv_session_2_outputs",
            "csv_session_1_rep_1_run",
            "csv_session_1_rep_2_run",
            "csv_session_2_run",
            "csv_session_1_rep_1_status",
            "csv_session_1_rep_2_status",
            "csv_session_2_status",
            "csv_session_1_rep_1_error",
            "csv_session_1_rep_2_error",
            "csv_session_2_error",
            "csv_session_1_rep_1_latency",
            "csv_session_1_rep_2_latency",
            "csv_session_2_latency",
            "csv_session_1_rep_1_tokens",
            "csv_session_1_rep_2_tokens",
            "csv_session_2_tokens",
            "csv_session_1_rep_1_total_cost",
            "csv_session_1_rep_2_total_cost",
            "csv_session_2_total_cost",
            "csv_session_1_rep_1_foo",
            "csv_session_1_rep_2_foo",
            "csv_session_2_foo",
        ]
        data = list(reader)
        assert len(data) == 10
        assert len(data[0]) == (7 + 1) * 3 + 3

        # check that the rows have the correct values
        for i, row in enumerate(data):
            corresponding_example = next(
                (e for e in examples if e["id"] == row[0]), None
            )
            assert corresponding_example is not None
            assert row[0] == str(corresponding_example["id"])
            assert json.loads(row[1]) == corresponding_example["inputs"]
            assert json.loads(row[2]) == corresponding_example["outputs"]
            runs_for_example = [r for r in runs if r["reference_example_id"] == row[0]]
            is_extra_example = not runs_for_example
            if is_extra_example:
                assert row[0] == str(new_example_id)
                assert row[3] == ""
                assert row[4] == ""
                assert row[6] == ""
                assert row[7] == ""
                assert row[9] == ""
                assert row[10] == ""
                assert row[12] == ""
                assert row[13] == ""
                assert row[18] == ""
                assert row[19] == ""

            else:
                assert len(runs_for_example) == 2
                response = await http_tenant_one.post(
                    "/runs/query",
                    json={"id": [r["id"] for r in runs_for_example]},
                )
                assert response.status_code == 200, response.text
                runs_for_example = response.json()["runs"]
                stringified_run_session_1_rep_1 = json.loads(row[6])
                assert stringified_run_session_1_rep_1["id"] in [
                    r["id"] for r in runs_for_example
                ]
                stringified_run_session_1_rep_2 = json.loads(row[7])
                assert stringified_run_session_1_rep_2["id"] in [
                    r["id"] for r in runs_for_example
                ]

                rep_1_run = next(
                    (
                        r
                        for r in runs_for_example
                        if r["id"] == stringified_run_session_1_rep_1["id"]
                    ),
                    None,
                )
                assert rep_1_run is not None
                rep_2_run = next(
                    (
                        r
                        for r in runs_for_example
                        if r["id"] == stringified_run_session_1_rep_2["id"]
                    ),
                    None,
                )
                assert rep_2_run is not None
                assert rep_1_run["outputs"] == json.loads(row[3])
                assert rep_2_run["outputs"] == json.loads(row[4])
                assert row[9] == "success"
                assert row[10] == "success"
                assert row[12] == ""
                assert row[13] == ""
                assert row[18] == str(rep_1_run["total_tokens"])
                assert row[19] == str(rep_2_run["total_tokens"])

            session_2_run = next(
                (r for r in runs_2 if r["reference_example_id"] == row[0]), None
            )
            assert session_2_run is not None
            response = await http_tenant_one.get(f"/runs/{session_2_run['id']}")
            assert response.status_code == 200
            session_2_run = response.json()
            assert session_2_run is not None
            assert session_2_run["outputs"] == json.loads(row[5])

            assert row[11] == "success"

            assert row[14] == ""

            assert row[20] == str(session_2_run["total_tokens"])

            # check feedback
            if not is_extra_example:
                assert rep_1_run is not None and rep_2_run is not None
                feedback_for_example = next(
                    (f for f in feedbacks if f["run_id"] == rep_1_run["id"]), None
                )
                if feedback_for_example is not None:
                    assert row[24] == "1.0"
                else:
                    assert row[24] == ""
                feedback_for_example_2 = next(
                    (f for f in feedbacks if f["run_id"] == rep_2_run["id"]), None
                )
                if feedback_for_example_2 is not None:
                    assert row[25] == "1.0"
                else:
                    assert row[25] == ""
                # we don't know which rep is which, but we know that one of the two should have feedback
                assert (
                    feedback_for_example is not None
                    or feedback_for_example_2 is not None
                ) and not (
                    feedback_for_example is not None
                    and feedback_for_example_2 is not None
                )
            else:
                assert row[24] == ""
                assert row[25] == ""

            assert row[26] == ""


async def test_upload_dataset(
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a dataset can be uploaded."""
    test_dataset_bytes = open(
        f"{os.path.dirname(os.path.realpath(__file__))}/test_dataset.csv", "rb"
    ).read()
    with tempfile.NamedTemporaryFile(suffix=".csv") as tmp_file:
        with open(tmp_file.name, "w") as f:
            # Write the contents of "test_dataset.csv" to the temporary file
            f.write(test_dataset_bytes.decode("utf-8"))
        # Upload the temporary file, passing the input keys and output keys in the form
        response = await http_tenant_one.post(
            "/datasets/upload",
            files={"file": (tmp_file.name, open(tmp_file.name, "rb"))},
            data={
                "input_keys": ["input1", "input2", "input3"],
                "output_keys": ["output1"],
                "description": "test",
            },
        )

        assert response.status_code == 200, response.text
        assert response.json()["name"] == tmp_file.name

        # Fetch the examples with the dataset id
        dataset_id = response.json()["id"]
        response = await http_tenant_one.get(
            "/examples",
            params={"dataset": dataset_id},
        )
        assert response.status_code == 200, response.text
        assert len(response.json()) == 12

        response = await http_tenant_one.get(
            "/examples/count",
            params={"dataset": dataset_id},
        )
        assert response.status_code == 200, response.text
        assert response.json() == 12


async def _create_dataset_and_populate(
    auth: AuthInfo, dataset_args: dict, example_args: list
) -> UUID:
    """Helper function to create a dataset and populate it with examples"""
    dataset = await crud.create_dataset(auth, schemas.DatasetCreate(**dataset_args))
    await _create_examples(
        auth,
        [
            schemas.ExampleCreate(dataset_id=UUID(str(dataset.id)), **args)
            for args in example_args
        ],
    )
    return UUID(str(dataset.id))


async def _test_endpoint(
    client: AsyncClient,
    url: str,
    expected_status: int,
    expected_responses: list | None = None,
) -> None:
    """Helper function to test a specific endpoint and validate the response"""
    response = await client.get(url)
    assert response.status_code == expected_status
    if expected_responses is not None:
        content = response.content.decode("utf-8").strip()
        rows = [json.loads(row) for row in content.split("\n")]
        assert len(rows) == len(expected_responses)
        expected_responses_ = expected_responses.copy()
        # Order isn't guaranteed so we just need to match any value
        for value in rows:
            assert value in expected_responses_
            expected_responses_.remove(value)


async def test_clone_dataset(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a dataset can be cloned to another dataset"""

    N = 20
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test message dataset, no outputs",
            "data_type": schemas.DataType.kv,
        },
        [
            {
                "inputs": {
                    "input": [{"type": "human", "data": {"content": f"hello {i}"}}]
                },
            }
            for i in range(N)
        ],
    )
    dataset_2_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test message dataset, no outputs",
            "data_type": schemas.DataType.kv,
        },
        [{"inputs": {"input": "asdf"}}],
    )
    dataset_3_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test message dataset, no outputs",
            "data_type": schemas.DataType.kv,
        },
        [{"inputs": {"input2": "asdf2"}}],
    )
    dataset_4_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test message dataset, no outputs",
            "data_type": schemas.DataType.kv,
        },
        [{"inputs": {"input2": "asdf3"}}],
    )

    res = await http_tenant_one.post(
        "/datasets/clone",
        json={
            "source_dataset_id": str(dataset_id),
            "target_dataset_id": str(dataset_2_id),
        },
    )
    assert res.status_code == 200

    response = await http_tenant_one.get(
        f"/examples?dataset={dataset_2_id}",
    )
    assert response.status_code == 200
    examples = [e["inputs"] for e in response.json()]
    assert len(examples) == N + 1
    for i in range(N):
        assert {
            "input": [{"type": "human", "data": {"content": f"hello {i}"}}]
        } in examples

    assert {"input": "asdf"} in examples

    response = await http_tenant_one.get(
        f"/examples/count?dataset={dataset_2_id}",
    )
    assert response.status_code == 200
    examples_count = response.json()
    assert examples_count == N + 1

    # Test with versioning
    example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            dataset_id=UUID(str(dataset_id)),
            **{
                "inputs": {
                    "input": {"type": "human", "data": {"content": "hello new"}}
                },
            },
        ),
    )
    recent_modified_at = cast(datetime, example.modified_at)

    for i in range(5):
        await crud.update_example(
            auth_tenant_one,
            example.id,
            schemas.ExampleUpdate(
                inputs={
                    "input": {"type": "human", "data": {"content": f"hello new {i}"}}
                }
            ),
        )

    res = await http_tenant_one.post(
        "/datasets/clone",
        json={
            "source_dataset_id": str(dataset_id),
            "target_dataset_id": str(dataset_3_id),
            "as_of": recent_modified_at.isoformat(),
        },
    )
    assert res.status_code == 200

    response = await http_tenant_one.get(
        f"/examples?dataset={dataset_3_id}",
    )
    assert response.status_code == 200
    examples = [e["inputs"] for e in response.json()]
    assert (
        len(examples) == N + 2
    )  # +2 because we copied N+1 examples, and there was already 1 in dataset_3
    for i in range(N):
        assert {
            "input": [{"type": "human", "data": {"content": f"hello {i}"}}]
        } in examples
    for i in range(5):
        assert {
            "input": [{"type": "human", "data": {"content": f"hello new {i}"}}]
        } not in examples
    assert {"input": {"type": "human", "data": {"content": "hello new"}}} in examples

    response = await http_tenant_one.get(
        f"/examples/count?dataset={dataset_3_id}",
    )
    assert response.status_code == 200
    examples_count = response.json()
    assert examples_count == N + 2

    response = await http_tenant_one.put(
        f"/datasets/{dataset_id}/tags",
        json={"tag": "dev", "as_of": recent_modified_at.isoformat()},
    )

    res = await http_tenant_one.post(
        "/datasets/clone",
        json={
            "source_dataset_id": str(dataset_id),
            "target_dataset_id": str(dataset_4_id),
            "as_of": "dev",
        },
    )
    assert res.status_code == 200

    response = await http_tenant_one.get(
        f"/examples?dataset={dataset_4_id}",
    )
    assert response.status_code == 200
    examples = [e["inputs"] for e in response.json()]
    assert (
        len(examples) == N + 2
    )  # +2 because we copied N+1 examples, and there was already 1 in dataset_3
    for i in range(N):
        assert {
            "input": [{"type": "human", "data": {"content": f"hello {i}"}}]
        } in examples
    for i in range(5):
        assert {
            "input": [{"type": "human", "data": {"content": f"hello new {i}"}}]
        } not in examples
    assert {"input": {"type": "human", "data": {"content": "hello new"}}} in examples

    response = await http_tenant_one.get(
        f"/examples/count?dataset={dataset_4_id}",
    )
    assert response.status_code == 200
    examples_count = response.json()
    assert examples_count == N + 2


async def test_export_dataset_openai(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a dataset can be exported to OpenAI JSONL"""

    N = 110
    # Test 1: Chat dataset with no outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test message dataset, no outputs",
            "data_type": schemas.DataType.chat,
        },
        [
            {
                "inputs": {
                    "input": [{"type": "human", "data": {"content": f"hello {i}"}}]
                },
            }
            for i in range(N)
        ],
    )

    # insert then delete an example to ensure it doesn't break the download
    example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            dataset_id=dataset_id,
            inputs={"input": [{"type": "human", "data": {"content": "hello new"}}]},
            outputs={"output": {"type": "human", "data": {"content": "hi new"}}},
        ),
    )
    await crud.delete_example(auth_tenant_one, example.id)

    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai",
        200,
        [
            {"input": [{"role": "user", "content": f"hello {i}"}], "ideal": None}
            for i in range(N)
        ],
    )

    # Test 2: Chat dataset with outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test message dataset, with outputs",
            "data_type": schemas.DataType.chat,
        },
        [
            {
                "inputs": {
                    "input": [{"type": "human", "data": {"content": f"hello {i}"}}]
                },
                "outputs": {
                    "output": {"type": "human", "data": {"content": f"hi {i}"}}
                },
            }
            for i in range(N)
        ],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai",
        200,
        [
            {
                "input": [{"role": "user", "content": f"hello {i}"}],
                "ideal": f"hi {i}",
            }
            for i in range(N)
        ],
    )

    # Test 3: LLM dataset with no outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test llm dataset, no outputs",
            "data_type": schemas.DataType.llm,
        },
        [{"inputs": {"input": f"hello {i}"}} for i in range(N)],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai",
        200,
        [{"input": f"hello {i}", "ideal": None} for i in range(N)],
    )

    # Test 4: LLM dataset with outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test llm dataset, with outputs",
            "data_type": schemas.DataType.llm,
        },
        [
            {
                "inputs": {"input": f"hello {i}"},
                "outputs": {"output": f"hi {i}"},
            }
            for i in range(N)
        ],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai",
        200,
        [{"input": f"hello {i}", "ideal": f"hi {i}"} for i in range(N)],
    )

    # Test 5: KV dataset with no outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, no outputs",
            "data_type": schemas.DataType.kv,
        },
        [{"inputs": {"foo": f"bar {i}"}} for i in range(N)],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai",
        200,
        [{"input": f"bar {i}", "ideal": None} for i in range(N)],
    )

    # Test 6: KV dataset with outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, with outputs",
            "data_type": schemas.DataType.kv,
        },
        [
            {
                "inputs": {"foo": f"bar {i}"},
                "outputs": {"foo": f"baz {i}"},
            }
            for i in range(N)
        ],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai",
        200,
        [{"input": f"bar {i}", "ideal": f"baz {i}"} for i in range(N)],
    )

    # Test 7: KV dataset with outputs and multiple inputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, with multiple inputs",
            "data_type": schemas.DataType.kv,
        },
        [
            {
                "inputs": {"foo": f"Test 7 bar {i}", "baz": f"Test 7 qux {i}"},
                "outputs": {"foo": f"Test 7 baz {i}"},
            }
            for i in range(N)
        ],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai",
        400,
    )

    # Test 8: KV dataset with inputs and multiple outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, with multiple outputs",
            "data_type": schemas.DataType.kv,
        },
        [
            {
                "inputs": {"foo": f"bar {i}"},
                "outputs": {"foo": f"baz {i}", "qux": f"quux {i}"},
            }
            for i in range(N)
        ],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai",
        400,
    )


async def test_export_dataset_openai_ft(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a dataset can be exported to OpenAI JSONL"""

    N = 110
    # Test 1: Chat dataset with no outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test message dataset, no outputs",
            "data_type": schemas.DataType.chat,
        },
        [
            {
                "inputs": {
                    "input": [{"type": "human", "data": {"content": f"hello {i}"}}]
                },
            }
            for i in range(N)
        ],
    )

    # insert then delete an example to ensure it doesn't break the download
    example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            dataset_id=dataset_id,
            inputs={"input": [{"type": "human", "data": {"content": "hello new"}}]},
            outputs={"output": {"type": "human", "data": {"content": "hi new"}}},
        ),
    )
    await crud.delete_example(auth_tenant_one, example.id)

    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai_ft",
        200,
        [
            {
                "messages": [
                    {"role": "user", "content": f"hello {i}"},
                ]
            }
            for i in range(N)
        ],
    )

    # Test 2: Chat dataset with outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test message dataset, with outputs",
            "data_type": schemas.DataType.chat,
        },
        [
            {
                "inputs": {
                    "input": [{"type": "human", "data": {"content": f"hello {i}"}}]
                },
                "outputs": {
                    "output": {"type": "human", "data": {"content": f"hi {i}"}}
                },
            }
            for i in range(N)
        ],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai_ft",
        200,
        [
            {
                "messages": [
                    {"role": "user", "content": f"hello {i}"},
                    {"role": "user", "content": f"hi {i}"},
                ],
            }
            for i in range(N)
        ],
    )

    # Test 3: LLM dataset with no outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test llm dataset, no outputs",
            "data_type": schemas.DataType.llm,
        },
        [{"inputs": {"input": f"hello {i}"}} for i in range(N)],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai_ft",
        200,
        [
            {
                "messages": [
                    {"role": "user", "content": f"hello {i}"},
                ],
            }
            for i in range(N)
        ],
    )

    # Test 4: LLM dataset with outputs
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test llm dataset, with outputs",
            "data_type": schemas.DataType.llm,
        },
        [
            {
                "inputs": {"input": f"hello {i}"},
                "outputs": {"output": f"hi {i}"},
            }
            for i in range(N)
        ],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai_ft",
        200,
        [
            {
                "messages": [
                    {"role": "user", "content": f"hello {i}"},
                    {"role": "assistant", "content": f"hi {i}"},
                ],
            }
            for i in range(N)
        ],
    )

    # Test 5: KV dataset errors
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, no outputs",
            "data_type": schemas.DataType.kv,
        },
        [{"inputs": {"foo": f"bar {i}"}} for i in range(N)],
    )
    await _test_endpoint(
        http_tenant_one,
        f"/datasets/{dataset_id}/openai_ft",
        400,
    )


async def test_export_dataset_csv(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that dataset can be exported to CSV"""

    dataset_size = 2000
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, with outputs",
            "data_type": schemas.DataType.kv,
        },
        [
            {
                "inputs": {"baz": f"qux {i}", "foo": f"bar {i}"},
                "outputs": {"foo": f"baz {i}"},
            }
            for i in range(dataset_size)
        ],
    )

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/csv",
    )
    assert response.status_code == 200
    body = response.content.decode("utf-8")
    # Contains header row
    assert body.startswith("input_baz,input_foo,output_foo\n")
    # Contains first example (in any position, sort order is not guaranteed)
    assert "qux 0,bar 0,baz 0\n" in body
    # Contains last example (in any position, sort order is not guaranteed)
    assert (
        f"qux {dataset_size - 1},bar {dataset_size - 1},baz {dataset_size - 1}\n"
        in body
    )
    # Contains correct number of rows, ie. dataset_size + 1 for header
    assert body.count("\n") == dataset_size + 1

    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, with inconsistent number of inputs",
            "data_type": schemas.DataType.kv,
        },
        [
            {
                "inputs": {"baz": "qux 0", "foo": "bar 0"},
                "outputs": {"foo": "baz 0"},
            },
            {
                "inputs": {"baz": "qux 1"},
                "outputs": {"foo": "baz 1"},
            },
        ],
    )

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/csv",
    )

    # Dataset has inconsistent number of inputs: should be allowed
    assert response.status_code == 200

    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, with inconsistent number of outputs",
            "data_type": schemas.DataType.kv,
        },
        [
            {
                "inputs": {"baz": "qux 0"},
                "outputs": {"foo": "baz 0", "qux": "quux 0"},
            },
            {
                "inputs": {"baz": "qux 1"},
                "outputs": {"foo": "baz 1"},
            },
        ],
    )

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/csv",
    )

    # Dataset has inconsistent number of outputs: should be allowed
    assert response.status_code == 200

    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, with bad key names",
            "data_type": schemas.DataType.kv,
        },
        [
            {
                "inputs": {"baz": "qux 0", "bar bar": "bar 0"},
                "outputs": {"foo": "baz 0"},
            },
            {
                "inputs": {"baz": "qux 1", "bar bar": "bar 1"},
                "outputs": {"foo": "baz 1"},
            },
        ],
    )

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/csv",
    )

    # Dataset has bad key names (spaces)
    assert response.status_code == 400


async def test_export_dataset_jsonl(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that dataset can be exported to CSV"""

    dataset_size = 2000
    example_args = [
        {
            "inputs": {"baz": f"qux {i}", "foo": f"bar {i}"},
            "outputs": {"foo": f"baz {i}"},
            "metadata": {"foo": f"bar {i}"},
        }
        for i in range(dataset_size)
    ]
    dataset_id = await _create_dataset_and_populate(
        auth_tenant_one,
        {
            "name": random_lower_string(),
            "description": "test kv dataset, with outputs",
            "data_type": schemas.DataType.kv,
        },
        example_args,
    )

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/jsonl",
    )
    assert response.status_code == 200
    body = response.content.decode("utf-8")

    ordered_examples = {i: example_args[i] for i in range(dataset_size)}
    jsonified_body = body.split("\n")
    received_values = [json.loads(j) for j in jsonified_body if j]

    assert len(received_values) == dataset_size
    for received_value in received_values:
        index = int(received_value["metadata"]["foo"][4:])
        assert received_value["inputs"] == ordered_examples[index]["inputs"]
        assert received_value["outputs"] == ordered_examples[index]["outputs"]
        assert received_value["metadata"] == {
            **(ordered_examples[index]["metadata"]),
            **{"dataset_split": ["base"]},
        }


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_read_shared_dataset(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": "input_val_1", "input_2": "input_val_2"},
            "outputs": {"output_1": "output_val_1", "output_2": "output_val_2"},
            "dataset_id": str(dataset_id),
        },
    )
    assert response.status_code == 200
    example_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "reference_dataset_id": str(dataset_id),
            "start_time": "2023-05-05T05:13:24.571809",
        },
    )
    assert response.status_code == 200
    session_id = response.json()["id"]

    run_id_1 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "reference_example_id": example_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "test",
            "score": 10,
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.put(f"/datasets/{dataset_id}/share")
    assert response.status_code == 200
    share_token = response.json()["share_token"]

    response = await http_tenant_one.get(f"/public/{share_token}/datasets")
    assert response.status_code == 200
    assert response.json()["id"] == dataset_id

    response = await http_tenant_one.get(f"/public/{share_token}/examples")
    assert response.status_code == 200
    assert response.json()[0]["id"] == example_id

    response = await http_tenant_one.get(f"/public/{share_token}/datasets/sessions")
    assert response.status_code == 200
    sessions = response.json()
    assert len(sessions) == 1
    assert sessions[0]["id"] == session_id
    assert sessions[0].get("latency_p50")
    assert sessions[0].get("latency_p99")
    assert sessions[0].get("feedback_stats").get("test")

    response = await http_tenant_one.post(
        f"/public/{share_token}/datasets/runs/query", json={"session": [session_id]}
    )

    assert response.status_code == 200, response.text
    assert response.json()["runs"][0]["id"] == str(run_id_1)

    response = await http_tenant_one.post(
        f"/public/{share_token}/datasets/runs/query",
        json={"reference_example": [example_id]},
    )

    assert response.status_code == 200, response.text
    assert response.json()["runs"][0]["id"] == str(run_id_1)

    response = await http_tenant_one.post(
        f"/public/{share_token}/examples/runs",
        json={
            "session_ids": [session_id],
        },
    )
    assert response.status_code == 200
    assert response.json()[0]["id"] == example_id
    assert response.json()[0]["runs"][0]["id"] == str(run_id_1)

    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/feedback?run={run_id_1}"
    )
    assert response.status_code == 200
    assert response.json()[0]["run_id"] == str(run_id_1)
    assert response.json()[0]["score"] == 10

    # test that we can't read another project from the sharer if it's not associated with the dataset

    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
        },
    )
    assert response.status_code == 200
    session_id_2 = response.json()["id"]

    run_id_2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id_2,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "test",
            "score": 10,
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code in (200, 202)

    await wait_until_task_queue_empty()

    response = await http_tenant_one.get(f"/public/{share_token}/datasets/sessions")
    assert response.status_code == 200
    assert session_id_2 not in [session["id"] for session in response.json()]

    response = await http_tenant_one.post(
        f"/public/{share_token}/datasets/runs/query", json={"session": [session_id_2]}
    )
    assert response.status_code == 400, response.text

    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/runs/{run_id_2}"
    )
    assert response.status_code == 404

    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/feedback?run={run_id_2}"
    )
    assert response.status_code == 200
    assert response.json() == []

    # delete share
    response = await http_tenant_one.delete(f"/datasets/{dataset_id}/share")
    assert response.status_code == 200
    response = await http_tenant_one.get(f"/public/{share_token}/datasets")
    assert response.status_code == 404
    response = await http_tenant_one.get(f"/public/{share_token}/datasets")
    assert response.status_code == 404
    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/feedback?run={run_id_1}"
    )
    assert response.status_code == 404


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["oauth", "none"],
    reason="write queue/multiple tenants",
)
async def test_read_test_run_comparisons(
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    # Create an isolated dataset for this test for tenant one
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "comparison view test",
        },
    )
    assert response.status_code == 200

    # Create 4 examples for this dataset
    dataset_id = response.json()["id"]
    example_ids = [uuid4() for _ in range(4)]
    example_tasks = [
        http_tenant_one.post(
            "/examples",
            json={
                "inputs": {"input": f"input_val_{example_id}"},
                "outputs": {"output": f"output_val_{example_id}"},
                "dataset_id": str(dataset_id),
                "id": str(example_id),
            },
        )
        for example_id in example_ids
    ]
    responses = await asyncio.gather(*example_tasks)
    for response in responses:
        assert response.status_code == 200

    # Create 3 test sessions for this dataset
    session_ids = [uuid4() for _ in range(3)]
    session_tasks = [
        http_tenant_one.post(
            "/sessions",
            json={
                "name": random_lower_string(),
                "reference_dataset_id": str(dataset_id),
                "start_time": datetime.now(timezone.utc).isoformat(),
                "id": str(session_id),
            },
        )
        for session_id in session_ids
    ]
    responses = await asyncio.gather(*session_tasks)
    for response in responses:
        assert response.status_code == 200

    def create_run_feedback_tasks(session_index):
        run_ids = [uuid4() for _ in range(4)]

        if session_index == 0:
            names = ["run_2", "run_1", "run_1", "run_1"]
            feedback_scores = [100, 10, 0, 0]
            feedback_values = ["correct", "incorrect", "incorrect", "incorrect"]
        elif session_index == 1:
            names = ["run_2", "run_1", "run_2", "run_2"]
            feedback_scores = [0, 10, 10, 10]
            feedback_values = ["incorrect", "correct", "correct", "correct"]
        else:  # Assuming session_index == 2
            names = ["run_3", "run_3", "run_3", "run_3"]
            feedback_scores = [10, 10, 10, 9]
            feedback_values = ["correct", "correct", "incorrect", "incorrect"]

        return (
            [
                http_tenant_one.post(
                    "/runs",
                    json={
                        "name": name,
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "AgentExecutor"},
                        "inputs": {"input": f"input_val_{example_id}"},
                        "outputs": {"output": f"output_val_{example_id}"},
                        "session_id": str(session_ids[session_index]),
                        "reference_example_id": str(example_id),
                        "parent_run_id": None,
                        "run_type": "chain",
                        "id": str(run_id),
                    },
                )
                for run_id, name, example_id in zip(run_ids, names, example_ids)
            ],
            [
                http_tenant_one.post(
                    "/feedback",
                    json={
                        "run_id": str(run_id),
                        "key": "test",
                        "score": score,
                        "feedback_source": {"type": "api"},
                    },
                )
                for run_id, score, value in zip(
                    run_ids, feedback_scores, feedback_values
                )
            ]
            + [
                http_tenant_one.post(
                    "/feedback",
                    json={
                        "run_id": str(run_id),
                        "key": "test_val",
                        "value": value,
                        "feedback_source": {"type": "api"},
                    },
                )
                for run_id, score, value in zip(
                    run_ids, feedback_scores, feedback_values
                )
            ],
        )

    # Gather all run tasks across all sessions
    all_run_tasks, all_feedback_tasks = [], []
    for i in range(3):
        run_tasks, feedback_tasks = create_run_feedback_tasks(i)
        all_run_tasks.extend(run_tasks)
        all_feedback_tasks.extend(feedback_tasks)
    responses = await asyncio.gather(*all_run_tasks)
    for response in responses:
        assert response.status_code == 202
    responses = await asyncio.gather(*all_feedback_tasks)
    for response in responses:
        assert response.status_code in (200, 202), response.text

    feedback_id_to_correct_1 = responses[0].json()["id"]
    feedback_score_to_correct_1 = 10
    feedback_id_to_correct_2 = responses[9].json()["id"]
    feedback_score_to_correct_2 = 0

    await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]

    # Add corrections to the feedback and make sure these get used in the feedback stats instead of the original scores
    response = await http_tenant_one.patch(
        f"/feedback/{feedback_id_to_correct_1}",
        json={
            "correction": {"score": feedback_score_to_correct_1},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.patch(
        f"/feedback/{feedback_id_to_correct_2}",
        json={
            "correction": {"score": feedback_score_to_correct_2},
        },
    )
    assert response.status_code == 200

    # Run a query for the test runs, simulating comparison view with no filters
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
        },
    )

    assert response.status_code == 200
    examples_with_runs_no_filters = response.json()
    assert len(examples_with_runs_no_filters) == 4
    # Find the example with id example_ids[0]
    example_1 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[0])
    )
    assert len(example_1["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_1["runs"], key=lambda x: x["name"])
    ) == [
        "run_2",
        "run_2",
        "run_3",
    ]
    for run in example_1["runs"]:
        assert run["feedback_stats"]["test"]["n"] == 1
        if run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[0]) or run["session_id"] == str(
                session_ids[1]
            )
            if run["session_id"] == str(session_ids[0]):
                assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
            else:
                assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
            assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        else:
            assert False
        assert run["inputs"] == {"input": f"input_val_{example_ids[0]}"}
        assert run["outputs"] == {"output": f"output_val_{example_ids[0]}"}

    # Find the example with id example_ids[1]
    example_2 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[1])
    )
    assert len(example_2["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_2["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_1",
        "run_3",
    ]
    for run in example_2["runs"]:
        assert run["feedback_stats"]["test"]["n"] == 1
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0]) or run["session_id"] == str(
                session_ids[1]
            )
            if run["session_id"] == str(session_ids[0]):
                assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}
            else:
                assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
            assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        else:
            assert False

    # Find the example with id example_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[2])
    )

    assert len(example_3["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_3["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_3["runs"]:
        assert run["feedback_stats"]["test"]["n"] == 1
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
            assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
            assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
            assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}

    # Find the example with id example_ids[3]
    example_4 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[3])
    )
    assert len(example_4["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_4["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_4["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
            assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
            assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
            assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}

    # Try querying with a filter per session
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200

    examples_with_runs_with_filters = response.json()

    assert len(examples_with_runs_with_filters) == 2

    # Find the example with id example_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[2])
    )

    assert len(example_3["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_3["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_3["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Find the example with id example_ids[3]
    example_4 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[3])
    )
    assert len(example_4["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_4["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_4["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Try querying with a feedback filter
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "0"))'
                ],
            },
        },
    )

    assert response.status_code == 200

    examples_with_runs_with_filters = response.json()

    assert len(examples_with_runs_with_filters) == 2

    # Find the example with id example_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[2])
    )

    assert len(example_3["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_3["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_3["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Find the example with id example_ids[3]
    example_4 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[3])
    )
    assert len(example_4["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_4["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_4["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Try querying with a feedback filter (for both score and value)
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "filters": {
                str(session_ids[0]): [
                    'and(and(eq(feedback_key, "test"), eq(feedback_score, "100")), and(eq(feedback_key, "test_val"), eq(feedback_value, "correct")))'
                ],
            },
        },
    )

    assert response.status_code == 200

    examples_with_runs_with_filters = response.json()

    assert len(examples_with_runs_with_filters) == 1

    # Find the example with id example_ids[0]
    example_1 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[0])
    )

    assert len(example_1["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_1["runs"], key=lambda x: x["name"])
    ) == [
        "run_2",
        "run_2",
        "run_3",
    ]
    for run in example_1["runs"]:
        if run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[0]) or run["session_id"] == str(
                session_ids[1]
            )
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    feedback_key = "test"
    # Try querying with sort param DESC
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
            ],
            "sort_params": {
                "sort_by": feedback_key,
                "sort_order": "DESC",
            },
        },
    )
    assert response.status_code == 200
    examples_with_runs_with_sort = response.json()
    # ensure values are in decreasing order
    for i in range(len(examples_with_runs_with_sort) - 1):
        assert (
            examples_with_runs_with_sort[i]["runs"][0]["feedback_stats"]["test"]["avg"]
            >= examples_with_runs_with_sort[i + 1]["runs"][0]["feedback_stats"]["test"][
                "avg"
            ]
        )

    # Try querying with sort param ASC
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
            ],
            "sort_params": {
                "sort_by": feedback_key,
                "sort_order": "ASC",
            },
        },
    )
    assert response.status_code == 200
    examples_with_runs_with_sort = response.json()
    # ensure values are in increasing order
    for i in range(len(examples_with_runs_with_sort) - 1):
        assert (
            examples_with_runs_with_sort[i]["runs"][0]["feedback_stats"][feedback_key][
                "avg"
            ]
            <= examples_with_runs_with_sort[i + 1]["runs"][0]["feedback_stats"][
                feedback_key
            ]["avg"]
        )

    # Try querying with a sort param and a filter
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
            ],
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "0"))'
                ],
            },
            "sort_params": {
                "sort_by": feedback_key,
                "sort_order": "ASC",
            },
        },
    )
    assert response.status_code == 200
    examples_with_runs_with_sort = response.json()
    assert len(examples_with_runs_with_sort) == 2
    assert (
        examples_with_runs_with_sort[0]["runs"][0]["feedback_stats"][feedback_key][
            "avg"
        ]
        == 0
    )
    assert (
        examples_with_runs_with_sort[1]["runs"][0]["feedback_stats"][feedback_key][
            "avg"
        ]
        == 0
    )

    # Try querying with a sort param that doesn't exist
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
            ],
            "sort_params": {
                "sort_by": "asdfasdfasdf",
                "sort_order": "ASC",
            },
        },
    )
    assert response.status_code == 200
    examples_with_runs_with_sort = response.json()
    assert len(examples_with_runs_with_sort) == 4

    # Try filtering for a session that doesn't exist
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(uuid4()),
            ],
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 0

    # Test pagination
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "limit": 2,
            "offset": 2,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.headers["x-pagination-total"] == "4"

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "limit": 2,
            "offset": 1,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.headers["x-pagination-total"] == "4"

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "limit": 2,
            "offset": 0,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.headers["x-pagination-total"] == "3"

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "limit": 2,
            "offset": 3,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.headers["x-pagination-total"] == "4"

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
            "limit": 1,
            "offset": 0,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.headers["x-pagination-total"] == "2"

    # session 0: [10, 10, 0, 0]
    # session 1: [0, 0, 10, 10]
    # session 2: [10, 10, 10, 9]

    example_id_strs = [str(example_id) for example_id in example_ids]

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert res["feedback_deltas"][str(session_ids[2])]["regressed_examples"] == [
        example_id_strs[3]
    ]

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    # Test with filters applied

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_1)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "0"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_1)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "0"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    # Test with multiple sessions

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
                str(session_ids[0]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert res["feedback_deltas"][str(session_ids[2])]["regressed_examples"] == [
        example_id_strs[3]
    ]
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
                str(session_ids[0]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "10"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 1
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    # Test deltas with public shared dataset

    response = await http_tenant_one.put(f"/datasets/{dataset_id}/share")
    assert response.status_code == 200
    share_token = response.json()["share_token"]

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert res["feedback_deltas"][str(session_ids[2])]["regressed_examples"] == [
        example_id_strs[3]
    ]

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    # Test with filters applied

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_1)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "0"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_1)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "0"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    # reverse feedback direction
    response = await http_tenant_one.patch(
        "/feedback-configs/",
        json={
            "feedback_key": "test",
            "is_lower_score_better": True,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
                str(session_ids[0]),
            ],
            "feedback_key": "test",
            "filters": {
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test"), eq(feedback_score, "10"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 1
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )

    # test you can read a pairwise experiment

    # create a pairwise experiment
    response = await http_tenant_one.post(
        "/datasets/comparative",
        json={
            "name": "test",
            "description": "test",
            "experiment_ids": [str(session_ids[0]), str(session_ids[1])],
            "reference_dataset_id": str(dataset_id),
        },
    )
    assert response.status_code == 200
    comparative_experiment_id = response.json()["id"]

    # read it back
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
            ],
            "comparative_experiment_id": str(comparative_experiment_id),
        },
    )
    assert response.status_code == 200


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["oauth", "none"],
    reason="write queue",
)
async def test_read_comparison_view_versioning(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    # Create an isolated dataset for this test for tenant one
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "comparison view test",
        },
    )
    assert response.status_code == 200

    # Create 4 examples for this dataset
    dataset_id = response.json()["id"]
    example_ids = [uuid4() for _ in range(5)]
    example_tasks = [
        http_tenant_one.post(
            "/examples",
            json={
                "inputs": {"input": f"input_val_{example_id}"},
                "outputs": {"output": f"output_val_{example_id}"},
                "dataset_id": str(dataset_id),
                "id": str(example_id),
            },
        )
        for example_id in example_ids[:4]
    ]
    responses = await asyncio.gather(*example_tasks)
    for response in responses:
        assert response.status_code == 200

    # Create 3 test sessions for this dataset
    session_ids = [uuid4() for _ in range(3)]
    session_tasks = [
        http_tenant_one.post(
            "/sessions",
            json={
                "name": random_lower_string(),
                "reference_dataset_id": str(dataset_id),
                "start_time": datetime.now(timezone.utc).isoformat(),
                "id": str(session_id),
            },
        )
        for session_id in session_ids
    ]
    responses = await asyncio.gather(*session_tasks)
    for response in responses:
        assert response.status_code == 200

    # Create one more example after the start time of the session
    response = await http_tenant_one.post(
        "/examples",
        json={
            "inputs": {"input": f"input_val_{example_ids[4]}"},
            "outputs": {"output": f"output_val_{example_ids[4]}"},
            "dataset_id": str(dataset_id),
            "id": str(example_ids[4]),
        },
    )
    assert response.status_code == 200

    def create_run_tasks(session_index):
        run_ids = [uuid4() for _ in range(5)]

        if session_index == 0:
            names = ["run_2", "run_1", "run_1", "run_1", "run_2"]
        elif session_index == 1:
            names = ["run_2", "run_1", "run_2", "run_2", "run_1"]
        else:  # Assuming session_index == 2
            names = ["run_3", "run_3", "run_3", "run_3", "run_3"]

        return [
            http_tenant_one.post(
                "/runs",
                json={
                    "name": name,
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": f"input_val_{example_id}"},
                    "outputs": {"output": f"output_val_{example_id}"},
                    "session_id": str(session_ids[session_index]),
                    "reference_example_id": str(example_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                },
            )
            for run_id, name, example_id in zip(run_ids, names, example_ids)
        ]

    # Gather all run tasks across all sessions
    all_run_tasks = []
    for i in range(3):
        run_tasks = create_run_tasks(i)
        all_run_tasks.extend(run_tasks)
    responses = await asyncio.gather(*all_run_tasks)
    for response in responses:
        assert response.status_code == 202

    await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]

    # Run a query for the test runs, simulating comparison view with no filters
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
        },
    )

    assert response.status_code == 200
    examples_with_runs_no_filters = response.json()
    assert len(examples_with_runs_no_filters) == 5
    # Find the example with id example_ids[0]
    example_1 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[0])
    )
    assert len(example_1["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_1["runs"], key=lambda x: x["name"])
    ) == [
        "run_2",
        "run_2",
        "run_3",
    ]
    for run in example_1["runs"]:
        if run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[0]) or run["session_id"] == str(
                session_ids[1]
            )
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
        else:
            assert False
        assert run["inputs"] == {"input": f"input_val_{example_ids[0]}"}
        assert run["outputs"] == {"output": f"output_val_{example_ids[0]}"}

    # Find the example with id example_ids[1]
    example_2 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[1])
    )
    assert len(example_2["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_2["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_1",
        "run_3",
    ]
    for run in example_2["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0]) or run["session_id"] == str(
                session_ids[1]
            )
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
        else:
            assert False

    # Find the example with id example_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[2])
    )

    assert len(example_3["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_3["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_3["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Find the example with id example_ids[3]
    example_4 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[3])
    )
    assert len(example_4["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_4["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_4["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Find the example with id example_ids[4]
    example_5 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[4])
    )
    assert len(example_5["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_5["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_5["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])


async def test_example_count(
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that the example count is correct after example CRUD."""
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
        ),
    )
    assert dataset.created_at == dataset.modified_at
    example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            dataset_id=UUID(str(dataset.id)),
            inputs={"input": "input_val"},
            outputs={"output": "output_val"},
        ),
    )

    dataset = await crud.get_dataset(auth_tenant_one, dataset.id)  # type: ignore
    assert dataset.example_count == 1
    example = await crud.update_example(
        auth_tenant_one,
        example.id,
        schemas.ExampleUpdate(
            inputs={"input": "input_val"},
            outputs={"output": "output_val"},
        ),
    )

    dataset = await crud.get_dataset(auth_tenant_one, dataset.id)  # type: ignore
    assert dataset.example_count == 1
    await crud.delete_example(auth_tenant_one, example.id)
    dataset = await crud.get_dataset(auth_tenant_one, dataset.id)  # type: ignore
    assert dataset.example_count == 0


async def test_dataset_versioning(
    http_tenant_one: AsyncClient,
) -> None:
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1_version_1": "input_val_1_version_1"},
            "outputs": {"output_1": "output_val_1"},
            "dataset_id": str(dataset_id),
        },
    )
    assert response.status_code == 200
    example_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1_2_version_1": "input_val_1_2_version_1"},
            "outputs": {"output_1": "output_val_1"},
            "dataset_id": str(dataset_id),
        },
    )
    assert response.status_code == 200
    example_id_2 = response.json()["id"]

    response = await http_tenant_one.patch(
        f"/examples/{example_id}",
        json={"inputs": {"input_1_version_2": "input_val_1_version_2"}},
    )
    assert response.status_code == 200

    response = await http_tenant_one.patch(
        f"/examples/{example_id_2}",
        json={"inputs": {"input_1_2_version_2": "input_val_version_2"}},
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions",
    )
    assert response.status_code == 200
    all_versions = response.json()
    assert len(all_versions) == 3
    all_versions = sorted(all_versions, key=lambda x: x["as_of"])

    original_version = all_versions[0]
    original_version_as_of = original_version["as_of"]

    second_version = all_versions[1]
    second_version_as_of = second_version["as_of"]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/version?tag=latest",
    )
    assert response.status_code == 200
    latest_version = response.json()
    assert latest_version["as_of"] == all_versions[-1]["as_of"]
    latest_version_time = latest_version["as_of"]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions?example={example_id}",
    )
    assert response.status_code == 200
    example_versions = response.json()
    assert len(example_versions) == 2

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions?search=late",
    )
    assert response.status_code == 200
    example_versions = response.json()
    assert len(example_versions) == 1

    response = await http_tenant_one.put(
        f"/datasets/{dataset_id}/tags",
        json={"tag": "dev", "as_of": latest_version_time},
    )
    assert response.status_code == 200

    response = await http_tenant_one.put(
        f"/datasets/{dataset_id}/tags",
        json={"tag": "prod", "as_of": original_version_as_of},
    )
    assert response.status_code == 200

    response = await http_tenant_one.put(
        f"/datasets/{dataset_id}/tags",
        json={"tag": "staging", "as_of": second_version_as_of},
    )
    assert response.status_code == 200

    response = await http_tenant_one.put(
        f"/datasets/{dataset_id}/tags",
        json={"tag": "staging2", "as_of": second_version_as_of},
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/version?tag=staging2",
    )
    assert response.status_code == 200
    assert response.json()["as_of"] == second_version_as_of
    assert set(response.json()["tags"]) == {"staging", "staging2"}

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/version?tag=dev",
    )
    assert response.status_code == 200
    assert response.json()["as_of"] == latest_version_time

    response = await http_tenant_one.get(
        f"/examples?as_of=latest&dataset={dataset_id}",
    )
    assert response.status_code == 200
    latest_version_examples = response.json()
    assert len(latest_version_examples) == 2
    example_1 = next(
        example
        for example in latest_version_examples
        if example["id"] == str(example_id)
    )
    example_2 = next(
        example
        for example in latest_version_examples
        if example["id"] == str(example_id_2)
    )
    assert example_1["inputs"] == {"input_1_version_2": "input_val_1_version_2"}
    assert example_2["inputs"] == {"input_1_2_version_2": "input_val_version_2"}

    response = await http_tenant_one.get(
        f"/examples/count?as_of=latest&dataset={dataset_id}",
    )
    assert response.status_code == 200
    prod_version_examples_count = response.json()
    assert prod_version_examples_count == 2

    response = await http_tenant_one.get(
        f"/examples/{example_id}?as_of=latest",
    )
    assert response.status_code == 200
    latest_version_example_1 = response.json()
    assert {
        **latest_version_example_1,
        "modified_at": datetime.fromisoformat(latest_version_example_1["modified_at"]),
    } == {**example_1, "modified_at": datetime.fromisoformat(example_1["modified_at"])}

    response = await http_tenant_one.get(
        f"/examples?as_of=dev&dataset={dataset_id}",
    )
    assert response.status_code == 200
    dev_version_examples = response.json()
    assert len(dev_version_examples) == 2
    example_1 = next(
        example for example in dev_version_examples if example["id"] == str(example_id)
    )
    example_2 = next(
        example
        for example in dev_version_examples
        if example["id"] == str(example_id_2)
    )
    assert example_1["inputs"] == {"input_1_version_2": "input_val_1_version_2"}
    assert example_2["inputs"] == {"input_1_2_version_2": "input_val_version_2"}

    response = await http_tenant_one.get(
        f"/examples/count?as_of=dev&dataset={dataset_id}",
    )
    assert response.status_code == 200
    prod_version_examples_count = response.json()
    assert prod_version_examples_count == 2

    response = await http_tenant_one.get(
        f"/examples?as_of=prod&dataset={dataset_id}",
    )
    assert response.status_code == 200
    prod_version_examples = response.json()
    assert len(prod_version_examples) == 2
    example_1 = next(
        example for example in prod_version_examples if example["id"] == str(example_id)
    )
    example_2 = next(
        example
        for example in prod_version_examples
        if example["id"] == str(example_id_2)
    )
    assert example_1["inputs"] == {"input_1_version_1": "input_val_1_version_1"}
    assert example_2["inputs"] == {"input_1_2_version_1": "input_val_1_2_version_1"}

    response = await http_tenant_one.get(
        f"/examples/count?as_of=prod&dataset={dataset_id}",
    )
    assert response.status_code == 200
    prod_version_examples_count = response.json()
    assert prod_version_examples_count == 2

    response = await http_tenant_one.get(
        f"/examples?as_of={urllib.parse.quote(original_version_as_of)}&dataset={dataset_id}",
    )
    assert response.status_code == 200
    prod_version_examples = response.json()
    assert len(prod_version_examples) == 2
    example_1 = next(
        example for example in prod_version_examples if example["id"] == str(example_id)
    )
    example_2 = next(
        example
        for example in prod_version_examples
        if example["id"] == str(example_id_2)
    )
    assert example_1["inputs"] == {"input_1_version_1": "input_val_1_version_1"}
    assert example_2["inputs"] == {"input_1_2_version_1": "input_val_1_2_version_1"}

    response = await http_tenant_one.get(
        f"/examples/count?as_of={urllib.parse.quote(original_version_as_of)}&dataset={dataset_id}",
    )
    assert response.status_code == 200
    prod_version_examples_count = response.json()
    assert prod_version_examples_count == 2

    response = await http_tenant_one.get(
        f"/examples/{example_id}?as_of={urllib.parse.quote(second_version_as_of)}",
    )
    assert response.status_code == 200, response.text
    second_version_example_1 = response.json()
    assert second_version_example_1["inputs"] == {
        "input_1_version_2": "input_val_1_version_2"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id}?as_of=staging",
    )
    assert response.status_code == 200
    second_version_example_1 = response.json()
    assert second_version_example_1["inputs"] == {
        "input_1_version_2": "input_val_1_version_2"
    }

    # In the second version, the first example should have been updated, but not the second
    response = await http_tenant_one.get(
        f"/examples/{example_id_2}?as_of={urllib.parse.quote(second_version_as_of)}",
    )
    assert response.status_code == 200
    second_version_example_2 = response.json()
    assert second_version_example_2["inputs"] == {
        "input_1_2_version_1": "input_val_1_2_version_1"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id_2}?as_of=staging",
    )
    assert response.status_code == 200
    second_version_example_2 = response.json()
    assert second_version_example_2["inputs"] == {
        "input_1_2_version_1": "input_val_1_2_version_1"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id}?as_of={urllib.parse.quote(original_version_as_of)}",
    )
    assert response.status_code == 200
    original_version_example_1 = response.json()
    assert original_version_example_1["inputs"] == {
        "input_1_version_1": "input_val_1_version_1"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id}?as_of=prod",
    )
    assert response.status_code == 200
    original_version_example_1 = response.json()
    assert original_version_example_1["inputs"] == {
        "input_1_version_1": "input_val_1_version_1"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id_2}?as_of={urllib.parse.quote(original_version_as_of)}",
    )
    assert response.status_code == 200
    orgiginal_version_example_2 = response.json()
    assert orgiginal_version_example_2["inputs"] == {
        "input_1_2_version_1": "input_val_1_2_version_1"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id_2}?as_of=prod",
    )
    assert response.status_code == 200
    orgiginal_version_example_2 = response.json()
    assert orgiginal_version_example_2["inputs"] == {
        "input_1_2_version_1": "input_val_1_2_version_1"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id}?as_of={urllib.parse.quote(latest_version_time)}",
    )
    assert response.status_code == 200
    latest_version_example_1 = response.json()
    assert latest_version_example_1["inputs"] == {
        "input_1_version_2": "input_val_1_version_2"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id}?as_of=dev",
    )
    assert response.status_code == 200
    latest_version_example_1 = response.json()
    assert latest_version_example_1["inputs"] == {
        "input_1_version_2": "input_val_1_version_2"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id_2}?as_of={urllib.parse.quote(latest_version_time)}",
    )
    assert response.status_code == 200
    latest_version_example_2 = response.json()
    assert latest_version_example_2["inputs"] == {
        "input_1_2_version_2": "input_val_version_2"
    }

    response = await http_tenant_one.get(
        f"/examples/{example_id_2}?as_of=dev",
    )
    assert response.status_code == 200
    latest_version_example_2 = response.json()
    assert latest_version_example_2["inputs"] == {
        "input_1_2_version_2": "input_val_version_2"
    }

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions/diff?from_version=dev&to_version=prod",
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions/diff?from_version=prod&to_version=dev",
    )
    assert response.status_code == 200
    diff = response.json()
    assert len(diff["examples_removed"]) == 0
    assert len(diff["examples_added"]) == 0
    assert len(diff["examples_modified"]) == 2

    assert (
        example_id in diff["examples_modified"]
        and example_id_2 in diff["examples_modified"]
    )

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions/diff?from_version=staging&to_version=prod",
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions/diff?from_version=prod&to_version=staging",
    )
    assert response.status_code == 200
    diff = response.json()
    assert len(diff["examples_removed"]) == 0
    assert len(diff["examples_added"]) == 0
    assert len(diff["examples_modified"]) == 1

    assert example_id in diff["examples_modified"]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions/diff?from_version=dev&to_version=staging",
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions/diff?from_version=staging&to_version=dev",
    )
    assert response.status_code == 200
    diff = response.json()
    assert len(diff["examples_removed"]) == 0
    assert len(diff["examples_added"]) == 0
    assert len(diff["examples_modified"]) == 1

    assert example_id_2 in diff["examples_modified"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "inputs": {"input_1_3_version_1": "input_val_1_3_version_1"},
            "outputs": {"output_1": "output_val_1"},
            "dataset_id": str(dataset_id),
        },
    )
    assert response.status_code == 200
    example_id_3 = response.json()["id"]

    response = await http_tenant_one.delete(
        f"/examples/{example_id}",
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions/diff?from_version=dev&to_version=latest",
    )
    assert response.status_code == 200
    diff = response.json()
    assert len(diff["examples_removed"]) == 1
    assert len(diff["examples_added"]) == 1
    assert len(diff["examples_modified"]) == 0

    assert example_id in diff["examples_removed"]
    assert example_id_3 in diff["examples_added"]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}/versions/diff?from_version={urllib.parse.quote(latest_version_time)}&to_version=latest",
    )
    assert response.status_code == 200
    diff = response.json()
    assert len(diff["examples_removed"]) == 1
    assert len(diff["examples_added"]) == 1
    assert len(diff["examples_modified"]) == 0

    assert example_id in diff["examples_removed"]
    assert example_id_3 in diff["examples_added"]


async def test_shared_dataset_versioning(
    http_tenant_one: AsyncClient,
) -> None:
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    _dataset_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1_version_1": "input_val_1_version_1"},
            "outputs": {"output_1": "output_val_1"},
            "dataset_id": str(_dataset_id),
        },
    )
    assert response.status_code == 200
    example_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1_2_version_1": "input_val_1_2_version_1"},
            "outputs": {"output_1": "output_val_1"},
            "dataset_id": str(_dataset_id),
        },
    )
    assert response.status_code == 200
    example_id_2 = response.json()["id"]

    response = await http_tenant_one.patch(
        f"/examples/{example_id}",
        json={"inputs": {"input_1_version_2": "input_val_1_version_2"}},
    )
    assert response.status_code == 200

    response = await http_tenant_one.patch(
        f"/examples/{example_id_2}",
        json={"inputs": {"input_1_2_version_2": "input_val_version_2"}},
    )
    assert response.status_code == 200

    response = await http_tenant_one.put(f"/datasets/{_dataset_id}/share")
    assert response.status_code == 200
    share_token = response.json()["share_token"]

    # TODO: make listing versions available publicly
    response = await http_tenant_one.get(
        f"/datasets/{_dataset_id}/versions",
    )
    assert response.status_code == 200
    all_versions = response.json()
    assert len(all_versions) == 3
    all_versions = sorted(all_versions, key=lambda x: x["as_of"])

    original_version = all_versions[0]
    original_version_as_of = original_version["as_of"]

    second_version = all_versions[1]
    second_version_as_of = second_version["as_of"]

    response = await http_tenant_one.get(
        f"/datasets/{_dataset_id}/version?tag=latest",
    )
    assert response.status_code == 200
    latest_version = response.json()
    assert latest_version["as_of"] == all_versions[-1]["as_of"]
    latest_version_time = latest_version["as_of"]

    response = await http_tenant_one.put(
        f"/datasets/{_dataset_id}/tags",
        json={"tag": "dev", "as_of": latest_version_time},
    )
    assert response.status_code == 200

    response = await http_tenant_one.put(
        f"/datasets/{_dataset_id}/tags",
        json={"tag": "prod", "as_of": original_version_as_of},
    )
    assert response.status_code == 200

    response = await http_tenant_one.put(
        f"/datasets/{_dataset_id}/tags",
        json={"tag": "staging", "as_of": second_version_as_of},
    )
    assert response.status_code == 200

    response = await http_tenant_one.put(
        f"/datasets/{_dataset_id}/tags",
        json={"tag": "staging2", "as_of": second_version_as_of},
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples?as_of=latest",
    )
    assert response.status_code == 200
    latest_version_examples = response.json()
    assert len(latest_version_examples) == 2
    example_1 = next(
        example
        for example in latest_version_examples
        if example["id"] == str(example_id)
    )
    example_2 = next(
        example
        for example in latest_version_examples
        if example["id"] == str(example_id_2)
    )
    assert example_1["inputs"] == {"input_1_version_2": "input_val_1_version_2"}
    assert example_2["inputs"] == {"input_1_2_version_2": "input_val_version_2"}

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples/count?as_of=latest",
    )
    assert response.status_code == 200
    prod_version_examples_count = response.json()
    assert prod_version_examples_count == 2

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples?as_of=dev",
    )
    assert response.status_code == 200
    dev_version_examples = response.json()
    assert len(dev_version_examples) == 2
    example_1 = next(
        example for example in dev_version_examples if example["id"] == str(example_id)
    )
    example_2 = next(
        example
        for example in dev_version_examples
        if example["id"] == str(example_id_2)
    )
    assert example_1["inputs"] == {"input_1_version_2": "input_val_1_version_2"}
    assert example_2["inputs"] == {"input_1_2_version_2": "input_val_version_2"}

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples/count?as_of=dev",
    )
    assert response.status_code == 200
    prod_version_examples_count = response.json()
    assert prod_version_examples_count == 2

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples?as_of=prod",
    )
    assert response.status_code == 200
    prod_version_examples = response.json()
    assert len(prod_version_examples) == 2
    example_1 = next(
        example for example in prod_version_examples if example["id"] == str(example_id)
    )
    example_2 = next(
        example
        for example in prod_version_examples
        if example["id"] == str(example_id_2)
    )
    assert example_1["inputs"] == {"input_1_version_1": "input_val_1_version_1"}
    assert example_2["inputs"] == {"input_1_2_version_1": "input_val_1_2_version_1"}

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples/count?as_of=prod",
    )
    assert response.status_code == 200
    prod_version_examples_count = response.json()
    assert prod_version_examples_count == 2

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples?as_of={urllib.parse.quote(original_version_as_of)}",
    )
    assert response.status_code == 200
    prod_version_examples = response.json()
    assert len(prod_version_examples) == 2
    example_1 = next(
        example for example in prod_version_examples if example["id"] == str(example_id)
    )
    example_2 = next(
        example
        for example in prod_version_examples
        if example["id"] == str(example_id_2)
    )
    assert example_1["inputs"] == {"input_1_version_1": "input_val_1_version_1"}
    assert example_2["inputs"] == {"input_1_2_version_1": "input_val_1_2_version_1"}

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples/count?as_of={urllib.parse.quote(original_version_as_of)}",
    )
    assert response.status_code == 200
    prod_version_examples_count = response.json()
    assert prod_version_examples_count == 2

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples?as_of={urllib.parse.quote(original_version_as_of)}",
    )
    assert response.status_code == 200
    prod_version_examples = response.json()
    assert len(prod_version_examples) == 2
    example_1 = next(
        example for example in prod_version_examples if example["id"] == str(example_id)
    )
    example_2 = next(
        example
        for example in prod_version_examples
        if example["id"] == str(example_id_2)
    )
    assert example_1["inputs"] == {"input_1_version_1": "input_val_1_version_1"}
    assert example_2["inputs"] == {"input_1_2_version_1": "input_val_1_2_version_1"}

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples/count?as_of={urllib.parse.quote(original_version_as_of)}",
    )
    assert response.status_code == 200
    prod_version_examples_count = response.json()
    assert prod_version_examples_count == 2


async def test_dataset_schema(
    http_tenant_one: AsyncClient,
) -> None:
    simple_schema_definition = {
        "type": "object",
        "properties": {
            "input_1": {"type": "string"},
            "input_2": {"type": "number"},
        },
        "required": ["input_1"],
    }
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
            "inputs_schema_definition": simple_schema_definition,
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["inputs_schema_definition"] == simple_schema_definition
    assert dataset["outputs_schema_definition"] is None

    new_name = random_lower_string()
    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "name": new_name,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["name"] == new_name
    assert dataset["inputs_schema_definition"] == simple_schema_definition
    assert dataset["outputs_schema_definition"] is None

    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "inputs_schema_definition": None,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["name"] == new_name
    assert dataset["inputs_schema_definition"] is None
    assert dataset["outputs_schema_definition"] is None

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
            "outputs_schema_definition": simple_schema_definition,
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["inputs_schema_definition"] is None
    assert dataset["outputs_schema_definition"] == simple_schema_definition

    new_name = random_lower_string()
    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "name": new_name,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["name"] == new_name
    assert dataset["inputs_schema_definition"] is None
    assert dataset["outputs_schema_definition"] == simple_schema_definition

    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "outputs_schema_definition": None,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["name"] == new_name
    assert dataset["inputs_schema_definition"] is None
    assert dataset["outputs_schema_definition"] is None

    invalid_json_schema = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "type": "object",
        "properties": {
            "items": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "integer"},
                        "name": {"type": "string"},
                        "quantity": {"type": "integer"},
                    },
                    "required": ["id", "name", "quantity"],
                },
            }
        },
        "required": ["items"],
        "additionalProperties": "false",  # This is invalid because it should be a boolean, not a string
    }

    invalid_json_schema_2 = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "type": "object",
        "properties": {
            "items": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "integer"},
                        "name": {"type": "string"},
                        "quantity": {"type": "integer"},
                    },
                    "required": ["id", "name", "quantity"],
                },
            }
        },
        "required": ["items"],
        "minProperties": -1,  # This is invalid because minProperties cannot be negative
    }

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
            "inputs_schema_definition": invalid_json_schema,
        },
    )
    assert response.status_code == 400

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
            "outputs_schema_definition": invalid_json_schema_2,
        },
    )
    assert response.status_code == 400


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["oauth", "none"],
    reason="write queue",
)
@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_upload_experiment_results(
    fresh_tenant: tuple[AsyncClient, AuthInfo],
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    patch_session_aggregation: bool,
) -> None:
    """Test uploading experiment results"""
    http_tenant_one, _ = fresh_tenant
    dataset_id = uuid4()
    dataset_name = random_lower_string()
    dataset_description = random_lower_string()
    experiment_name = random_lower_string()
    experiment_description = random_lower_string()
    experiment_metadata = {"key": random_lower_string()}

    summary_experiment_scores = [
        {
            "key": "summary_accuracy",
            "score": 0.9,
            "comment": "Great job!",
        }
    ]

    row_ids = [uuid4(), uuid4(), uuid4()]
    inputs = [random_lower_string(), random_lower_string(), random_lower_string()]
    outputs = [None, random_lower_string(), random_lower_string()]
    expected_outputs = [random_lower_string(), None, random_lower_string()]
    accuracy_scores = [0.9, 0.8, 0.7]
    accuracy_comments = [
        random_lower_string(),
        random_lower_string(),
        random_lower_string(),
    ]

    correctness_scores = [0.9, 0.8, 0.7]
    correctness_comments = [
        random_lower_string(),
        random_lower_string(),
        random_lower_string(),
    ]

    color_values = [random_lower_string(), random_lower_string(), random_lower_string()]

    experiment_start_time = datetime.now(timezone.utc)

    # run start times must be after experiment start time
    run_start_times = [
        experiment_start_time + timedelta(seconds=1),
        experiment_start_time + timedelta(seconds=2),
        experiment_start_time + timedelta(seconds=3),
    ]
    run_end_times = [
        run_start_time + timedelta(seconds=1) for run_start_time in run_start_times
    ]

    experiment_end_time = datetime.now(timezone.utc) + timedelta(seconds=5)

    run_names = [random_lower_string(), random_lower_string(), None]
    error = [None, None, random_lower_string()]
    run_metadata = [
        {"key": random_lower_string()},
        None,
        {"key": random_lower_string()},
    ]

    results = [
        {
            "row_id": str(row_id),
            "inputs": {"input": input},
            "expected_outputs": {"output": expected_output}
            if expected_output is not None
            else None,
            "actual_outputs": {"output": output} if output is not None else None,
            "evaluation_scores": [
                {
                    "key": "accuracy",
                    "score": accuracy_score,
                    "comment": accuracy_comment,
                },
                {
                    "key": "correctness",
                    "score": correctness_score,
                    "comment": correctness_comment,
                },
                {"key": "color", "value": color_value},
            ],
            "start_time": run_start_time.isoformat(),
            "end_time": run_end_time.isoformat(),
            "run_name": run_name,
            "error": error,
            "run_metadata": metadata,
        }
        for row_id, input, output, expected_output, accuracy_score, accuracy_comment, correctness_score, correctness_comment, color_value, run_start_time, run_end_time, run_name, error, metadata in zip(
            row_ids,
            inputs,
            outputs,
            expected_outputs,
            accuracy_scores,
            accuracy_comments,
            correctness_scores,
            correctness_comments,
            color_values,
            run_start_times,
            run_end_times,
            run_names,
            error,
            run_metadata,
        )
    ]

    payload = {
        "experiment_name": experiment_name,
        "experiment_description": experiment_description,
        "dataset_id": str(dataset_id),
        "dataset_name": dataset_name,
        "dataset_description": dataset_description,
        "summary_experiment_scores": summary_experiment_scores,
        "results": results,
        "experiment_start_time": experiment_start_time.isoformat(),
        "experiment_end_time": experiment_end_time.isoformat(),
        "experiment_metadata": experiment_metadata,
    }

    response = await http_tenant_one.post(
        "/datasets/upload-experiment",
        json=payload,
    )
    assert response.status_code == 200
    session_id = response.json()["experiment"]["id"]

    await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["name"] == dataset_name
    assert dataset["description"] == dataset_description
    assert dataset["example_count"] == 3
    assert dataset["session_count"] == 1
    assert dataset["data_type"] == "kv"

    response = await http_tenant_one.get(
        f"/examples?dataset={dataset_id}",
    )
    assert response.status_code == 200

    examples = response.json()
    assert len(examples) == 3

    response = await http_tenant_one.get(
        f"/examples/count?dataset={dataset_id}",
    )
    assert response.status_code == 200

    examples_count = response.json()
    assert examples_count == 3

    for row_id, input, expected_output in zip(row_ids, inputs, expected_outputs):
        example = next(example for example in examples if example["id"] == str(row_id))
        assert example["inputs"] == {"input": input}
        assert example["outputs"] == (
            {"output": expected_output} if expected_output is not None else None
        )

    response = await http_tenant_one.get(
        f"/sessions?reference_dataset={dataset_id}",
    )
    assert response.status_code == 200

    sessions = response.json()
    assert len(sessions) == 1

    session = sessions[0]
    assert session["name"] == experiment_name
    assert session["description"] == experiment_description
    assert session["extra"] == {"metadata": experiment_metadata}
    assert (
        session["start_time"] == experiment_start_time.replace(tzinfo=None).isoformat()
    )  # have to remove tzinfo because the start_time column is not timezone aware
    assert session["end_time"] == experiment_end_time.isoformat()
    assert session["run_count"] == 3
    assert session["test_run_number"] == 1
    assert session["latency_p50"] == 1.0
    assert session["latency_p99"] == 1.0
    assert session["error_rate"] == 1 / 3
    assert (
        session["last_run_start_time"]
        == run_start_times[-1].replace(tzinfo=None).isoformat()
    )
    feedback_stats = session["feedback_stats"]

    assert math.isclose(feedback_stats["accuracy"]["avg"], 0.8, rel_tol=1e-9)
    assert math.isclose(
        feedback_stats["accuracy"]["stdev"], 0.081649658092773, rel_tol=1e-9
    )
    assert feedback_stats["accuracy"]["n"] == 3
    assert math.isclose(feedback_stats["correctness"]["avg"], 0.8, rel_tol=1e-9)
    assert math.isclose(
        feedback_stats["correctness"]["stdev"], 0.081649658092773, rel_tol=1e-9
    )
    assert feedback_stats["correctness"]["n"] == 3
    assert feedback_stats["color"]["avg"] is None
    assert feedback_stats["color"]["stdev"] is None
    assert feedback_stats["color"]["n"] == 3
    assert feedback_stats["color"]["values"] == {k: 1 for k in color_values}

    # Run a query for the test runs, simulating comparison view with no filters
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_id),
            ],
        },
    )

    assert response.status_code == 200
    examples_with_runs_no_filters = response.json()
    assert len(examples_with_runs_no_filters) == 3
    # Find the example with id row_ids[0]
    example_1 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[0])
    )
    assert len(example_1["runs"]) == 1
    assert example_1["runs"][0]["name"] == run_names[0]
    assert example_1["runs"][0]["feedback_stats"]["correctness"]["n"] == 1
    assert example_1["runs"][0]["feedback_stats"]["correctness"]["avg"] == 0.9
    assert example_1["runs"][0]["feedback_stats"]["accuracy"]["n"] == 1
    assert example_1["runs"][0]["feedback_stats"]["accuracy"]["avg"] == 0.9
    assert example_1["runs"][0]["feedback_stats"]["color"]["values"] == {
        color_values[0]: 1
    }
    assert example_1["runs"][0]["error"] is None
    assert example_1["runs"][0]["extra"] == {
        **(run_metadata[0] if run_metadata[0] is not None else {}),
        "metadata": {"ls_example_dataset_split": ["base"], "ls_run_depth": 0},
    }
    assert (
        example_1["runs"][0]["start_time"]
        == run_start_times[0].replace(tzinfo=None).isoformat()
    )
    assert (
        example_1["runs"][0]["end_time"]
        == run_end_times[0].replace(tzinfo=None).isoformat()
    )
    assert example_1["runs"][0]["inputs"] == {"input": inputs[0]}
    assert example_1["runs"][0]["outputs"] is None

    assert example_1["inputs"] == {"input": inputs[0]}
    assert example_1["outputs"] == {"output": expected_outputs[0]}

    # Find the example with id row_ids[1]
    example_2 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[1])
    )
    assert len(example_2["runs"]) == 1
    assert example_2["runs"][0]["name"] == run_names[1]
    assert example_2["runs"][0]["feedback_stats"]["correctness"]["n"] == 1
    assert example_2["runs"][0]["feedback_stats"]["correctness"]["avg"] == 0.8
    assert example_2["runs"][0]["feedback_stats"]["accuracy"]["n"] == 1
    assert example_2["runs"][0]["feedback_stats"]["accuracy"]["avg"] == 0.8
    assert example_2["runs"][0]["feedback_stats"]["color"]["values"] == {
        color_values[1]: 1
    }
    assert example_2["runs"][0]["error"] is None
    assert example_2["runs"][0]["extra"] == {
        "metadata": {"ls_example_dataset_split": ["base"], "ls_run_depth": 0}
    }
    assert (
        example_2["runs"][0]["start_time"]
        == run_start_times[1].replace(tzinfo=None).isoformat()
    )
    assert (
        example_2["runs"][0]["end_time"]
        == run_end_times[1].replace(tzinfo=None).isoformat()
    )
    assert example_2["runs"][0]["inputs"] == {"input": inputs[1]}
    assert example_2["runs"][0]["outputs"] == {"output": outputs[1]}

    assert example_2["inputs"] == {"input": inputs[1]}
    assert example_2["outputs"] is None

    # Find the example with id row_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[2])
    )
    assert len(example_3["runs"]) == 1
    assert example_3["runs"][0]["name"] == "Target"
    assert example_3["runs"][0]["feedback_stats"]["correctness"]["n"] == 1
    assert example_3["runs"][0]["feedback_stats"]["correctness"]["avg"] == 0.7
    assert example_3["runs"][0]["feedback_stats"]["accuracy"]["n"] == 1
    assert example_3["runs"][0]["feedback_stats"]["accuracy"]["avg"] == 0.7
    assert example_3["runs"][0]["feedback_stats"]["color"]["values"] == {
        color_values[2]: 1
    }
    assert example_3["runs"][0]["error"] == error[2]
    assert example_3["runs"][0]["extra"] == {
        **(run_metadata[2] if run_metadata[2] is not None else {}),
        "metadata": {"ls_example_dataset_split": ["base"], "ls_run_depth": 0},
    }
    assert (
        example_3["runs"][0]["start_time"]
        == run_start_times[2].replace(tzinfo=None).isoformat()
    )
    assert (
        example_3["runs"][0]["end_time"]
        == run_end_times[2].replace(tzinfo=None).isoformat()
    )
    assert example_3["runs"][0]["inputs"] == {"input": inputs[2]}
    assert example_3["runs"][0]["outputs"] == {"output": outputs[2]}

    assert example_3["inputs"] == {"input": inputs[2]}
    assert example_3["outputs"] == {"output": expected_outputs[2]}

    # Now create another experiment with updated examples (but the same row_ids)

    experiment_name = random_lower_string()
    inputs = [random_lower_string(), random_lower_string(), random_lower_string()]
    outputs = [random_lower_string(), random_lower_string(), None]
    expected_outputs = [None, random_lower_string(), random_lower_string()]

    experiment_start_time = datetime.now(timezone.utc)

    # run start times must be after experiment start time
    run_start_times = [
        experiment_start_time + timedelta(seconds=1),
        experiment_start_time + timedelta(seconds=2),
        experiment_start_time + timedelta(seconds=3),
    ]
    run_end_times = [
        run_start_time + timedelta(seconds=1) for run_start_time in run_start_times
    ]

    experiment_end_time = datetime.now(timezone.utc) + timedelta(seconds=5)

    run_names = [random_lower_string(), random_lower_string(), None]
    error = [None, random_lower_string(), random_lower_string()]
    run_metadata = [
        {"key": random_lower_string()},
        None,
        {"key": random_lower_string()},
    ]

    results = [
        {
            "row_id": str(row_id),
            "inputs": {"input": input},
            "expected_outputs": {"output": expected_output}
            if expected_output is not None
            else None,
            "actual_outputs": {"output": output} if output is not None else None,
            "evaluation_scores": [
                {
                    "key": "accuracy",
                    "score": accuracy_score,
                    "comment": accuracy_comment,
                },
                {
                    "key": "correctness",
                    "score": correctness_score,
                    "comment": correctness_comment,
                },
                {"key": "color", "value": color_value},
            ],
            "start_time": run_start_time.isoformat(),
            "end_time": run_end_time.isoformat(),
            "run_name": run_name,
            "error": error,
            "run_metadata": metadata,
        }
        for row_id, input, output, expected_output, accuracy_score, accuracy_comment, correctness_score, correctness_comment, color_value, run_start_time, run_end_time, run_name, error, metadata in zip(
            row_ids,
            inputs,
            outputs,
            expected_outputs,
            accuracy_scores,
            accuracy_comments,
            correctness_scores,
            correctness_comments,
            color_values,
            run_start_times,
            run_end_times,
            run_names,
            error,
            run_metadata,
        )
    ]

    payload = {
        "experiment_name": experiment_name,
        "experiment_description": experiment_description,
        "dataset_name": dataset_name,
        "dataset_description": dataset_description,
        "summary_experiment_scores": summary_experiment_scores,
        "results": results,
        "experiment_start_time": experiment_start_time.isoformat(),
        "experiment_end_time": experiment_end_time.isoformat(),
        "experiment_metadata": experiment_metadata,
    }

    response = await http_tenant_one.post(
        "/datasets/upload-experiment",
        json=payload,
    )
    assert response.status_code == 200
    session_id_2 = response.json()["experiment"]["id"]

    await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["name"] == dataset_name
    assert dataset["description"] == dataset_description
    assert dataset["example_count"] == 3
    assert dataset["session_count"] == 2
    assert dataset["data_type"] == "kv"

    response = await http_tenant_one.get(
        f"/examples?dataset={dataset_id}",
    )
    assert response.status_code == 200

    examples = response.json()
    assert len(examples) == 3

    response = await http_tenant_one.get(
        f"/examples/count?dataset={dataset_id}",
    )
    assert response.status_code == 200

    examples_count = response.json()
    assert examples_count == 3

    for row_id, input, expected_output in zip(row_ids, inputs, expected_outputs):
        example = next(example for example in examples if example["id"] == str(row_id))
        assert example["inputs"] == {"input": input}
        assert example["outputs"] == (
            {"output": expected_output} if expected_output is not None else None
        )

    response = await http_tenant_one.get(
        f"/sessions?reference_dataset={dataset_id}",
    )
    assert response.status_code == 200

    sessions = response.json()
    assert len(sessions) == 2

    session = next(session for session in sessions if session["id"] != session_id)
    assert session["name"] == experiment_name
    assert session["description"] == experiment_description
    assert session["extra"] == {"metadata": experiment_metadata}
    assert (
        session["start_time"] == experiment_start_time.replace(tzinfo=None).isoformat()
    )  # have to remove tzinfo because the start_time column is not timezone aware
    assert session["end_time"] == experiment_end_time.isoformat()
    assert session["run_count"] == 3
    assert session["test_run_number"] == 2
    assert session["latency_p50"] == 1.0
    assert session["latency_p99"] == 1.0
    assert session["error_rate"] == 2 / 3
    assert (
        session["last_run_start_time"]
        == run_start_times[-1].replace(tzinfo=None).isoformat()
    )
    feedback_stats = session["feedback_stats"]
    assert math.isclose(feedback_stats["accuracy"]["avg"], 0.8, rel_tol=1e-9)
    assert math.isclose(
        feedback_stats["accuracy"]["stdev"], 0.081649658092773, rel_tol=1e-9
    )
    assert feedback_stats["accuracy"]["n"] == 3
    assert math.isclose(feedback_stats["correctness"]["avg"], 0.8, rel_tol=1e-9)
    assert math.isclose(
        feedback_stats["correctness"]["stdev"], 0.081649658092773, rel_tol=1e-9
    )
    assert feedback_stats["correctness"]["n"] == 3
    assert feedback_stats["color"]["avg"] is None
    assert feedback_stats["color"]["stdev"] is None
    assert feedback_stats["color"]["n"] == 3
    assert feedback_stats["color"]["values"] == {k: 1 for k in color_values}

    # Run a query for the test runs, simulating comparison view with no filters
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_id_2),
            ],
        },
    )

    assert response.status_code == 200
    examples_with_runs_no_filters = response.json()
    assert len(examples_with_runs_no_filters) == 3
    # Find the example with id row_ids[0]
    example_1 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[0])
    )
    assert len(example_1["runs"]) == 1
    assert example_1["runs"][0]["name"] == run_names[0]
    assert example_1["runs"][0]["feedback_stats"]["correctness"]["n"] == 1
    assert example_1["runs"][0]["feedback_stats"]["correctness"]["avg"] == 0.9
    assert example_1["runs"][0]["feedback_stats"]["accuracy"]["n"] == 1
    assert example_1["runs"][0]["feedback_stats"]["accuracy"]["avg"] == 0.9
    assert example_1["runs"][0]["feedback_stats"]["color"]["values"] == {
        color_values[0]: 1
    }
    assert example_1["runs"][0]["error"] is None
    assert example_1["runs"][0]["extra"] == {
        **(run_metadata[0] if run_metadata[0] is not None else {}),
        "metadata": {"ls_example_dataset_split": ["base"], "ls_run_depth": 0},
    }
    assert (
        example_1["runs"][0]["start_time"]
        == run_start_times[0].replace(tzinfo=None).isoformat()
    )
    assert (
        example_1["runs"][0]["end_time"]
        == run_end_times[0].replace(tzinfo=None).isoformat()
    )
    assert example_1["runs"][0]["inputs"] == {"input": inputs[0]}
    assert example_1["runs"][0]["outputs"] == {"output": outputs[0]}

    assert example_1["inputs"] == {"input": inputs[0]}
    assert example_1["outputs"] is None

    # Find the example with id row_ids[1]
    example_2 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[1])
    )
    assert len(example_2["runs"]) == 1
    assert example_2["runs"][0]["name"] == run_names[1]
    assert example_2["runs"][0]["feedback_stats"]["correctness"]["n"] == 1
    assert example_2["runs"][0]["feedback_stats"]["correctness"]["avg"] == 0.8
    assert example_2["runs"][0]["feedback_stats"]["accuracy"]["n"] == 1
    assert example_2["runs"][0]["feedback_stats"]["accuracy"]["avg"] == 0.8
    assert example_2["runs"][0]["feedback_stats"]["color"]["values"] == {
        color_values[1]: 1
    }
    assert example_2["runs"][0]["error"] == error[1]
    assert example_2["runs"][0]["extra"] == {
        "metadata": {"ls_example_dataset_split": ["base"], "ls_run_depth": 0}
    }
    assert (
        example_2["runs"][0]["start_time"]
        == run_start_times[1].replace(tzinfo=None).isoformat()
    )
    assert (
        example_2["runs"][0]["end_time"]
        == run_end_times[1].replace(tzinfo=None).isoformat()
    )
    assert example_2["runs"][0]["inputs"] == {"input": inputs[1]}
    assert example_2["runs"][0]["outputs"] == {"output": outputs[1]}

    assert example_2["inputs"] == {"input": inputs[1]}
    assert example_2["outputs"] == {"output": expected_outputs[1]}

    # Find the example with id row_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[2])
    )
    assert len(example_3["runs"]) == 1
    assert example_3["runs"][0]["name"] == "Target"
    assert example_3["runs"][0]["feedback_stats"]["correctness"]["n"] == 1
    assert example_3["runs"][0]["feedback_stats"]["correctness"]["avg"] == 0.7
    assert example_3["runs"][0]["feedback_stats"]["accuracy"]["n"] == 1
    assert example_3["runs"][0]["feedback_stats"]["accuracy"]["avg"] == 0.7
    assert example_3["runs"][0]["feedback_stats"]["color"]["values"] == {
        color_values[2]: 1
    }
    assert example_3["runs"][0]["error"] == error[2]
    assert example_3["runs"][0]["extra"] == {
        **(run_metadata[2] if run_metadata[2] is not None else {}),
        "metadata": {"ls_example_dataset_split": ["base"], "ls_run_depth": 0},
    }
    assert (
        example_3["runs"][0]["start_time"]
        == run_start_times[2].replace(tzinfo=None).isoformat()
    )
    assert (
        example_3["runs"][0]["end_time"]
        == run_end_times[2].replace(tzinfo=None).isoformat()
    )
    assert example_3["runs"][0]["inputs"] == {"input": inputs[2]}
    assert example_3["runs"][0]["outputs"] is None

    assert example_3["inputs"] == {"input": inputs[2]}
    assert example_3["outputs"] == {"output": expected_outputs[2]}

    # Now create another experiment with updated examples which occurred before the previous experiments. The latest version of the examples should not be updated, but we should see the new inputs/outputs when we pull the session in the comparison view.

    experiment_name = random_lower_string()
    past_inputs = [random_lower_string(), random_lower_string(), random_lower_string()]
    past_outputs = [random_lower_string(), None, random_lower_string()]
    past_expected_outputs = [random_lower_string(), random_lower_string(), None]

    experiment_start_time = datetime.now(timezone.utc) - timedelta(days=10)

    # run start times must be after experiment start time
    run_start_times = [
        experiment_start_time + timedelta(seconds=1),
        experiment_start_time + timedelta(seconds=2),
        experiment_start_time + timedelta(seconds=3),
    ]
    run_end_times = [
        run_start_time + timedelta(seconds=1) for run_start_time in run_start_times
    ]

    experiment_end_time = experiment_start_time + timedelta(seconds=10)

    run_names = [random_lower_string(), random_lower_string(), None]
    error = [None, random_lower_string(), random_lower_string()]
    run_metadata = [
        {"key": random_lower_string()},
        None,
        {"key": random_lower_string()},
    ]

    results = [
        {
            "row_id": str(row_id),
            "inputs": {"input": input},
            "expected_outputs": {"output": expected_output}
            if expected_output is not None
            else None,
            "actual_outputs": {"output": output} if output is not None else None,
            "evaluation_scores": [
                {
                    "key": "accuracy",
                    "score": accuracy_score,
                    "comment": accuracy_comment,
                },
                {
                    "key": "correctness",
                    "score": correctness_score,
                    "comment": correctness_comment,
                },
                {"key": "color", "value": color_value},
            ],
            "start_time": run_start_time.isoformat(),
            "end_time": run_end_time.isoformat(),
            "run_name": run_name,
            "error": error,
            "run_metadata": metadata,
        }
        for row_id, input, output, expected_output, accuracy_score, accuracy_comment, correctness_score, correctness_comment, color_value, run_start_time, run_end_time, run_name, error, metadata in zip(
            row_ids,
            past_inputs,
            past_outputs,
            past_expected_outputs,
            accuracy_scores,
            accuracy_comments,
            correctness_scores,
            correctness_comments,
            color_values,
            run_start_times,
            run_end_times,
            run_names,
            error,
            run_metadata,
        )
    ]

    payload = {
        "experiment_name": experiment_name,
        "experiment_description": experiment_description,
        "dataset_name": dataset_name,
        "dataset_description": dataset_description,
        "summary_experiment_scores": summary_experiment_scores,
        "results": results,
        "experiment_start_time": experiment_start_time.isoformat(),
        "experiment_end_time": experiment_end_time.isoformat(),
        "experiment_metadata": experiment_metadata,
    }

    response = await http_tenant_one.post(
        "/datasets/upload-experiment",
        json=payload,
    )
    assert response.status_code == 200
    session_id_3 = response.json()["experiment"]["id"]

    await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["name"] == dataset_name
    assert dataset["description"] == dataset_description
    assert dataset["example_count"] == 3
    assert dataset["session_count"] == 3
    assert dataset["data_type"] == "kv"

    response = await http_tenant_one.get(
        f"/examples?dataset={dataset_id}",
    )
    assert response.status_code == 200

    examples = response.json()
    assert len(examples) == 3

    response = await http_tenant_one.get(
        f"/examples/count?dataset={dataset_id}",
    )
    assert response.status_code == 200

    examples_count = response.json()
    assert examples_count == 3

    for row_id, input, expected_output in zip(
        row_ids, inputs, expected_outputs
    ):  # NOT past_inputs and past_expected_outputs - we are implicitly fetching the latest version here
        example = next(example for example in examples if example["id"] == str(row_id))
        assert example["inputs"] == {"input": input}
        assert example["outputs"] == (
            {"output": expected_output} if expected_output is not None else None
        )

    response = await http_tenant_one.get(
        f"/sessions?reference_dataset={dataset_id}",
    )
    assert response.status_code == 200

    sessions = response.json()
    assert len(sessions) == 3

    session = next(
        session
        for session in sessions
        if session["id"] != session_id and session["id"] != session_id_2
    )
    assert session["name"] == experiment_name
    assert session["description"] == experiment_description
    assert session["extra"] == {"metadata": experiment_metadata}
    assert (
        session["start_time"] == experiment_start_time.replace(tzinfo=None).isoformat()
    )  # have to remove tzinfo because the start_time column is not timezone aware
    assert session["end_time"] == experiment_end_time.isoformat()
    assert session["run_count"] == 3
    assert session["test_run_number"] == 3
    assert session["latency_p50"] == 1.0
    assert session["latency_p99"] == 1.0
    assert session["error_rate"] == 2 / 3
    assert (
        session["last_run_start_time"]
        == run_start_times[-1].replace(tzinfo=None).isoformat()
    )
    feedback_stats = session["feedback_stats"]
    assert math.isclose(feedback_stats["accuracy"]["avg"], 0.8, rel_tol=1e-9)
    assert math.isclose(
        feedback_stats["accuracy"]["stdev"], 0.081649658092773, rel_tol=1e-9
    )
    assert feedback_stats["accuracy"]["n"] == 3
    assert math.isclose(feedback_stats["correctness"]["avg"], 0.8, rel_tol=1e-9)
    assert math.isclose(
        feedback_stats["correctness"]["stdev"], 0.081649658092773, rel_tol=1e-9
    )
    assert feedback_stats["correctness"]["n"] == 3
    assert feedback_stats["color"]["avg"] is None
    assert feedback_stats["color"]["stdev"] is None
    assert feedback_stats["color"]["n"] == 3
    assert feedback_stats["color"]["values"] == {k: 1 for k in color_values}

    # Run a query for the test runs, simulating comparison view with no filters
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_id_3),
            ],
        },
    )

    assert response.status_code == 200
    examples_with_runs_no_filters = response.json()
    assert len(examples_with_runs_no_filters) == 3
    # Find the example with id row_ids[0]
    example_1 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[0])
    )
    assert len(example_1["runs"]) == 1
    assert example_1["runs"][0]["name"] == run_names[0]
    assert example_1["runs"][0]["feedback_stats"]["correctness"]["n"] == 1
    assert example_1["runs"][0]["feedback_stats"]["correctness"]["avg"] == 0.9
    assert example_1["runs"][0]["feedback_stats"]["accuracy"]["n"] == 1
    assert example_1["runs"][0]["feedback_stats"]["accuracy"]["avg"] == 0.9
    assert example_1["runs"][0]["feedback_stats"]["color"]["values"] == {
        color_values[0]: 1
    }
    assert example_1["runs"][0]["error"] is None
    assert example_1["runs"][0]["extra"] == {
        **(run_metadata[0] if run_metadata[0] is not None else {}),
        "metadata": {"ls_example_dataset_split": ["base"], "ls_run_depth": 0},
    }
    assert (
        example_1["runs"][0]["start_time"]
        == run_start_times[0].replace(tzinfo=None).isoformat()
    )
    assert (
        example_1["runs"][0]["end_time"]
        == run_end_times[0].replace(tzinfo=None).isoformat()
    )
    assert example_1["runs"][0]["inputs"] == {"input": past_inputs[0]}
    assert example_1["runs"][0]["outputs"] == {"output": past_outputs[0]}

    assert example_1["inputs"] == {"input": past_inputs[0]}
    assert example_1["outputs"] == {"output": past_expected_outputs[0]}

    # Find the example with id row_ids[1]
    example_2 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[1])
    )
    assert len(example_2["runs"]) == 1
    assert example_2["runs"][0]["name"] == run_names[1]
    assert example_2["runs"][0]["feedback_stats"]["correctness"]["n"] == 1
    assert example_2["runs"][0]["feedback_stats"]["correctness"]["avg"] == 0.8
    assert example_2["runs"][0]["feedback_stats"]["accuracy"]["n"] == 1
    assert example_2["runs"][0]["feedback_stats"]["accuracy"]["avg"] == 0.8
    assert example_2["runs"][0]["feedback_stats"]["color"]["values"] == {
        color_values[1]: 1
    }
    assert example_2["runs"][0]["error"] == error[1]
    assert example_2["runs"][0]["extra"] == {
        "metadata": {"ls_example_dataset_split": ["base"], "ls_run_depth": 0}
    }
    assert (
        example_2["runs"][0]["start_time"]
        == run_start_times[1].replace(tzinfo=None).isoformat()
    )
    assert (
        example_2["runs"][0]["end_time"]
        == run_end_times[1].replace(tzinfo=None).isoformat()
    )
    assert example_2["runs"][0]["inputs"] == {"input": past_inputs[1]}
    assert example_2["runs"][0]["outputs"] is None

    assert example_2["inputs"] == {"input": past_inputs[1]}
    assert example_2["outputs"] == {"output": past_expected_outputs[1]}

    # Find the example with id row_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[2])
    )
    assert len(example_3["runs"]) == 1
    assert example_3["runs"][0]["name"] == "Target"
    assert example_3["runs"][0]["feedback_stats"]["correctness"]["n"] == 1
    assert example_3["runs"][0]["feedback_stats"]["correctness"]["avg"] == 0.7
    assert example_3["runs"][0]["feedback_stats"]["accuracy"]["n"] == 1
    assert example_3["runs"][0]["feedback_stats"]["accuracy"]["avg"] == 0.7
    assert example_3["runs"][0]["feedback_stats"]["color"]["values"] == {
        color_values[2]: 1
    }
    assert example_3["runs"][0]["error"] == error[2]
    assert example_3["runs"][0]["extra"] == {
        **(run_metadata[2] if run_metadata[2] is not None else {}),
        "metadata": {"ls_example_dataset_split": ["base"], "ls_run_depth": 0},
    }
    assert (
        example_3["runs"][0]["start_time"]
        == run_start_times[2].replace(tzinfo=None).isoformat()
    )
    assert (
        example_3["runs"][0]["end_time"]
        == run_end_times[2].replace(tzinfo=None).isoformat()
    )
    assert example_3["runs"][0]["inputs"] == {"input": past_inputs[2]}
    assert example_3["runs"][0]["outputs"] == {"output": past_outputs[2]}

    assert example_3["inputs"] == {"input": past_inputs[2]}
    assert example_3["outputs"] is None

    # Now fetch the comparison view for session_id_2 and make sure we still see the same inputs/outputs as before

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_id_2),
            ],
        },
    )

    assert response.status_code == 200
    examples_with_runs_no_filters = response.json()
    assert len(examples_with_runs_no_filters) == 3
    # Find the example with id row_ids[0]
    example_1 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[0])
    )
    assert len(example_1["runs"]) == 1
    assert example_1["runs"][0]["inputs"] == {"input": inputs[0]}
    assert example_1["runs"][0]["outputs"] == {"output": outputs[0]}

    assert example_1["inputs"] == {"input": inputs[0]}
    assert example_1["outputs"] is None

    # Find the example with id row_ids[1]
    example_2 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[1])
    )
    assert len(example_2["runs"]) == 1
    assert example_2["runs"][0]["inputs"] == {"input": inputs[1]}
    assert example_2["runs"][0]["outputs"] == {"output": outputs[1]}

    assert example_2["inputs"] == {"input": inputs[1]}
    assert example_2["outputs"] == {"output": expected_outputs[1]}

    # Find the example with id row_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(row_ids[2])
    )
    assert len(example_3["runs"]) == 1
    assert example_3["runs"][0]["inputs"] == {"input": inputs[2]}
    assert example_3["runs"][0]["outputs"] is None

    assert example_3["inputs"] == {"input": inputs[2]}
    assert example_3["outputs"] == {"output": expected_outputs[2]}

    # Now simulate an error and make sure the experiment is not created and the examples are not updated

    experiment_name = random_lower_string()
    cancelled_inputs = [
        random_lower_string(),
        random_lower_string(),
        random_lower_string(),
    ]
    cancelled_outputs = [random_lower_string(), None, random_lower_string()]
    cancelled_expected_outputs = [random_lower_string(), random_lower_string(), None]

    experiment_start_time = datetime.now(timezone.utc)

    # run start times must be after experiment start time
    run_start_times = [
        experiment_start_time + timedelta(seconds=1),
        experiment_start_time + timedelta(seconds=2),
        experiment_start_time + timedelta(seconds=3),
    ]
    run_end_times = [
        run_start_time + timedelta(seconds=1) for run_start_time in run_start_times
    ]

    experiment_end_time = experiment_start_time + timedelta(seconds=10)

    run_names = [random_lower_string(), random_lower_string(), None]
    error = [None, random_lower_string(), random_lower_string()]
    run_metadata = [
        {"key": random_lower_string()},
        None,
        {"key": random_lower_string()},
    ]

    results = [
        {
            "row_id": str(row_id),
            "inputs": {"input": input},
            "expected_outputs": {"output": expected_output}
            if expected_output is not None
            else None,
            "actual_outputs": {"output": output} if output is not None else None,
            "evaluation_scores": [
                {
                    "key": "accuracy",
                    "score": accuracy_score,
                    "comment": accuracy_comment,
                },
                {
                    "key": "correctness",
                    "score": correctness_score,
                    "comment": correctness_comment,
                },
                {"key": "color", "value": color_value},
            ],
            "start_time": run_start_time.isoformat(),
            "end_time": run_end_time.isoformat(),
            "run_name": run_name,
            "error": error,
            "run_metadata": metadata,
        }
        for row_id, input, output, expected_output, accuracy_score, accuracy_comment, correctness_score, correctness_comment, color_value, run_start_time, run_end_time, run_name, error, metadata in zip(
            row_ids,
            cancelled_inputs,
            cancelled_outputs,
            cancelled_expected_outputs,
            accuracy_scores,
            accuracy_comments,
            correctness_scores,
            correctness_comments,
            color_values,
            run_start_times,
            run_end_times,
            run_names,
            error,
            run_metadata,
        )
    ]

    payload = {
        "experiment_name": experiment_name,
        "experiment_description": experiment_description,
        "dataset_name": dataset_name,
        "dataset_description": dataset_description,
        "summary_experiment_scores": summary_experiment_scores,
        "results": results,
        "experiment_start_time": experiment_start_time.isoformat(),
        "experiment_end_time": experiment_end_time.isoformat(),
        "experiment_metadata": experiment_metadata,
    }

    with patch(
        "app.models.datasets.upload_existing._enqueue_feedback",
        side_effect=ValueError("An error occurred"),
    ):
        response = await http_tenant_one.post(
            "/datasets/upload-experiment",
            json=payload,
        )
        assert response.status_code == 500

    await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]

    response = await http_tenant_one.get(
        f"/datasets/{dataset_id}",
    )
    assert response.status_code == 200
    dataset = response.json()
    assert dataset["name"] == dataset_name
    assert dataset["description"] == dataset_description
    assert dataset["example_count"] == 3
    assert dataset["session_count"] == 3
    assert dataset["data_type"] == "kv"

    response = await http_tenant_one.get(
        f"/examples?dataset={dataset_id}",
    )
    assert response.status_code == 200

    examples = response.json()
    assert len(examples) == 3

    response = await http_tenant_one.get(
        f"/examples/count?dataset={dataset_id}",
    )
    assert response.status_code == 200

    examples_count = response.json()
    assert examples_count == 3

    for row_id, input, expected_output in zip(
        row_ids, inputs, expected_outputs
    ):  # NOT cancelled_inputs and cancelled_expected_outputs - we should have undone the changes because of the error
        example = next(example for example in examples if example["id"] == str(row_id))
        assert example["inputs"] == {"input": input}
        assert example["outputs"] == (
            {"output": expected_output} if expected_output is not None else None
        )

    response = await http_tenant_one.get(
        f"/sessions?name={experiment_name}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 0


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["oauth", "none"],
    reason="write queue/multiple tenants",
)
async def test_read_test_run_comparisons_with_uploaded_experiment(
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    dataset_id = uuid4()
    dataset_name = random_lower_string()
    dataset_description = random_lower_string()
    experiment_name = random_lower_string()
    experiment_description = random_lower_string()
    experiment_metadata = {"key": random_lower_string()}

    summary_experiment_scores = [
        {
            "key": "summary_accuracy",
            "score": 0.9,
            "comment": "Great job!",
        }
    ]

    # Create 4 examples for this dataset
    example_ids = [uuid4() for _ in range(4)]
    inputs = [f"input_val_{example_id}" for example_id in example_ids]
    expected_outputs = [f"output_val_{example_id}" for example_id in example_ids]
    outputs = [f"output_val_{example_id}" for example_id in example_ids]

    run_names = [
        ["run_2", "run_1", "run_1", "run_1"],
        ["run_2", "run_1", "run_2", "run_2"],
        ["run_3", "run_3", "run_3", "run_3"],
    ]
    feedback_scores = [[10, 10, 0, 0], [0, 0, 10, 10], [10, 10, 10, 9]]
    feedback_values = [
        ["correct", "incorrect", "incorrect", "incorrect"],
        ["incorrect", "correct", "correct", "correct"],
        ["correct", "correct", "incorrect", "incorrect"],
    ]

    experiment_start_time = datetime.now(timezone.utc)

    # run start times must be after experiment start time
    run_start_times = [
        experiment_start_time + timedelta(seconds=1),
        experiment_start_time + timedelta(seconds=2),
        experiment_start_time + timedelta(seconds=3),
        experiment_start_time + timedelta(seconds=4),
    ]
    run_end_times = [
        run_start_time + timedelta(seconds=1) for run_start_time in run_start_times
    ]

    experiment_end_time = datetime.now(timezone.utc) + timedelta(seconds=7)

    results = [
        {
            "row_id": str(row_id),
            "inputs": {"input": input},
            "expected_outputs": {"output": expected_output}
            if expected_output is not None
            else None,
            "actual_outputs": {"output": output} if output is not None else None,
            "evaluation_scores": [
                {
                    "key": "test_new",
                    "score": test_score,
                    "feedback_source": {"type": "api"},
                },
                {
                    "key": "test_val",
                    "value": test_value,
                    "feedback_source": {"type": "api"},
                },
            ],
            "start_time": run_start_time.isoformat(),
            "end_time": run_end_time.isoformat(),
            "run_name": run_name,
        }
        for row_id, input, output, expected_output, test_score, test_value, run_start_time, run_end_time, run_name in zip(
            example_ids,
            inputs,
            outputs,
            expected_outputs,
            feedback_scores[0],
            feedback_values[0],
            run_start_times,
            run_end_times,
            run_names[0],
        )
    ]

    payload = {
        "experiment_name": experiment_name,
        "experiment_description": experiment_description,
        "dataset_id": str(dataset_id),
        "dataset_name": dataset_name,
        "dataset_description": dataset_description,
        "summary_experiment_scores": summary_experiment_scores,
        "results": results,
        "experiment_start_time": experiment_start_time.isoformat(),
        "experiment_end_time": experiment_end_time.isoformat(),
        "experiment_metadata": experiment_metadata,
    }
    session_ids = []
    response = await http_tenant_one.post(
        "/datasets/upload-experiment",
        json=payload,
    )
    assert response.status_code == 200
    session_ids.append(response.json()["experiment"]["id"])

    await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]

    results = [
        {
            "row_id": str(row_id),
            "inputs": {"input": input},
            "expected_outputs": {"output": expected_output}
            if expected_output is not None
            else None,
            "actual_outputs": {"output": output} if output is not None else None,
            "evaluation_scores": [
                {
                    "key": "test_new",
                    "score": test_score,
                    "feedback_source": {"type": "api"},
                },
                {
                    "key": "test_val",
                    "value": test_value,
                    "feedback_source": {"type": "api"},
                },
            ],
            "start_time": run_start_time.isoformat(),
            "end_time": run_end_time.isoformat(),
            "run_name": run_name,
        }
        for row_id, input, output, expected_output, test_score, test_value, run_start_time, run_end_time, run_name in zip(
            example_ids,
            inputs,
            outputs,
            expected_outputs,
            feedback_scores[1],
            feedback_values[1],
            run_start_times,
            run_end_times,
            run_names[1],
        )
    ]

    experiment_name = random_lower_string()
    payload = {
        "experiment_name": experiment_name,
        "experiment_description": experiment_description,
        "dataset_name": dataset_name,
        "dataset_description": dataset_description,
        "summary_experiment_scores": summary_experiment_scores,
        "results": results,
        "experiment_start_time": experiment_start_time.isoformat(),
        "experiment_end_time": experiment_end_time.isoformat(),
        "experiment_metadata": experiment_metadata,
    }

    response = await http_tenant_one.post(
        "/datasets/upload-experiment",
        json=payload,
    )
    assert response.status_code == 200
    session_ids.append(response.json()["experiment"]["id"])

    await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]
    results = [
        {
            "row_id": str(row_id),
            "inputs": {"input": input},
            "expected_outputs": {"output": expected_output}
            if expected_output is not None
            else None,
            "actual_outputs": {"output": output} if output is not None else None,
            "evaluation_scores": [
                {
                    "key": "test_new",
                    "score": test_score,
                    "feedback_source": {"type": "api"},
                },
                {
                    "key": "test_val",
                    "value": test_value,
                    "feedback_source": {"type": "api"},
                },
            ],
            "start_time": run_start_time.isoformat(),
            "end_time": run_end_time.isoformat(),
            "run_name": run_name,
        }
        for row_id, input, output, expected_output, test_score, test_value, run_start_time, run_end_time, run_name in zip(
            example_ids,
            inputs,
            outputs,
            expected_outputs,
            feedback_scores[2],
            feedback_values[2],
            run_start_times,
            run_end_times,
            run_names[2],
        )
    ]

    experiment_name = random_lower_string()
    payload = {
        "experiment_name": experiment_name,
        "experiment_description": experiment_description,
        "dataset_id": str(dataset_id),
        "dataset_name": dataset_name,
        "dataset_description": dataset_description,
        "summary_experiment_scores": summary_experiment_scores,
        "results": results,
        "experiment_start_time": experiment_start_time.isoformat(),
        "experiment_end_time": experiment_end_time.isoformat(),
        "experiment_metadata": experiment_metadata,
    }

    response = await http_tenant_one.post(
        "/datasets/upload-experiment",
        json=payload,
    )
    assert response.status_code == 200
    session_ids.append(response.json()["experiment"]["id"])

    await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]

    # Test querying the comparison view runs:

    # Run a query for the test runs, simulating comparison view with no filters
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
        },
    )

    assert response.status_code == 200
    examples_with_runs_no_filters = response.json()
    assert len(examples_with_runs_no_filters) == 4
    # Find the example with id example_ids[0]
    example_1 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[0])
    )
    assert len(example_1["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_1["runs"], key=lambda x: x["name"])
    ) == [
        "run_2",
        "run_2",
        "run_3",
    ]
    for run in example_1["runs"]:
        assert run["feedback_stats"]["test_new"]["n"] == 1
        if run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[0]) or run["session_id"] == str(
                session_ids[1]
            )
            if run["session_id"] == str(session_ids[0]):
                assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
            else:
                assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
            assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        else:
            assert False
        assert run["inputs"] == {"input": f"input_val_{example_ids[0]}"}
        assert run["outputs"] == {"output": f"output_val_{example_ids[0]}"}
        assert run["feedback_stats"]["test_new"]["session_min_score"] == 0
        assert run["feedback_stats"]["test_new"]["session_max_score"] == 10
        assert run["feedback_stats"]["test_val"]["session_min_score"] is None
        assert run["feedback_stats"]["test_val"]["session_max_score"] is None

    # Find the example with id example_ids[1]
    example_2 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[1])
    )
    assert len(example_2["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_2["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_1",
        "run_3",
    ]
    for run in example_2["runs"]:
        assert run["feedback_stats"]["test_new"]["n"] == 1
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0]) or run["session_id"] == str(
                session_ids[1]
            )
            if run["session_id"] == str(session_ids[0]):
                assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}
            else:
                assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
            assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        else:
            assert False
        assert run["feedback_stats"]["test_new"]["session_min_score"] == 0
        assert run["feedback_stats"]["test_new"]["session_max_score"] == 10
        assert run["feedback_stats"]["test_val"]["session_min_score"] is None
        assert run["feedback_stats"]["test_val"]["session_max_score"] is None

    # Find the example with id example_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[2])
    )

    assert len(example_3["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_3["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_3["runs"]:
        assert run["feedback_stats"]["test_new"]["n"] == 1
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
            assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
            assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
            assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}

    # Find the example with id example_ids[3]
    example_4 = next(
        example
        for example in examples_with_runs_no_filters
        if example["id"] == str(example_ids[3])
    )
    assert len(example_4["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_4["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_4["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
            assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
            assert run["feedback_stats"]["test_val"]["values"] == {"correct": 1}
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])
            assert run["feedback_stats"]["test_val"]["values"] == {"incorrect": 1}

    # Try querying with a filter per session
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200

    examples_with_runs_with_filters = response.json()

    assert len(examples_with_runs_with_filters) == 2

    # Find the example with id example_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[2])
    )

    assert len(example_3["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_3["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_3["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Find the example with id example_ids[3]
    example_4 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[3])
    )
    assert len(example_4["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_4["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_4["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Try querying with a feedback filter
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "0"))'
                ],
            },
        },
    )

    assert response.status_code == 200

    examples_with_runs_with_filters = response.json()

    assert len(examples_with_runs_with_filters) == 2

    # Find the example with id example_ids[2]
    example_3 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[2])
    )

    assert len(example_3["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_3["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_3["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Find the example with id example_ids[3]
    example_4 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[3])
    )
    assert len(example_4["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_4["runs"], key=lambda x: x["name"])
    ) == [
        "run_1",
        "run_2",
        "run_3",
    ]
    for run in example_4["runs"]:
        if run["name"] == "run_1":
            assert run["session_id"] == str(session_ids[0])
        elif run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[1])
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Try querying with a feedback filter (for both score and value)
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "filters": {
                str(session_ids[0]): [
                    'and(and(eq(feedback_key, "test_new"), eq(feedback_score, "10")), and(eq(feedback_key, "test_val"), eq(feedback_value, "correct")))'
                ],
            },
        },
    )

    assert response.status_code == 200

    examples_with_runs_with_filters = response.json()

    assert len(examples_with_runs_with_filters) == 1

    # Find the example with id example_ids[0]
    example_1 = next(
        example
        for example in examples_with_runs_with_filters
        if example["id"] == str(example_ids[0])
    )

    assert len(example_1["runs"]) == 3
    assert list(
        r["name"] for r in sorted(example_1["runs"], key=lambda x: x["name"])
    ) == [
        "run_2",
        "run_2",
        "run_3",
    ]
    for run in example_1["runs"]:
        if run["name"] == "run_2":
            assert run["session_id"] == str(session_ids[0]) or run["session_id"] == str(
                session_ids[1]
            )
        elif run["name"] == "run_3":
            assert run["session_id"] == str(session_ids[2])

    # Try filtering for a session that doesn't exist
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(uuid4()),
            ],
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 0

    # Test pagination
    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "limit": 2,
            "offset": 2,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.headers["x-pagination-total"] == "4"

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "limit": 2,
            "offset": 1,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.headers["x-pagination-total"] == "4"

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "limit": 2,
            "offset": 0,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.headers["x-pagination-total"] == "3"

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "limit": 2,
            "offset": 3,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.headers["x-pagination-total"] == "4"

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs",
        json={
            "session_ids": [
                str(session_ids[0]),
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
            "limit": 1,
            "offset": 0,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.headers["x-pagination-total"] == "2"

    # session 0: [10, 10, 0, 0]
    # session 1: [0, 0, 10, 10]
    # session 2: [10, 10, 10, 9]

    example_id_strs = [str(example_id) for example_id in example_ids]

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert res["feedback_deltas"][str(session_ids[2])]["regressed_examples"] == [
        example_id_strs[3]
    ]

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    # Test with filters applied

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_1)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "0"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_1)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "0"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    # Test with multiple sessions

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert res["feedback_deltas"][str(session_ids[2])]["regressed_examples"] == [
        example_id_strs[3]
    ]
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "10"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 1
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    # Test deltas with public shared dataset

    response = await http_tenant_one.put(f"/datasets/{dataset_id}/share")
    assert response.status_code == 200
    share_token = response.json()["share_token"]

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert res["feedback_deltas"][str(session_ids[2])]["regressed_examples"] == [
        example_id_strs[3]
    ]

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    # Test with filters applied

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_1)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "0"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_1)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 0

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )

    response = await http_tenant_two.post(
        f"/public/{share_token}/datasets/runs/delta",
        json={
            "baseline_session_id": str(session_ids[2]),
            "comparison_session_ids": [
                str(session_ids[1]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "0"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 1
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["improved_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 0

    # reverse feedback direction
    response = await http_tenant_one.patch(
        "/feedback-configs/",
        json={
            "feedback_key": "test_new",
            "is_lower_score_better": True,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[1]): ["eq(name, run_2)"],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 2
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[0]),
            "comparison_session_ids": [
                str(session_ids[1]),
                str(session_ids[2]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[0]): ["eq(name, run_1)"],
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "9"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[1])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[1])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 1
    assert (
        example_id_strs[3]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0

    response = await http_tenant_one.post(
        f"/datasets/{dataset_id}/runs/delta",
        json={
            "baseline_session_id": str(session_ids[1]),
            "comparison_session_ids": [
                str(session_ids[2]),
                str(session_ids[0]),
            ],
            "feedback_key": "test_new",
            "filters": {
                str(session_ids[2]): [
                    'and(eq(feedback_key, "test_new"), eq(feedback_score, "10"))'
                ],
            },
        },
    )
    assert response.status_code == 200
    res = response.json()
    assert len(res["feedback_deltas"]) == 2
    assert len(res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[2])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[2])]["improved_examples"]) == 0
    assert len(res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]) == 2
    assert (
        example_id_strs[0]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert (
        example_id_strs[1]
        in res["feedback_deltas"][str(session_ids[0])]["regressed_examples"]
    )
    assert len(res["feedback_deltas"][str(session_ids[0])]["improved_examples"]) == 1
    assert (
        example_id_strs[2]
        in res["feedback_deltas"][str(session_ids[0])]["improved_examples"]
    )


async def test_dataset_tagging(fresh_tenant: tuple[AsyncClient, AuthInfo]) -> None:
    client, _ = fresh_tenant
    key1 = random_lower_string()
    response = await client.post(
        "/workspaces/current/tag-keys",
        json={"key": key1},
    )

    assert response.status_code == 200
    key1_id = response.json()["id"]

    value1 = random_lower_string()
    response = await client.post(
        f"/workspaces/current/tag-keys/{key1_id}/tag-values",
        json={"value": value1},
    )

    assert response.status_code == 200
    value1_id = response.json()["id"]

    value2 = random_lower_string()
    response = await client.post(
        f"/workspaces/current/tag-keys/{key1_id}/tag-values",
        json={"value": value2},
    )

    assert response.status_code == 200
    value2_id = response.json()["id"]

    # create three datasets concurrently
    responses = await asyncio.gather(
        *[
            client.post(
                "/datasets",
                json={
                    "name": random_lower_string(),
                },
            )
            for _ in range(3)
        ]
    )

    assert len(responses) == 3
    assert all(response.status_code == 200 for response in responses)
    dataset1_id, dataset2_id, dataset3_id = [
        response.json()["id"] for response in responses
    ]

    # tag datasets
    responses_taggings = await asyncio.gather(
        client.post(
            "/workspaces/current/taggings",
            json={
                "resource_type": "dataset",
                "resource_id": dataset1_id,
                "tag_value_id": value1_id,
            },
        ),
        client.post(
            "/workspaces/current/taggings",
            json={
                "resource_type": "dataset",
                "resource_id": dataset1_id,
                "tag_value_id": value2_id,
            },
        ),
        client.post(
            "/workspaces/current/taggings",
            json={
                "resource_type": "dataset",
                "resource_id": dataset2_id,
                "tag_value_id": value1_id,
            },
        ),
    )

    assert all(response.status_code == 200 for response in responses_taggings)

    # fetch the datasets without any tag_value_id query param
    response = await client.get("/datasets")
    assert response.status_code == 200
    datasets = response.json()
    assert len(datasets) >= 3

    assert any(dataset["id"] == dataset1_id for dataset in datasets)
    assert any(dataset["id"] == dataset2_id for dataset in datasets)
    assert any(dataset["id"] == dataset3_id for dataset in datasets)

    # fetch the datasets with tag_value_id query param
    response = await client.get(
        f"/datasets?tag_value_id={value1_id}",
    )
    assert response.status_code == 200
    datasets = response.json()
    assert len(datasets) == 2

    assert any(dataset["id"] == dataset1_id for dataset in datasets)
    assert any(dataset["id"] == dataset2_id for dataset in datasets)

    response = await client.get(
        f"/datasets?tag_value_id={value2_id}",
    )
    assert response.status_code == 200
    datasets = response.json()
    assert len(datasets) == 1

    assert any(dataset["id"] == dataset1_id for dataset in datasets)

    # fetch the datasets with multiple tag_value_id query param
    response = await client.get(
        f"/datasets?tag_value_id={value1_id}&tag_value_id={value2_id}",
    )
    assert response.status_code == 200
    datasets = response.json()
    assert len(datasets) == 1

    assert any(dataset["id"] == dataset1_id for dataset in datasets)

    # fetch with tag_value_id and name_contains
    dataset_name = random_lower_string()
    response = await client.post(
        "/datasets",
        json={
            "name": dataset_name,
        },
    )
    assert response.status_code == 200
    dataset4_id = response.json()["id"]

    response = await client.post(
        "/workspaces/current/taggings",
        json={
            "resource_type": "dataset",
            "resource_id": dataset4_id,
            "tag_value_id": value1_id,
        },
    )
    assert response.status_code == 200

    response = await client.get(
        f"/datasets?tag_value_id={value1_id}&name_contains={dataset_name}",
    )
    assert response.status_code == 200
    datasets = response.json()
    assert len(datasets) == 1
    assert datasets[0]["id"] == dataset4_id


single_str_json_schema = {
    "type": "object",
    "properties": {
        "name": {"type": "string"},
    },
    "required": ["name"],
    "additionalProperties": False,
}

messages_json_schema = {
    "type": "array",
    "items": {"$ref": "/public/schemas/v1/message.json"},
}

message_json_schema = {
    "type": "object",
    "properties": {
        "message": {"$ref": "/public/schemas/v1/message.json"},
    },
    "required": ["message"],
    "additionalProperties": False,
}

nested_messages_json_schema = {
    "type": "object",
    "properties": {"foo": messages_json_schema},
}

updated_nested_messages_json_schema = {
    "type": "object",
    "properties": {"bar": messages_json_schema},
}

_INVALID_UPDATE_TRANSFORMATIONS_TEST_CASES = [
    (
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "foo", "*"],
            }
        ],
        nested_messages_json_schema,
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "name"],
            }
        ],
        single_str_json_schema,
    ),
    (
        [{"transformation_type": "remove_system_messages", "path": ["inputs", "*"]}],
        messages_json_schema,
        [{"transformation_type": "remove_system_messages", "path": ["inputs", "*"]}],
        nested_messages_json_schema,
    ),
    (
        [{"transformation_type": "remove_system_messages", "path": ["inputs", "*"]}],
        messages_json_schema,
        [{"transformation_type": "remove_system_messages", "path": ["*"]}],
        nested_messages_json_schema,
    ),
]

_VALID_UPDATE_TRANSFORMATIONS_TEST_CASES = [
    (
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "foo", "*"],
            }
        ],
        nested_messages_json_schema,
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "bar", "*"],
            }
        ],
        updated_nested_messages_json_schema,
    ),
    (
        [{"transformation_type": "remove_system_messages", "path": ["inputs", "*"]}],
        messages_json_schema,
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "foo", "*"],
            }
        ],
        nested_messages_json_schema,
    ),
    (
        [{"transformation_type": "remove_system_messages", "path": ["inputs", "*"]}],
        messages_json_schema,
        None,
        messages_json_schema,
    ),
    (
        [{"transformation_type": "remove_system_messages", "path": ["inputs", "*"]}],
        messages_json_schema,
        [],
        messages_json_schema,
    ),
]

_INVALID_TRANSFORMATION_TEST_CASES = [
    (  # filter on a string field
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "name"],
            }
        ],
        single_str_json_schema,
    ),
    (  # filter on a non existant field
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "name"],
            }
        ],
        messages_json_schema,
    ),
    (  # one valid and one invalid filter
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "name"],
            },
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "message"],
            },
        ],
        messages_json_schema,
    ),
    (  # filter on a single message field
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "message"],
            }
        ],
        messages_json_schema,
    ),
    (  # filter uses a * as a fieldname instead of a wildcard
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "*", "*"],
            }
        ],
        nested_messages_json_schema,
    ),
    (  # filter doesn't specify inputs or outputs
        [{"transformation_type": "remove_system_messages", "path": ["*"]}],
        messages_json_schema,
    ),
]

_VALID_TRANSFORMATIONS_TEST_CASES = [
    (
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "*"],
            }
        ],
        messages_json_schema,
    ),
    (
        [
            {
                "transformation_type": "remove_system_messages",
                "path": ["inputs", "foo", "*"],
            }
        ],
        nested_messages_json_schema,
    ),
]


@asynccontextmanager
async def messages_kv_enabled_tenant_client(
    db_asyncpg: asyncpg.Connection, use_api_key: bool
) -> AsyncGenerator[FreshTenantClient, Any]:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        await db_asyncpg.execute(
            """
            UPDATE organizations
            SET config = config || '{"kv_dataset_message_support": true}'
            WHERE id = $1
            """,
            authed_client.auth.organization_id,
        )
        yield authed_client


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize(
    "transformations,input_schema", _INVALID_TRANSFORMATION_TEST_CASES
)
async def test_invalid_dataset_transformations(
    input_schema: dict[str, Any],
    transformations: list[dict[str, str]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with messages_kv_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        aclient = authed_client.client

        # Create a dataset
        response = await aclient.post(
            "/datasets",
            json={
                "name": "test_dataset",
                "inputs_schema_definition": input_schema,
                "transformations": transformations,
            },
        )
        assert response.status_code == 400, response.text


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize(
    "transformations,input_schema", _VALID_TRANSFORMATIONS_TEST_CASES
)
async def test_valid_dataset_transformations(
    input_schema: dict[str, Any],
    transformations: list[dict[str, str]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with messages_kv_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        aclient = authed_client.client

        # Create a dataset
        response = await aclient.post(
            "/datasets",
            json={
                "name": "test_dataset",
                "inputs_schema_definition": input_schema,
                "transformations": transformations,
            },
        )
        assert response.status_code == 200, response.text

        response = await aclient.get(f"/datasets/{response.json()['id']}")
        assert response.status_code == 200, response.text
        dataset = response.json()
        assert dataset["transformations"] == transformations


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize(
    "transformations,input_schema,updated_transformations,updated_input_schema",
    _VALID_UPDATE_TRANSFORMATIONS_TEST_CASES,
)
async def test_valid_update_datasets_with_transformations(
    input_schema: dict[str, Any],
    transformations: list[dict[str, str]],
    updated_input_schema: dict[str, Any],
    updated_transformations: list[dict[str, str]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a dataset with transformations can be updated."""
    async with messages_kv_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        aclient = authed_client.client

        # Create a dataset
        response = await aclient.post(
            "/datasets",
            json={
                "name": "test_dataset",
                "inputs_schema_definition": input_schema,
                "transformations": transformations,
            },
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        update_json: dict[str, Any] = {
            "inputs_schema_definition": updated_input_schema,
        }
        if updated_transformations is not None:
            update_json["transformations"] = updated_transformations

        expected_transformations = (
            transformations
            if updated_transformations is None
            else (None if updated_transformations == [] else updated_transformations)
        )
        response = await aclient.patch(
            f"/datasets/{dataset_id}",
            json=update_json,
        )
        assert response.status_code == 200, response.text
        assert response.json()["transformations"] == expected_transformations

        response = await aclient.get(f"/datasets/{dataset_id}")

        stored_transformations = response.json()["transformations"]

        assert stored_transformations == expected_transformations, (
            "Database transformations don't match expected transformations"
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize(
    "transformations,input_schema,updated_transformations,updated_input_schema",
    _INVALID_UPDATE_TRANSFORMATIONS_TEST_CASES,
)
async def test_invalid_update_datasets_with_transformations(
    input_schema: dict[str, Any],
    transformations: list[dict[str, str]],
    updated_input_schema: dict[str, Any],
    updated_transformations: list[dict[str, str]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a dataset with transformations can be updated."""
    async with messages_kv_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        aclient = authed_client.client

        # Create a dataset
        response = await aclient.post(
            "/datasets",
            json={
                "name": "test_dataset",
                "inputs_schema_definition": input_schema,
                "transformations": transformations,
            },
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        response = await aclient.patch(
            f"/datasets/{dataset_id}",
            json={
                "inputs_schema_definition": updated_input_schema,
                "transformations": updated_transformations,
            },
        )
        assert response.status_code == 400, response.text


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_cannot_add_transformations_without_schema(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with messages_kv_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        aclient = authed_client.client

        # Create a dataset and fail without a schema
        response = await aclient.post(
            "/datasets",
            json={
                "name": "test_dataset",
                "transformations": [
                    {"path": ["inputs"], "transformation_type": "remove_extra_fields"}
                ],
            },
        )
        assert response.status_code == 400, response.text

        # Create a dataset with no schema, then try to update it to just have transformations
        response = await aclient.post(
            "/datasets",
            json={"name": "test_dataset"},
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        response = await aclient.patch(
            f"/datasets/{dataset_id}",
            json={
                "transformations": [
                    {"path": ["inputs"], "transformation_type": "remove_extra_fields"}
                ]
            },
        )
        assert response.status_code == 400, response.text


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_transformations_not_applied_when_validating_old_data(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a dataset with transformations can be updated."""
    async with messages_kv_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        aclient = authed_client.client

        # Create a dataset
        chat_dataset = await create_chat_preset_dataset(
            "test dataset",
            aclient,
        )

        chat_example = {
            "inputs": {
                "messages": [
                    [
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain", "schema", "messages", "SystemMessage"],
                            "kwargs": {
                                "content": "You are a helpful assistant. Please respond to the user's request only based on the given context.",
                                "type": "system",
                            },
                        },
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain", "schema", "messages", "HumanMessage"],
                            "kwargs": {
                                "content": "Question: Can you summarize this morning's meetings?\nContext: During this morning's meeting, we solved all world conflict.",
                                "type": "human",
                            },
                        },
                    ]
                ]
            },
            "outputs": {
                "generations": [
                    [
                        {
                            "text": "In this morning's meeting, we successfully resolved all world conflicts.",
                            "generation_info": {
                                "finish_reason": "stop",
                                "logprobs": None,
                            },
                            "type": "ChatGeneration",
                            "message": {
                                "lc": 1,
                                "type": "constructor",
                                "id": ["langchain", "schema", "messages", "AIMessage"],
                                "kwargs": {
                                    "content": "In this morning's meeting, we successfully resolved all world conflicts.",
                                    "additional_kwargs": {"refusal": None},
                                    "response_metadata": {
                                        "token_usage": {
                                            "completion_tokens": 13,
                                            "prompt_tokens": 54,
                                            "total_tokens": 67,
                                            "completion_tokens_details": {
                                                "reasoning_tokens": 0
                                            },
                                            "prompt_tokens_details": {
                                                "cached_tokens": 0
                                            },
                                        },
                                        "model_name": "gpt-4o-2024-08-06",
                                        "system_fingerprint": "fp_45cf54deae",
                                        "finish_reason": "stop",
                                        "logprobs": None,
                                    },
                                    "type": "ai",
                                    "id": "run-ea314874-92ff-4eb5-8e18-277b07f4cef9-0",
                                    "usage_metadata": {
                                        "input_tokens": 54,
                                        "output_tokens": 13,
                                        "total_tokens": 67,
                                        "input_token_details": {"cache_read": 0},
                                        "output_token_details": {"reasoning": 0},
                                    },
                                    "tool_calls": [],
                                    "invalid_tool_calls": [],
                                },
                            },
                        }
                    ]
                ],
                "llm_output": {
                    "token_usage": {
                        "completion_tokens": 13,
                        "prompt_tokens": 54,
                        "total_tokens": 67,
                        "completion_tokens_details": {"reasoning_tokens": 0},
                        "prompt_tokens_details": {"cached_tokens": 0},
                    },
                    "model_name": "gpt-4o-2024-08-06",
                    "system_fingerprint": "fp_45cf54deae",
                },
                "run": None,
                "type": "LLMResult",
            },
        }
        create_example_req = {
            "dataset_id": chat_dataset["id"],
            "inputs": chat_example["inputs"],
            "outputs": chat_example["outputs"],
        }

        example_resp = await aclient.post(
            "/examples",
            json=create_example_req,
        )
        assert example_resp.status_code == 200, example_resp.text
        assert example_resp.json()["inputs"] != chat_example["inputs"]
        assert example_resp.json()["outputs"] != chat_example["outputs"]

        # Update the dataset to not have transformations or input schema
        response = await aclient.patch(
            f"/datasets/{chat_dataset['id']}",
            json={
                "inputs_schema_definition": None,
                "outputs_schema_definition": None,
                "transformations": [],
            },
        )
        assert response.status_code == 200, response.text
        assert response.json()["inputs_schema_definition"] is None
        assert response.json()["outputs_schema_definition"] is None

        # remove the example so we know there's no format issues in the dataset
        delete_resp = await aclient.delete(
            f"/examples/{example_resp.json()['id']}",
        )
        assert delete_resp.status_code == 200, delete_resp.text

        # Create a new example with the same data that should NOT be transformed
        example_resp_no_transform = await aclient.post(
            "/examples",
            json=create_example_req,
        )
        assert example_resp_no_transform.status_code == 200, (
            example_resp_no_transform.text
        )
        assert example_resp_no_transform.json()["inputs"] == chat_example["inputs"]
        assert example_resp_no_transform.json()["outputs"] == chat_example["outputs"]

        # First, patch the dataset to have some transformations, but no schema, which should
        # succeed, but leave the examples in the dataset unchanged
        response = await aclient.patch(
            f"/datasets/{chat_dataset['id']}",
            json={
                "inputs_schema_definition": {"type": "object"},
                "outputs_schema_definition": {"type": "object"},
                "transformations": [
                    {"transformation_type": "remove_extra_fields", "path": ["inputs"]}
                ],
            },
        )
        assert response.status_code == 200, response.text
        existing_example_resp = await aclient.get(
            f"/examples/{example_resp_no_transform.json()['id']}"
        )
        assert existing_example_resp.status_code == 200
        # all inputs would be removed if this transformation were applied
        assert existing_example_resp.json()["inputs"] == chat_example["inputs"]
        assert existing_example_resp.json()["outputs"] == chat_example["outputs"]

        # Now, try to update the dataset with a schema and transformations
        # the update should fail, because we do not apply transformations to existing data
        # which means that the examples are not converted to openai format, and thus fail
        # schema validation
        response = await aclient.patch(
            f"/datasets/{chat_dataset['id']}",
            json={
                "inputs_schema_definition": chat_dataset["inputs_schema_definition"],
                "outputs_schema_definition": chat_dataset["outputs_schema_definition"],
                "transformations": chat_dataset["transformations"],
            },
        )
        assert response.status_code == 400
        assert response.json()["detail"]["non_conforming_examples"] == [
            example_resp_no_transform.json()["id"]
        ]


@pytest.mark.parametrize(
    "ingest_endpoint",
    [
        "/runs/multipart",
        "/runs/multipart|s3",
    ],
)
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_fetch_comparison_view_preview(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    ingest_endpoint: str,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test preview mode works for comparison view."""

    run_preview_io_max_chars = settings.RUN_PREVIEW_IO_MAX_CHARS

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        # create a dataset
        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "comparison view test",
            },
        )
        assert response.status_code == 200

        # create a few examples for this dataset
        dataset_id = response.json()["id"]
        example_ids = [uuid4() for _ in range(4)]
        example_tasks = [
            aclient.post(
                "/examples",
                json={
                    "inputs": {"input": f"input_val_{example_id}"},
                    "outputs": {"output": f"output_val_{example_id}"},
                    "dataset_id": str(dataset_id),
                    "id": str(example_id),
                },
            )
            for example_id in example_ids
        ]
        responses = await asyncio.gather(*example_tasks)
        assert all(response.status_code == 200 for response in responses)

        # create a couple sessions for this dataset
        session_ids = [uuid4() for _ in range(2)]
        session_tasks = [
            aclient.post(
                "/sessions",
                json={
                    "name": random_lower_string(),
                    "reference_dataset_id": str(dataset_id),
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "id": str(session_id),
                },
            )
            for session_id in session_ids
        ]
        responses = await asyncio.gather(*session_tasks)
        assert all(response.status_code == 200 for response in responses)

        # create a few runs for each session
        run_posts = []
        run_with_error_id = None
        input_output_content = "a" * (2 * run_preview_io_max_chars)
        for session_idx, session_id in enumerate(session_ids):
            for idx, example_id in enumerate(example_ids):
                run_id = str(uuid4())
                start_time, end_time = (
                    datetime.now(timezone.utc),
                    datetime.now(timezone.utc),
                )
                dotted_order = f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{run_id}"
                if idx == 0 and session_idx == 0:
                    run_with_error_id = run_id
                run_posts.append(
                    {
                        "name": "AgentExecutor",
                        "start_time": start_time.isoformat(),
                        "end_time": end_time.isoformat(),
                        "extra": {"foo": "bar"},
                        "error": random_lower_string()
                        if idx == 0 and session_idx == 0
                        else None,
                        "execution_order": 1,
                        "serialized": {"name": "AgentExecutor"},
                        "inputs": {"input": input_output_content},
                        "outputs": None
                        if idx == 0 and session_idx == 0
                        else {"output": input_output_content},
                        "session_id": str(session_id),
                        "run_type": "chain",
                        "id": run_id,
                        "trace_id": run_id,
                        "dotted_order": dotted_order,
                        "reference_example_id": str(example_id),
                    }
                )

        await post_runs(ingest_endpoint, aclient, post=run_posts)
        await wait_until_task_queue_empty()

        # run a query for the test runs, simulating comparison view with no filters
        response = await aclient.post(
            f"/datasets/{dataset_id}/runs",
            json={
                "session_ids": [
                    str(session_ids[0]),
                    str(session_ids[1]),
                ],
                "preview": True,
            },
        )

        assert response.status_code == 200
        examples_with_runs = response.json()
        assert len(examples_with_runs) == 4

        for idx, example in enumerate(examples_with_runs):
            assert len(example["runs"]) == 2
            for session_idx, run in enumerate(example["runs"]):
                assert run.get("inputs_preview") is not None
                assert len(run["inputs_preview"]) == run_preview_io_max_chars

                if run.get("id") == run_with_error_id:
                    assert run.get("error") is not None
                    assert run.get("outputs_preview") is None
                else:
                    assert run.get("error") is None
                    assert run.get("outputs_preview") is not None
                    assert len(run["outputs_preview"]) == run_preview_io_max_chars

                if "s3" in ingest_endpoint:
                    if (
                        len(input_output_content)
                        > settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
                    ):
                        assert run.get("inputs") is None
                        assert run.get("outputs") is None

                        assert run.get("inputs_s3_urls") is not None
                        assert ROOT_S3_KEY in run["inputs_s3_urls"]
                        assert "presigned_url" in run["inputs_s3_urls"][ROOT_S3_KEY]
                        assert "s3_url" in run["inputs_s3_urls"][ROOT_S3_KEY]
                    else:
                        assert run.get("inputs") is not None
                        assert (
                            run.get("outputs") is not None
                            or run.get("id") == run_with_error_id
                        )

                    if run.get("id") != run_with_error_id:
                        if (
                            len(input_output_content)
                            > settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
                        ):
                            assert run.get("outputs_s3_urls") is not None
                            assert ROOT_S3_KEY in run["outputs_s3_urls"]
                            assert (
                                "presigned_url" in run["outputs_s3_urls"][ROOT_S3_KEY]
                            )
                            assert "s3_url" in run["outputs_s3_urls"][ROOT_S3_KEY]
                        else:
                            assert run.get("outputs") is not None
                else:
                    if run.get("id") != run_with_error_id:
                        assert run.get("inputs")["input"] == input_output_content
                        assert run.get("outputs")["output"] == input_output_content


@pytest.mark.parametrize(
    "ingest_endpoint",
    [
        "/runs/multipart",
        "/runs/multipart|s3",
        "/runs/batch",
    ],
)
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_fetch_comparison_view_deleted_examples(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    ingest_endpoint: str,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test preview mode works for comparison view."""

    run_preview_io_max_chars = settings.RUN_PREVIEW_IO_MAX_CHARS

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        # create a dataset
        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "comparison view test",
            },
        )
        assert response.status_code == 200

        # create a few examples for this dataset
        dataset_id = response.json()["id"]
        example_ids = [uuid4() for _ in range(4)]
        example_tasks = [
            aclient.post(
                "/examples",
                json={
                    "inputs": {"input": f"input_val_{example_id}"},
                    "outputs": {"output": f"output_val_{example_id}"},
                    "dataset_id": str(dataset_id),
                    "id": str(example_id),
                },
            )
            for example_id in example_ids
        ]
        responses = await asyncio.gather(*example_tasks)
        assert all(response.status_code == 200 for response in responses)

        # create a session without the correct metadata
        session_id_1 = uuid4()
        await aclient.post(
            "/sessions",
            json={
                "name": random_lower_string(),
                "reference_dataset_id": str(dataset_id),
                "start_time": datetime.now(timezone.utc).isoformat(),
                "id": str(session_id_1),
            },
        )
        assert response.status_code == 200

        # create a session with the correct metadata
        session_id_2 = uuid4()
        await aclient.post(
            "/sessions",
            json={
                "name": random_lower_string(),
                "reference_dataset_id": str(dataset_id),
                "start_time": datetime.now(timezone.utc).isoformat(),
                "id": str(session_id_2),
                "extra": {
                    "metadata": {
                        "dataset_version": datetime.now(timezone.utc).isoformat()
                    }
                },
            },
        )
        assert response.status_code == 200

        # to avoid any potential race condition
        time.sleep(1)

        await crud.delete_examples(authed_client.auth, example_ids)

        fetched_examples = await aclient.get(f"/examples?dataset={str(dataset_id)}")
        assert fetched_examples.status_code == 200
        assert len(fetched_examples.json()) == 0

        for top_lvl_idx, session_id in enumerate([session_id_1, session_id_2]):
            # create a few runs for each session
            run_posts = []
            run_with_error_id = None
            input_output_content = "a" * (2 * run_preview_io_max_chars)
            for idx, example_id in enumerate(example_ids):
                run_id = str(uuid4())
                start_time, end_time = (
                    datetime.now(timezone.utc),
                    datetime.now(timezone.utc),
                )
                dotted_order = f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{run_id}"
                if idx == 0:
                    run_with_error_id = run_id
                run_posts.append(
                    {
                        "name": "AgentExecutor",
                        "start_time": start_time.isoformat(),
                        "end_time": end_time.isoformat(),
                        "extra": {"foo": "bar"},
                        "error": random_lower_string() if idx == 0 else None,
                        "execution_order": 1,
                        "serialized": {"name": "AgentExecutor"},
                        "inputs": {"input": input_output_content},
                        "outputs": None
                        if idx == 0
                        else {"output": input_output_content},
                        "session_id": str(session_id),
                        "run_type": "chain",
                        "id": run_id,
                        "trace_id": run_id,
                        "dotted_order": dotted_order,
                        "reference_example_id": str(example_id),
                    }
                )

            await post_runs(ingest_endpoint, aclient, post=run_posts)
            await wait_until_task_queue_empty()

            # ensure that the runs were persisted
            runs_response = await aclient.post(
                "/runs/query",
                json={
                    "session": [str(session_id)],
                },
            )
            assert runs_response.status_code == 200
            assert len(runs_response.json()) == 4

            # run a query for the test runs, simulating comparison view with no filters
            response = await aclient.post(
                f"/datasets/{dataset_id}/runs",
                json={
                    "session_ids": [
                        str(session_id),
                    ],
                    "preview": True,
                },
            )

            assert response.status_code == 200
            examples_with_runs = response.json()

            if top_lvl_idx == 0:
                # if we didn't add the correct metadata, the runs won't show up in the comparison view correctly, but they will be persisted
                assert len(examples_with_runs) == 0
            else:
                assert len(examples_with_runs) == 4

                for idx, example in enumerate(examples_with_runs):
                    assert len(example["runs"]) == 1
                    run = example["runs"][0]
                    assert run.get("inputs_preview") is not None
                    assert len(run["inputs_preview"]) == run_preview_io_max_chars

                    if run.get("id") == run_with_error_id:
                        assert run.get("error") is not None
                        assert run.get("outputs_preview") is None
                    else:
                        assert run.get("error") is None
                        assert run.get("outputs_preview") is not None
                        assert len(run["outputs_preview"]) == run_preview_io_max_chars

                    if "s3" in ingest_endpoint:
                        if (
                            len(input_output_content)
                            > settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
                        ):
                            assert run.get("inputs") is None
                            assert run.get("outputs") is None

                            assert run.get("inputs_s3_urls") is not None
                            assert ROOT_S3_KEY in run["inputs_s3_urls"]
                            assert "presigned_url" in run["inputs_s3_urls"][ROOT_S3_KEY]
                            assert "s3_url" in run["inputs_s3_urls"][ROOT_S3_KEY]
                        else:
                            assert run.get("inputs") is not None
                            assert (
                                run.get("outputs") is not None
                                or run.get("id") == run_with_error_id
                            )

                        if run.get("id") != run_with_error_id:
                            if (
                                len(input_output_content)
                                > settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
                            ):
                                assert run.get("outputs_s3_urls") is not None
                                assert ROOT_S3_KEY in run["outputs_s3_urls"]
                                assert (
                                    "presigned_url"
                                    in run["outputs_s3_urls"][ROOT_S3_KEY]
                                )
                                assert "s3_url" in run["outputs_s3_urls"][ROOT_S3_KEY]
                            else:
                                assert run.get("outputs") is not None
                    else:
                        if run.get("id") != run_with_error_id:
                            assert run.get("inputs")["input"] == input_output_content
                            assert run.get("outputs")["output"] == input_output_content


@pytest.mark.flaky
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_batch_run_over_dataset(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test preview mode works for comparison view."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        # create a dataset
        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "comparison view test",
            },
        )
        assert response.status_code == 200
        id = response.json()["id"]

        # create an example for this dataset
        example_id = uuid4()
        response = await aclient.post(
            "/examples",
            json={
                "inputs": {"question": "who are you?"},
                "outputs": {"answer": "jimmy jimson"},
                "dataset_id": str(id),
                "id": str(example_id),
            },
        )

        # set up an autoevalutors with run rules
        evaluator = {
            "prompt": [
                [
                    "system",
                    "You are evaluating the following value for toxicity, topic, and whether it's long.",
                ],
                [
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ],
            ],
            "schema": {
                "name": "eval",
                "description": "",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "toxic": {"type": "number"},
                    },
                },
            },
            "model": dumpd(
                FakeMessagesListChatModel(
                    responses=[
                        AIMessage(
                            tool_calls=[
                                ToolCall(name="eval", args={"toxic": "1"}, id="notused")
                            ],
                            content="",
                        )
                    ],
                    a_great_secret="PLACEHOLDER",
                )
            ),
        }
        response = await aclient.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )

        response = await aclient.post(
            "/runs/rules",
            json={
                "dataset_id": id,
                "evaluators": [{"structured": evaluator}],
                "filter": "eq(is_root, true)",
                "display_name": "test auto evaluator",
                "sampling_rate": 1,
                "is_enabled": True,
            },
        )
        rule = response.json()
        assert rule.get("evaluators"), json.dumps(rule)
        assert (
            "test_auto_evaluator" in rule["evaluators"][0]["structured"]["hub_ref"]
        ), json.dumps(rule)
        evaluator_id = rule["id"]

        # set up a model to run directly over the dataset
        fake_model = FakeMessagesListChatModel(
            responses=[AIMessage(content="I am a bot", type="ai")],
            a_great_secret="PLACEHOLDER",
        )
        fake_model_serialized = dumpd(
            ChatPromptTemplate([("user", "{question}")]) | fake_model
        )

        req = PlaygroundRunOverDatasetRequestSchema(
            manifest=fake_model_serialized,
            dataset_id=id,
            secrets={"A_GREAT_SECRET": "howdyho"},
            options={},
            project_name="Playground",
            repetitions=1,
            evaluator_rules=[evaluator_id],
        )

        response = await aclient.post(
            "/datasets/playground_experiment/batch", data=req.model_dump_json()
        )
        assert response.status_code == 200, response.text
        assert len(response.json()) == 1
        run_id = response.json()[0]["id"]
        # Add retry mechanism to wait for feedback processing
        max_retries = 10
        for _ in range(max_retries):
            await wait_until_task_queue_empty()

            response = await aclient.get("/feedback", params={"run": run_id})
            assert response.status_code == 200, response.text

            if len(response.json()) > 0:
                break

            await asyncio.sleep(0.5)

        # Final assertion
        assert len(response.json()) == 1, "No feedback was generated after waiting"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_batch_run_over_dataset_batch_size(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test batch size works for playground experiments."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        import app.models.datasets.run_over_dataset as run_over_dataset

        with patch.object(
            run_over_dataset.shared_settings,
            "PLAYGROUND_RUN_OVER_DATASET_EXAMPLE_BATCH_SIZE",
            1,
        ):
            aclient = authed_client.client

            await db_asyncpg.execute(
                """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
                authed_client.auth.organization_id,
            )

            # create a dataset
            response = await aclient.post(
                "/datasets",
                json={
                    "name": random_lower_string(),
                    "description": "comparison view test",
                },
            )
            assert response.status_code == 200
            id = response.json()["id"]

            # create an example for this dataset
            example_ids = [uuid4() for _ in range(100)]
            response = await aclient.post(
                "/examples/bulk",
                json=[
                    {
                        "inputs": {"question": "who are you?"},
                        "outputs": {"answer": "jimmy jimson"},
                        "dataset_id": str(id),
                        "id": str(example_id),
                    }
                    for example_id in example_ids
                ],
            )
            assert response.status_code == 200

            # set up a model to run directly over the dataset
            fake_model = FakeMessagesListChatModel(
                responses=[AIMessage(content="I am a bot", type="ai")],
                a_great_secret="PLACEHOLDER",
            )
            fake_model_serialized = dumpd(
                ChatPromptTemplate([("user", "{question}")]) | fake_model
            )

            broken_req = PlaygroundRunOverDatasetBatchRequestSchema(
                manifest=fake_model_serialized,
                dataset_id=id,
                secrets={"A_GREAT_SECRET": "howdyho"},
                options={},
                project_name="Playground",
                repetitions=7,
                evaluator_rules=[],
                batch_size=100,
            )
            response = await aclient.post(
                "/datasets/playground_experiment/batch",
                data=broken_req.model_dump_json(),
            )
            assert response.status_code == 400, response.text

            broken_req = PlaygroundRunOverDatasetBatchRequestSchema(
                manifest=fake_model_serialized,
                dataset_id=id,
                secrets={"A_GREAT_SECRET": "howdyho"},
                options={},
                project_name="Playground",
                repetitions=30,
                evaluator_rules=[],
                batch_size=30,
            )
            response = await aclient.post(
                "/datasets/playground_experiment/batch",
                data=broken_req.model_dump_json(),
            )
            assert response.status_code == 400, response.text

            req = PlaygroundRunOverDatasetBatchRequestSchema(
                manifest=fake_model_serialized,
                dataset_id=id,
                secrets={"A_GREAT_SECRET": "howdyho"},
                options={},
                project_name="Playground",
                repetitions=1,
                evaluator_rules=[],
            )

            response = await aclient.post(
                "/datasets/playground_experiment/batch", data=req.model_dump_json()
            )
            assert response.status_code == 200, response.text
            assert len(response.json()) == 1

            req = PlaygroundRunOverDatasetBatchRequestSchema(
                manifest=fake_model_serialized,
                dataset_id=id,
                secrets={"A_GREAT_SECRET": "howdyho"},
                options={},
                project_name="Playground",
                repetitions=1,
                evaluator_rules=[],
                batch_size=5,
            )

            response = await aclient.post(
                "/datasets/playground_experiment/batch", data=req.model_dump_json()
            )
            assert response.status_code == 200, response.text
            assert len(response.json()) == 5


@pytest.mark.flaky
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_stream_run_over_dataset(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test streaming mode works for playground experiment."""

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        # create a dataset
        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "streaming playground test",
            },
        )
        assert response.status_code == 200
        id = response.json()["id"]

        # create an example for this dataset
        example_id = uuid4()
        response = await aclient.post(
            "/examples",
            json={
                "inputs": {"question": "who are you?"},
                "outputs": {"answer": "jimmy jimson"},
                "dataset_id": str(id),
                "id": str(example_id),
            },
        )

        # set up an autoevaluator with run rules
        evaluator = {
            "prompt": [
                [
                    "system",
                    "You are evaluating the following value for toxicity, topic, and whether it's long.",
                ],
                [
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ],
            ],
            "schema": {
                "name": "eval",
                "description": "",
                "properties": {
                    "toxic": {"type": "number"},
                },
            },
            "model": dumpd(
                FakeMessagesListChatModel(
                    responses=[
                        AIMessage(
                            tool_calls=[
                                ToolCall(name="eval", args={"toxic": 1}, id="notused")
                            ],
                            content="",
                        )
                    ],
                    a_great_secret="PLACEHOLDER",
                )
            ),
        }
        response = await aclient.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )

        response = await aclient.post(
            "/runs/rules",
            json={
                "dataset_id": id,
                "evaluators": [{"structured": evaluator}],
                "filter": "eq(is_root, true)",
                "display_name": "test auto evaluator",
                "sampling_rate": 1,
                "is_enabled": True,
            },
        )
        evaluator_id = response.json()["id"]

        # set up a model to run directly over the dataset
        # Use FakeStreamingMessagesListChatModel with a longer message that will be streamed in chunks
        multipart_response = "I am a bot, and I will respond with multiple streaming chunks to test that the streaming functionality works as expected."
        fake_model = FakeStreamingMessagesListChatModel(
            responses=[AIMessage(content=multipart_response, type="ai")],
            a_great_secret="PLACEHOLDER",
            chunk_size=5,  # Break response into chunks of 5 characters
        )
        fake_model_serialized = dumpd(
            ChatPromptTemplate([("user", "{question}")]) | fake_model
        )

        req = PlaygroundRunOverDatasetRequestSchema(
            manifest=fake_model_serialized,
            dataset_id=id,
            secrets={"A_GREAT_SECRET": "howdyho"},
            options={},
            project_name="Playground",
            evaluator_rules=[evaluator_id],
        )

        # Function to parse SSE events from response chunks
        def parse_sse_events(chunk):
            events = []
            # Split by SSE event delimiter (double newline with carriage returns)
            event_strings = chunk.decode("utf-8").split("\n\n")

            for event_str in event_strings:
                if not event_str:
                    continue

                # Split the event into lines
                lines = event_str.split("\n")
                event_type = None
                event_data = None

                for line in lines:
                    if line.startswith("event:"):
                        event_type = line.replace("event:", "").strip()
                    elif line.startswith("data:"):
                        event_data = line.replace("data:", "").strip()

                # Process data events with JSON content
                if event_type == "data" and event_data:
                    try:
                        data = json.loads(event_data)
                        if "patch" in data and isinstance(data["patch"], list):
                            # Extract each patch operation as a separate event
                            for patch_op in data["patch"]:
                                events.append(patch_op)
                    except json.JSONDecodeError:
                        pass

            return events

        # Call the streaming endpoint
        async with aclient.stream(
            "POST", "/datasets/playground_experiment/stream", data=req.model_dump_json()
        ) as response:
            assert response.status_code == 200, (
                f"Received status code {response.status_code}"
            )

            # Collect and parse all streamed events
            all_events = []
            run_id = None

            async for chunk in response.aiter_bytes():
                events = parse_sse_events(chunk)
                all_events.extend(events)

                # Extract run_id from the first event with op='replace'
                if run_id is None and events:
                    for event in events:
                        if event.get("op") == "replace" and "id" in event.get(
                            "value", {}
                        ):
                            run_id = event["value"]["id"]
                            break

        # Verify we got at least one event and a run_id
        assert len(all_events) > 0, "No SSE events were received"
        assert run_id is not None, "No run ID was found in the streaming response"

        # Wait for feedback processing
        max_retries = 10
        feedback = None

        for _ in range(max_retries):
            await wait_until_task_queue_empty()

            response = await aclient.get("/feedback", params={"run": run_id})
            assert response.status_code == 200, response.text

            if len(response.json()) > 0:
                feedback = response.json()
                break

            await asyncio.sleep(0.5)

        # Final assertion for feedback
        assert feedback, "No feedback was generated after waiting"
        assert feedback[0]["key"] == "toxic"
        assert feedback[0]["score"] == 1

        # Verify the content from streamed events
        add_operation_count = 0
        replace_operation_count = 0

        for i, event in enumerate(all_events):
            if i == 0:
                assert event.get("op") == "replace"
                assert event.get("path") == f"/runs/{str(example_id)}"
                assert event.get("value", {}).get("id") == str(run_id)
                assert event.get("value", {}).get("reference_example_id") == str(
                    example_id
                )
                assert event.get("value", {}).get("trace_id") == str(run_id)
                assert (
                    event.get("value", {})
                    .get("session_name")
                    .startswith("Playground::")
                )
                assert (
                    event.get("value", {}).get("outputs")[0]["content"]
                    == multipart_response[0:5]
                )
                replace_operation_count += 1
            elif i < 25:
                end_idx = min((i + 1) * 5, len(multipart_response))
                assert event.get("op") == "add"
                assert event.get("path") == f"/runs/{str(example_id)}/outputs/-"
                assert (
                    event.get("value", {})["kwargs"]["content"]
                    == multipart_response[i * 5 : end_idx]
                )
                add_operation_count += 1
            else:
                assert event.get("op") == "replace"
                assert event.get("path") == f"/runs/{str(example_id)}"
                assert event.get("value", {}).get("id") == str(run_id)
                assert event.get("value", {}).get("reference_example_id") == str(
                    example_id
                )
                assert event.get("value", {}).get("trace_id") == str(run_id)
                assert (
                    event.get("value", {})
                    .get("session_name")
                    .startswith("Playground::")
                )
                assert (
                    event.get("value", {}).get("outputs")["output"]["content"]
                    == multipart_response
                )
                replace_operation_count += 1

        # Ensure we received multiple add operations (one for each chunk)
        # With a chunk size of 5 and our response length, we should have multiple chunks
        assert add_operation_count == 24, (
            f"Expected 24 'add' operations for streaming chunks, got {add_operation_count}"
        )
        assert replace_operation_count == 2, (
            f"Expected 2 'replace' operations, got {replace_operation_count}"
        )


@pytest.mark.asyncio
async def test_create_dataset_with_created_at(http_tenant_one: AsyncClient) -> None:
    """
    Test that when a dataset is created with a specified created_at,
    the API respects that created_at value.
    """
    from datetime import datetime, timedelta, timezone

    # We'll pick a custom date/time in the past
    custom_dt = datetime.now(timezone.utc) - timedelta(days=1)
    custom_dt_str = custom_dt.isoformat()

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "Testing created_at parameter",
            "created_at": custom_dt_str,
        },
    )

    assert response.status_code == 200, response.text
    data = response.json()

    # Check the created_at in the response
    returned_created_at_str = data["created_at"]
    returned_created_at = datetime.fromisoformat(returned_created_at_str)

    # Verify the returned created_at is the same as what we specified
    # (allowing a small floating-second difference if needed)
    assert abs((returned_created_at - custom_dt).total_seconds()) < 1, (
        f"Expected created_at={custom_dt}, but got {returned_created_at}"
    )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_fetch_grouped_comparison_view(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "comparison view test",
            },
        )
        assert response.status_code == 200

        # Create 4 examples for this dataset
        dataset_id = response.json()["id"]

        # feedback keys are helpfulness_score, toxicity_score, correctness, and stupidity
        example_metadata = [
            "a",
            "b",
            "c",
            "a",
            "b",
            "c",
            "a",
            "b",
            "c",
            "a",
            "b",
            "c",
        ]
        example_metadata_2 = [
            "test",
            None,
            "test",
            None,
            "test",
            None,
            "test",
            None,
            "test",
            None,
            "test",
            None,
        ]
        example_metadata_3 = [
            "test",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "test",
            "test",
            "test",
            "MISSING",
            "test",
            "MISSING",
        ]
        helpfulness_indices_to_correct = [3, 4]
        helpfulness_scores = [100, 0, 0, 50, 100, 0, 100, None, None, 0, 100, 0]
        toxicity_scores = [10, 20, 0, 10, 10, 20, 10, 20, 10, 10, 0, 20]
        correctness_scores = [0, 1, 0, 0, 0, None, None, 0, 1, 0, 1, 0]
        stupidity_scores = [1, 1, 0, 0, None, None, 1, 0, 1, 1, 1, 0]

        correctness_values = [
            "incorrect",
            "correct",
            "incorrect",
            "incorrect",
            "incorrect",
            "correct",
            "correct",
            "incorrect",
            "correct",
            "incorrect",
            "correct",
            "incorrect",
        ]
        stupidity_values = [
            "stupid",
            "stupid",
            "not stupid",
            "not stupid",
            None,
            None,
            "stupid",
            "not stupid",
            "stupid",
            "stupid",
            "stupid",
            "not stupid",
        ]
        metadata_vals_1 = [1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2]
        metadata_vals_2 = [
            "a",
            "b",
            "c",
            "d",
            "e",
            "f",
            "g",
            "h",
            "i",
            "j",
            None,
            "MISSING",
        ]
        metadata_vals_3 = [
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
            "MISSING",
        ]
        total_tokens = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]
        errors = [
            None,
            "error",
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            "error",
            None,
        ]

        example_ids = [uuid4() for _ in range(12)]
        # we sort examples by reference_example_id in both endpoints
        example_ids.sort()
        examples = [
            {
                "inputs": {"input": random_lower_string()},
                "outputs": {"output": random_lower_string()},
                "dataset_id": str(dataset_id),
                "id": str(example_ids[i]),
                "metadata": {
                    "group": example_metadata[i],
                    "group2": example_metadata_2[i],
                    **(
                        {"group3": example_metadata_3[i]}
                        if example_metadata_3[i] != "MISSING"
                        else {}
                    ),
                },
            }
            for i in range(12)
        ]
        example_tasks = [
            aclient.post(
                "/examples",
                json=e,
            )
            for e in examples
        ]
        responses = await asyncio.gather(*example_tasks)
        for response in responses:
            assert response.status_code == 200

        # do this twice to test redis caching of the example metadata
        for _ in range(2):
            run_ids = [uuid4() for _ in range(12)]
            session_id = uuid4()
            response = await aclient.post(
                "/sessions",
                json={
                    "name": random_lower_string(),
                    "reference_dataset_id": str(dataset_id),
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "id": str(session_id),
                },
            )
            assert response.status_code == 200

            runs = []
            for i in range(12):
                time.sleep(0.1)
                now = datetime.now(timezone.utc)
                runs.append(
                    {
                        "name": "AgentExecutor",
                        "start_time": (now - timedelta(minutes=1)).isoformat(),
                        "end_time": now.isoformat(),
                        "extra": {
                            "metadata": {
                                "foo": metadata_vals_1[i],
                                **(
                                    {"bar": metadata_vals_2[i]}
                                    if metadata_vals_2[i] != "MISSING"
                                    else {}
                                ),
                                **(
                                    {"baz": metadata_vals_3[i]}
                                    if metadata_vals_3[i] != "MISSING"
                                    else {}
                                ),
                            },
                            "batch_size": 1,
                            "invocation_params": {"model": "gpt-3.5-turbo-0125"},
                        },
                        "error": errors[i],
                        "execution_order": 1,
                        "serialized": {"name": "AgentExecutor"},
                        "inputs": {"input": random_lower_string()},
                        "outputs": {
                            "generations": [[{"text": random_lower_string()}]],
                            "llm_output": {
                                "token_usage": {
                                    "prompt_tokens": total_tokens[i] / 2,
                                    "completion_tokens": total_tokens[i] / 2,
                                },
                            },
                        },
                        "session_id": str(session_id),
                        "reference_example_id": str(example_ids[i]),
                        "parent_run_id": None,
                        "trace_id": str(run_ids[i]),
                        "run_type": "llm",
                        "id": str(run_ids[i]),
                        "dotted_order": f"{(now - timedelta(minutes=1)).strftime('%Y%m%dT%H%M%S%f')}Z{str(run_ids[i])}",
                    }
                )

            # create the four feedbacks for each run
            all_feedbacks = []
            feedback_index = 0
            feedback_indices_to_correct = []
            for i in range(12):
                if helpfulness_scores[i] is not None:
                    score_to_use = helpfulness_scores[i]
                    if i in helpfulness_indices_to_correct:
                        score_to_use = 0
                        feedback_indices_to_correct.append(feedback_index)
                    all_feedbacks.append(
                        {
                            "run_id": str(run_ids[i]),
                            "key": "helpfulness_score",
                            "score": score_to_use,
                            "feedback_source": {"type": "api"},
                        }
                    )
                    feedback_index += 1
                if toxicity_scores[i] is not None:
                    all_feedbacks.append(
                        {
                            "run_id": str(run_ids[i]),
                            "key": "toxicity_score",
                            "score": toxicity_scores[i],
                            "feedback_source": {"type": "api"},
                        }
                    )
                    feedback_index += 1
                if (
                    correctness_scores[i] is not None
                    or correctness_values[i] is not None
                ):
                    all_feedbacks.append(
                        {
                            "run_id": str(run_ids[i]),
                            "key": "correctness",
                            "score": correctness_scores[i],
                            "value": correctness_values[i],
                            "feedback_source": {"type": "api"},
                        }
                    )
                    feedback_index += 1
                if stupidity_scores[i] is not None or stupidity_values[i] is not None:
                    all_feedbacks.append(
                        {
                            "run_id": str(run_ids[i]),
                            "key": "stupidity",
                            "score": stupidity_scores[i],
                            "value": stupidity_values[i],
                            "feedback_source": {"type": "api"},
                        }
                    )
                    feedback_index += 1

            await post_runs("/runs/batch", aclient, post=runs)

            await wait_until_task_queue_empty()  # type: ignore[call-arg]

            feedback_tasks = []
            for feedback in all_feedbacks:
                feedback_tasks.append(
                    aclient.post(
                        "/feedback",
                        json=feedback,
                    )
                )
            responses = await asyncio.gather(*feedback_tasks)
            for response in responses:
                assert response.status_code == 200

            feedback_id_to_correct_1 = responses[feedback_indices_to_correct[0]].json()[
                "id"
            ]
            feedback_id_to_correct_2 = responses[feedback_indices_to_correct[1]].json()[
                "id"
            ]
            feedback_score_to_correct_1 = helpfulness_scores[
                helpfulness_indices_to_correct[0]
            ]
            feedback_score_to_correct_2 = helpfulness_scores[
                helpfulness_indices_to_correct[1]
            ]

            # update all_feedbacks to reflect the corrections (for easier checking later):
            all_feedbacks[feedback_indices_to_correct[0]]["score"] = helpfulness_scores[
                helpfulness_indices_to_correct[0]
            ]
            all_feedbacks[feedback_indices_to_correct[1]]["score"] = helpfulness_scores[
                helpfulness_indices_to_correct[1]
            ]

            await wait_until_task_queue_empty(timeout=120, n_empty_threshold=3)  # type: ignore[call-arg]

            # Add corrections to the feedback and make sure these get used in the feedback stats instead of the original scores
            response = await aclient.patch(
                f"/feedback/{feedback_id_to_correct_1}",
                json={
                    "correction": {"score": feedback_score_to_correct_1},
                },
            )
            assert response.status_code == 200

            response = await aclient.patch(
                f"/feedback/{feedback_id_to_correct_2}",
                json={
                    "correction": {"score": feedback_score_to_correct_2},
                },
            )
            assert response.status_code == 200

            async def check_group(group_data: dict, metadata_vals: list) -> None:
                metadata_val = group_data["group_key"]
                all_indices_with_metadata = [
                    i
                    for i, val in enumerate(metadata_vals)
                    if val == metadata_val
                    or (
                        (val is None and metadata_val == "null")
                        or (val == "MISSING" and metadata_val == "Missing")
                    )
                ]
                relevant_example_ids = [
                    str(example_ids[i]) for i in all_indices_with_metadata
                ]
                relevant_run_ids = [str(run_ids[i]) for i in all_indices_with_metadata]

                helpfulness_feedbacks = [
                    feedback
                    for feedback in all_feedbacks
                    if feedback["run_id"] in relevant_run_ids
                    and feedback["key"] == "helpfulness_score"
                ]
                toxicity_feedbacks = [
                    feedback
                    for feedback in all_feedbacks
                    if feedback["run_id"] in relevant_run_ids
                    and feedback["key"] == "toxicity_score"
                ]
                correctness_feedbacks = [
                    feedback
                    for feedback in all_feedbacks
                    if feedback["run_id"] in relevant_run_ids
                    and feedback["key"] == "correctness"
                ]
                stupidity_feedbacks = [
                    feedback
                    for feedback in all_feedbacks
                    if feedback["run_id"] in relevant_run_ids
                    and feedback["key"] == "stupidity"
                ]

                helpfulness_score_scores: list[float] = [
                    cast(float, feedback["score"])
                    for feedback in helpfulness_feedbacks
                    if feedback["score"] is not None
                ]
                expected_helpfulness_avg = (
                    sum(helpfulness_score_scores) / len(helpfulness_score_scores)
                    if len(helpfulness_score_scores) > 0
                    else None
                )
                expected_helpfulness_n = len(helpfulness_score_scores)

                toxicity_score_scores = [
                    cast(float, feedback["score"])
                    for feedback in toxicity_feedbacks
                    if feedback["score"] is not None
                ]
                expected_toxicity_avg = (
                    sum(toxicity_score_scores) / len(toxicity_score_scores)
                    if len(toxicity_score_scores) > 0
                    else None
                )
                expected_toxicity_n = len(toxicity_score_scores)

                correctness_score_scores = [
                    cast(float, feedback["score"])
                    for feedback in correctness_feedbacks
                    if feedback["score"] is not None
                ]
                expected_correctness_avg = (
                    sum(correctness_score_scores) / len(correctness_score_scores)
                    if len(correctness_score_scores) > 0
                    else None
                )
                expected_correctness_n = len(correctness_score_scores)

                correctness_vals = [
                    feedback["value"]
                    for feedback in correctness_feedbacks
                    if feedback["value"] is not None
                ]
                expected_correctness_value_dict = {
                    val: correctness_vals.count(val) for val in set(correctness_vals)
                }

                stupidity_score_scores = [
                    cast(float, feedback["score"])
                    for feedback in stupidity_feedbacks
                    if feedback["score"] is not None
                ]
                expected_stupidity_avg = (
                    sum(stupidity_score_scores) / len(stupidity_score_scores)
                    if len(stupidity_score_scores) > 0
                    else None
                )
                expected_stupidity_n = len(stupidity_score_scores)

                stupidity_vals = [
                    feedback["value"]
                    for feedback in stupidity_feedbacks
                    if feedback["value"] is not None
                ]
                expected_stupidity_value_dict = {
                    val: stupidity_vals.count(val) for val in set(stupidity_vals)
                }

                assert group_data["count"] == len(all_indices_with_metadata)

                feedback_stats = group_data["feedback_stats"]
                if expected_helpfulness_n > 0:
                    assert (
                        feedback_stats["helpfulness_score"]["n"]
                        == expected_helpfulness_n
                    )
                    assert (
                        feedback_stats["helpfulness_score"]["avg"]
                        == expected_helpfulness_avg
                    )
                else:
                    assert "helpfulness_score" not in feedback_stats

                assert feedback_stats["toxicity_score"]["n"] == expected_toxicity_n
                assert feedback_stats["toxicity_score"]["avg"] == expected_toxicity_avg

                assert feedback_stats["correctness"]["n"] == expected_correctness_n
                assert feedback_stats["correctness"]["avg"] == expected_correctness_avg
                assert (
                    feedback_stats["correctness"]["values"]
                    == expected_correctness_value_dict
                )

                if expected_stupidity_n > 0:
                    assert feedback_stats["stupidity"]["n"] == expected_stupidity_n
                    assert feedback_stats["stupidity"]["avg"] == expected_stupidity_avg
                    assert (
                        feedback_stats["stupidity"]["values"]
                        == expected_stupidity_value_dict
                    )
                else:
                    assert "stupidity" not in feedback_stats

                expected_total_tokens = sum(
                    total_tokens[i] for i in all_indices_with_metadata
                )
                assert group_data["total_tokens"] == expected_total_tokens

                assert group_data["prompt_tokens"] == expected_total_tokens / 2
                assert group_data["completion_tokens"] == expected_total_tokens / 2

                expected_min_start_time = next(
                    (
                        cast(str, run["start_time"])
                        for run in runs
                        if run["id"] in relevant_run_ids
                    ),
                    "",
                )[:-6]
                expected_max_start_time = [
                    cast(str, run["start_time"])
                    for run in runs
                    if run["id"] in relevant_run_ids
                ][-1][:-6]
                assert group_data["min_start_time"] == expected_min_start_time
                assert group_data["max_start_time"] == expected_max_start_time

                expected_latency_p50 = 60
                expected_latency_p99 = 60

                assert group_data["latency_p50"] == expected_latency_p50
                assert group_data["latency_p99"] == expected_latency_p99

                # get the example object from `examples` for each example_id
                all_possible_examples = [
                    example
                    for example in examples
                    if example["id"] in relevant_example_ids
                ]
                all_possible_runs = [
                    run for run in runs if run["id"] in relevant_run_ids
                ]

                expected_error_rate = len(
                    [run for run in all_possible_runs if run["error"] is not None]
                ) / len(all_possible_runs)
                assert group_data["error_rate"] == expected_error_rate

                def check_example(my_example, index):
                    assert my_example["id"] == relevant_example_ids[index]
                    overall_example_index = example_ids.index(UUID(my_example["id"]))
                    example_obj = next(
                        e for e in all_possible_examples if e["id"] == my_example["id"]
                    )
                    assert example_obj["inputs"] == my_example["inputs"]
                    assert example_obj["outputs"] == my_example["outputs"]
                    assert len(my_example["runs"]) == 1
                    run = my_example["runs"][0]

                    assert run["id"] == str(run_ids[overall_example_index])
                    run_obj = next(r for r in all_possible_runs if r["id"] == run["id"])
                    assert run_obj["inputs"] == run["inputs"]
                    assert run_obj["outputs"] == run["outputs"]
                    assert run_obj["error"] == run["error"]

                    for k, v in example_obj["metadata"].items():
                        run_obj["extra"]["metadata"][f"ls_example_{k}"] = v

                    run_obj["extra"]["metadata"]["ls_example_dataset_split"] = ["base"]
                    run_obj["extra"]["metadata"]["ls_run_depth"] = 0
                    assert run_obj["extra"] == run["extra"]
                    assert run_obj["session_id"] == str(run["session_id"])
                    assert run_obj["reference_example_id"] == str(
                        run["reference_example_id"]
                    )
                    assert run_obj["reference_example_id"] == str(my_example["id"])
                    assert run_obj["run_type"] == run["run_type"]
                    assert run_obj["start_time"][:-6] == run["start_time"]
                    assert run_obj["end_time"][:-6] == run["end_time"]
                    assert (
                        run_obj["outputs"]["llm_output"]["token_usage"]["prompt_tokens"]
                        == run["prompt_tokens"]
                    )
                    assert (
                        run_obj["outputs"]["llm_output"]["token_usage"][
                            "completion_tokens"
                        ]
                        == run["completion_tokens"]
                    )
                    assert (
                        run_obj["outputs"]["llm_output"]["token_usage"][
                            "completion_tokens"
                        ]
                        + run_obj["outputs"]["llm_output"]["token_usage"][
                            "prompt_tokens"
                        ]
                        == run["total_tokens"]
                    )

                    assert (
                        run["status"] == "success"
                        if run_obj["error"] is None
                        else "error"
                    )

                    # check run_obj["feedback_stats"]
                    feedback_stats = run["feedback_stats"]

                    helpfulness_feedbacks = [
                        feedback
                        for feedback in all_feedbacks
                        if feedback["run_id"] == run_obj["id"]
                        and feedback["key"] == "helpfulness_score"
                    ]
                    toxicity_feedbacks = [
                        feedback
                        for feedback in all_feedbacks
                        if feedback["run_id"] == run_obj["id"]
                        and feedback["key"] == "toxicity_score"
                    ]
                    correctness_feedbacks = [
                        feedback
                        for feedback in all_feedbacks
                        if feedback["run_id"] == run_obj["id"]
                        and feedback["key"] == "correctness"
                    ]
                    stupidity_feedbacks = [
                        feedback
                        for feedback in all_feedbacks
                        if feedback["run_id"] == run_obj["id"]
                        and feedback["key"] == "stupidity"
                    ]

                    helpfulness_score_scores = [
                        feedback["score"]
                        for feedback in helpfulness_feedbacks
                        if feedback["score"] is not None
                    ]
                    if len(helpfulness_score_scores) > 0:
                        expected_helpfulness_avg = (
                            sum(helpfulness_score_scores)
                            / len(helpfulness_score_scores)
                            if len(helpfulness_score_scores) > 0
                            else None
                        )
                        expected_helpfulness_n = len(helpfulness_score_scores)

                        assert (
                            feedback_stats["helpfulness_score"]["n"]
                            == expected_helpfulness_n
                        )
                        assert (
                            feedback_stats["helpfulness_score"]["avg"]
                            == expected_helpfulness_avg
                        )
                    else:
                        assert "helpfulness_score" not in feedback_stats

                    toxicity_score_scores = [
                        feedback["score"]
                        for feedback in toxicity_feedbacks
                        if feedback["score"] is not None
                    ]
                    if len(toxicity_score_scores) > 0:
                        expected_toxicity_avg = (
                            sum(toxicity_score_scores) / len(toxicity_score_scores)
                            if len(toxicity_score_scores) > 0
                            else None
                        )
                        expected_toxicity_n = len(toxicity_score_scores)

                        assert (
                            feedback_stats["toxicity_score"]["n"] == expected_toxicity_n
                        )
                        assert (
                            feedback_stats["toxicity_score"]["avg"]
                            == expected_toxicity_avg
                        )
                    else:
                        assert "toxicity_score" not in feedback_stats

                    correctness_score_scores = [
                        feedback["score"]
                        for feedback in correctness_feedbacks
                        if feedback["score"] is not None
                    ]
                    correctness_feedbacks_exist = [
                        feedback
                        for feedback in correctness_feedbacks
                        if feedback["score"] is not None
                        or feedback["value"] is not None
                    ]
                    if len(correctness_feedbacks_exist) > 0:
                        expected_correctness_avg = (
                            sum(correctness_score_scores)
                            / len(correctness_score_scores)
                            if len(correctness_score_scores) > 0
                            else None
                        )
                        expected_correctness_n = len(correctness_score_scores)

                        correctness_vals = [
                            feedback["value"]
                            for feedback in correctness_feedbacks
                            if feedback["value"] is not None
                        ]
                        expected_correctness_value_dict = {
                            val: correctness_vals.count(val)
                            for val in set(correctness_vals)
                        }

                        assert (
                            feedback_stats["correctness"]["n"] == expected_correctness_n
                        )
                        assert (
                            feedback_stats["correctness"]["avg"]
                            == expected_correctness_avg
                        )
                        assert (
                            feedback_stats["correctness"]["values"]
                            == expected_correctness_value_dict
                        )
                    else:
                        assert "correctness" not in feedback_stats

                    stupidity_score_scores = [
                        feedback["score"]
                        for feedback in stupidity_feedbacks
                        if feedback["score"] is not None
                    ]
                    stupidity_feedbacks_exist = [
                        feedback
                        for feedback in stupidity_feedbacks
                        if feedback["score"] is not None
                        or feedback["value"] is not None
                    ]
                    if len(stupidity_feedbacks_exist) > 0:
                        expected_stupidity_avg = (
                            sum(stupidity_score_scores) / len(stupidity_score_scores)
                            if len(stupidity_score_scores) > 0
                            else None
                        )
                        expected_stupidity_n = len(stupidity_score_scores)

                        stupidity_vals = [
                            feedback["value"]
                            for feedback in stupidity_feedbacks
                            if feedback["value"] is not None
                        ]
                        expected_stupidity_value_dict = {
                            val: stupidity_vals.count(val)
                            for val in set(stupidity_vals)
                        }

                        assert feedback_stats["stupidity"]["n"] == expected_stupidity_n
                        assert (
                            feedback_stats["stupidity"]["avg"] == expected_stupidity_avg
                        )
                        assert (
                            feedback_stats["stupidity"]["values"]
                            == expected_stupidity_value_dict
                        )
                    else:
                        assert "stupidity" not in feedback_stats

                assert len(group_data["examples"]) == min(len(relevant_example_ids), 5)
                if len(relevant_example_ids) > 5:
                    filter = group_data["filter"]
                    # use the filter to fetch more runs from the current group:
                    response = await aclient.post(
                        f"/datasets/{dataset_id}/runs",
                        json={
                            "session_ids": [str(session_id)],
                            "filters": {str(session_id): [filter]},
                            "limit": 10,
                            "offset": 5,
                        },
                    )
                    assert response.status_code == 200, response.text
                    more_examples = response.json()
                    assert len(more_examples) == len(relevant_example_ids) - 5
                    for i, example in enumerate(more_examples):
                        check_example(example, 5 + i)

                for i, example in enumerate(group_data["examples"]):
                    check_example(example, i)

            # Now, time to test the group endpoint
            response = await aclient.post(
                f"/datasets/{dataset_id}/group/runs",
                json={
                    "group_by": "run_metadata",
                    "metadata_key": "foo",
                    "session_ids": [str(session_id)],
                    "offset": 0,
                    "limit": 10,
                },
            )
            assert response.status_code == 200
            data = response.json()["groups"]

            assert len(data) == 2
            assert response.headers["X-Pagination-Total"] == "2"

            await check_group(data[0], metadata_vals_1)
            await check_group(data[1], metadata_vals_1)

            group_keys = [group["group_key"] for group in data]
            assert 1 in group_keys
            assert 2 in group_keys

            response = await aclient.post(
                f"/datasets/{dataset_id}/group/runs",
                json={
                    "group_by": "run_metadata",
                    "metadata_key": "bar",
                    "session_ids": [str(session_id)],
                    "offset": 0,
                    "limit": 10,
                },
            )
            assert response.status_code == 200
            data = response.json()["groups"]

            assert len(data) == 10
            assert response.headers["X-Pagination-Total"] == "11"

            for i in range(10):
                await check_group(data[i], metadata_vals_2)

            group_keys = [group["group_key"] for group in data]
            assert "Missing" in group_keys
            assert "a" in group_keys
            assert "b" in group_keys
            assert "c" in group_keys
            assert "d" in group_keys
            assert "e" in group_keys
            assert "f" in group_keys
            assert "g" in group_keys
            assert "h" in group_keys
            assert "i" in group_keys
            assert "j" not in group_keys
            assert "null" not in group_keys

            response = await aclient.post(
                f"/datasets/{dataset_id}/group/runs",
                json={
                    "group_by": "run_metadata",
                    "metadata_key": "bar",
                    "session_ids": [str(session_id)],
                    "offset": 10,
                    "limit": 10,
                },
            )
            assert response.status_code == 200
            data = response.json()["groups"]

            assert len(data) == 2
            assert response.headers["X-Pagination-Total"] == "12"

            await check_group(data[0], metadata_vals_2)
            await check_group(data[1], metadata_vals_2)

            group_keys = [group["group_key"] for group in data]
            assert "null" in group_keys
            assert "j" in group_keys

            response = await aclient.post(
                f"/datasets/{dataset_id}/group/runs",
                json={
                    "group_by": "example_metadata",
                    "metadata_key": "group",
                    "session_ids": [str(session_id)],
                    "offset": 0,
                    "limit": 10,
                },
            )
            assert response.status_code == 200
            data = response.json()["groups"]

            assert len(data) == 3
            assert response.headers["X-Pagination-Total"] == "3"

            await check_group(data[0], example_metadata)
            await check_group(data[1], example_metadata)
            await check_group(data[2], example_metadata)

            group_keys = [group["group_key"] for group in data]
            assert "a" in group_keys
            assert "b" in group_keys
            assert "c" in group_keys

            response = await aclient.post(
                f"/datasets/{dataset_id}/group/runs",
                json={
                    "group_by": "example_metadata",
                    "metadata_key": "group2",
                    "session_ids": [str(session_id)],
                    "offset": 0,
                    "limit": 10,
                },
            )
            assert response.status_code == 200
            data = response.json()["groups"]

            assert len(data) == 2
            assert response.headers["X-Pagination-Total"] == "2"

            await check_group(data[0], example_metadata_2)
            await check_group(data[1], example_metadata_2)

            group_keys = [group["group_key"] for group in data]
            assert "test" in group_keys
            assert "null" in group_keys

            response = await aclient.post(
                f"/datasets/{dataset_id}/group/runs",
                json={
                    "group_by": "run_metadata",
                    "metadata_key": "baz",
                    "session_ids": [str(session_id)],
                    "offset": 0,
                    "limit": 10,
                },
            )
            assert response.status_code == 200
            data = response.json()["groups"]

            assert len(data) == 1
            assert response.headers["X-Pagination-Total"] == "1"

            await check_group(data[0], metadata_vals_3)

            group_keys = [group["group_key"] for group in data]
            assert "Missing" in group_keys

            response = await aclient.post(
                f"/datasets/{dataset_id}/group/runs",
                json={
                    "group_by": "example_metadata",
                    "metadata_key": "group3",
                    "session_ids": [str(session_id)],
                    "offset": 0,
                    "limit": 10,
                },
            )
            assert response.status_code == 200
            data = response.json()["groups"]

            assert len(data) == 2
            assert response.headers["X-Pagination-Total"] == "2"

            await check_group(data[0], example_metadata_3)
            await check_group(data[1], example_metadata_3)

            group_keys = [group["group_key"] for group in data]
            assert "Missing" in group_keys
            assert "test" in group_keys


async def test_merge_run_infos() -> None:
    run_infos = merge_run_infos(
        [
            {"id": "'99463cce-bca8-427f-97e3-a2b1d2a10658"},
            {"reference_example_id'": "'1f63b79a-d3f4-4981-9721-8fed0635286b"},
            {"start_time'": "2025-02-18 22:16:35.145487"},
            {"id": "'85b42e42-29cb-4fea-90fc-d0f21d2ddc8f"},
            {"reference_example_id'": "'44a0b304-f870-4940-a3e8-a9e527703002"},
            {"start_time'": "2025-02-18 22:16:35.765706"},
            {"id": "'52923fe7-1087-4d76-8f83-55f086a60494"},
            {"reference_example_id'": "'645dd290-d1da-4996-8f96-694ccf63d279"},
            {"start_time'": "2025-02-18 22:16:36.616160"},
            {"id": "'d5b0a5d1-8b29-4919-ae56-66be8c627eae"},
            {"reference_example_id'": "'f806d0f5-3e4c-4e20-b803-f1a702cf5709"},
            {"start_time'": "2025-02-18 22:16:40.017577"},
        ]
    )
    assert run_infos == [
        {
            "id": "99463cce-bca8-427f-97e3-a2b1d2a10658",
            "reference_example_id": "1f63b79a-d3f4-4981-9721-8fed0635286b",
            "start_time": "2025-02-18 22:16:35.145487",
        },
        {
            "id": "85b42e42-29cb-4fea-90fc-d0f21d2ddc8f",
            "reference_example_id": "44a0b304-f870-4940-a3e8-a9e527703002",
            "start_time": "2025-02-18 22:16:35.765706",
        },
        {
            "id": "52923fe7-1087-4d76-8f83-55f086a60494",
            "reference_example_id": "645dd290-d1da-4996-8f96-694ccf63d279",
            "start_time": "2025-02-18 22:16:36.616160",
        },
        {
            "id": "d5b0a5d1-8b29-4919-ae56-66be8c627eae",
            "reference_example_id": "f806d0f5-3e4c-4e20-b803-f1a702cf5709",
            "start_time": "2025-02-18 22:16:40.017577",
        },
    ]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_feedback_corrections(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    now = datetime.now(timezone.utc).isoformat()
    later = (datetime.now(timezone.utc) + timedelta(seconds=6)).isoformat()
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client
        auth = authed_client.auth

        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "comparison view test",
            },
        )
        assert response.status_code == 200

        dataset_id = response.json()["id"]

        example_id = uuid4()
        response = await aclient.post(
            "/examples",
            json={
                "inputs": {"input": f"input_val_{example_id}"},
                "outputs": {"output": f"output_val_{example_id}"},
                "dataset_id": str(dataset_id),
                "id": str(example_id),
            },
        )
        assert response.status_code == 200

        response = await aclient.post(
            "/sessions",
            json={
                "reference_dataset_id": str(dataset_id),
                "name": random_lower_string(),
                "start_time": now,
            },
        )
        assert response.status_code == 200
        session_id = response.json()["id"]

        run_id = uuid4()
        response = await aclient.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": now,
                "end_time": later,
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in czechia as of 2023?"},
                "outputs": {"output": "110,498,692"},
                "session_id": str(session_id),
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
                "reference_example_id": str(example_id),
            },
        )

        assert response.status_code == 202
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id),
                "key": "foo",
                "score": 0,
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        rows = await db_asyncpg.fetch(
            "SELECT * FROM feedbacks WHERE run_id = $1 AND tenant_id = $2",
            run_id,
            auth.tenant_id,
        )
        # PATCH the feedback
        response = await aclient.patch(
            f"/feedback/{rows[0]['id']}",
            json={"score": 0.6},
        )
        assert response.status_code == 200

        response = await aclient.post(
            f"/datasets/{dataset_id}/runs",
            json={
                "session_ids": [str(session_id)],
            },
        )
        assert response.status_code == 200

        examples_with_runs_no_filters = response.json()

        example_1 = next(
            example
            for example in examples_with_runs_no_filters
            if example["id"] == str(example_id)
        )

        for run in example_1["runs"]:
            assert run["feedback_stats"]["foo"]["avg"] == 0.6
