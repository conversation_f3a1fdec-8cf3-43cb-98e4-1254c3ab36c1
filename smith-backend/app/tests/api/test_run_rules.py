import json
import random
import string
from contextlib import asynccontextmanager
from copy import deepcopy
from datetime import datetime, timedelta, timezone
from time import sleep
from typing import Any, AsyncGenerator, Awaitable, Callable, NamedTuple, cast
from unittest import mock
from uuid import UUID, uuid4

import asyncpg
import pytest
from aiochclient import ChClient, ChClientError
from fastapi import Response
from httpx import AsyncClient
from langchain.load import dumpd
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.prompts.structured import StructuredPrompt
from lc_config.settings import shared_settings as settings
from playground.models.playground.fake import FakeStreamingMessagesListChatModel

from app import config, schemas
from app.api.auth import AuthInfo
from app.models.datasets.schema import PlaygroundRunOverDatasetRequestSchema
from app.models.feedback.ingest import FeedbackInsert
from app.models.runs.rules import get_chain_from_evaluator, list_rules, trigger_rule
from app.models.runs.rules_apply import (
    NULL_UUID,
    RunRulesQueryCursors,
    apply_run_rule,
    cron_schedule_apply_rules,
)
from app.models.runs.utils import parse_feedback
from app.models.usage_limits import user_defined_limits
from app.tests.utils import (
    FreshTenantClient,
    chat_run_1_payload,
    create_chat_preset_dataset,
    fresh_tenant_client,
    random_lower_string,
)

pytestmark = [
    pytest.mark.skipif(
        config.settings.AUTH_TYPE != "supabase", reason="oauth not support in queue"
    ),
]


def get_evaluators(repo_full_name: str) -> list[dict[str, Any]]:
    return [
        {
            "structured": {
                "prompt": [
                    [
                        "system",
                        "You are evaluating the following value for toxicity, topic, and whether it's long.",
                    ],
                    [
                        "user",
                        "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                    ],
                ],
                "schema": {
                    "name": "eval",
                    "description": "",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "toxic": {"type": "boolean"},
                            "long": {"type": "boolean"},
                            "topic": {"type": "string"},
                        },
                        "required": ["toxic", "long", "topic"],
                    },
                },
                "model": {
                    "lc": 1,
                    "type": "constructor",
                    "id": [
                        "langchain",
                        "chat_models",
                        "fake",
                        "FakeMessagesListChatModel",
                    ],
                    "name": "FakeMessagesListChatModel",
                    "kwargs": {
                        "a_great_secret": {
                            "lc": 1,
                            "type": "secret",
                            "id": ["A_GREAT_SECRET"],
                        },
                        "responses": [
                            {
                                "lc": 1,
                                "type": "constructor",
                                "id": ["langchain", "schema", "messages", "AIMessage"],
                                "kwargs": {
                                    "content": "",
                                    "additional_kwargs": {
                                        "tool_calls": [
                                            {
                                                "function": {
                                                    "name": "eval",
                                                    "arguments": json.dumps(
                                                        {
                                                            "toxic": True,
                                                            "long": False,
                                                            "topic": "hello",
                                                        }
                                                    ),
                                                }
                                            }
                                        ]
                                    },
                                },
                            }
                        ],
                    },
                },
            }
        },
        {
            "structured": {
                "hub_ref": repo_full_name,
                "model": {
                    "lc": 1,
                    "type": "constructor",
                    "id": [
                        "langchain",
                        "chat_models",
                        "fake",
                        "FakeMessagesListChatModel",
                    ],
                    "name": "FakeMessagesListChatModel",
                    "kwargs": {
                        "a_great_secret": {
                            "lc": 1,
                            "type": "secret",
                            "id": ["A_GREAT_SECRET"],
                        },
                        "responses": [
                            {
                                "lc": 1,
                                "type": "constructor",
                                "id": ["langchain", "schema", "messages", "AIMessage"],
                                "kwargs": {
                                    "content": "",
                                    "additional_kwargs": {
                                        "tool_calls": [
                                            {
                                                "function": {
                                                    "name": "eval",
                                                    "arguments": json.dumps(
                                                        {"is_it_great": True}
                                                    ),
                                                }
                                            }
                                        ]
                                    },
                                },
                            }
                        ],
                    },
                },
            }
        },
    ]


STRUCTURED_PROMPT = StructuredPrompt.from_messages_and_schema(
    [
        (
            "system",
            "You are evaluating the following value for toxicity, topic, and whether it's long.",
        ),
        (
            "user",
            "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
        ),
    ],
    {
        "name": "eval",
        "description": "",
        "parameters": {
            "type": "object",
            "properties": {"is_it_great": {"type": "boolean"}},
            "required": ["is_it_great"],
        },
    },
)


class RulesSetup(NamedTuple):
    session_id: str
    dataset_id: str
    annotation_queue_id: str

    rule_send_to_dataset_id: str
    rule_send_to_annotation_queue_id: str
    rule_online_evals_id: str
    rule_send_to_queue_after_online_evals_id: str
    rule_from_dataset_id: str

    rule_send_to_dataset_created_at: str
    rule_send_to_annotation_queue_created_at: str
    rule_online_evals_created_at: str
    rule_send_to_queue_after_online_evals_created_at: str
    rule_from_dataset_created_at: str


async def _set_up_rules(
    client: AsyncClient,
    add_alert: bool = False,
    session_id: str | None = None,
    backfill_from: str | None = None,
) -> RulesSetup:
    repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
    response = await client.post(
        "/repos/",
        json={"repo_handle": repo_handle, "is_public": False},
    )

    assert response.status_code == 200, response.text

    repo_full_name = response.json()["repo"]["full_name"]

    response = await client.post(
        f"/commits/{repo_full_name}",
        json={"manifest": dumpd(STRUCTURED_PROMPT)},
    )

    assert response.status_code == 200, response.text

    response = await client.post(
        "/annotation-queues",
        json={"name": random_lower_string()},
    )
    assert response.status_code == 200, response.text

    annotation_queue_id = response.json()["id"]

    response = await client.post(
        "/datasets",
        json={"name": random_lower_string()},
    )
    assert response.status_code == 200, response.text

    dataset_id = response.json()["id"]

    if not session_id:
        response = await client.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text

        session_id = response.json()["id"]

    response = await client.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": 'eq(run_type, "chain")',
            "display_name": "first rule",
            "sampling_rate": 1,
            "add_to_annotation_queue_id": annotation_queue_id,
            "alerts": (
                [{"type": "pagerduty", "routing_key": "abcd", "severity": "critical"}]
                if add_alert
                else []
            ),
            "backfill_from": backfill_from,
        },
    )

    assert response.status_code == 200, response.text

    rule_send_to_annotation_queue_id = response.json()["id"]
    rule_send_to_annotation_queue_created_at = response.json()["created_at"]

    response = await client.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": 'eq(run_type, "tool")',
            "display_name": "second rule",
            "sampling_rate": 1,
            "add_to_dataset_id": dataset_id,
            "add_to_dataset_prefer_correction": True,
            "backfill_from": backfill_from,
        },
    )

    assert response.status_code == 200, response.text

    rule_send_to_dataset_id = response.json()["id"]
    rule_send_to_dataset_created_at = response.json()["created_at"]

    response = await client.post(
        "/workspaces/current/secrets",
        json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
    )

    assert response.status_code == 200, response.text

    response = await client.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "evaluators": get_evaluators(repo_full_name),
            "filter": 'and(eq(run_type, "tool"), eq(tag, "eval"))',
            "display_name": "third rule",
            "sampling_rate": 1,
            "backfill_from": backfill_from,
        },
    )

    assert response.status_code == 200, response.text

    rule_online_evals_id = response.json()["id"]
    rule_online_evals_created_at = response.json()["created_at"]

    response = await client.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "add_to_annotation_queue_id": annotation_queue_id,
            "webhooks": [
                {
                    "url": "http://testserver/webhook",
                    "headers": {
                        "Authorization": "Bearer test",
                        "TestHeader": "value 1",
                    },
                }
            ],
            "filter": 'and(eq(feedback_key, "toxic"), eq(feedback_score, "1"))',
            "display_name": "fourth rule",
            "sampling_rate": 1,
            "backfill_from": backfill_from,
        },
    )

    assert response.status_code == 200, response.text

    rule_send_to_queue_after_online_evals_id = response.json()["id"]
    rule_send_to_queue_after_online_evals_created_at = response.json()["created_at"]

    response = await client.post(
        "/runs/rules",
        json={
            "dataset_id": dataset_id,
            "add_to_annotation_queue_id": annotation_queue_id,
            "evaluators": get_evaluators(repo_full_name),
            "filter": "eq(is_root, true)",
            "display_name": "fifth rule",
            "sampling_rate": 1,
            "backfill_from": backfill_from,
        },
    )

    assert response.status_code == 200, response.text

    rule_from_dataset_id = response.json()["id"]
    rule_from_dataset_created_at = response.json()["created_at"]

    response = await client.get("/runs/rules")

    assert response.status_code == 200, response.text
    assert rule_send_to_annotation_queue_id in [r["id"] for r in response.json()]
    assert rule_send_to_dataset_id in [r["id"] for r in response.json()]
    assert rule_online_evals_id in [r["id"] for r in response.json()]
    assert rule_send_to_queue_after_online_evals_id in [
        r["id"] for r in response.json()
    ]
    assert rule_from_dataset_id in [r["id"] for r in response.json()]

    return RulesSetup(
        session_id=cast(str, session_id),
        dataset_id=dataset_id,
        annotation_queue_id=annotation_queue_id,
        rule_send_to_dataset_id=rule_send_to_dataset_id,
        rule_send_to_annotation_queue_id=rule_send_to_annotation_queue_id,
        rule_online_evals_id=rule_online_evals_id,
        rule_send_to_queue_after_online_evals_id=rule_send_to_queue_after_online_evals_id,
        rule_from_dataset_id=rule_from_dataset_id,
        rule_send_to_dataset_created_at=rule_send_to_dataset_created_at,
        rule_send_to_annotation_queue_created_at=rule_send_to_annotation_queue_created_at,
        rule_online_evals_created_at=rule_online_evals_created_at,
        rule_send_to_queue_after_online_evals_created_at=rule_send_to_queue_after_online_evals_created_at,
        rule_from_dataset_created_at=rule_from_dataset_created_at,
    )


async def test_run_rules_with_is_enabled_false_should_not_run(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    # Setup initial rule with is_enabled set to False
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/annotation-queues",
        json={"name": random_lower_string()},
    )
    assert response.status_code == 200, response.text
    annotation_queue_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": "eq(is_root, true)",
            "display_name": "disabled rule",
            "sampling_rate": 1,
            "add_to_annotation_queue_id": annotation_queue_id,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # Disable the rule
    response = await http_tenant_one.patch(
        f"/runs/rules/{rule_id}",
        json={
            "session_id": session_id,
            "filter": "eq(is_root, true)",
            "display_name": "disabled rule",
            "sampling_rate": 1,
            "add_to_annotation_queue_id": annotation_queue_id,
            "is_enabled": False,
        },
    )
    assert response.status_code == 200, response.text

    # Insert a run to see if the rule is applied
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"20230505T051324571809Z{run_id}",
                }
            ]
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    # Check if the rule was applied
    annotation_queue_runs = await db_asyncpg.fetch(
        "select * from annotation_queue_runs where run_id = $1", run_id
    )
    assert len(annotation_queue_runs) == 0

    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id = $1", run_id
    )
    assert len(applications) == 0


async def test_run_rules_with_deleted_ws_disabled_org_should_not_run(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        client = authed_client.client

        # Setup initial rule
        response = await client.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text
        session_id = response.json()["id"]

        response = await client.post(
            "/annotation-queues",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        annotation_queue_id = response.json()["id"]

        response = await client.post(
            "/runs/rules",
            json={
                "session_id": session_id,
                "filter": "eq(is_root, true)",
                "display_name": "regular aq rule",
                "sampling_rate": 1,
                "add_to_annotation_queue_id": annotation_queue_id,
            },
        )
        assert response.status_code == 200, response.text
        rule_id = response.json()["id"]

        # Insert a run to see if the rule is applied
        run_id = uuid4()
        response = await client.post(
            "/runs/batch",
            json={
                "post": [
                    {
                        "name": "AgentExecutor",
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "AgentExecutor"},
                        "inputs": {
                            "input": "How many people live in canada as of 2023?"
                        },
                        "outputs": {"output": "39,566,248"},
                        "session_id": str(session_id),
                        "parent_run_id": None,
                        "run_type": "chain",
                        "id": str(run_id),
                        "trace_id": str(run_id),
                        "dotted_order": f"20230505T051324571809Z{run_id}",
                    }
                ]
            },
        )
        assert response.status_code == 202, response.text
        await wait_until_task_queue_empty()

        # Disable the org (must be after the run is inserted)
        await db_asyncpg.execute(
            "UPDATE organizations SET disabled = true WHERE id = $1",
            auth.organization_id,
        )

        await cron_schedule_apply_rules(rule_ids=[rule_id])
        await wait_until_task_queue_empty()

        # Check if the rule was applied
        annotation_queue_runs = await db_asyncpg.fetch(
            "select * from annotation_queue_runs where run_id = $1", run_id
        )
        assert len(annotation_queue_runs) == 0

        applications = await db_asyncpg.fetch(
            "select * from run_rules_applications where run_id = $1", run_id
        )
        assert len(applications) == 0

        # Re-enable the org
        await db_asyncpg.execute(
            "UPDATE organizations SET disabled = false WHERE id = $1",
            auth.organization_id,
        )

        # Insert another run
        run_id = uuid4()
        response = await client.post(
            "/runs/batch",
            json={
                "post": [
                    {
                        "name": "AgentExecutor",
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "AgentExecutor"},
                        "inputs": {
                            "input": "How many people live in canada as of 2023?"
                        },
                        "outputs": {"output": "39,566,248"},
                        "session_id": str(session_id),
                        "parent_run_id": None,
                        "run_type": "chain",
                        "id": str(run_id),
                        "trace_id": str(run_id),
                        "dotted_order": f"20230505T051324571809Z{run_id}",
                    }
                ]
            },
        )
        assert response.status_code == 202, response.text
        await wait_until_task_queue_empty()

        # Soft-delete the ws
        await db_asyncpg.execute(
            "UPDATE tenants SET is_deleted = true WHERE id = $1", auth.tenant_id
        )

        await cron_schedule_apply_rules(rule_ids=[rule_id])
        await wait_until_task_queue_empty()

        # Check if the rule was applied
        annotation_queue_runs = await db_asyncpg.fetch(
            "select * from annotation_queue_runs where run_id = $1", run_id
        )
        assert len(annotation_queue_runs) == 0

        applications = await db_asyncpg.fetch(
            "select * from run_rules_applications where run_id = $1", run_id
        )
        assert len(applications) == 0


async def test_run_rules_with_extend_only(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: ChClient,
):
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": "eq(is_root, true)",
            "display_name": "extend only rule",
            "sampling_rate": 1,
            "extend_only": True,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # Insert a run to see if the rule is applied
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"20230505T051324571809Z{run_id}",
                }
            ]
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id = $1", run_id
    )
    assert len(applications) == 1

    if settings.FF_TRACE_TIERS_ENABLED:
        runs = await ch_client.fetch(
            "select * from runs FINAL where id = {run_id}",
            params={"run_id": run_id},
        )
        run = runs[0]
        assert run["trace_tier"] == schemas.TraceTier.longlived
        assert run["ttl_seconds"] == schemas.TraceTier.longlived.ttl_seconds


async def test_run_rules_filtering_and_backfill(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: ChClient,
):
    """
    Test that rules are working with filter + backfill and pagination.
    """
    backfill_from = (datetime.now(timezone.utc) - timedelta(minutes=1)).isoformat()
    filter_expression = 'and(eq(is_root, true), and(search("canada"), and(eq(metadata_key, "langgraph_step"), eq(metadata_value, "3"))), eq(tag, "test_tag"))'

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    async def insert_runs():
        # Insert a run to see if the rule is applied
        run_ids = []
        for _ in range(4):
            run_id = uuid4()
            run_ids.append(run_id)
            response = await http_tenant_one.post(
                "/runs/batch",
                json={
                    "post": [
                        {
                            "name": "AgentExecutor",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "error": "an error",
                            "execution_order": 1,
                            "serialized": {"name": "AgentExecutor"},
                            "inputs": {
                                "input": "How many people live in canada as of 2023?"
                            },
                            "outputs": {"output": "39,566,248"},
                            "session_id": str(session_id),
                            "parent_run_id": None,
                            "run_type": "chain",
                            "id": str(run_id),
                            "trace_id": str(run_id),
                            "dotted_order": f"20230505T051324571809Z{run_id}",
                            "extra": {"metadata": {"langgraph_step": "3"}},
                            "tags": ["test_tag"],
                        }
                    ]
                },
            )
            assert response.status_code in (
                200,
                202,
            ), response.text
            response = await http_tenant_one.post(
                "/feedback",
                json={
                    "run_id": str(run_id),
                    "key": "yoooo",
                    "correction": "39,568,000",
                },
            )
            assert response.status_code in (
                200,
                202,
            ), response.text

        await wait_until_task_queue_empty()
        return run_ids

    if backfill_from:
        run_ids = await insert_runs()

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": filter_expression,
            "display_name": "extend only rule",
            "sampling_rate": 1,
            "extend_only": True,
            "backfill_from": backfill_from,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    if not backfill_from:
        run_ids = await insert_runs()

    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id = ANY($1::uuid[])", run_ids
    )
    assert len(applications) == 4


async def test_run_rules_catchup_cursor(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: ChClient,
):
    """
    Test that run rules uses a cursor to catch up on runs.
    """
    backfill_from = None
    filter_expression = "eq(is_root, true)"

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    async def insert_runs():
        # Insert a run to see if the rule is applied
        run_ids = []
        for _ in range(10):
            run_id = uuid4()
            run_ids.append(run_id)
            response = await http_tenant_one.post(
                "/runs/batch",
                json={
                    "post": [
                        {
                            "name": "AgentExecutor",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "error": "an error",
                            "execution_order": 1,
                            "serialized": {"name": "AgentExecutor"},
                            "inputs": {
                                "input": "How many people live in canada as of 2023?"
                            },
                            "outputs": {"output": "39,566,248"},
                            "session_id": str(session_id),
                            "parent_run_id": None,
                            "run_type": "chain",
                            "id": str(run_id),
                            "trace_id": str(run_id),
                            "dotted_order": f"20230505T051324571809Z{run_id}",
                            "extra": {"metadata": {"langgraph_step": "3"}},
                            "tags": ["test_tag"],
                        }
                    ]
                },
            )
            assert response.status_code in (
                200,
                202,
            ), response.text
            response = await http_tenant_one.post(
                "/feedback",
                json={
                    "run_id": str(run_id),
                    "key": "yoooo",
                    "correction": "39,568,000",
                },
            )
            assert response.status_code in (
                200,
                202,
            ), response.text

        await wait_until_task_queue_empty()
        return run_ids

    run_ids = await insert_runs()

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": filter_expression,
            "display_name": "extend only rule",
            "sampling_rate": 1,
            "extend_only": True,
            "backfill_from": backfill_from,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # set an empty rule marker back in time to test that the rule catches up
    await db_asyncpg.fetch(
        """
        insert into run_rules_applications (rule_id, run_id, start_time, end_time, query_cursors)
        select * from unnest($1::uuid[], $2::uuid[], $3::timestamptz[], $4::timestamptz[], $5::jsonb[])
        on conflict do nothing
        returning run_id::text""",
        [rule_id],
        [NULL_UUID],
        [datetime.now(timezone.utc) - timedelta(days=3)],
        [
            datetime.now(timezone.utc)
            - timedelta(days=2, hours=23, minutes=59, seconds=59)
        ],
        [RunRulesQueryCursors(next_feedback_cursor=None, next_run_cursor=None)],
    )

    # limit is 2 so apply needed number of run rules
    for _ in range(len(run_ids) // 2 + 1):
        await cron_schedule_apply_rules(rule_ids=[rule_id])
        await wait_until_task_queue_empty()

    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id = ANY($1::uuid[])", run_ids
    )
    assert len(applications) == 10


async def test_alerts_crud(
    http_tenant_one: AsyncClient,
):
    setup = await _set_up_rules(http_tenant_one, add_alert=True)
    response = await http_tenant_one.get(
        "/runs/rules",
    )
    assert response.status_code == 200
    rule = [
        rule
        for rule in response.json()
        if rule["id"] == setup.rule_send_to_annotation_queue_id
    ][0]
    assert rule["alerts"]
    assert rule["alerts"][0]["type"] == "pagerduty"
    assert rule["alerts"][0]["routing_key"] == "abcd"
    assert rule["alerts"][0]["severity"] == "critical"

    rule["alerts"] = [
        {"type": "pagerduty", "routing_key": "efg", "severity": "warning"}
    ]
    response = await http_tenant_one.patch(
        f"/runs/rules/{setup.rule_send_to_annotation_queue_id}",
        json=rule,
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        "/runs/rules",
    )
    assert response.status_code == 200
    rule = [
        rule
        for rule in response.json()
        if rule["id"] == setup.rule_send_to_annotation_queue_id
    ][0]
    assert rule["alerts"]
    assert rule["alerts"][0]["type"] == "pagerduty"
    assert rule["alerts"][0]["routing_key"] == "efg"
    assert rule["alerts"][0]["severity"] == "warning"

    assert response.status_code == 200, response.text


async def test_webhooks_crud(
    http_tenant_one: AsyncClient,
):
    setup = await _set_up_rules(http_tenant_one, add_alert=True)
    response = await http_tenant_one.get(
        "/runs/rules",
    )
    assert response.status_code == 200
    rule = [
        rule
        for rule in response.json()
        if rule["id"] == setup.rule_send_to_queue_after_online_evals_id
    ][0]
    assert rule["webhooks"]
    assert rule["webhooks"][0]["url"] == "http://testserver/webhook"
    assert rule["webhooks"][0]["headers"] == {
        "Authorization": "Bearer test",
        "TestHeader": "value 1",
    }

    rule["webhooks"] = [
        {
            "url": "http://testanotherserver/webhook",
            "headers": {"Authorization": "Bearer testing", "TestHeader": "value 2"},
        }
    ]
    response = await http_tenant_one.patch(
        f"/runs/rules/{setup.rule_send_to_queue_after_online_evals_id}",
        json=rule,
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        "/runs/rules",
    )
    assert response.status_code == 200
    rule = [
        rule
        for rule in response.json()
        if rule["id"] == setup.rule_send_to_queue_after_online_evals_id
    ][0]
    assert rule["webhooks"]
    assert rule["webhooks"][0]["url"] == "http://testanotherserver/webhook"
    assert rule["webhooks"][0]["headers"] == {
        "Authorization": "Bearer testing",
        "TestHeader": "value 2",
    }


async def test_broken_online_evaluator(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a broken online evaluator is handled correctly."""

    # Setup initial rule with a broken evaluator
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    broken_evaluator = {
        "structured": {
            "prompt": [
                [
                    "system",
                    "You are a broken evaluator.",
                ],
                [
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ],
            ],
            "schema": {
                "type": "object",
                "name": "eval",
                "description": "",
                "properties": {
                    "broken": {"type": "boolean"},
                },
            },
            "model": {
                "lc": 1,
                "type": "constructor",
                "id": [
                    "langchain",
                    "chat_models",
                    "fake",
                    "FakeMessagesListChatModel",
                ],
                "kwargs": {
                    "a_great_secret": {
                        "lc": 1,
                        "type": "secret",
                        "id": ["A_GREAT_SECRET"],
                    },
                    "responses": [],
                    "error": "Error",
                },
            },
        }
    }

    response = await http_tenant_one.post(
        "/workspaces/current/secrets",
        json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
    )

    assert response.status_code == 200, response.text

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "evaluators": [broken_evaluator],
            "filter": 'eq(run_type, "tool")',
            "display_name": "broken_evaluator_rule",
            "sampling_rate": 1,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # Send runs that match the rule
    run_one = uuid4()
    run_two = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "How many people live in Canada?"},
                    "outputs": {"output": "38 million"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_one),
                    "trace_id": str(run_one),
                    "dotted_order": f"20230505T051324571809Z{run_one}",
                },
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "What's the capital of France?"},
                    "outputs": {"output": "Paris"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_two),
                    "trace_id": str(run_two),
                    "dotted_order": f"20230505T051324571809Z{run_two}",
                },
            ]
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    # Apply the rule
    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    # Confirm the rule ran by checking the run logs
    response = await http_tenant_one.get(f"/runs/rules/{rule_id}/logs")
    assert response.status_code == 200, response.text
    rule_logs = response.json()
    assert len(rule_logs) > 0
    assert rule_logs[0]["evaluators"]["outcome"] == "error"

    # Confirm that feedback was added to the runs with the proper error form
    for run_id in [run_one, run_two]:
        feedbacks = await ch_client.fetch(
            "SELECT * FROM feedbacks WHERE run_id = {run}",
            params={"run": run_id},
        )
        assert len(feedbacks) == 1, (
            f"Expected 1 feedback for run {run_id}, got {len(feedbacks)}"
        )
        feedback = feedbacks[0]
        assert feedback["feedback_source"] is not None
        source = json.loads(feedback["feedback_source"])
        assert source["type"] == "auto_eval"
        assert source["metadata"]["rule_id"] == rule_id
        assert feedback["key"] == "broken"
        assert feedback["comment"] != ""
        assert feedback["extra"] == '{"error":true}'


async def test_broken_online_code_evaluator(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
    use_api_key: bool,
) -> None:
    """Test that a broken online evaluator is handled correctly."""

    mock_broken_execute = [
        {
            "status": "failed",
            "stacktrace": "Traceback (most recent call last):\n  File \"<exec>\", line 16, in perform_eval\nKeyError: 'sajkdfhkjsadf'\n",
        }
    ]

    # Patch the specific function that's being called
    with mock.patch(
        "app.models.runs.rule_application.custom_code.execute_custom_code",
        return_value=mock_broken_execute,
    ):
        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
            # Setup initial rule with a broken evaluator
            response = await authed_client.client.post(
                "/sessions",
                json={"name": random_lower_string(), "trace_tier": "shortlived"},
            )
            assert response.status_code == 200, response.text
            session_id = response.json()["id"]

            broken_code_evaluator = """def perform_eval(run):
        return { "broken": run['sajkdfhkjsadf'] }"""  # We're mocking the response so it will always fail. But we WILL parse the feedback key name from this.

            assert response.status_code == 200, response.text

            response = await authed_client.client.post(
                "/runs/rules",
                json={
                    "session_id": session_id,
                    "code_evaluators": [{"code": broken_code_evaluator}],
                    "filter": 'eq(run_type, "tool")',
                    "display_name": "broken_evaluator_rule",
                    "sampling_rate": 1,
                },
            )
            assert response.status_code == 200, response.text
            rule_id = response.json()["id"]

            # Send runs that match the rule
            run_one = uuid4()
            run_two = uuid4()
            response = await authed_client.client.post(
                "/runs/batch",
                json={
                    "post": [
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": "How many people live in Canada?"},
                            "outputs": {"output": "38 million"},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_one),
                            "trace_id": str(run_one),
                            "dotted_order": f"20230505T051324571809Z{run_one}",
                        },
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": "What's the capital of France?"},
                            "outputs": {"output": "Paris"},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_two),
                            "trace_id": str(run_two),
                            "dotted_order": f"20230505T051324571809Z{run_two}",
                        },
                    ]
                },
            )
            assert response.status_code == 202, response.text
            await wait_until_task_queue_empty()

            # Apply the rule
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Confirm the rule ran by checking the run logs
            response = await authed_client.client.get(f"/runs/rules/{rule_id}/logs")
            assert response.status_code == 200, response.text
            rule_logs = response.json()
            assert len(rule_logs) > 0
            assert rule_logs[0]["evaluators"]["outcome"] == "error"

            # Confirm that feedback was added to the runs with the proper error form
            for run_id in [run_one, run_two]:
                feedbacks = await ch_client.fetch(
                    "SELECT * FROM feedbacks WHERE run_id = {run}",
                    params={"run": run_id},
                )
                assert len(feedbacks) == 1, (
                    f"Expected 1 feedback for run {run_id}, got {len(feedbacks)}"
                )
                feedback = feedbacks[0]
                assert feedback["feedback_source"] is not None
                source = json.loads(feedback["feedback_source"])
                assert source["type"] == "auto_eval"
                assert source["metadata"]["rule_id"] == rule_id
                assert feedback["key"] == "broken"
                assert feedback["comment"]
                assert feedback["extra"] == '{"error":true}'


async def test_not_broken_online_evaluator(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a broken online evaluator is handled correctly."""

    # Setup initial rule with a broken evaluator
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    non_broken_evaluator = {
        "structured": {
            "prompt": [
                [
                    "system",
                    "You are a non-broken evaluator.",
                ],
                [
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ],
            ],
            "schema": {
                "type": "object",
                "name": "eval",
                "description": "",
            },
            "model": {
                "lc": 1,
                "type": "constructor",
                "id": [
                    "langchain",
                    "chat_models",
                    "fake",
                    "FakeMessagesListChatModel",
                ],
                "kwargs": {
                    "a_great_secret": {
                        "lc": 1,
                        "type": "secret",
                        "id": ["A_GREAT_SECRET"],
                    },
                    "responses": [
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain", "schema", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "",
                                "additional_kwargs": {
                                    "tool_calls": [
                                        {
                                            "function": {
                                                "name": "eval",
                                                "arguments": json.dumps(
                                                    {
                                                        "toxic": True,
                                                    }
                                                ),
                                            }
                                        }
                                    ]
                                },
                            },
                        }
                    ],
                },
            },
        }
    }

    response = await http_tenant_one.post(
        "/workspaces/current/secrets",
        json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
    )

    assert response.status_code == 200, response.text

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "evaluators": [non_broken_evaluator],
            "filter": 'eq(run_type, "tool")',
            "display_name": "not_broken_evaluator_rule",
            "sampling_rate": 1,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # Send runs that match the rule
    run_one = uuid4()
    run_two = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "How many people live in Canada?"},
                    "outputs": {"output": "38 million"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_one),
                    "trace_id": str(run_one),
                    "dotted_order": f"20230505T051324571809Z{run_one}",
                },
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "What's the capital of France?"},
                    "outputs": {"output": "Paris"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_two),
                    "trace_id": str(run_two),
                    "dotted_order": f"20230505T051324571809Z{run_two}",
                },
            ]
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    # Apply the rule
    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    # Confirm the rule ran by checking the run logs
    response = await http_tenant_one.get(f"/runs/rules/{rule_id}/logs")
    assert response.status_code == 200, response.text
    rule_logs = response.json()
    assert len(rule_logs) > 0
    assert rule_logs[0]["evaluators"]["outcome"] == "success"

    # Confirm that feedback was added to the runs with the proper error form
    for run_id in [run_one, run_two]:
        feedbacks = await ch_client.fetch(
            "SELECT * FROM feedbacks WHERE run_id = {run}",
            params={"run": run_id},
        )
        assert len(feedbacks) == 1, (
            f"Expected 1 feedback for run {run_id}, got {len(feedbacks)}"
        )
        feedback = feedbacks[0]
        assert feedback["feedback_source"] is not None
        source = json.loads(feedback["feedback_source"])
        assert source["type"] == "auto_eval"
        assert source["metadata"]["rule_id"] == rule_id
        assert feedback["key"] == "toxic"
        assert feedback["comment"] == ""
        assert feedback["score"] == 1
        assert feedback["extra"] == '{"error":false}'


async def test_broken_online_evaluator_missing_prompt(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a broken online evaluator is handled correctly."""
    response = await http_tenant_one.post(
        "/repos/",
        json={"repo_handle": "broken_eval", "is_public": False},
    )
    assert response.status_code == 200, response.text

    repo_full_name = response.json()["repo"]["full_name"]

    response = await http_tenant_one.post(
        f"/commits/{repo_full_name}",
        json={"manifest": dumpd(STRUCTURED_PROMPT)},
    )

    assert response.status_code == 200, response.text

    # Setup initial rule with a broken evaluator
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    broken_evaluator = {
        "structured": {
            "hub_ref": repo_full_name,
            "model": {
                "lc": 1,
                "type": "constructor",
                "id": [
                    "langchain",
                    "chat_models",
                    "fake",
                    "FakeMessagesListChatModel",
                ],
                "kwargs": {
                    "a_great_secret": {
                        "lc": 1,
                        "type": "secret",
                        "id": ["A_GREAT_SECRET"],
                    },
                    "responses": [],
                    "error": "Error",
                },
            },
        }
    }

    response = await http_tenant_one.post(
        "/workspaces/current/secrets",
        json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
    )

    assert response.status_code == 200, response.text

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "evaluators": [broken_evaluator],
            "filter": 'eq(run_type, "tool")',
            "display_name": "broken_evaluator_rule",
            "sampling_rate": 1,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # Delete the repo
    response = await http_tenant_one.delete(
        f"/repos/{repo_full_name}",
    )
    assert response.status_code == 200, response.text

    # Send runs that match the rule
    run_one = uuid4()
    run_two = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "How many people live in Canada?"},
                    "outputs": {"output": "38 million"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_one),
                    "trace_id": str(run_one),
                    "dotted_order": f"20230505T051324571809Z{run_one}",
                },
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "What's the capital of France?"},
                    "outputs": {"output": "Paris"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_two),
                    "trace_id": str(run_two),
                    "dotted_order": f"20230505T051324571809Z{run_two}",
                },
            ]
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    # Apply the rule
    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    # Confirm the rule ran by checking the run logs
    response = await http_tenant_one.get(f"/runs/rules/{rule_id}/logs")
    assert response.status_code == 200, response.text
    rule_logs = response.json()
    assert len(rule_logs) > 0
    assert rule_logs[0]["evaluators"]["outcome"] == "error"


async def test_broken_online_evaluator_unmapped(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a broken online evaluator is handled correctly."""
    response = await http_tenant_one.post(
        "/repos/",
        json={"repo_handle": "broken_eval", "is_public": False},
    )
    assert response.status_code == 200, response.text

    repo_full_name = response.json()["repo"]["full_name"]

    response = await http_tenant_one.post(
        f"/commits/{repo_full_name}",
        json={"manifest": dumpd(STRUCTURED_PROMPT)},
    )

    assert response.status_code == 200, response.text

    # Setup initial rule with a broken evaluator
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    broken_evaluator = {
        "structured": {
            "hub_ref": repo_full_name,
            "model": {
                "lc": 1,
                "type": "constructor",
                "id": [
                    "langchain",
                    "chat_models",
                    "fake",
                    "FakeMessagesListChatModel",
                ],
                "kwargs": {
                    "a_great_secret": {
                        "lc": 1,
                        "type": "secret",
                        "id": ["A_GREAT_SECRET"],
                    },
                    "responses": [],
                    "error": "Error",
                },
            },
        }
    }

    response = await http_tenant_one.post(
        "/workspaces/current/secrets",
        json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
    )

    assert response.status_code == 200, response.text

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "evaluators": [broken_evaluator],
            "filter": 'eq(run_type, "tool")',
            "display_name": "broken_evaluator_rule",
            "sampling_rate": 1,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # Send runs that match the rule
    run_one = uuid4()
    run_two = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "How many people live in Canada?"},
                    "outputs": {"output": "38 million"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_one),
                    "trace_id": str(run_one),
                    "dotted_order": f"20230505T051324571809Z{run_one}",
                },
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "What's the capital of France?"},
                    "outputs": {"output": "Paris"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_two),
                    "trace_id": str(run_two),
                    "dotted_order": f"20230505T051324571809Z{run_two}",
                },
            ]
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    # Apply the rule
    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    # Confirm the rule ran by checking the run logs
    response = await http_tenant_one.get(f"/runs/rules/{rule_id}/logs")
    assert response.status_code == 200, response.text
    rule_logs = response.json()
    assert len(rule_logs) > 0
    assert rule_logs[0]["evaluators"]["outcome"] == "error"

    # Confirm that feedback was added to the runs with the proper error form
    for run_id in [run_one, run_two]:
        feedbacks = await ch_client.fetch(
            "SELECT * FROM feedbacks WHERE run_id = {run}",
            params={"run": run_id},
        )
        assert len(feedbacks) == 1, (
            f"Expected 1 feedback for run {run_id}, got {len(feedbacks)}"
        )
        feedback = feedbacks[0]
        assert feedback["feedback_source"] is not None
        source = json.loads(feedback["feedback_source"])
        assert source["type"] == "auto_eval"
        assert source["metadata"]["rule_id"] == rule_id
        assert feedback["key"] == "is_it_great"
        assert feedback["comment"]
        assert feedback["extra"] == '{"error":true}'


async def test_add_to_dataset_and_annotation_queue(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a run can be created."""
    setup = await _set_up_rules(http_tenant_one)

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    send_to_ds_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1",
        setup.rule_send_to_dataset_id,
    )
    assert len(send_to_ds_applications) == 1
    assert send_to_ds_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_ds_applications[0]["start_time"].isoformat()
        == setup.rule_send_to_dataset_created_at
    )

    send_to_aq_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1",
        setup.rule_send_to_annotation_queue_id,
    )
    assert len(send_to_aq_applications) == 1
    assert send_to_aq_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_aq_applications[0]["start_time"].isoformat()
        == setup.rule_send_to_annotation_queue_created_at
    )

    online_eval_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1",
        setup.rule_online_evals_id,
    )

    assert len(online_eval_applications) == 1
    assert online_eval_applications[0]["run_id"] == UUID(int=0)
    assert (
        online_eval_applications[0]["start_time"].isoformat()
        == setup.rule_online_evals_created_at
    )

    run_one = uuid4()
    run_two = uuid4()
    run_two_w_feedback = uuid4()
    await http_tenant_one.post(
        "/runs/test", json={}, headers={"X-Run-Id": str(run_one)}
    )
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "metadata": {"key": 1}},
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": setup.session_id,
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_one),
                    "trace_id": str(run_one),
                    "dotted_order": f"20230505T051324571809Z{run_one}",
                },
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "metadata": {"key": 1}},
                    "tags": ["eval"],
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "How many people live in mexico as of 2025?"},
                    "outputs": {"output": "39,567,000"},
                    "session_id": setup.session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_two),
                    "trace_id": str(run_two),
                    "dotted_order": f"20230505T051324571809Z{run_two}",
                },
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "metadata": {"key": 1}},
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "How many people live in mexico as of 2025?"},
                    "outputs": {"output": "39,567,000"},
                    "session_id": setup.session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_two_w_feedback),
                    "trace_id": str(run_two_w_feedback),
                    "dotted_order": f"20230505T051324571809Z{run_two_w_feedback}",
                },
            ]
        },
    )

    assert response.status_code == 202, response.text

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_two_w_feedback),
            "key": "yoooo",
            "correction": "39,568,000",
        },
    )

    assert response.status_code in (200, 202), response.text

    await wait_until_task_queue_empty()

    runs = await ch_client.fetch(
        "select * from runs where id in {runs}", params={"runs": [run_one, run_two]}
    )
    assert len(runs) == 2

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    send_to_ds_applications = await db_asyncpg.fetch(
        """select * from run_rules_applications where rule_id = $1
        order by start_time, run_id""",
        setup.rule_send_to_dataset_id,
    )
    assert len(send_to_ds_applications) == 3
    assert send_to_ds_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_ds_applications[0]["start_time"].isoformat()
        == setup.rule_send_to_dataset_created_at
    )
    assert send_to_ds_applications[1]["run_id"] == UUID(int=0)
    assert (
        send_to_ds_applications[1]["start_time"].isoformat()
        > setup.rule_send_to_dataset_created_at
    )
    assert send_to_ds_applications[1]["add_to_dataset"]["outcome"] == "success"
    assert send_to_ds_applications[2]["run_id"] == run_two_w_feedback
    assert (
        send_to_ds_applications[2]["start_time"].isoformat()
        > setup.rule_send_to_dataset_created_at
    )
    assert send_to_ds_applications[2]["add_to_dataset"]["outcome"] == "success"
    assert (
        send_to_ds_applications[2]["add_to_dataset"]["payload"]["example_id"]
        is not None
    )

    examples_inserted = await db_asyncpg.fetch(
        "select * from examples_log where source_run_id = $1", run_two_w_feedback
    )
    assert len(examples_inserted) == 1
    assert str(examples_inserted[0]["dataset_id"]) == setup.dataset_id
    assert examples_inserted[0]["inputs"] == {
        "input": "How many people live in mexico as of 2025?"
    }
    assert examples_inserted[0]["outputs"] == {"output": "39,568,000"}
    assert examples_inserted[0]["metadata"] == {
        "dataset_split": ["base"],
        "key": 1,
        "ls_run_depth": 0,
    }
    example_id = examples_inserted[0]["id"]

    send_to_aq_applications = await db_asyncpg.fetch(
        """select * from run_rules_applications where rule_id = $1
        order by start_time, run_id""",
        setup.rule_send_to_annotation_queue_id,
    )

    assert len(send_to_aq_applications) == 3
    assert send_to_aq_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_aq_applications[0]["start_time"].isoformat()
        == setup.rule_send_to_annotation_queue_created_at
    )
    assert send_to_aq_applications[1]["run_id"] == UUID(int=0)
    assert (
        send_to_aq_applications[1]["start_time"].isoformat()
        > setup.rule_send_to_annotation_queue_created_at
    )
    assert send_to_aq_applications[1]["add_to_annotation_queue"]["outcome"] == "success"
    assert send_to_aq_applications[2]["run_id"] == run_one
    assert (
        send_to_aq_applications[2]["start_time"].isoformat()
        > setup.rule_send_to_annotation_queue_created_at
    )
    assert send_to_aq_applications[2]["add_to_annotation_queue"]["outcome"] == "success"

    annotation_queue_runs = await db_asyncpg.fetch(
        "select * from annotation_queue_runs where run_id = $1", run_one
    )
    assert len(annotation_queue_runs) == 1
    assert str(annotation_queue_runs[0]["queue_id"]) == setup.annotation_queue_id

    online_eval_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1 order by start_time, run_id",
        setup.rule_online_evals_id,
    )

    assert len(online_eval_applications) == 3
    assert online_eval_applications[0]["run_id"] == UUID(int=0)

    assert online_eval_applications[1]["run_id"] == UUID(int=0)
    assert online_eval_applications[1]["evaluators"]["outcome"] == "success"

    assert online_eval_applications[2]["run_id"] == run_two
    assert online_eval_applications[2]["evaluators"]["outcome"] == "success"

    feedbacks = await ch_client.fetch(
        "select * from feedbacks where run_id = {run}", params={"run": run_two}
    )

    assert len(feedbacks) == 4, [dict(feedback) for feedback in feedbacks]
    assert set(
        online_eval_applications[2]["evaluators"]["payload"]["feedback_ids"]
    ) == set([str(feedback["id"]) for feedback in feedbacks])

    evaluator_run_ids: set[str] = set()
    for feedback in feedbacks:
        assert feedback["run_id"] == run_two
        source = json.loads(feedback["feedback_source"])
        assert source["type"] == "auto_eval"
        assert source["metadata"]["rule_id"] == setup.rule_online_evals_id
        evaluator_run_ids.add(source["metadata"]["__run"]["run_id"])

    assert len(evaluator_run_ids) == 2

    evaluator_runs = await ch_client.fetch(
        "select * from runs where id in {runs}",
        params={"runs": list(evaluator_run_ids)},
    )

    assert len(evaluator_runs) == len(evaluator_run_ids)

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    send_to_queue_after_online_evals_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1 order by start_time, run_id",
        setup.rule_send_to_queue_after_online_evals_id,
    )

    assert len(send_to_queue_after_online_evals_applications) == 4
    assert send_to_queue_after_online_evals_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_queue_after_online_evals_applications[0]["start_time"].isoformat()
        == setup.rule_send_to_queue_after_online_evals_created_at
    )
    assert send_to_queue_after_online_evals_applications[1]["run_id"] == UUID(int=0)
    assert (
        send_to_queue_after_online_evals_applications[1]["start_time"].isoformat()
        > setup.rule_send_to_queue_after_online_evals_created_at
    )
    assert send_to_queue_after_online_evals_applications[2]["run_id"] == UUID(int=0)
    assert (
        send_to_queue_after_online_evals_applications[2]["start_time"].isoformat()
        > setup.rule_send_to_queue_after_online_evals_created_at
    )
    assert send_to_queue_after_online_evals_applications[2]["webhooks"] == {
        "outcome": "success",
        "payload": None,
    }
    assert send_to_queue_after_online_evals_applications[3]["run_id"] == run_two
    assert send_to_queue_after_online_evals_applications[3][
        "add_to_annotation_queue"
    ] == {"outcome": "success", "payload": None}
    assert (
        send_to_queue_after_online_evals_applications[3]["start_time"].isoformat()
        > setup.rule_send_to_queue_after_online_evals_created_at
    )

    annotation_queue_runs = await db_asyncpg.fetch(
        "select * from annotation_queue_runs where run_id = $1", run_two
    )

    assert len(annotation_queue_runs) == 1
    assert str(annotation_queue_runs[0]["queue_id"]) == setup.annotation_queue_id

    run_three = uuid4()
    test_session_id = await db_asyncpg.fetchval(
        """insert into tracer_session (id, tenant_id, name, reference_dataset_id, start_time)
        values (gen_random_uuid(), $1, $2, $3, now())
        returning id""",
        auth_tenant_one.tenant_id,
        random_lower_string(),
        setup.dataset_id,
    )
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "metadata": {"key": 1}},
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(test_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_three),
                    "trace_id": str(run_three),
                    "dotted_order": f"20230505T051324571809Z{run_three}",
                    "reference_example_id": str(example_id),
                },
            ]
        },
    )

    assert response.status_code == 202, response.text

    await wait_until_task_queue_empty()

    runs = await ch_client.fetch(
        "select * from runs where id in {runs}", params={"runs": [run_three]}
    )
    assert len(runs) == 1
    assert runs[0]["session_id"] == test_session_id
    assert runs[0]["reference_example_id"] == example_id
    assert runs[0]["reference_dataset_id"] == UUID(setup.dataset_id)

    pre_rule_run_time = datetime.now(timezone.utc)

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    response = await http_tenant_one.get(
        f"/runs/rules/{setup.rule_from_dataset_id}/last_applied"
    )
    assert response.status_code == 200, response.text
    last_applied = response.json()
    assert last_applied["rule_id"] == setup.rule_from_dataset_id
    assert last_applied["application_time"] > pre_rule_run_time.isoformat()

    from_ds_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1",
        setup.rule_from_dataset_id,
    )
    assert len(from_ds_applications) == 5
    applications_w_runs = [
        app for app in from_ds_applications if app["run_id"] != UUID(int=0)
    ]
    assert len(applications_w_runs) == 1
    assert applications_w_runs[0]["run_id"] == run_three

    annotation_queue_runs = await db_asyncpg.fetch(
        "select * from annotation_queue_runs where run_id = $1", run_three
    )

    assert len(annotation_queue_runs) == 1
    assert str(annotation_queue_runs[0]["queue_id"]) == setup.annotation_queue_id

    await wait_until_task_queue_empty()

    # assert that trace TTL has been upgraded for these
    if settings.FF_TRACE_TIERS_ENABLED:
        all_runs = await ch_client.fetch(
            "select * from runs FINAL where id in {runs}",
            params={"runs": [run_one, run_two, run_three, run_two_w_feedback]},
        )
        for run in all_runs:
            assert run["trace_tier"] == schemas.TraceTier.longlived
            assert run["ttl_seconds"] == schemas.TraceTier.longlived.ttl_seconds


@pytest.mark.skip(reason="heavy on ci")
async def test_add_to_dataset_large_example(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a run can be created."""
    setup = await _set_up_rules(http_tenant_one)

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    run_one = uuid4()

    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "metadata": {"key": 1}},
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"data": "x" * (270 * 1024 * 1024)},
                    "outputs": {"output": "39,567,000"},
                    "session_id": setup.session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_one),
                    "trace_id": str(run_one),
                    "dotted_order": f"20230505T051324571809Z{run_one}",
                },
            ]
        },
    )

    assert response.status_code == 202, response.text

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_one),
            "key": "yoooo",
            "correction": "39,568,000",
        },
    )

    assert response.status_code in (200, 202), response.text

    await wait_until_task_queue_empty()

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    send_to_ds_applications = await db_asyncpg.fetch(
        """select * from run_rules_applications where rule_id = $1
        order by start_time, run_id""",
        setup.rule_send_to_dataset_id,
    )

    assert len(send_to_ds_applications) == 3

    assert send_to_ds_applications[1]["run_id"] == UUID(int=0)
    assert (
        send_to_ds_applications[1]["start_time"].isoformat()
        > setup.rule_send_to_dataset_created_at
    )
    assert send_to_ds_applications[1]["add_to_dataset"]["outcome"] == "error"


@pytest.mark.parametrize("test_row_limit_reached", [True, False])
async def test_post_backfill_from_date(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    test_row_limit_reached: bool,
) -> None:
    """Test posting backfill_from_date parameter."""

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    if test_row_limit_reached:
        for _ in range(2):
            run_id = uuid4()
            response = await http_tenant_one.post(
                "/runs/batch",
                json={
                    "post": [
                        {
                            "name": "AgentExecutor",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar", "metadata": {"key": 1}},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "AgentExecutor"},
                            "inputs": {
                                "input": "How many people live in canada as of 2023?"
                            },
                            "outputs": {"output": "39,566,248"},
                            "session_id": str(session_id),
                            "parent_run_id": None,
                            "run_type": "chain",
                            "id": str(run_id),
                            "trace_id": str(run_id),
                            "dotted_order": f"20230505T051324571809Z{run_id}",
                        },
                    ]
                },
            )
            assert response.status_code == 202, response.text
            await wait_until_task_queue_empty()

    backfill_from_date = (datetime.now(timezone.utc) - timedelta(days=2)).isoformat()

    with mock.patch(
        "app.models.runs.rules_apply.shared_settings.RUN_RULES_BACKFILL_PROJECT_LIMIT",
        new=1,
    ):
        response = await http_tenant_one.post(
            "/annotation-queues",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        annotation_queue_id = response.json()["id"]

        response = await http_tenant_one.post(
            "/runs/rules",
            json={
                "session_id": session_id,
                "filter": 'eq(run_type, "chain")',
                "display_name": "first rule",
                "sampling_rate": 1,
                "add_to_annotation_queue_id": annotation_queue_id,
                "backfill_from": backfill_from_date,
            },
        )
        if test_row_limit_reached:
            assert response.status_code == 400, response.text
        else:
            assert response.status_code == 200, response.text
            rule_id = response.json()["id"]
            sleep(1)
            # Fetch the rule to verify backfill_from_date was set correctly
            response = await http_tenant_one.get(
                "/runs/rules",
            )
            assert response.status_code == 200, response.text
            rule = [rule for rule in response.json() if rule["id"] == rule_id][0]
            assert rule["backfill_from"] == backfill_from_date

        if test_row_limit_reached:
            # test with sampling rate to ensure it is reflected in the limit counts
            response = await http_tenant_one.post(
                "/runs/rules",
                json={
                    "session_id": session_id,
                    "filter": 'eq(run_type, "chain")',
                    "display_name": "first rule",
                    "sampling_rate": 0.01,
                    "add_to_annotation_queue_id": annotation_queue_id,
                    "backfill_from": backfill_from_date,
                },
            )
            assert response.status_code == 200, response.text

            # test with filters to ensure they are reflected in the limit counts
            response = await http_tenant_one.post(
                "/runs/rules",
                json={
                    "session_id": session_id,
                    "filter": 'eq(run_type, "random")',
                    "display_name": "first rule",
                    "sampling_rate": 1,
                    "add_to_annotation_queue_id": annotation_queue_id,
                    "backfill_from": backfill_from_date,
                },
            )
            assert response.status_code == 200, response.text


async def test_post_backfill_from_date_with_dataset(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test posting backfill_from_date parameter."""

    backfill_from_date = (datetime.now(timezone.utc) - timedelta(days=2)).isoformat()
    response = await http_tenant_one.post(
        "/annotation-queues",
        json={"name": random_lower_string()},
    )
    assert response.status_code == 200, response.text
    annotation_queue_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/datasets",
        json={"name": random_lower_string()},
    )
    assert response.status_code == 200, response.text
    dataset_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "dataset_id": dataset_id,
            "filter": 'eq(run_type, "chain")',
            "display_name": "first rule",
            "sampling_rate": 1,
            "add_to_annotation_queue_id": annotation_queue_id,
            "backfill_from": backfill_from_date,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # Fetch the rule to verify backfill_from_date was set correctly
    response = await http_tenant_one.get(
        "/runs/rules",
    )
    assert response.status_code == 200, response.text
    rule = [rule for rule in response.json() if rule["id"] == rule_id][0]
    assert rule["backfill_from"] == backfill_from_date


@pytest.mark.flaky
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_run_rule_longlived_usage_limiting(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # STEP 1: usage limit that will allow only two (since it lets you go
        #         over by one) longlived traces per month
        client = authed_client.client

        # No API key access, so call function directly to set usage limit
        payload = user_defined_limits.UpsertUsageLimit(
            limit_type=user_defined_limits.UsageLimitType.MONTHLY_LONGLIVED_TRACES,
            limit_value=2,
        )
        await user_defined_limits.upsert_user_defined_usage_limit(
            authed_client.auth, payload
        )

        rules_setup = await _set_up_rules(client)

        # STEP 2: Send two runs and ensure they are both shortlived
        async def send_runs(run_ids: list[UUID]) -> Response:
            return await client.post(
                "/runs/batch",
                json={
                    "post": [
                        {
                            "name": "AgentExecutor",
                            "start_time": datetime.utcnow().isoformat(),
                            "end_time": (
                                datetime.utcnow() + timedelta(seconds=1)
                            ).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Agent"},
                            "inputs": {
                                "input": "How many people live in canada as of 2023?"
                            },
                            "session_id": rules_setup.session_id,
                            "run_type": "chain",
                            "id": str(run_id),
                            "trace_id": str(run_id),
                            "dotted_order": datetime.utcnow().strftime(
                                "%Y%m%dT%H%M%S%fZ"
                            )
                            + str(run_id),
                            "parent_run_id": None,
                        }
                        for run_id in run_ids
                    ]
                },
            )

        run_one_id = uuid4()
        run_two_id = uuid4()
        run_three_id = uuid4()

        resp = await send_runs([run_one_id, run_two_id])
        assert resp.status_code == 202
        await wait_until_task_queue_empty()

        for run in [run_one_id, run_two_id]:
            resp = await client.get("/runs/" + str(run))
            assert resp.json()["trace_tier"] == schemas.TraceTier.shortlived.value

        await cron_schedule_apply_rules(
            rule_ids=[
                rules_setup.rule_send_to_dataset_id,
                rules_setup.rule_send_to_annotation_queue_id,
                rules_setup.rule_online_evals_id,
                rules_setup.rule_send_to_queue_after_online_evals_id,
                rules_setup.rule_from_dataset_id,
            ]
        )
        await wait_until_task_queue_empty()

        for run in [run_one_id, run_two_id]:
            resp = await client.get("/runs/" + str(run))
            assert resp.json()["trace_tier"] == schemas.TraceTier.longlived.value

        resp = await send_runs([run_three_id])
        assert resp.status_code == 202
        await wait_until_task_queue_empty()

        resp = await client.get("/runs/" + str(run_three_id))
        assert resp.json()["trace_tier"] == schemas.TraceTier.shortlived.value

        resp = await client.get(
            f"/runs/rules/{rules_setup.rule_send_to_dataset_id}/logs"
        )
        dataset_rule_logs = resp.json()
        last_dataset_rule_start = dataset_rule_logs[0]["start_time"]

        await cron_schedule_apply_rules(
            rule_ids=[
                rules_setup.rule_send_to_dataset_id,
                rules_setup.rule_send_to_annotation_queue_id,
                rules_setup.rule_online_evals_id,
                rules_setup.rule_send_to_queue_after_online_evals_id,
                rules_setup.rule_from_dataset_id,
            ]
        )
        await wait_until_task_queue_empty()

        # This one should not be upgraded to longlived
        resp = await client.get("/runs/" + str(run_three_id))
        assert resp.json()["trace_tier"] == schemas.TraceTier.shortlived.value

        expected_error = {
            "outcome": "error",
            "payload": {"error": "longlived trace usage limit exceeded"},
        }

        resp = await client.get(
            f"/runs/rules/{rules_setup.rule_send_to_dataset_id}/logs"
        )
        dataset_rule_logs = resp.json()
        assert dataset_rule_logs[0]["add_to_dataset"] == expected_error
        dataset_end_time = dataset_rule_logs[0]["end_time"]
        dataset_application_start = dataset_rule_logs[0]["start_time"]
        assert dataset_application_start != last_dataset_rule_start

        resp = await client.get(
            f"/runs/rules/{rules_setup.rule_send_to_annotation_queue_id}/logs"
        )
        annotation_queue_rule_logs = resp.json()
        assert (
            annotation_queue_rule_logs[0]["add_to_annotation_queue"] == expected_error
        )
        annotation_queue_end_time = annotation_queue_rule_logs[0]["end_time"]

        resp = await client.get(f"/runs/rules/{rules_setup.rule_online_evals_id}/logs")
        online_evals_rule_logs = resp.json()
        assert online_evals_rule_logs[0]["evaluators"] == expected_error
        online_evals_end_time = online_evals_rule_logs[0]["end_time"]

        await cron_schedule_apply_rules(
            rule_ids=[
                rules_setup.rule_send_to_dataset_id,
                rules_setup.rule_send_to_annotation_queue_id,
                rules_setup.rule_online_evals_id,
                rules_setup.rule_send_to_queue_after_online_evals_id,
                rules_setup.rule_from_dataset_id,
            ]
        )
        await wait_until_task_queue_empty()

        # check run logs - as there were no new runs these should be reset
        resp = await client.get(
            f"/runs/rules/{rules_setup.rule_send_to_dataset_id}/logs"
        )
        dataset_rule_logs = resp.json()
        assert dataset_rule_logs[0]["end_time"] > dataset_end_time

        resp = await client.get(
            f"/runs/rules/{rules_setup.rule_send_to_annotation_queue_id}/logs"
        )
        annotation_queue_rule_logs = resp.json()
        assert annotation_queue_rule_logs[0]["end_time"] > annotation_queue_end_time

        resp = await client.get(f"/runs/rules/{rules_setup.rule_online_evals_id}/logs")
        online_evals_rule_logs = resp.json()
        assert online_evals_rule_logs[0]["end_time"] > online_evals_end_time


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_run_rule_all_traces_usage_limiting(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # STEP 1: usage limit that will allow only two (since it lets you go
        #         over by one) longlived traces per month
        client = authed_client.client

        # No API key access, so call function directly to set usage limit
        payload = user_defined_limits.UpsertUsageLimit(
            limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
            limit_value=3,
        )
        await user_defined_limits.upsert_user_defined_usage_limit(
            authed_client.auth, payload
        )

        rules_setup = await _set_up_rules(client)

        # STEP 2: Send two runs and ensure they are both shortlived
        async def send_runs(run_ids: list[UUID]) -> Response:
            return await client.post(
                "/runs/batch",
                json={
                    "post": [
                        {
                            "name": "Search",
                            "start_time": datetime.utcnow().isoformat(),
                            "end_time": (
                                datetime.utcnow() + timedelta(seconds=1)
                            ).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {
                                "input": "How many people live in canada as of 2023?"
                            },
                            "tags": ["eval"],
                            "session_id": rules_setup.session_id,
                            "run_type": "tool",
                            "id": str(run_id),
                            "trace_id": str(run_id),
                            "dotted_order": datetime.utcnow().strftime(
                                "%Y%m%dT%H%M%S%fZ"
                            )
                            + str(run_id),
                            "parent_run_id": None,
                        }
                        for run_id in run_ids
                    ]
                },
            )

        run_one_id = uuid4()
        run_two_id = uuid4()
        run_three_id = uuid4()

        resp = await send_runs([run_one_id, run_two_id])
        assert resp.status_code == 202
        await wait_until_task_queue_empty()

        for run in [run_one_id, run_two_id]:
            resp = await client.get("/runs/" + str(run))

        for run in [run_one_id, run_two_id]:
            resp = await client.get("/runs/" + str(run))
            assert resp.status_code == 200

        # Third run should go through since it is the one that trips the limit
        resp = await send_runs([run_three_id])
        assert resp.status_code == 202

        failed_resp = await send_runs([uuid4()])
        assert failed_resp.status_code == 429

        await wait_until_task_queue_empty()

        resp = await client.get("/runs/" + str(run_three_id))

        await cron_schedule_apply_rules(
            rule_ids=[
                rules_setup.rule_send_to_dataset_id,
                rules_setup.rule_send_to_annotation_queue_id,
                rules_setup.rule_online_evals_id,
                rules_setup.rule_send_to_queue_after_online_evals_id,
                rules_setup.rule_from_dataset_id,
            ]
        )
        await wait_until_task_queue_empty()

        for run in [run_one_id, run_two_id, run_three_id]:
            resp = await client.get("/runs/" + str(run))
            # no runs should be upgraded because tracing their evaluators should
            # have been rate limited
            assert resp.json()["trace_tier"] == schemas.TraceTier.shortlived.value

        expected_error = {
            "outcome": "error",
            "payload": {"error": "total trace usage limit exceeded"},
        }

        resp = await client.get(f"/runs/rules/{rules_setup.rule_online_evals_id}/logs")
        online_evals_rule_logs = resp.json()

        # The online evals rule should have been checked / failed
        assert len(online_evals_rule_logs) == 1
        assert online_evals_rule_logs[0]["evaluators"] == expected_error
        assert (
            online_evals_rule_logs[0]["run_id"]
            == "00000000-0000-0000-0000-000000000000"
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_corrections_dataset(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Make sure the evaluators project doesn't exist
        evaluator_session = await db_asyncpg.fetchval(
            "select id from tracer_session where name='evaluators' and tenant_id=$1",
            authed_client.auth.tenant_id,
        )
        assert evaluator_session is None

        response = await client.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text

        session_id = response.json()["id"]

        response = await client.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text

        session_id_2 = response.json()["id"]

        evaluator = {
            "structured": {
                "prompt": [
                    [
                        "system",
                        "You are evaluating the following value for toxicity, topic, and whether it's long.",
                    ],
                    [
                        "user",
                        "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                    ],
                ],
                "schema": {
                    "name": "eval",
                    "description": "",
                    "properties": {
                        "toxic": {"type": "number"},
                    },
                },
                "model": {
                    "lc": 1,
                    "type": "constructor",
                    "id": [
                        "langchain",
                        "chat_models",
                        "fake",
                        "FakeMessagesListChatModel",
                    ],
                    "name": "FakeMessagesListChatModel",
                    "kwargs": {
                        "a_great_secret": {
                            "lc": 1,
                            "type": "secret",
                            "id": ["A_GREAT_SECRET"],
                        },
                        "responses": [
                            {
                                "lc": 1,
                                "type": "constructor",
                                "id": ["langchain", "schema", "messages", "AIMessage"],
                                "kwargs": {
                                    "content": "",
                                    "additional_kwargs": {
                                        "tool_calls": [
                                            {
                                                "function": {
                                                    "name": "eval",
                                                    "arguments": json.dumps(
                                                        {
                                                            "toxic": 1,
                                                        }
                                                    ),
                                                }
                                            }
                                        ]
                                    },
                                },
                            }
                        ],
                    },
                },
            }
        }

        # Setup a rule without secret fails
        response = await client.post(
            "/runs/rules",
            json={
                "session_id": session_id,
                "evaluators": [evaluator],
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": False,
            },
        )
        assert response.status_code == 400, response.text

        response = await client.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )

        assert response.status_code == 200, response.text

        # Set up a rule with a valid secret
        response = await client.post(
            "/runs/rules",
            json={
                "session_id": session_id,
                "evaluators": [evaluator],
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": False,
            },
        )
        assert response.status_code == 200, response.text
        parent_rule_id = response.json()["id"]

        # Make sure the evaluators project now exists
        evaluator_session = await db_asyncpg.fetchval(
            "select id from tracer_session where name='evaluators' and tenant_id=$1",
            authed_client.auth.tenant_id,
        )
        assert evaluator_session is not None

        # Make sure a rule was created on the evaluators project
        response = await client.get(
            f"/runs/rules?session_id={str(evaluator_session)}",
        )
        assert response.status_code == 200, response.text
        rule = response.json()[0]

        # Make sure the dataset was created
        ds_name = f"ds-corrections-new_cool_rule-toxic-{str(parent_rule_id)}"
        corrections_dataset_id = await db_asyncpg.fetchval(
            "select id from dataset where name=$1",
            ds_name,
        )
        original_corrections_dataset = corrections_dataset_id

        assert rule["evaluators"] is None
        assert rule["add_to_dataset_id"] == str(corrections_dataset_id)
        assert rule["add_to_dataset_name"] == ds_name
        assert rule["session_id"] == str(evaluator_session)
        assert (
            rule["filter"]
            == f"and(eq(is_root, true), and(eq(metadata_key, 'rule_id'), eq(metadata_value, '{str(parent_rule_id)}')), eq(feedback_key, 'toxic'))"
        )

        # Make sure the rule has the correct corrections_dataset_id
        response = await client.get(
            f"/runs/rules?session_id={str(session_id)}",
        )
        assert response.status_code == 200, response.text
        rule = response.json()[0]
        assert rule["corrections_dataset_id"] == str(corrections_dataset_id)
        assert rule["use_corrections_dataset"] is False

        # Now update the rule to have use_corrections_dataset set to True and make sure everything works
        response = await client.patch(
            f"/runs/rules/{parent_rule_id}",
            json={
                "evaluators": [evaluator],
                "session_id": session_id,
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": True,
            },
        )
        assert response.status_code == 200, response.text

        response = await client.get(
            f"/runs/rules?session_id={str(session_id)}",
        )
        assert response.status_code == 200, response.text
        rule = response.json()[0]
        assert rule["corrections_dataset_id"] == str(corrections_dataset_id)
        assert rule["use_corrections_dataset"] is True

        # Now update the rule to have use_corrections_dataset set to False again
        response = await client.patch(
            f"/runs/rules/{parent_rule_id}",
            json={
                "evaluators": [evaluator],
                "session_id": session_id,
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": False,
            },
        )
        assert response.status_code == 200, response.text

        response = await client.get(
            f"/runs/rules?session_id={str(session_id)}",
        )
        assert response.status_code == 200, response.text
        rule = response.json()[0]
        assert rule["corrections_dataset_id"] == str(corrections_dataset_id)
        assert rule["use_corrections_dataset"] is False

        # Now update the rule to have a diff feedback key and make sure the corrections dataset is updated even though use_corrections_dataset is False
        evaluator_different_feedback_key = {
            "structured": {
                "prompt": [
                    [
                        "system",
                        "You are evaluating the following value for toxicity, topic, and whether it's long.",
                    ],
                    [
                        "user",
                        "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                    ],
                ],
                "schema": {
                    "name": "eval",
                    "description": "",
                    "properties": {
                        "another_tag": {"type": "number"},
                    },
                },
                "model": {
                    "lc": 1,
                    "type": "constructor",
                    "id": [
                        "langchain",
                        "chat_models",
                        "fake",
                        "FakeMessagesListChatModel",
                    ],
                    "name": "FakeMessagesListChatModel",
                    "kwargs": {
                        "a_great_secret": {
                            "lc": 1,
                            "type": "secret",
                            "id": ["A_GREAT_SECRET"],
                        },
                        "responses": [
                            {
                                "lc": 1,
                                "type": "constructor",
                                "id": ["langchain", "schema", "messages", "AIMessage"],
                                "kwargs": {
                                    "content": "",
                                    "additional_kwargs": {
                                        "tool_calls": [
                                            {
                                                "function": {
                                                    "name": "eval",
                                                    "arguments": json.dumps(
                                                        {
                                                            "another_tag": 1,
                                                        }
                                                    ),
                                                }
                                            }
                                        ]
                                    },
                                },
                            }
                        ],
                    },
                },
            }
        }

        response = await client.patch(
            f"/runs/rules/{parent_rule_id}",
            json={
                "evaluators": [evaluator_different_feedback_key],
                "session_id": session_id,
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": False,
            },
        )
        assert response.status_code == 200, response.text

        # Make sure the rule was updated on the evaluators project
        response = await client.get(
            f"/runs/rules?session_id={str(evaluator_session)}",
        )
        assert response.status_code == 200, response.text
        rules = response.json()
        assert len(rules) == 1
        rule = rules[0]
        assert rule["use_corrections_dataset"] is False

        # Make sure the dataset was created
        corrections_dataset_id = await db_asyncpg.fetchval(
            "select id from dataset where name=$1",
            f"ds-corrections-new_cool_rule-another_tag-{str(parent_rule_id)}",
        )

        assert rule["evaluators"] is None
        assert rule["add_to_dataset_id"] == str(corrections_dataset_id)
        expected_ds_name = (
            f"ds-corrections-new_cool_rule-another_tag-{str(parent_rule_id)}"
        )
        assert rule["add_to_dataset_name"] == expected_ds_name
        assert rule["session_id"] == str(evaluator_session)
        assert (
            rule["filter"]
            == f"and(eq(is_root, true), and(eq(metadata_key, 'rule_id'), eq(metadata_value, '{str(parent_rule_id)}')), eq(feedback_key, 'another_tag'))"
        )

        # Now update an unrelated field on the rule and turn on use_corrections_dataset and make sure the corrections dataset is not recreated
        response = await client.patch(
            f"/runs/rules/{parent_rule_id}",
            json={
                "evaluators": [evaluator_different_feedback_key],
                "session_id": session_id,
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 0.5,
                "use_corrections_dataset": True,
            },
        )
        assert response.status_code == 200, response.text

        response = await client.get(
            f"/runs/rules?session_id={str(evaluator_session)}",
        )
        assert response.status_code == 200, response.text
        rules = response.json()
        assert len(rules) == 1
        rule = rules[0]

        ds_name = f"ds-corrections-new_cool_rule-another_tag-{str(parent_rule_id)}"
        corrections_dataset_id = await db_asyncpg.fetchval(
            "select id from dataset where name=$1",
            ds_name,
        )

        assert rule["evaluators"] is None
        assert rule["add_to_dataset_id"] == str(corrections_dataset_id)
        assert rule["add_to_dataset_name"] == ds_name
        assert rule["session_id"] == str(evaluator_session)
        assert (
            rule["filter"]
            == f"and(eq(is_root, true), and(eq(metadata_key, 'rule_id'), eq(metadata_value, '{str(parent_rule_id)}')), eq(feedback_key, 'another_tag'))"
        )

        # Now update back to the previous feedback key and make sure we go back to the previous dataset
        response = await client.patch(
            f"/runs/rules/{parent_rule_id}",
            json={
                "evaluators": [evaluator],
                "session_id": session_id,
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 0.5,
                "use_corrections_dataset": True,
            },
        )
        assert response.status_code == 200, response.text

        response = await client.get(
            f"/runs/rules?session_id={str(evaluator_session)}",
        )
        assert response.status_code == 200, response.text
        rules = response.json()
        assert len(rules) == 1
        rule = rules[0]

        ds_name = f"ds-corrections-new_cool_rule-toxic-{str(parent_rule_id)}"
        corrections_dataset_ids = await db_asyncpg.fetch(
            "select id from dataset where name=$1",
            ds_name,
        )
        assert len(corrections_dataset_ids) == 1
        corrections_dataset_id = corrections_dataset_ids[0]["id"]
        assert corrections_dataset_id == original_corrections_dataset

        assert rule["evaluators"] is None
        assert rule["add_to_dataset_id"] == str(corrections_dataset_id)
        assert rule["add_to_dataset_name"] == ds_name
        assert rule["session_id"] == str(evaluator_session)
        assert (
            rule["filter"]
            == f"and(eq(is_root, true), and(eq(metadata_key, 'rule_id'), eq(metadata_value, '{str(parent_rule_id)}')), eq(feedback_key, 'toxic'))"
        )

        # Now update the rule to have multiple feedback keys and make sure it doesn't work
        evaluator_multiple_feedback_keys = {
            "structured": {
                "prompt": [
                    [
                        "system",
                        "You are evaluating the following value for toxicity, topic, and whether it's long.",
                    ],
                    [
                        "user",
                        "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                    ],
                ],
                "schema": {
                    "name": "eval",
                    "description": "",
                    "properties": {
                        "another_tag": {"type": "number"},
                        "toxic": {"type": "number"},
                    },
                },
                "model": {
                    "lc": 1,
                    "type": "constructor",
                    "id": [
                        "langchain",
                        "chat_models",
                        "fake",
                        "FakeMessagesListChatModel",
                    ],
                    "name": "FakeMessagesListChatModel",
                    "kwargs": {
                        "a_great_secret": {
                            "lc": 1,
                            "type": "secret",
                            "id": ["A_GREAT_SECRET"],
                        },
                        "responses": [
                            {
                                "lc": 1,
                                "type": "constructor",
                                "id": ["langchain", "schema", "messages", "AIMessage"],
                                "kwargs": {
                                    "content": "",
                                    "additional_kwargs": {
                                        "tool_calls": [
                                            {
                                                "function": {
                                                    "name": "eval",
                                                    "arguments": json.dumps(
                                                        {
                                                            "another_tag": 1,
                                                            "toxic": 1,
                                                        }
                                                    ),
                                                }
                                            }
                                        ]
                                    },
                                },
                            }
                        ],
                    },
                },
            }
        }

        response = await client.patch(
            f"/runs/rules/{parent_rule_id}",
            json={
                "evaluators": [evaluator_multiple_feedback_keys],
                "session_id": session_id,
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": True,
            },
        )
        assert response.status_code == 400

        # Make sure the rule still exists on the evaluators project
        response = await client.get(
            f"/runs/rules?session_id={str(evaluator_session)}",
        )
        assert response.status_code == 200, response.text
        rules = response.json()
        assert len(rules) == 1
        rule = rules[0]

        # Make sure the dataset still exists
        ds_name = f"ds-corrections-new_cool_rule-toxic-{str(parent_rule_id)}"
        corrections_dataset_id = await db_asyncpg.fetchval(
            "select id from dataset where name=$1",
            ds_name,
        )

        assert rule["evaluators"] is None
        assert rule["add_to_dataset_id"] == str(corrections_dataset_id)
        assert rule["add_to_dataset_name"] == ds_name
        assert rule["session_id"] == str(evaluator_session)
        assert (
            rule["filter"]
            == f"and(eq(is_root, true), and(eq(metadata_key, 'rule_id'), eq(metadata_value, '{str(parent_rule_id)}')), eq(feedback_key, 'toxic'))"
        )

        # Test disabling the corrections dataset
        response = await client.patch(
            f"/runs/rules/{parent_rule_id}",
            json={
                "use_corrections_dataset": False,
                "evaluators": [evaluator],
                "session_id": session_id,
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
            },
        )
        assert response.status_code == 200, response.text

        # Make sure the rule was not deleted on the evaluators project (we keep it in case they want to turn it back on)
        response = await client.get(
            f"/runs/rules?session_id={str(evaluator_session)}",
        )
        assert response.status_code == 200, response.text
        rules = response.json()
        assert len(rules) == 1

        # Make sure the parent rule has a corrections_dataset_id, but use_corrections is False
        response = await client.get(
            f"/runs/rules?session_id={str(session_id)}",
        )
        assert response.status_code == 200, response.text
        rule = response.json()[0]
        assert rule["corrections_dataset_id"] == str(corrections_dataset_id)
        assert rule["use_corrections_dataset"] is False

        evaluator_multi_tool = {
            "structured": {
                "prompt": [
                    [
                        "system",
                        "You are evaluating the following value for toxicity, topic, and whether it's long.",
                    ],
                    [
                        "user",
                        "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                    ],
                ],
                "schema": {
                    "name": "eval",
                    "description": "",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "toxic": {"type": "number"},
                            "topic": {"type": "string"},
                        },
                        "required": ["toxic"],
                    },
                },
                "model": {
                    "lc": 1,
                    "type": "constructor",
                    "id": [
                        "langchain",
                        "chat_models",
                        "fake",
                        "FakeMessagesListChatModel",
                    ],
                    "name": "FakeMessagesListChatModel",
                    "kwargs": {
                        "a_great_secret": {
                            "lc": 1,
                            "type": "secret",
                            "id": ["A_GREAT_SECRET"],
                        },
                        "responses": [
                            {
                                "lc": 1,
                                "type": "constructor",
                                "id": ["langchain", "schema", "messages", "AIMessage"],
                                "kwargs": {
                                    "content": "",
                                    "additional_kwargs": {
                                        "tool_calls": [
                                            {
                                                "function": {
                                                    "name": "eval",
                                                    "arguments": json.dumps(
                                                        {
                                                            "toxic": 1,
                                                            "topic": "test",
                                                        }
                                                    ),
                                                }
                                            }
                                        ]
                                    },
                                },
                            }
                        ],
                    },
                },
            }
        }

        # Ensure that you can't create a rule with use_corrections_dataset and multiple tools
        response = await client.post(
            "/runs/rules",
            json={
                "session_id": session_id,
                "evaluators": [evaluator_multi_tool],
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": True,
            },
        )
        assert response.status_code == 400

        # Test creating an invalid rule, then updating it to be valid
        response = await client.post(
            "/runs/rules",
            json={
                "session_id": str(session_id_2),
                "evaluators": [evaluator_multi_tool],
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": False,
            },
        )
        assert response.status_code == 200, response.text
        parent_rule_id = response.json()["id"]

        # Make sure the rule has no corrections_dataset_id
        response = await client.get(
            f"/runs/rules?session_id={str(session_id_2)}",
        )
        assert response.status_code == 200, response.text
        rule = response.json()[0]
        assert rule["corrections_dataset_id"] is None
        assert rule["use_corrections_dataset"] is False

        # Make sure a rule was not created on the evaluators project
        response = await client.get(
            f"/runs/rules?session_id={str(evaluator_session)}",
        )
        assert response.status_code == 200, response.text
        assert len(response.json()) == 1

        # Patch the rule to be valid but still have use_corrections_dataset set to False
        response = await client.patch(
            f"/runs/rules/{parent_rule_id}",
            json={
                "evaluators": [evaluator],
                "session_id": str(session_id_2),
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": False,
            },
        )
        assert response.status_code == 200, response.text

        # Make sure a rule was created on the evaluators project
        response = await client.get(
            f"/runs/rules?session_id={str(evaluator_session)}",
        )
        assert response.status_code == 200, response.text
        assert len(response.json()) == 2
        # find the rule with the correct filter
        rule = next(
            (
                rule
                for rule in response.json()
                if rule["filter"]
                == f"and(eq(is_root, true), and(eq(metadata_key, 'rule_id'), eq(metadata_value, '{str(parent_rule_id)}')), eq(feedback_key, 'toxic'))"
            ),
            None,
        )
        assert rule is not None

        # Make sure the dataset was created
        ds_name = f"ds-corrections-new_cool_rule-toxic-{str(parent_rule_id)}"
        corrections_dataset_id = await db_asyncpg.fetchval(
            "select id from dataset where name=$1",
            ds_name,
        )
        original_corrections_dataset = corrections_dataset_id

        assert rule["evaluators"] is None
        assert rule["add_to_dataset_id"] == str(corrections_dataset_id)
        assert rule["add_to_dataset_name"] == ds_name
        assert rule["session_id"] == str(evaluator_session)
        assert (
            rule["filter"]
            == f"and(eq(is_root, true), and(eq(metadata_key, 'rule_id'), eq(metadata_value, '{str(parent_rule_id)}')), eq(feedback_key, 'toxic'))"
        )

        # Make sure the rule has the correct corrections_dataset_id
        response = await client.get(
            f"/runs/rules?session_id={str(session_id_2)}",
        )
        assert response.status_code == 200, response.text
        rule = response.json()[0]
        assert rule["corrections_dataset_id"] == str(corrections_dataset_id)
        assert rule["use_corrections_dataset"] is False

        # Now update the rule to be invalid and make sure the sub-rule is deleted
        response = await client.patch(
            f"/runs/rules/{parent_rule_id}",
            json={
                "evaluators": [evaluator_multi_tool],
                "session_id": str(session_id_2),
                "filter": 'eq(run_type, "tool")',
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
                "use_corrections_dataset": False,
            },
        )
        assert response.status_code == 200, response.text

        # Make sure the rule was deleted on the evaluators project
        response = await client.get(
            f"/runs/rules?session_id={str(evaluator_session)}",
        )
        assert response.status_code == 200, response.text
        assert len(response.json()) == 1


async def test_rules_apply_with_backfill_from(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a run can be created."""

    # setup backfill dates
    backfill_start_time = datetime.now(timezone.utc) - timedelta(minutes=1)
    backfill_start_time_iso = backfill_start_time.isoformat()

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    # insert runs before adding the rules to test backfill
    run_one = uuid4()
    run_two = uuid4()
    run_two_w_feedback = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "metadata": {"key": 1}},
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_one),
                    "trace_id": str(run_one),
                    "dotted_order": f"20230505T051324571809Z{run_one}",
                },
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "metadata": {"key": 1}},
                    "tags": ["eval"],
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "How many people live in mexico as of 2025?"},
                    "outputs": {"output": "39,567,000"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_two),
                    "trace_id": str(run_two),
                    "dotted_order": f"20230505T051324571809Z{run_two}",
                },
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "metadata": {"key": 1}},
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "How many people live in mexico as of 2025?"},
                    "outputs": {"output": "39,567,000"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_two_w_feedback),
                    "trace_id": str(run_two_w_feedback),
                    "dotted_order": f"20230505T051324571809Z{run_two_w_feedback}",
                },
            ]
        },
    )

    assert response.status_code == 202, response.text

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_two_w_feedback),
            "key": "yoooo",
            "correction": "39,568,000",
        },
    )

    assert response.status_code in (200, 202), response.text

    await wait_until_task_queue_empty()

    runs = await ch_client.fetch(
        "select * from runs where id in {runs}", params={"runs": [run_one, run_two]}
    )
    assert len(runs) == 2

    setup = await _set_up_rules(
        http_tenant_one, session_id=session_id, backfill_from=backfill_start_time_iso
    )
    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    send_to_ds_applications = await db_asyncpg.fetch(
        """select * from run_rules_applications where rule_id = $1
        order by run_id, start_time""",
        setup.rule_send_to_dataset_id,
    )
    assert len(send_to_ds_applications) == 2
    assert send_to_ds_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_ds_applications[0]["start_time"].isoformat() == backfill_start_time_iso
    )
    assert send_to_ds_applications[1]["run_id"] == run_two_w_feedback
    assert (
        send_to_ds_applications[1]["start_time"].isoformat()
        < setup.rule_send_to_dataset_created_at
    )
    assert send_to_ds_applications[1]["add_to_dataset"]["outcome"] == "success"
    assert (
        send_to_ds_applications[1]["add_to_dataset"]["payload"]["example_id"]
        is not None
    )

    examples_inserted = await db_asyncpg.fetch(
        "select * from examples_log where source_run_id = $1", run_two_w_feedback
    )
    assert len(examples_inserted) == 1
    assert str(examples_inserted[0]["dataset_id"]) == setup.dataset_id
    assert examples_inserted[0]["inputs"] == {
        "input": "How many people live in mexico as of 2025?"
    }
    assert examples_inserted[0]["outputs"] == {"output": "39,568,000"}
    assert examples_inserted[0]["metadata"] == {
        "dataset_split": ["base"],
        "key": 1,
        "ls_run_depth": 0,
    }
    example_id = examples_inserted[0]["id"]

    send_to_aq_applications = await db_asyncpg.fetch(
        """select * from run_rules_applications where rule_id = $1
        order by run_id, start_time""",
        setup.rule_send_to_annotation_queue_id,
    )

    assert len(send_to_aq_applications) == 2
    assert send_to_aq_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_aq_applications[0]["start_time"].isoformat() == backfill_start_time_iso
    )
    assert send_to_aq_applications[1]["run_id"] == run_one
    assert (
        send_to_aq_applications[1]["start_time"].isoformat()
        < setup.rule_send_to_annotation_queue_created_at
    )
    assert send_to_aq_applications[1]["add_to_annotation_queue"]["outcome"] == "success"

    annotation_queue_runs = await db_asyncpg.fetch(
        "select * from annotation_queue_runs where run_id = $1", run_one
    )
    assert len(annotation_queue_runs) == 1
    assert str(annotation_queue_runs[0]["queue_id"]) == setup.annotation_queue_id

    online_eval_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1 order by run_id, start_time",
        setup.rule_online_evals_id,
    )

    assert len(online_eval_applications) == 2
    assert online_eval_applications[0]["run_id"] == UUID(int=0)

    assert online_eval_applications[1]["run_id"] == run_two
    assert online_eval_applications[1]["evaluators"]["outcome"] == "success"

    feedbacks = await ch_client.fetch(
        "select * from feedbacks where run_id = {run}", params={"run": run_two}
    )

    assert len(feedbacks) == 4, [dict(feedback) for feedback in feedbacks]
    assert set(
        online_eval_applications[1]["evaluators"]["payload"]["feedback_ids"]
    ) == set([str(feedback["id"]) for feedback in feedbacks])

    evaluator_run_ids: set[str] = set()
    for feedback in feedbacks:
        assert feedback["run_id"] == run_two
        source = json.loads(feedback["feedback_source"])
        assert source["type"] == "auto_eval"
        assert source["metadata"]["rule_id"] == setup.rule_online_evals_id
        evaluator_run_ids.add(source["metadata"]["__run"]["run_id"])

    assert len(evaluator_run_ids) == 2

    evaluator_runs = await ch_client.fetch(
        "select * from runs where id in {runs}",
        params={"runs": list(evaluator_run_ids)},
    )

    assert len(evaluator_runs) == len(evaluator_run_ids)

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    send_to_queue_after_online_evals_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1 order by run_id, start_time",
        setup.rule_send_to_queue_after_online_evals_id,
    )

    assert len(send_to_queue_after_online_evals_applications) == 3
    assert send_to_queue_after_online_evals_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_queue_after_online_evals_applications[0]["start_time"].isoformat()
        == backfill_start_time_iso
    )
    assert send_to_queue_after_online_evals_applications[1]["run_id"] == UUID(int=0)
    assert (
        send_to_queue_after_online_evals_applications[1]["start_time"].isoformat()
        > setup.rule_send_to_queue_after_online_evals_created_at
    )
    webhook_rule_application = next(
        (
            rule_application
            for rule_application in send_to_queue_after_online_evals_applications
            if rule_application["run_id"] == UUID(int=0)
            and rule_application.get("webhooks")
        ),
        None,
    )
    assert webhook_rule_application
    assert webhook_rule_application["webhooks"] == {
        "outcome": "success",
        "payload": None,
    }
    annotation_rule_application = next(
        (
            rule_application
            for rule_application in send_to_queue_after_online_evals_applications
            if rule_application["run_id"] == run_two
        ),
        None,
    )
    assert annotation_rule_application
    assert annotation_rule_application["add_to_annotation_queue"] == {
        "outcome": "success",
        "payload": None,
    }
    assert (
        annotation_rule_application["start_time"].isoformat()
        > setup.rule_send_to_queue_after_online_evals_created_at
    )

    annotation_queue_runs = await db_asyncpg.fetch(
        "select * from annotation_queue_runs where run_id = $1", run_two
    )
    assert len(annotation_queue_runs) == 1
    assert str(annotation_queue_runs[0]["queue_id"]) == setup.annotation_queue_id

    run_three = uuid4()
    test_session_id = await db_asyncpg.fetchval(
        """insert into tracer_session (id, tenant_id, name, reference_dataset_id, start_time)
        values (gen_random_uuid(), $1, $2, $3, now())
        returning id""",
        auth_tenant_one.tenant_id,
        random_lower_string(),
        setup.dataset_id,
    )
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "metadata": {"key": 1}},
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(test_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_three),
                    "trace_id": str(run_three),
                    "dotted_order": f"20230505T051324571809Z{run_three}",
                    "reference_example_id": str(example_id),
                },
            ]
        },
    )

    assert response.status_code == 202, response.text

    await wait_until_task_queue_empty()

    runs = await ch_client.fetch(
        "select * from runs where id in {runs}", params={"runs": [run_three]}
    )
    assert len(runs) == 1
    assert runs[0]["session_id"] == test_session_id
    assert runs[0]["reference_example_id"] == example_id
    assert runs[0]["reference_dataset_id"] == UUID(setup.dataset_id)

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    from_ds_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1",
        setup.rule_from_dataset_id,
    )
    assert len(from_ds_applications) == 4
    applications_w_runs = [
        app for app in from_ds_applications if app["run_id"] != UUID(int=0)
    ]
    assert len(applications_w_runs) == 1
    assert applications_w_runs[0]["run_id"] == run_three

    annotation_queue_runs = await db_asyncpg.fetch(
        "select * from annotation_queue_runs where run_id = $1", run_three
    )

    assert len(annotation_queue_runs) == 1
    assert str(annotation_queue_runs[0]["queue_id"]) == setup.annotation_queue_id

    await wait_until_task_queue_empty()

    # assert that trace TTL has been upgraded for these
    if settings.FF_TRACE_TIERS_ENABLED:
        all_runs = await ch_client.fetch(
            "select * from runs FINAL where id in {runs}",
            params={"runs": [run_one, run_two, run_three, run_two_w_feedback]},
        )
        for run in all_runs:
            assert run["trace_tier"] == schemas.TraceTier.longlived
            assert run["ttl_seconds"] == schemas.TraceTier.longlived.ttl_seconds


@pytest.mark.skip(reason="Flakey")
async def test_rules_apply_with_backfill_and_batching(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a run can be created."""

    # setup backfill dates
    backfill_start_time = datetime.now(timezone.utc) - timedelta(minutes=1)
    backfill_start_time_iso = backfill_start_time.isoformat()

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    # insert runs before adding the rules to test backfill
    # also make sure batching is hit so insert more runs than limit size
    run_ids = []
    feedback_run_ids = []
    for i in range(4):
        run_one = uuid4()
        run_two_w_feedback = uuid4()

        run_ids.append(run_one)
        feedback_run_ids.append(run_two_w_feedback)

        response = await http_tenant_one.post(
            "/runs/batch",
            json={
                "post": [
                    {
                        "name": "AgentExecutor",
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar", "metadata": {"key": 1}},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "AgentExecutor"},
                        "inputs": {
                            "input": f"How many people live in canada as of 2023? Iteration {i}"
                        },
                        "outputs": {"output": "39,566,248"},
                        "session_id": session_id,
                        "parent_run_id": None,
                        "run_type": "chain",
                        "id": str(run_one),
                        "trace_id": str(run_one),
                        "dotted_order": f"20230505T051324571809Z{run_one}",
                    },
                    {
                        "name": "Search",
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar", "metadata": {"key": 1}},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Search"},
                        "inputs": {
                            "input": f"How many people live in mexico as of 2025? Iteration {i}"
                        },
                        "outputs": {"output": "39,567,000"},
                        "session_id": session_id,
                        "parent_run_id": None,
                        "run_type": "tool",
                        "id": str(run_two_w_feedback),
                        "trace_id": str(run_two_w_feedback),
                        "dotted_order": f"20230505T051324571809Z{run_two_w_feedback}",
                    },
                ]
            },
        )
        assert response.status_code == 202, response.text

        response = await http_tenant_one.post(
            "/feedback",
            json={
                "run_id": str(run_two_w_feedback),
                "key": "yoooo",
                "correction": "39,568,000",
            },
        )

        assert response.status_code in (200, 202), response.text

    await wait_until_task_queue_empty()

    # Optimize table so merges performed
    await ch_client.execute("OPTIMIZE TABLE runs FINAL")
    await ch_client.execute("OPTIMIZE TABLE feedbacks_rmt FINAL")

    runs = await ch_client.fetch(
        "select * from runs where id in {runs}",
        params={"runs": run_ids + feedback_run_ids},
    )
    assert len(runs) == 8

    setup = await _set_up_rules(
        http_tenant_one, session_id=session_id, backfill_from=backfill_start_time_iso
    )

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    send_to_ds_applications = await db_asyncpg.fetch(
        """select * from run_rules_applications where rule_id = $1
        order by run_id, start_time""",
        setup.rule_send_to_dataset_id,
    )

    # should have at least 2 records and 1 marker record (with more records left on next batch)
    assert len(send_to_ds_applications) >= 3

    assert send_to_ds_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_ds_applications[0]["start_time"].isoformat() == backfill_start_time_iso
    )
    assert send_to_ds_applications[0]["query_cursors"] is not None

    send_to_aq_applications = await db_asyncpg.fetch(
        """select * from run_rules_applications where rule_id = $1
        order by run_id, start_time""",
        setup.rule_send_to_annotation_queue_id,
    )

    assert len(send_to_aq_applications) == 3
    assert send_to_aq_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_aq_applications[0]["start_time"].isoformat() == backfill_start_time_iso
    )
    assert send_to_aq_applications[0]["query_cursors"] is not None

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
            setup.rule_send_to_annotation_queue_id,
            setup.rule_online_evals_id,
            setup.rule_send_to_queue_after_online_evals_id,
            setup.rule_from_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    send_to_ds_applications = await db_asyncpg.fetch(
        """select * from run_rules_applications where rule_id = $1
        order by run_id, start_time""",
        setup.rule_send_to_dataset_id,
    )

    # 2 marker records + 4 runs
    assert len(send_to_ds_applications) == 6

    # test run_ids there
    assert set(
        [
            str(app["run_id"])
            for app in send_to_ds_applications
            if app["run_id"] != UUID(int=0)
        ]
    ) == set([str(run_id) for run_id in feedback_run_ids])

    send_to_aq_applications = await db_asyncpg.fetch(
        """select * from run_rules_applications where rule_id = $1
        order by run_id, start_time""",
        setup.rule_send_to_annotation_queue_id,
    )

    # 2 marker records + 4 runs
    assert len(send_to_aq_applications) == 6
    assert send_to_aq_applications[0]["run_id"] == UUID(int=0)
    assert send_to_aq_applications[1]["run_id"] == UUID(int=0)
    # test run_ids there
    assert set(
        [
            str(app["run_id"])
            for app in send_to_aq_applications
            if app["run_id"] != UUID(int=0)
        ]
    ) == set([str(run_id) for run_id in run_ids])


async def test_rules_evaluator_output_feedback_parsing() -> None:
    feedback_inserts: list[FeedbackInsert] = []
    run = {
        "trace_id": str(uuid4()),
        "session_id": str(uuid4()),
        "start_time": datetime.now(timezone.utc).isoformat(),
        "id": str(uuid4()),
    }
    metadata = {
        "run_id": str(uuid4()),
    }
    rule_id = uuid4()

    output: Any = {
        "top_level_key": {
            "nested_key": {
                "feedback": [
                    {
                        "this_is_a_feedback": "yes",
                    },
                    {
                        "this_is_a_score": 0.5,
                    },
                ]
            }
        }
    }
    evaluator_feedbacks_ids: dict[str, list[UUID]] = {
        str(run["id"]): [],
    }
    await parse_feedback(
        output,
        run,
        rule_id,
        metadata["run_id"],
        feedback_inserts,
        evaluator_feedbacks_ids,
    )
    assert len(feedback_inserts) == 2
    assert len(evaluator_feedbacks_ids[run["id"]]) == 2

    for insert in feedback_inserts:
        assert insert.payload["key"] in ["this_is_a_feedback", "this_is_a_score"]
        if insert.payload["key"] == "this_is_a_feedback":
            assert insert.payload["value"] == "yes"
            assert insert.payload["score"] is None
        else:
            assert insert.payload["value"] is None
            assert insert.payload["score"] == 0.5

    feedback_inserts = []
    evaluator_feedbacks_ids = {
        str(run["id"]): [],
    }

    output = {"top_level_key": {"nested_key": {"feedback": [1, "hello"]}}}

    await parse_feedback(
        output,
        run,
        rule_id,
        metadata["run_id"],
        feedback_inserts,
        evaluator_feedbacks_ids,
    )
    assert len(feedback_inserts) == 2
    assert len(evaluator_feedbacks_ids[run["id"]]) == 2

    hello_insert = next(
        (insert for insert in feedback_inserts if insert.payload["value"] == "hello"),
        None,
    )
    assert hello_insert is not None
    assert hello_insert.payload["key"] == "feedback"
    assert hello_insert.payload["value"] == "hello"
    assert hello_insert.payload["score"] is None

    one_insert = next(
        (insert for insert in feedback_inserts if insert.payload["score"] == 1),
        None,
    )
    assert one_insert is not None
    assert one_insert.payload["key"] == "feedback"
    assert one_insert.payload["value"] is None
    assert one_insert.payload["score"] == 1

    feedback_inserts = []
    evaluator_feedbacks_ids = {
        str(run["id"]): [],
    }

    output = {"top_level_key": {"nested_key": {"feedback": [1, {"color": "blue"}]}}}

    await parse_feedback(
        output,
        run,
        rule_id,
        metadata["run_id"],
        feedback_inserts,
        evaluator_feedbacks_ids,
    )
    assert len(feedback_inserts) == 2
    assert len(evaluator_feedbacks_ids[run["id"]]) == 2

    hello_insert = next(
        (insert for insert in feedback_inserts if insert.payload["value"] == "blue"),
        None,
    )
    assert hello_insert is not None
    assert hello_insert.payload["key"] == "color"
    assert hello_insert.payload["value"] == "blue"
    assert hello_insert.payload["score"] is None

    one_insert = next(
        (insert for insert in feedback_inserts if insert.payload["score"] == 1),
        None,
    )
    assert one_insert is not None
    assert one_insert.payload["key"] == "feedback"
    assert one_insert.payload["value"] is None
    assert one_insert.payload["score"] == 1

    feedback_inserts = []
    evaluator_feedbacks_ids = {
        str(run["id"]): [],
    }

    output = {
        "top_level_key": [
            {"nested_key": {"feedback": [{"color": "red", "reasoning": "because"}]}},
            {"feedback": [{"color": "blue", "reasoning": "because2"}]},
        ]
    }

    await parse_feedback(
        output,
        run,
        rule_id,
        metadata["run_id"],
        feedback_inserts,
        evaluator_feedbacks_ids,
    )
    assert len(feedback_inserts) == 4
    assert len(evaluator_feedbacks_ids[run["id"]]) == 4

    hello_insert = next(
        (insert for insert in feedback_inserts if insert.payload["value"] == "blue"),
        None,
    )
    assert hello_insert is not None
    assert hello_insert.payload["key"] == "color"
    assert hello_insert.payload["value"] == "blue"
    assert hello_insert.payload["score"] is None

    red_insert = next(
        (insert for insert in feedback_inserts if insert.payload["value"] == "red"),
        None,
    )
    assert red_insert is not None
    assert red_insert.payload["key"] == "color"
    assert red_insert.payload["value"] == "red"
    assert red_insert.payload["score"] is None

    one_insert = next(
        (insert for insert in feedback_inserts if insert.payload["value"] == "because"),
        None,
    )
    assert one_insert is not None
    assert one_insert.payload["key"] == "reasoning"
    assert one_insert.payload["value"] == "because"
    assert one_insert.payload["score"] is None

    two_insert = next(
        (
            insert
            for insert in feedback_inserts
            if insert.payload["value"] == "because2"
        ),
        None,
    )
    assert two_insert is not None
    assert two_insert.payload["key"] == "reasoning"
    assert two_insert.payload["value"] == "because2"
    assert two_insert.payload["score"] is None


async def test_run_rules_skip_no_data(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """Test that rule run filtering works correctly."""
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "longlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": "eq(is_root, true)",
            "display_name": "extend only rule",
            "sampling_rate": 1,
            "extend_only": True,
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # another feedback rule
    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": "and(eq(is_root, true), eq(feedback_key, 'accuracy'))",
            "display_name": "extend only rule",
            "sampling_rate": 1,
            "extend_only": True,
        },
    )
    assert response.status_code == 200, response.text
    feedback_rule_id = response.json()["id"]

    # Insert a run to see if the rule is applied
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": "an error",
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"20230505T051324571809Z{run_id}",
                }
            ]
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    await cron_schedule_apply_rules(rule_ids=[rule_id, feedback_rule_id])
    await wait_until_task_queue_empty()

    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id = $1", run_id
    )
    assert len(applications) == 1

    # check marker applications
    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id = $1 and rule_id = $2",
        UUID(int=0),
        rule_id,
    )
    assert len(applications) == 1

    # with mock.patch('app.models.runs.rules_apply._enqueue_one') as mock_enqueue_one:
    await cron_schedule_apply_rules(rule_ids=[rule_id, feedback_rule_id])
    await wait_until_task_queue_empty()

    # Should not enqueue since there shouldn't be new runs
    # mock_enqueue_one.assert_not_called()

    # check marker applications
    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id = $1 and rule_id = $2",
        UUID(int=0),
        rule_id,
    )
    assert len(applications) == 2

    # check marker applications for feedback rule
    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id = $1 and rule_id = $2",
        UUID(int=0),
        feedback_rule_id,
    )
    assert len(applications) == 2

    # check non-marker applications for feedback rule
    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id <> $1 and rule_id = $2",
        UUID(int=0),
        feedback_rule_id,
    )
    assert len(applications) == 0

    # Add feedback to the new run
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id),
            "key": "accuracy",
            "score": 1.0,
        },
    )
    assert response.status_code in (200, 202), response.text
    await wait_until_task_queue_empty()

    await cron_schedule_apply_rules(rule_ids=[rule_id, feedback_rule_id])
    await wait_until_task_queue_empty()

    # check run applications after feedback
    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where run_id <> $1 and rule_id = $2",
        UUID(int=0),
        feedback_rule_id,
    )
    assert len(applications) == 1


@asynccontextmanager
async def messages_kv_enabled_tenant_client(
    db_asyncpg: asyncpg.Connection, use_api_key: bool
) -> AsyncGenerator[FreshTenantClient, Any]:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        await db_asyncpg.execute(
            """
            UPDATE organizations
            SET config = config || '{"kv_dataset_message_support": true}'
            WHERE id = $1
            """,
            authed_client.auth.organization_id,
        )
        yield authed_client


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_transform_validate_add_to_dataset(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with messages_kv_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        client = authed_client.client

        response = await client.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text
        session_id = response.json()["id"]

        dataset = await create_chat_preset_dataset("my dataset", client)

        # Set up a rule with a valid secret
        response = await client.post(
            "/runs/rules",
            json={
                "session_id": session_id,
                "add_to_dataset_id": dataset["id"],
                "display_name": "new_cool_rule",
                "sampling_rate": 1,
            },
        )
        assert response.status_code == 200, response.text
        rule_id = response.json()["id"]

        # Submit two runs, one that is properly formatted and one that is not:

        run_id_1 = uuid4()
        run_id_2 = uuid4()
        # set start and end time since run rules uses these also for filtering
        chat_run_payload = deepcopy(chat_run_1_payload(session_id, run_id_1))
        chat_run_payload["start_time"] = datetime.now(timezone.utc).isoformat()
        chat_run_payload["end_time"] = (
            datetime.now(timezone.utc) + timedelta(seconds=20)
        ).isoformat()

        runs = [
            chat_run_payload,
            {
                "name": "AgentExecutor",
                "start_time": datetime.now(timezone.utc).isoformat(),
                "end_time": datetime.now(timezone.utc).isoformat(),
                "extra": {"foo": "bar", "metadata": {"key": 1}},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": session_id,
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id_2),
                "trace_id": str(run_id_2),
                "dotted_order": datetime.utcnow().strftime("%Y%m%dT%H%M%S%fZ")
                + str(run_id_2),
            },
        ]

        response = await client.post(
            "/runs/batch",
            json={"post": runs},
        )

        assert response.status_code == 202, response.text

        await wait_until_task_queue_empty()
        await cron_schedule_apply_rules(rule_ids=[rule_id])

        await wait_until_task_queue_empty()

        results = await client.get(
            "/examples",
            params={"dataset": dataset["id"]},
        )
        assert results.status_code == 200, results.text

        # Only the LLM run should be added since the other should fail validation
        assert len(results.json()) == 1
        assert results.json()[0]["source_run_id"] == str(run_id_1)

        # assert transformations were successful
        assert "messages" in results.json()[0]["inputs"]
        assert "tools" in results.json()[0]["inputs"]

        rule_applications_resp = await client.get(
            f"/runs/rules/{rule_id}/logs",
        )
        assert rule_applications_resp.status_code == 200, rule_applications_resp.text
        rule_applications = rule_applications_resp.json()

        assert len(rule_applications) == 3
        null_rule_applications = [
            app
            for app in rule_applications
            if app["run_id"] == "00000000-0000-0000-0000-000000000000"
        ]
        assert len(null_rule_applications) == 1
        assert null_rule_applications[0]["add_to_dataset"]["outcome"] == "error"

        run_1_rule_applications = [
            app for app in rule_applications if app["run_id"] == str(run_id_1)
        ]
        assert len(run_1_rule_applications) == 1
        assert run_1_rule_applications[0]["add_to_dataset"]["outcome"] == "success"
        assert (
            run_1_rule_applications[0]["add_to_dataset"]["payload"]["example_id"]
            == results.json()[0]["id"]
        )

        run_2_rule_applications = [
            app for app in rule_applications if app["run_id"] == str(run_id_2)
        ]
        assert len(run_2_rule_applications) == 1
        assert run_2_rule_applications[0]["add_to_dataset"]["outcome"] == "error"
        assert "error" in run_2_rule_applications[0]["add_to_dataset"]["payload"]


async def test_transient_rule(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a transient rule is deleted after being backfilled."""

    # Setup initial rule
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    evaluator = {
        "structured": {
            "prompt": [
                [
                    "system",
                    "You are evaluating the following value for toxicity, topic, and whether it's long.",
                ],
                [
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ],
            ],
            "schema": {
                "name": "eval",
                "description": "",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "toxic": {"type": "boolean"},
                    },
                    "required": ["toxic"],
                },
            },
            "model": {
                "lc": 1,
                "type": "constructor",
                "id": [
                    "langchain",
                    "chat_models",
                    "fake",
                    "FakeMessagesListChatModel",
                ],
                "name": "FakeMessagesListChatModel",
                "kwargs": {
                    "a_great_secret": {
                        "lc": 1,
                        "type": "secret",
                        "id": ["A_GREAT_SECRET"],
                    },
                    "responses": [
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain", "schema", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "",
                                "additional_kwargs": {
                                    "tool_calls": [
                                        {
                                            "function": {
                                                "name": "eval",
                                                "arguments": json.dumps(
                                                    {
                                                        "toxic": True,
                                                    }
                                                ),
                                            }
                                        }
                                    ]
                                },
                            },
                        }
                    ],
                },
            },
        }
    }

    response = await http_tenant_one.post(
        "/workspaces/current/secrets",
        json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
    )

    assert response.status_code == 200, response.text

    # Send runs that match the rule
    run_one = uuid4()
    run_two = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "How many people live in Canada?"},
                    "outputs": {"output": "38 million"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_one),
                    "trace_id": str(run_one),
                    "dotted_order": f"20230505T051324571809Z{run_one}",
                },
                {
                    "name": "Search",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Search"},
                    "inputs": {"input": "What's the capital of France?"},
                    "outputs": {"output": "Paris"},
                    "session_id": session_id,
                    "parent_run_id": None,
                    "run_type": "tool",
                    "id": str(run_two),
                    "trace_id": str(run_two),
                    "dotted_order": f"20230505T051324571809Z{run_two}",
                },
            ]
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)
    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "evaluators": [evaluator],
            "filter": 'eq(run_type, "tool")',
            "display_name": "transient_rule",
            "sampling_rate": 1,
            "transient": True,
            "backfill_from": one_hour_ago.isoformat(),
        },
    )
    assert response.status_code == 200, response.text
    rule_id = response.json()["id"]

    # Confirm that the rule was created
    rules = await db_asyncpg.fetch("SELECT * FROM run_rules WHERE id = $1", rule_id)
    assert len(rules) == 1

    # Apply the rule
    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    # Apply the rule again
    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    # Apply the rule again - online evals in rules only fetch one run at a time, so need to apply three times until the cursor is null
    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    # Confirm that feedback was added to the runs
    for run_id in [run_one, run_two]:
        feedbacks = await ch_client.fetch(
            "SELECT * FROM feedbacks WHERE run_id = {run}",
            params={"run": run_id},
        )
        assert len(feedbacks) == 1, (
            f"Expected 1 feedback for run {run_id}, got {len(feedbacks)}"
        )
        feedback = feedbacks[0]
        assert feedback["feedback_source"] is not None
        source = json.loads(feedback["feedback_source"])
        assert source["type"] == "auto_eval"
        assert source["metadata"]["rule_id"] == rule_id
        assert feedback["key"] == "toxic"
        assert feedback["score"] == 1.0

    # Confirm that the rule was deleted
    rules = await db_asyncpg.fetch("SELECT * FROM run_rules WHERE id = $1", rule_id)
    assert len(rules) == 0


@pytest.mark.flaky
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_annotation_queue_backfill(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a rule created to backfill a newly-created annotation queue works and is deleted as expected."""
    with mock.patch(
        "app.crud.ANNOTATION_QUEUE_SYNC_RUN_FETCH_LIMIT",
        new=1,
    ):
        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
            # Setup initial rule
            response = await authed_client.client.post(
                "/datasets",
                json={"name": random_lower_string()},
            )
            assert response.status_code == 200, response.text
            dataset_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in Canada?"},
                    "outputs": {"output": "38 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_1_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in the United States?"},
                    "outputs": {"output": "334 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_2_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in the United States?"},
                    "outputs": {"output": "334 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_3_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in the United States?"},
                    "outputs": {"output": "334 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_4_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in the United States?"},
                    "outputs": {"output": "334 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_5_id = response.json()["id"]

            response = await authed_client.client.post(
                "/sessions",
                json={
                    "name": random_lower_string(),
                    "trace_tier": "shortlived",
                    "reference_dataset_id": dataset_id,
                },
            )
            assert response.status_code == 200, response.text
            session_id = response.json()["id"]

            # Send runs that match the rule
            run_ids = [uuid4() for _ in range(5)]
            inputs = [random_lower_string() for _ in range(5)]
            outputs = [random_lower_string() for _ in range(5)]
            response = await authed_client.client.post(
                "/runs/batch",
                json={
                    "post": [
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[0]},
                            "outputs": {"output": outputs[0]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[0]),
                            "trace_id": str(run_ids[0]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[0]}",
                            "reference_example_id": example_1_id,
                        },
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[1]},
                            "outputs": {"output": outputs[1]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[1]),
                            "trace_id": str(run_ids[1]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[1]}",
                            "reference_example_id": example_2_id,
                        },
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[2]},
                            "outputs": {"output": outputs[2]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[2]),
                            "trace_id": str(run_ids[2]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[2]}",
                            "reference_example_id": example_3_id,
                        },
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[3]},
                            "outputs": {"output": outputs[3]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[3]),
                            "trace_id": str(run_ids[3]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[3]}",
                            "reference_example_id": example_4_id,
                        },
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[4]},
                            "outputs": {"output": outputs[4]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[4]),
                            "trace_id": str(run_ids[4]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[4]}",
                            "reference_example_id": example_5_id,
                        },
                    ]
                },
            )
            assert response.status_code == 202, response.text
            await wait_until_task_queue_empty()

            response = await authed_client.client.post(
                "/annotation-queues",
                json={
                    "session_ids": [session_id],
                    "name": random_lower_string(),
                },
            )
            assert response.status_code == 200, response.text
            annotation_queue_id = response.json()["id"]

            # Confirm that the rule was created
            rules = await db_asyncpg.fetch(
                "SELECT * FROM run_rules WHERE display_name ILIKE 'Annotation Queue backfill' AND tenant_id = $1",
                authed_client.auth.tenant_id,
            )
            if len(rules) != 1:
                all_rules = await db_asyncpg.fetch(
                    "SELECT * FROM run_rules WHERE tenant_id = $1",
                    authed_client.auth.tenant_id,
                )
                assert len(rules) == 1, (
                    f"Found no backfill rule in rules for tenant {authed_client.auth.tenant_id}: {all_rules}"
                )

            rule_id = rules[0]["id"]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/size"
            )
            assert response.status_code == 200
            assert response.json()["size"] == 1

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/0"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in [str(run_id) for run_id in run_ids]
            idx = run_ids.index(UUID(run["id"]))
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}"
            )
            assert response.status_code == 200
            assert response.json()["source_rule_id"] == str(rule_id)

            # Apply the rule
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Check that the other runs were added to the annotation queue
            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/size"
            )
            assert response.status_code == 200
            assert response.json()["size"] == 5

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/0"
            )
            assert response.status_code == 200
            run = response.json()
            run_id_strings = [str(run_id) for run_id in run_ids]
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/1"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/2"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/3"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/4"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            # Confirm that the rule was deleted
            rules = await db_asyncpg.fetch(
                "SELECT * FROM run_rules WHERE id = $1", rule_id
            )
            assert len(rules) == 0


@pytest.mark.flaky
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_existing_annotation_queue_backfill(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a rule created to backfill an EXISTING annotation queue works and is deleted as expected."""
    with mock.patch(
        "app.crud.ANNOTATION_QUEUE_SYNC_RUN_FETCH_LIMIT",
        new=1,
    ):
        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
            # Setup initial rule
            response = await authed_client.client.post(
                "/datasets",
                json={"name": random_lower_string()},
            )
            assert response.status_code == 200, response.text
            dataset_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in Canada?"},
                    "outputs": {"output": "38 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_1_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in the United States?"},
                    "outputs": {"output": "334 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_2_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in the United States?"},
                    "outputs": {"output": "334 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_3_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in the United States?"},
                    "outputs": {"output": "334 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_4_id = response.json()["id"]

            response = await authed_client.client.post(
                "/examples",
                json={
                    "dataset_id": dataset_id,
                    "inputs": {"input": "How many people live in the United States?"},
                    "outputs": {"output": "334 million"},
                },
            )
            assert response.status_code == 200, response.text
            example_5_id = response.json()["id"]

            response = await authed_client.client.post(
                "/sessions",
                json={
                    "name": random_lower_string(),
                    "trace_tier": "shortlived",
                    "reference_dataset_id": dataset_id,
                },
            )
            assert response.status_code == 200, response.text
            session_id = response.json()["id"]

            # Send runs that match the rule
            run_ids = [uuid4() for _ in range(5)]
            inputs = [random_lower_string() for _ in range(5)]
            outputs = [random_lower_string() for _ in range(5)]
            response = await authed_client.client.post(
                "/runs/batch",
                json={
                    "post": [
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[0]},
                            "outputs": {"output": outputs[0]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[0]),
                            "trace_id": str(run_ids[0]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[0]}",
                            "reference_example_id": example_1_id,
                        },
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[1]},
                            "outputs": {"output": outputs[1]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[1]),
                            "trace_id": str(run_ids[1]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[1]}",
                            "reference_example_id": example_2_id,
                        },
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[2]},
                            "outputs": {"output": outputs[2]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[2]),
                            "trace_id": str(run_ids[2]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[2]}",
                            "reference_example_id": example_3_id,
                        },
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[3]},
                            "outputs": {"output": outputs[3]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[3]),
                            "trace_id": str(run_ids[3]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[3]}",
                            "reference_example_id": example_4_id,
                        },
                        {
                            "name": "Search",
                            "start_time": datetime.now(timezone.utc).isoformat(),
                            "end_time": datetime.now(timezone.utc).isoformat(),
                            "extra": {"foo": "bar"},
                            "error": None,
                            "execution_order": 1,
                            "serialized": {"name": "Search"},
                            "inputs": {"input": inputs[4]},
                            "outputs": {"output": outputs[4]},
                            "session_id": session_id,
                            "parent_run_id": None,
                            "run_type": "tool",
                            "id": str(run_ids[4]),
                            "trace_id": str(run_ids[4]),
                            "dotted_order": f"20230505T051324571809Z{run_ids[4]}",
                            "reference_example_id": example_5_id,
                        },
                    ]
                },
            )
            assert response.status_code == 202, response.text
            await wait_until_task_queue_empty()

            response = await authed_client.client.post(
                "/annotation-queues",
                json={
                    "name": random_lower_string(),
                },
            )
            assert response.status_code == 200, response.text
            annotation_queue_id = response.json()["id"]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/size"
            )
            assert response.status_code == 200
            assert response.json()["size"] == 0

            response = await authed_client.client.post(
                "/annotation-queues/populate",
                json={
                    "queue_id": annotation_queue_id,
                    "session_ids": [session_id],
                },
            )
            assert response.status_code == 200, response.text

            # Confirm that the rule was created
            rules = await db_asyncpg.fetch(
                "SELECT * FROM run_rules WHERE display_name ILIKE 'Annotation Queue backfill' AND tenant_id = $1",
                authed_client.auth.tenant_id,
            )
            if len(rules) != 1:
                all_rules = await db_asyncpg.fetch(
                    "SELECT * FROM run_rules WHERE tenant_id = $1",
                    authed_client.auth.tenant_id,
                )
                assert len(rules) == 1, (
                    f"Found no backfill rule in rules for tenant {authed_client.auth.tenant_id}: {all_rules}"
                )

            rule_id = rules[0]["id"]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/size"
            )
            assert response.status_code == 200
            assert response.json()["size"] == 1

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/0"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in [str(run_id) for run_id in run_ids]
            idx = run_ids.index(UUID(run["id"]))
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}"
            )
            assert response.status_code == 200
            assert response.json()["source_rule_id"] == str(rule_id)

            # Apply the rule
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Apply the rule again
            await cron_schedule_apply_rules(rule_ids=[rule_id])
            await wait_until_task_queue_empty()

            # Check that the other runs were added to the annotation queue
            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/size"
            )
            assert response.status_code == 200
            assert response.json()["size"] == 5

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/0"
            )
            assert response.status_code == 200
            run = response.json()
            run_id_strings = [str(run_id) for run_id in run_ids]
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/1"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/2"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/3"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            response = await authed_client.client.get(
                f"/annotation-queues/{annotation_queue_id}/run/4"
            )
            assert response.status_code == 200
            run = response.json()
            assert run["id"] in run_id_strings
            idx = run_id_strings.index(run["id"])
            assert run["inputs"]["input"] == inputs[idx]
            assert run["outputs"]["output"] == outputs[idx]

            # Confirm that the rule was deleted
            rules = await db_asyncpg.fetch(
                "SELECT * FROM run_rules WHERE id = $1", rule_id
            )
            assert len(rules) == 0


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_run_rules_invalid_feedback(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        client = authed_client.client

        # Setup project and rules
        response = await client.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text
        session_id = response.json()["id"]
        setup = await _set_up_rules(client, session_id=session_id)

        # Insert a run
        run_id = uuid4()
        response = await client.post(
            "/runs/batch",
            json={
                "post": [
                    {
                        "name": "AgentExecutor",
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "AgentExecutor"},
                        "inputs": {
                            "input": "How many people live in canada as of 2023?"
                        },
                        "outputs": {"output": "39,566,248"},
                        "session_id": str(session_id),
                        "parent_run_id": None,
                        "run_type": "tool",
                        "tags": ["eval"],
                        "id": str(run_id),
                        "trace_id": str(run_id),
                        "dotted_order": f"20230505T051324571809Z{run_id}",
                    }
                ]
            },
        )
        assert response.status_code == 202, response.text
        await wait_until_task_queue_empty()

        # Get online eval rule
        rules_resp = await list_rules(auth, id=[setup.rule_online_evals_id])
        assert len(rules_resp) == 1
        rule = rules_resp[0]
        rule_dict = rule.model_dump(by_alias=True)
        with mock.patch(
            "app.models.runs.rule_application.online_eval.upsert_feedback",
            side_effect=ChClientError(
                "Code: 69. DB::Exception: Decimal value is too big (ARGUMENT_OUT_OF_BOUND)"
            ),
        ):
            await apply_run_rule(
                start_time=(
                    datetime.now(timezone.utc) - timedelta(minutes=1)
                ).isoformat(),
                end_time=datetime.now(timezone.utc).isoformat(),
                rule_dict=rule_dict,
            )

        # Check that the rule was applied and failure recorded
        response = await client.get(f"/runs/rules/{setup.rule_online_evals_id}/logs")
        assert response.status_code == 200, response.text
        rule_logs = response.json()
        assert len(rule_logs) == 2
        null_rule_log = next(
            log for log in rule_logs if log["run_id"] == str(UUID(int=0))
        )
        run_rule_log = next(log for log in rule_logs if log["run_id"] == str(run_id))
        assert null_rule_log["evaluators"] is not None, (
            f"Expected {null_rule_log} to have evaluators"
        )
        assert null_rule_log["evaluators"]["outcome"] == "error", (
            f"Expected {null_rule_log} to have outcome error"
        )
        assert run_rule_log["evaluators"] is None, (
            f"Expected {run_rule_log} to NOT have evaluators"
        )


async def test_uncommitted_rules(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
) -> None:
    """Test uncommitted run rules."""
    setup = await _set_up_rules(http_tenant_one)

    await cron_schedule_apply_rules(
        rule_ids=[
            setup.rule_send_to_dataset_id,
        ]
    )
    await wait_until_task_queue_empty()

    send_to_ds_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1",
        setup.rule_send_to_dataset_id,
    )
    assert len(send_to_ds_applications) == 1
    assert send_to_ds_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_ds_applications[0]["start_time"].isoformat()
        == setup.rule_send_to_dataset_created_at
    )
    assert send_to_ds_applications[0]["committed"] is True
    initial_application = send_to_ds_applications[0]

    new_start_time = send_to_ds_applications[0]["end_time"]
    new_end_time = new_start_time + timedelta(seconds=1)

    await db_asyncpg.execute(
        "INSERT INTO run_rules_applications (rule_id, run_id, start_time, end_time, committed) VALUES ($1, $2, $3, $4, $5)",
        setup.rule_send_to_dataset_id,
        UUID(int=0),
        new_start_time,
        new_end_time,
        False,
    )

    send_to_ds_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1 ORDER BY start_time DESC",
        setup.rule_send_to_dataset_id,
    )
    assert len(send_to_ds_applications) == 2
    assert send_to_ds_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_ds_applications[0]["start_time"].isoformat()
        != setup.rule_send_to_dataset_created_at
    )
    assert send_to_ds_applications[0]["committed"] is False

    # force trigger rule since there may not be runs for it
    await trigger_rule(auth_tenant_one, setup.rule_send_to_dataset_id, db_asyncpg)
    await wait_until_task_queue_empty()

    send_to_ds_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1 and committed=True ORDER BY start_time DESC, end_time DESC",
        setup.rule_send_to_dataset_id,
    )
    assert len(send_to_ds_applications) == 2
    assert send_to_ds_applications[0]["run_id"] == UUID(int=0)
    assert (
        send_to_ds_applications[0]["start_time"].isoformat()
        != setup.rule_send_to_dataset_created_at
    )
    assert send_to_ds_applications[0]["committed"] is True
    assert send_to_ds_applications[0]["start_time"] > initial_application["start_time"]
    assert send_to_ds_applications[0]["end_time"] > initial_application["end_time"]


@pytest.mark.parametrize(
    "filter_expression,expected_error",
    [
        (
            'and(eq(is_root, true), and(eq(feedback_key, "feedback"), gte(feedback_score, 0.5)), and(eq(feedback_value_key, "is_bias"), eq(feedback_value, "0")))',
            "Attribute feedback_value_key not accepted.",
        ),
        (
            'eq(is_root, "positive")',
            'Filter expression is invalid: eq(is_root, "positive")',
        ),
    ],
)
async def test_run_rules_non_transient_errors(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    filter_expression: str,
    expected_error: str,
):
    """
    Test that rules with non-transient errors failed and committed.
    """

    response = await http_tenant_one.post(
        "/annotation-queues",
        json={"name": random_lower_string()},
    )
    assert response.status_code == 200, response.text
    annotation_queue_id = response.json()["id"]

    # Create a session
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    # Create a run with feedback
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "TestRun",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "execution_order": 1,
                    "serialized": {"name": "TestRun"},
                    "inputs": {"input": "Test input"},
                    "outputs": {"output": "Test output"},
                    "session_id": str(session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"20230505T051324571809Z{run_id}",
                }
            ]
        },
    )
    assert response.status_code == 202, response.text

    # Add feedback to the run
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id),
            "key": "feedback",
            "score": 0.8,
            "value": {"is_bias": "0"},
        },
    )
    assert response.status_code in (202, 200), response.text
    await wait_until_task_queue_empty()

    # Create a rule with a non-transient filter error
    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": filter_expression,
            "display_name": "rule with non-transient error",
            "sampling_rate": 1,
            "add_to_annotation_queue_id": annotation_queue_id,
        },
    )
    assert response.status_code == 200, response.text
    non_transient_rule_id = response.json()["id"]

    # Apply the rule
    await cron_schedule_apply_rules(rule_ids=[non_transient_rule_id])
    await wait_until_task_queue_empty()

    # Check applications for non-transient rule - should have one failed attempt
    non_transient_applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1",
        non_transient_rule_id,
    )
    assert len(non_transient_applications) == 1
    assert non_transient_applications[0]["committed"] is True
    assert (
        non_transient_applications[0]["add_to_annotation_queue"]["outcome"] == "error"
    )
    assert non_transient_applications[0]["add_to_annotation_queue"]["payload"] == {
        "error": expected_error
    }


async def test_run_rules_transient_errors(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """
    Test that rules with transient errors and retries.
    """

    # Define how your "fake" _enqueue_one should behave.
    async def fake_enqueue_one(
        semaphore, start_time, end_time, rule, query_cursors, queue
    ):
        return await apply_run_rule(
            start_time.isoformat(),
            end_time.isoformat(),
            rule,
            query_cursors=query_cursors,
        )

    response = await http_tenant_one.post(
        "/annotation-queues",
        json={"name": random_lower_string()},
    )
    assert response.status_code == 200, response.text
    annotation_queue_id = response.json()["id"]

    # Create a session
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    # Create a run with feedback
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "TestRun",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "execution_order": 1,
                    "serialized": {"name": "TestRun"},
                    "inputs": {"input": "Test input"},
                    "outputs": {"output": "Test output"},
                    "session_id": str(session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"20230505T051324571809Z{run_id}",
                }
            ]
        },
    )
    assert response.status_code == 202, response.text

    # Add feedback to the run
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id),
            "key": "feedback",
            "score": 0.8,
            "value": {"is_bias": "0"},
        },
    )
    assert response.status_code in (202, 200), response.text
    await wait_until_task_queue_empty()

    # Create a rule
    filter = "eq(is_root, true)"
    response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "session_id": session_id,
            "filter": filter,
            "display_name": "rule with non-transient error",
            "sampling_rate": 1,
            "add_to_annotation_queue_id": annotation_queue_id,
        },
    )
    assert response.status_code == 200, response.text
    transient_rule_id = response.json()["id"]

    # Apply the rule and patch to raise transient error with 1 retry
    with (
        mock.patch(
            "app.models.runs.rules_apply._enqueue_one", new_callable=mock.AsyncMock
        ) as mock_enqueue_one,
        mock.patch(
            "app.models.runs.rule_application.add_to_annotation_queue.AddToAnnotationQueueRuleApplier.apply",
            new_callable=mock.AsyncMock,
        ) as mock_add_to_queue,
        mock.patch("app.models.runs.rules_apply.settings.RUN_RULES_MAX_RETRIES", 1),
    ):
        # Make the patched mock call our fake_enqueue_one.
        mock_enqueue_one.side_effect = fake_enqueue_one
        # Make the annotation queue applier raise an error
        mock_add_to_queue.side_effect = Exception("Transient error")

        await cron_schedule_apply_rules(rule_ids=[transient_rule_id])

        # Check applications for transient error rule - should have one failed attempt
        non_transient_applications = await db_asyncpg.fetch(
            "select * from run_rules_applications where rule_id = $1 and run_id = $2",
            transient_rule_id,
            NULL_UUID,
        )
        assert len(non_transient_applications) == 1
        assert non_transient_applications[0]["committed"] is False

        # run rule again but retries exceeded
        await cron_schedule_apply_rules(rule_ids=[transient_rule_id])

        # Check applications for transient error rule - should have maxed out retries
        non_transient_applications = await db_asyncpg.fetch(
            "select * from run_rules_applications where rule_id = $1 and run_id = $2",
            transient_rule_id,
            NULL_UUID,
        )
        assert len(non_transient_applications) == 1
        assert non_transient_applications[0]["committed"] is True
        assert (
            non_transient_applications[0]["add_to_annotation_queue"]["outcome"]
            == "error"
        )
        assert non_transient_applications[0]["add_to_annotation_queue"]["payload"] == {
            "error": "Rule application retries limit reached: 1"
        }


async def test_feedback_and_token_stats_included_for_webhooks(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
) -> None:
    # Create a session
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "trace_tier": "shortlived"},
    )
    assert response.status_code == 200, response.text
    session_id = response.json()["id"]

    # Create a run with feedback
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "LLM1",
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "end_time": datetime.now(timezone.utc).isoformat(),
                    "extra": {"foo": "bar", "batch_size": 1},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {
                        "prompts": ["How many people live in canada as of 2023?"]
                    },
                    "outputs": {
                        "generations": [[{"text": "39,566,248"}]],
                        "llm_output": {
                            "token_usage": {
                                "prompt_tokens": 599,
                                "completion_tokens": 401,
                                "total_tokens": 1000,
                            },
                        },
                    },
                    "session_id": str(session_id),
                    "parent_run_id": None,
                    "run_type": "llm",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"20230505T051324571809Z{run_id}",
                }
            ]
        },
    )
    assert response.status_code == 202, response.text

    # Add feedback to the run
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id),
            "key": "feedback",
            "score": 0.8,
            "value": {"is_bias": "0"},
        },
    )
    assert response.status_code in (202, 200), response.text
    await wait_until_task_queue_empty()

    # Create a rule with a webhook
    rule_response = await http_tenant_one.post(
        "/runs/rules",
        json={
            "display_name": "test-webhook-feedback-stats",
            "session_id": session_id,
            "sampling_rate": 1.0,
            "webhooks": [{"url": "http://foo.com/webhook"}],
        },
    )
    rule_id = rule_response.json()["id"]

    await cron_schedule_apply_rules(rule_ids=[rule_id])
    await wait_until_task_queue_empty()

    # Check applications
    applications = await db_asyncpg.fetch(
        "select * from run_rules_applications where rule_id = $1",
        rule_id,
    )
    assert applications
    assert all(rule_app["committed"] for rule_app in applications)
    assert any(rule_app["webhooks"] for rule_app in applications)
    assert all(
        rule_app["webhooks"]["outcome"] == "success"
        and "errors" not in (rule_app["webhooks"].get("payload") or {})
        for rule_app in applications
        if rule_app["webhooks"]
    )


async def test_chain_with_rule_name(
    auth_tenant_one: AuthInfo,
    db_asyncpg: asyncpg.Connection,
) -> None:
    """Test that get_chain_from_evaluator correctly sets the rule name."""
    evaluator = {
        "structured": {
            "prompt": [
                ["system", "You are an evaluator."],
                [
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ],
            ],
            "schema": {
                "type": "object",
                "name": "eval",
                "description": "",
            },
            "model": {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain", "chat_models", "fake", "FakeMessagesListChatModel"],
                "kwargs": {
                    "responses": [
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain", "schema", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "",
                                "additional_kwargs": {
                                    "tool_calls": [
                                        {
                                            "function": {
                                                "name": "eval",
                                                "arguments": json.dumps({"score": 1}),
                                            }
                                        }
                                    ]
                                },
                            },
                        }
                    ],
                },
            },
        }
    }

    # Test with rule name
    chain, _ = await get_chain_from_evaluator(
        schemas.EvaluatorTopLevel.model_validate(evaluator),
        auth_tenant_one,
        db_asyncpg,
        rule_name="test_rule",
    )
    assert chain["kwargs"]["name"] == "test_rule"

    # Test without rule name
    chain, _ = await get_chain_from_evaluator(
        schemas.EvaluatorTopLevel.model_validate(evaluator), auth_tenant_one, db_asyncpg
    )
    assert chain["kwargs"]["name"] is None


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_creating_rule_from_existing_prompt(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        start_time = datetime.now(timezone.utc) + timedelta(days=1)
        end_time = start_time + timedelta(seconds=8)

        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        client = authed_client.client
        repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
        response = await client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )

        assert response.status_code == 200, response.text

        repo_full_name = response.json()["repo"]["full_name"]

        structured_prompt = StructuredPrompt.from_messages_and_schema(
            [
                (
                    "system",
                    "You are evaluating the following value for toxicity, topic, and whether it's long.",
                ),
                (
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ),
            ],
            {
                "name": "eval",
                "description": "",
                "parameters": {
                    "type": "object",
                    "properties": {"is_it_great": {"type": "string"}},
                    "required": ["is_it_great"],
                },
            },
        )
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={"manifest": dumpd(structured_prompt)},
        )

        assert response.status_code == 200, response.text
        await wait_until_task_queue_empty()

        response = await client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text

        dataset_id = response.json()["id"]

        response = await client.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )

        assert response.status_code == 200, response.text

        # Hub ref evaluator
        raw_prompt_evaluator, hub_ref_evaluator = get_evaluators(repo_full_name)
        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "first rule",
                "sampling_rate": 1,
                "evaluators": [hub_ref_evaluator],
            },
        )

        assert response.status_code == 200, response.text
        rule_id = response.json()["id"]

        response = await client.get(
            "/runs/rules",
        )
        assert response.status_code == 200

        # Create 10 examples (this is the limit)
        examples = []
        example_ids = [uuid4() for _ in range(10)]
        for i in range(10):
            examples.append(
                {
                    "inputs": {"input": f"test input {i}"},
                    "outputs": {"output": f"test output {i}"},
                    "dataset_id": str(dataset_id),
                    "id": str(example_ids[i]),
                }
            )

        response = await client.post("/examples/bulk", json=examples)
        assert response.status_code == 200

        # Create a experiment session
        response = await client.post(
            "/sessions",
            json={
                "name": "csv_session_1",
                "reference_dataset_id": str(dataset_id),
            },
        )
        assert response.status_code == 200
        session_id = response.json()["id"]

        runs = []
        run_ids = []
        for example_id in example_ids:
            for i in range(2):
                run_id = uuid4()
                run_ids.append(run_id)
                runs.append(
                    {
                        "name": "Target",
                        "start_time": start_time.isoformat(),
                        "end_time": end_time.isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Target"},
                        "inputs": {"input": f"{str(example_id)} {i}"},
                        "outputs": {
                            "generations": [
                                [
                                    {
                                        "text": str(example_id),
                                        "generation_info": {
                                            "finish_reason": "stop",
                                            "logprobs": None,
                                        },
                                        "type": "ChatGeneration",
                                        "message": {
                                            "lc": 1,
                                            "type": "constructor",
                                            "id": [
                                                "langchain",
                                                "schema",
                                                "messages",
                                                "AIMessage",
                                            ],
                                            "kwargs": {
                                                "content": "In this morning's meeting, we successfully resolved all world conflicts.",
                                                "additional_kwargs": {"refusal": None},
                                                "response_metadata": {
                                                    "token_usage": {
                                                        "completion_tokens": i,
                                                        "prompt_tokens": i,
                                                        "total_tokens": 2 * i,
                                                        "completion_tokens_details": {
                                                            "reasoning_tokens": 0
                                                        },
                                                        "prompt_tokens_details": {
                                                            "cached_tokens": 0
                                                        },
                                                    },
                                                    "model_name": "gpt-4o-2024-08-06",
                                                    "system_fingerprint": "fp_45cf54deae",
                                                    "finish_reason": "stop",
                                                    "logprobs": None,
                                                },
                                                "type": "ai",
                                                "id": "run-ea314874-92ff-4eb5-8e18-277b07f4cef9-0",
                                                "usage_metadata": {
                                                    "input_tokens": 3 * i,
                                                    "output_tokens": i,
                                                    "total_tokens": 4 * i,
                                                    "input_token_details": {
                                                        "cache_read": 0
                                                    },
                                                    "output_token_details": {
                                                        "reasoning": 0
                                                    },
                                                },
                                                "tool_calls": [],
                                                "invalid_tool_calls": [],
                                            },
                                        },
                                    }
                                ]
                            ],
                            "llm_output": {
                                "token_usage": {
                                    "completion_tokens": 3 * i,
                                    "prompt_tokens": i,
                                    "total_tokens": 4 * i,
                                    "completion_tokens_details": {
                                        "reasoning_tokens": 0
                                    },
                                    "prompt_tokens_details": {"cached_tokens": 0},
                                },
                                "model_name": "gpt-4o-2024-08-06",
                                "system_fingerprint": "fp_45cf54deae",
                            },
                            "run": None,
                            "type": "LLMResult",
                        },
                        "session_id": str(session_id),
                        "reference_example_id": str(example_id),
                        "parent_run_id": None,
                        "run_type": "llm",
                        "id": str(run_id),
                        "trace_id": str(run_id),
                        "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%f')}Z{run_id}",
                        "total_tokens": 4 * i,
                    },
                )

        response = await client.post(
            "/runs/batch",
            json={
                "post": [
                    {k: v for k, v in run.items() if k != "total_tokens"}
                    for run in runs
                ],
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        await cron_schedule_apply_rules(rule_ids=[rule_id])
        await wait_until_task_queue_empty()

        response = await client.get(f"/runs/rules/{rule_id}/logs")

        assert response.status_code == 200
        # the NULL application, 20 runs from the experiment
        assert len(response.json()) == 21

        # Assert that patching doesn't change the evaluator
        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
            },
        )
        assert response.status_code == 200, response.text
        await wait_until_task_queue_empty()

        response = await client.get(
            f"/runs/rules?id={rule_id}",
        )
        assert response.status_code == 200
        assert response.json()[0]["display_name"] == "modified first rule"
        assert (
            response.json()[0]["evaluators"][0]["structured"]["hub_ref"].split(":")[0]
            == repo_handle
        )

        response = await client.get(f"/repos/-/{repo_handle}")
        assert response.status_code == 200
        assert response.json()["repo"]["num_commits"] == 1

        # Assert that patching evaluator creates new commit
        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
            },
        )
        assert response.status_code == 200, response.text

        response = await client.get(
            f"/runs/rules?id={rule_id}",
        )
        assert response.status_code == 200
        assert (
            response.json()[0]["evaluators"][0]["structured"]["hub_ref"].split(":")[0]
            != repo_handle
        )

        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "first rule",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
            },
        )
        assert response.status_code == 200, response.text
        rule_2_id = response.json()["id"]

        await cron_schedule_apply_rules(rule_ids=[rule_2_id])
        await wait_until_task_queue_empty()

        # edit the rule and rerun

        response = await client.get(f"/runs/rules/{rule_2_id}/logs")

        assert response.status_code == 200
        # the NULL application, 20 runs from the experiment
        assert len(response.json()) == 21

        response = await client.get(
            f"/runs/rules?id={rule_2_id}",
        )
        assert response.status_code == 200
        original_rule_2_repo_handle = response.json()[0]["evaluators"][0]["structured"][
            "hub_ref"
        ].split(":")[0]

        response = await client.patch(
            f"/runs/rules/{rule_2_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
            },
        )
        assert response.status_code == 200, response.text

        response = await client.get(
            f"/runs/rules?id={rule_2_id}",
        )
        assert response.status_code == 200
        assert (
            response.json()[0]["evaluators"][0]["structured"]["hub_ref"].split(":")[0]
            != original_rule_2_repo_handle
        )


async def test_create_rule_with_llm_evaluator_prompt_ownership(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test creating a rule with LLM evaluator only works when selecting a prompt that belongs to you."""

    # Create two separate tenants
    async with fresh_tenant_client(db_asyncpg, use_api_key) as tenant_a:
        async with fresh_tenant_client(db_asyncpg, use_api_key) as tenant_b:
            # Tenant A creates a repo and prompt
            repo_handle_a = "repo_" + "".join(
                random.choices(string.ascii_lowercase, k=20)
            )
            response = await tenant_a.client.post(
                "/repos/",
                json={"repo_handle": repo_handle_a, "is_public": False},
            )
            assert response.status_code == 200, response.text
            repo_full_name_a = response.json()["repo"]["full_name"]

            # Tenant A commits a structured prompt
            response = await tenant_a.client.post(
                f"/commits/{repo_full_name_a}",
                json={"manifest": dumpd(STRUCTURED_PROMPT)},
            )
            assert response.status_code == 200, response.text
            await wait_until_task_queue_empty()

            # Tenant A creates a dataset
            response = await tenant_a.client.post(
                "/datasets",
                json={"name": random_lower_string()},
            )
            assert response.status_code == 200, response.text
            dataset_id_a = response.json()["id"]

            # Tenant B creates a dataset
            response = await tenant_b.client.post(
                "/datasets",
                json={"name": random_lower_string()},
            )
            assert response.status_code == 200, response.text
            dataset_id_b = response.json()["id"]

            response = await tenant_b.client.post(
                "/workspaces/current/secrets",
                json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
            )
            assert response.status_code == 200, response.text
            response = await tenant_a.client.post(
                "/workspaces/current/secrets",
                json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
            )
            assert response.status_code == 200, response.text

            # Create evaluator that references tenant A's prompt
            hub_ref_evaluator = {
                "structured": {
                    "hub_ref": repo_full_name_a,
                    "model": {
                        "lc": 1,
                        "type": "constructor",
                        "id": [
                            "langchain",
                            "chat_models",
                            "fake",
                            "FakeMessagesListChatModel",
                        ],
                        "name": "FakeMessagesListChatModel",
                        "kwargs": {
                            "a_great_secret": {
                                "lc": 1,
                                "type": "secret",
                                "id": ["A_GREAT_SECRET"],
                            },
                            "responses": [
                                {
                                    "lc": 1,
                                    "type": "constructor",
                                    "id": [
                                        "langchain",
                                        "schema",
                                        "messages",
                                        "AIMessage",
                                    ],
                                    "kwargs": {
                                        "content": "",
                                        "additional_kwargs": {
                                            "tool_calls": [
                                                {
                                                    "function": {
                                                        "name": "eval",
                                                        "arguments": json.dumps(
                                                            {"is_it_great": True}
                                                        ),
                                                    }
                                                }
                                            ]
                                        },
                                    },
                                }
                            ],
                        },
                    },
                }
            }

            # Tenant B tries to create a rule using tenant A's prompt - should fail
            response = await tenant_b.client.post(
                "/runs/rules",
                json={
                    "dataset_id": dataset_id_b,
                    "filter": "eq(is_root, true)",
                    "display_name": "rule with other tenant's prompt",
                    "sampling_rate": 1,
                    "evaluators": [hub_ref_evaluator],
                },
            )
            assert response.status_code == 404, (
                f"Expected 404 but got {response.status_code}: {response.text}"
            )

            # Tenant A creates a rule using their own prompt - should succeed
            response = await tenant_a.client.post(
                "/runs/rules",
                json={
                    "dataset_id": dataset_id_a,
                    "filter": "eq(is_root, true)",
                    "display_name": "rule with own prompt",
                    "sampling_rate": 1,
                    "evaluators": [hub_ref_evaluator],
                },
            )
            assert response.status_code == 200, response.text


async def test_update_rule_with_llm_evaluator_prompt_ownership(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test updating a rule only works with prompts that belong to you."""

    # Create two separate tenants
    async with fresh_tenant_client(db_asyncpg, use_api_key) as tenant_a:
        async with fresh_tenant_client(db_asyncpg, use_api_key) as tenant_b:
            # Tenant A creates a repo and prompt
            repo_handle_a = "repo_" + "".join(
                random.choices(string.ascii_lowercase, k=20)
            )
            response = await tenant_a.client.post(
                "/repos/",
                json={"repo_handle": repo_handle_a, "is_public": False},
            )
            assert response.status_code == 200, response.text
            repo_full_name_a = response.json()["repo"]["full_name"]

            response = await tenant_a.client.post(
                f"/commits/{repo_full_name_a}",
                json={"manifest": dumpd(STRUCTURED_PROMPT)},
            )
            assert response.status_code == 200, response.text
            await wait_until_task_queue_empty()

            # Tenant B creates a repo and prompt
            repo_handle_b = "repo_" + "".join(
                random.choices(string.ascii_lowercase, k=20)
            )
            response = await tenant_b.client.post(
                "/repos/",
                json={"repo_handle": repo_handle_b, "is_public": False},
            )
            assert response.status_code == 200, response.text
            repo_full_name_b = response.json()["repo"]["full_name"]

            response = await tenant_b.client.post(
                f"/commits/{repo_full_name_b}",
                json={"manifest": dumpd(STRUCTURED_PROMPT)},
            )
            assert response.status_code == 200, response.text
            await wait_until_task_queue_empty()

            # Tenant A creates a dataset and rule
            response = await tenant_a.client.post(
                "/datasets",
                json={"name": random_lower_string()},
            )
            assert response.status_code == 200, response.text
            dataset_id_a = response.json()["id"]

            response = await tenant_b.client.post(
                "/workspaces/current/secrets",
                json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
            )
            assert response.status_code == 200, response.text
            response = await tenant_a.client.post(
                "/workspaces/current/secrets",
                json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
            )
            assert response.status_code == 200, response.text

            # Tenant A creates a rule with their own prompt
            hub_ref_evaluator_a = {
                "structured": {
                    "hub_ref": repo_full_name_a,
                    "model": {
                        "lc": 1,
                        "type": "constructor",
                        "id": [
                            "langchain",
                            "chat_models",
                            "fake",
                            "FakeMessagesListChatModel",
                        ],
                        "name": "FakeMessagesListChatModel",
                        "kwargs": {
                            "a_great_secret": {
                                "lc": 1,
                                "type": "secret",
                                "id": ["A_GREAT_SECRET"],
                            },
                            "responses": [
                                {
                                    "lc": 1,
                                    "type": "constructor",
                                    "id": [
                                        "langchain",
                                        "schema",
                                        "messages",
                                        "AIMessage",
                                    ],
                                    "kwargs": {
                                        "content": "",
                                        "additional_kwargs": {
                                            "tool_calls": [
                                                {
                                                    "function": {
                                                        "name": "eval",
                                                        "arguments": json.dumps(
                                                            {"is_it_great": True}
                                                        ),
                                                    }
                                                }
                                            ]
                                        },
                                    },
                                }
                            ],
                        },
                    },
                }
            }

            response = await tenant_a.client.post(
                "/runs/rules",
                json={
                    "dataset_id": dataset_id_a,
                    "filter": "eq(is_root, true)",
                    "display_name": "rule to update",
                    "sampling_rate": 1,
                    "evaluators": [hub_ref_evaluator_a],
                },
            )
            assert response.status_code == 200, response.text
            rule_id = response.json()["id"]

            # Create evaluator that references tenant B's prompt
            hub_ref_evaluator_b = {
                "structured": {
                    "hub_ref": repo_full_name_b,
                    "model": {
                        "lc": 1,
                        "type": "constructor",
                        "id": [
                            "langchain",
                            "chat_models",
                            "fake",
                            "FakeMessagesListChatModel",
                        ],
                        "name": "FakeMessagesListChatModel",
                        "kwargs": {
                            "responses": [
                                {
                                    "lc": 1,
                                    "type": "constructor",
                                    "id": [
                                        "langchain",
                                        "schema",
                                        "messages",
                                        "AIMessage",
                                    ],
                                    "kwargs": {
                                        "content": "",
                                        "additional_kwargs": {
                                            "tool_calls": [
                                                {
                                                    "function": {
                                                        "name": "eval",
                                                        "arguments": json.dumps(
                                                            {"is_it_great": True}
                                                        ),
                                                    }
                                                }
                                            ]
                                        },
                                    },
                                }
                            ],
                        },
                    },
                }
            }

            # Tenant A tries to update their rule to use tenant B's prompt - should fail
            response = await tenant_a.client.patch(
                f"/runs/rules/{rule_id}",
                json={
                    "dataset_id": dataset_id_a,
                    "display_name": "updated rule with other tenant's prompt",
                    "sampling_rate": 1,
                    "evaluators": [hub_ref_evaluator_b],
                },
            )
            assert response.status_code == 404, (
                f"Expected 404 but got {response.status_code}: {response.text}"
            )

            # Tenant A updates their rule with their own prompt - should succeed
            response = await tenant_a.client.patch(
                f"/runs/rules/{rule_id}",
                json={
                    "dataset_id": dataset_id_a,
                    "display_name": "updated rule with own prompt",
                    "sampling_rate": 1,
                    "evaluators": [hub_ref_evaluator_a],
                },
            )
            assert response.status_code == 200, response.text


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_db_deletes_work_for_run_rules_with_v3_evaluators(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        client = authed_client.client
        repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
        response = await client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )
        assert response.status_code == 200, response.text
        repo_id = response.json()["repo"]["id"]

        tenant_id = authed_client.auth.tenant_id
        queue_data = {
            "name": random_lower_string(),
            "description": "test",
            "tenant_id": str(tenant_id),
        }
        response = await client.post("/annotation-queues", json=queue_data)
        assert response.status_code == 200
        annotation_queue_id = response.json()["id"]

        evaluator_id = uuid4()

        await db_asyncpg.execute(
            """INSERT INTO evaluators (id, name) VALUES ($1, $2)""",
            evaluator_id,
            "foobar",
        )

        await db_asyncpg.execute(
            """INSERT INTO llm_evaluators (evaluator_id, prompt_id, annotation_queue_id) VALUES ($1, $2, $3)""",
            evaluator_id,
            repo_id,
            annotation_queue_id,
        )

        # Create a run rule with v3 evaluator with annotation queue
        await db_asyncpg.execute(
            """INSERT INTO run_rules (tenant_id, evaluator_id, sampling_rate, display_name) VALUES ($1, $2, $3, $4)""",
            tenant_id,
            evaluator_id,
            1.0,
            "foobar",
        )

        row = await db_asyncpg.fetch(
            """
            select rr.id, e.id, le.annotation_queue_id, aq.name
            from run_rules rr
            join evaluators e on e.id = rr.evaluator_id
            join llm_evaluators le on le.evaluator_id = e.id
            join annotation_queues aq on aq.id = le.annotation_queue_id
            where rr.tenant_id = $1
            """,
            tenant_id,
        )
        assert len(row) == 1

        await db_asyncpg.execute(
            """
            delete from run_rules where evaluator_id = $1 and tenant_id = $2
            """,
            evaluator_id,
            tenant_id,
        )

        row = await db_asyncpg.fetchrow(
            """
            select 
                (select count(*) from run_rules where tenant_id=$1) as run_rules_count,
                (select count(*) from evaluators join run_rules on run_rules.evaluator_id = evaluators.id where run_rules.tenant_id = $1) as evaluators_count,
                (select count(*) from llm_evaluators join run_rules on run_rules.evaluator_id = llm_evaluators.evaluator_id where run_rules.tenant_id = $1) as llm_evaluators_count,
                (select count(*) from annotation_queues where tenant_id = $1) as annotation_queues_count
            """,
            tenant_id,
        )

        assert row["run_rules_count"] == 0, f"run_rules_count: {row['run_rules_count']}"
        assert row["evaluators_count"] == 0, (
            f"evaluators_count: {row['evaluators_count']}"
        )
        assert row["llm_evaluators_count"] == 0, (
            f"llm_evaluators_count: {row['llm_evaluators_count']}"
        )
        assert row["annotation_queues_count"] == 0, (
            f"annotation_queues_count: {row['annotation_queues_count']}"
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_alignment_evaluator(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
        response = await client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )

        assert response.status_code == 200, response.text

        repo_full_name = response.json()["repo"]["full_name"]

        response = await client.post(
            f"/commits/{repo_full_name}",
            json={"manifest": dumpd(STRUCTURED_PROMPT)},
        )

        assert response.status_code == 200, response.text
        await wait_until_task_queue_empty()

        response = await client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text

        dataset_id = response.json()["id"]

        response = await client.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )

        assert response.status_code == 200, response.text

        raw_prompt_evaluator, _ = get_evaluators(repo_full_name)
        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "first rule",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
            },
        )
        assert response.status_code == 200, response.text

        raw_prompt_evaluator["structured"]["schema"] = {
            "title": "extract",
            "description": "Extract information from the user's response.",
            "type": "object",
            "properties": {
                "correctness": {
                    "type": "boolean",
                    "description": "Is the submission correct, accurate, and factual?",
                }
            },
            "required": ["correctness"],
        }
        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "first rule",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
            },
        )
        assert response.status_code == 200
        run_rule_id = response.json()["id"]

        queue_data = {
            "name": random_lower_string(),
            "description": "test",
            "tenant_id": str(authed_client.auth.tenant_id),
        }
        response = await client.post("/annotation-queues", json=queue_data)
        assert response.status_code == 200

        response = await client.get(f"/runs/rules?id={run_rule_id}")
        assert response.status_code == 200
        annotation_queue_id = response.json()[0]["alignment_annotation_queue_id"]
        assert annotation_queue_id
        await wait_until_task_queue_empty()

        response = await client.get("/annotation-queues?limit=2&offset=0")
        assert response.status_code == 200
        assert len(response.json()) == 2
        assert (
            response.json()[0]["run_rule_id"] is None
            or response.json()[1]["run_rule_id"] is None
        )
        assert (
            response.json()[0]["run_rule_id"] is not None
            or response.json()[1]["run_rule_id"] is not None
        )
        assert response.headers["X-Pagination-Total"] == "2"

        response = await client.get(f"/annotation-queues/{annotation_queue_id}")
        assert response.status_code == 200, response.text
        assert response.json()["run_rule_id"] == run_rule_id


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_v3_evaluator_with_corrections_and_new_experiment(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Set up v3 evaluator creation (copying from test_create_alignment_evaluator)
        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        # Create repo
        repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
        response = await client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )
        assert response.status_code == 200, response.text
        repo_full_name = response.json()["repo"]["full_name"]

        # Commit structured prompt
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={"manifest": dumpd(STRUCTURED_PROMPT)},
        )
        assert response.status_code == 200, response.text
        await wait_until_task_queue_empty()

        # Create dataset
        response = await client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        # Create secret
        response = await client.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )
        assert response.status_code == 200, response.text

        # Create v3 evaluator with single boolean key (following test_create_alignment_evaluator)
        raw_prompt_evaluator, _ = get_evaluators(repo_full_name)
        raw_prompt_evaluator["structured"]["schema"] = {
            "title": "extract",
            "description": "Extract information from the user's response.",
            "type": "object",
            "properties": {
                "correctness": {
                    "type": "boolean",
                    "description": "Is the submission correct, accurate, and factual?",
                }
            },
            "required": ["correctness"],
        }
        raw_prompt_evaluator["structured"]["model"]["kwargs"]["responses"] = [
            {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain", "schema", "messages", "AIMessage"],
                "kwargs": {
                    "content": "",
                    "additional_kwargs": {
                        "tool_calls": [
                            {
                                "function": {
                                    "name": "extract",
                                    "arguments": json.dumps(
                                        {
                                            "correctness": True,
                                        }
                                    ),
                                }
                            }
                        ]
                    },
                },
            }
        ]

        # Create the rule with the v3 evaluator
        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "v3 correctness evaluator",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
                "use_corrections_dataset": True,
            },
        )
        assert response.status_code == 200
        rule_id = response.json()["id"]

        # Create examples (following test_creating_rule_from_existing_prompt pattern)
        examples = []
        example_ids = [uuid4() for _ in range(5)]
        for i in range(5):
            examples.append(
                {
                    "inputs": {"input": f"test input {i}"},
                    "outputs": {"output": f"test output {i}"},
                    "dataset_id": str(dataset_id),
                    "id": str(example_ids[i]),
                }
            )

        response = await client.post("/examples/bulk", json=examples)
        assert response.status_code == 200

        # Create experiment session
        response = await client.post(
            "/sessions",
            json={
                "name": "correctness_session_1",
                "reference_dataset_id": str(dataset_id),
            },
        )
        assert response.status_code == 200
        session_id = response.json()["id"]

        # Create runs for the experiment (following test_creating_rule_from_existing_prompt pattern)
        start_time = datetime.now(timezone.utc) + timedelta(days=1)
        end_time = start_time + timedelta(seconds=8)

        runs = []
        run_ids = []
        for i, example_id in enumerate(example_ids):
            run_id = uuid4()
            run_ids.append(run_id)
            runs.append(
                {
                    "name": "Target",
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Target"},
                    "inputs": {"input": f"test input {i}"},
                    "outputs": {
                        "generations": [
                            [
                                {
                                    "text": f"response for example {i}",
                                    "generation_info": {
                                        "finish_reason": "stop",
                                        "logprobs": None,
                                    },
                                    "type": "ChatGeneration",
                                    "message": {
                                        "lc": 1,
                                        "type": "constructor",
                                        "id": [
                                            "langchain",
                                            "schema",
                                            "messages",
                                            "AIMessage",
                                        ],
                                        "kwargs": {
                                            "content": f"This is response {i}",
                                            "additional_kwargs": {"refusal": None},
                                            "response_metadata": {
                                                "token_usage": {
                                                    "completion_tokens": 10,
                                                    "prompt_tokens": 5,
                                                    "total_tokens": 15,
                                                    "completion_tokens_details": {
                                                        "reasoning_tokens": 0
                                                    },
                                                    "prompt_tokens_details": {
                                                        "cached_tokens": 0
                                                    },
                                                },
                                                "model_name": "gpt-4o-2024-08-06",
                                                "system_fingerprint": "fp_45cf54deae",
                                                "finish_reason": "stop",
                                            },
                                            "type": "ai",
                                            "id": f"run-{run_id}-0",
                                            "usage_metadata": {
                                                "input_tokens": 5,
                                                "output_tokens": 10,
                                                "total_tokens": 15,
                                                "input_token_details": {
                                                    "cache_read": 0
                                                },
                                                "output_token_details": {
                                                    "reasoning": 0
                                                },
                                            },
                                            "tool_calls": [],
                                            "invalid_tool_calls": [],
                                        },
                                    },
                                }
                            ]
                        ],
                        "llm_output": {
                            "token_usage": {
                                "completion_tokens": 10,
                                "prompt_tokens": 5,
                                "total_tokens": 15,
                                "completion_tokens_details": {"reasoning_tokens": 0},
                                "prompt_tokens_details": {"cached_tokens": 0},
                            },
                            "model_name": "gpt-4o-2024-08-06",
                            "system_fingerprint": "fp_45cf54deae",
                        },
                        "run": None,
                        "type": "LLMResult",
                    },
                    "session_id": str(session_id),
                    "reference_example_id": str(example_id),
                    "parent_run_id": None,
                    "run_type": "llm",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%f')}Z{run_id}",
                },
            )

        # Submit runs
        response = await client.post(
            "/runs/batch",
            json={
                "post": [run for run in runs],
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        # Evaluate the runs
        await cron_schedule_apply_rules(rule_ids=[rule_id])
        await wait_until_task_queue_empty()

        # Verify the rule was applied
        response = await client.get(f"/runs/rules/{rule_id}/logs")
        assert response.status_code == 200
        logs = response.json()
        # Should have 5 runs + 1 NULL application
        assert len(logs) == 6

        feedback = await db_asyncpg.fetchrow(
            "SELECT * FROM feedbacks WHERE run_id = $1", logs[1]["run_id"]
        )
        feedback_id = feedback["id"]
        # Correct one of the feedbacks
        response = await client.patch(
            f"/feedback/{feedback_id}",
            json={"correction": {"score": 2, "few_shot_explanation": "foo"}},
        )
        assert response.status_code == 200, response.text
        await wait_until_task_queue_empty()

        # Create a single new run
        new_run_id = uuid4()
        new_example_id = example_ids[0]  # Use same example as corrected run
        new_run = {
            "name": "Target",
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "Target"},
            "inputs": {"input": "test input 0"},
            "outputs": {
                "generations": [
                    [
                        {
                            "text": "new response for correction test",
                            "generation_info": {
                                "finish_reason": "stop",
                                "logprobs": None,
                            },
                            "type": "ChatGeneration",
                            "message": {
                                "lc": 1,
                                "type": "constructor",
                                "id": [
                                    "langchain",
                                    "schema",
                                    "messages",
                                    "AIMessage",
                                ],
                                "kwargs": {
                                    "content": "New response to test corrections",
                                    "additional_kwargs": {"refusal": None},
                                    "response_metadata": {
                                        "token_usage": {
                                            "completion_tokens": 10,
                                            "prompt_tokens": 5,
                                            "total_tokens": 15,
                                            "completion_tokens_details": {
                                                "reasoning_tokens": 0
                                            },
                                            "prompt_tokens_details": {
                                                "cached_tokens": 0
                                            },
                                        },
                                        "model_name": "gpt-4o-2024-08-06",
                                        "system_fingerprint": "fp_45cf54deae",
                                        "finish_reason": "stop",
                                    },
                                    "type": "ai",
                                    "id": f"run-{new_run_id}-0",
                                    "usage_metadata": {
                                        "input_tokens": 5,
                                        "output_tokens": 10,
                                        "total_tokens": 15,
                                        "input_token_details": {"cache_read": 0},
                                        "output_token_details": {"reasoning": 0},
                                    },
                                    "tool_calls": [],
                                    "invalid_tool_calls": [],
                                },
                            },
                        }
                    ]
                ],
                "llm_output": {
                    "token_usage": {
                        "completion_tokens": 10,
                        "prompt_tokens": 5,
                        "total_tokens": 15,
                        "completion_tokens_details": {"reasoning_tokens": 0},
                        "prompt_tokens_details": {"cached_tokens": 0},
                    },
                    "model_name": "gpt-4o-2024-08-06",
                    "system_fingerprint": "fp_45cf54deae",
                },
                "run": None,
                "type": "LLMResult",
            },
            "session_id": str(session_id),
            "reference_example_id": str(new_example_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(new_run_id),
            "trace_id": str(new_run_id),
            "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%f')}Z{new_run_id}",
        }

        # Submit the new run
        response = await client.post(
            "/runs/batch",
            json={
                "post": [new_run],
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        # Evaluate the new run (this should use the correction)
        await cron_schedule_apply_rules(rule_ids=[rule_id])
        await wait_until_task_queue_empty()

        # Verify that the correction example was passed to the evaluator correctly
        # Get the evaluation logs to check the new run was evaluated
        response = await client.get(f"/runs/rules/{rule_id}/logs")
        assert response.status_code == 200
        new_logs = response.json()
        # Should have original 6 logs + 2 new logs for the new run
        assert len(new_logs) == 8

        latest_log = new_logs[1]
        for log in new_logs[2:]:
            if log["application_time"] > latest_log["application_time"]:
                latest_log = log

        feedback = await db_asyncpg.fetchrow(
            "SELECT * FROM feedbacks WHERE run_id = $1",
            latest_log["run_id"],
        )
        run_with_correction_id = feedback["feedback_source"]["metadata"]["__run"][
            "run_id"
        ]

        response = await client.get(f"/runs/{run_with_correction_id}")
        assert response.status_code == 200
        assert response.json()["inputs"]["examples_few_shot"]
        assert (
            "few_shot_explanation" in response.json()["inputs"]["examples_few_shot"][0]
        )
        assert "correctness" in response.json()["inputs"]["examples_few_shot"][0]
        assert response.json()["inputs"]["examples_few_shot"][0]["correctness"] == "2"

        multipart_response = "I am a bot, and I will respond with multiple streaming chunks to test that the streaming functionality works as expected."
        fake_model = FakeStreamingMessagesListChatModel(
            responses=[AIMessage(content=multipart_response, type="ai")],
            a_great_secret="PLACEHOLDER",
            chunk_size=5,  # Break response into chunks of 5 characters
        )
        fake_model_serialized = dumpd(
            ChatPromptTemplate([("user", "{question}")]) | fake_model
        )

        req = PlaygroundRunOverDatasetRequestSchema(
            manifest=fake_model_serialized,
            dataset_id=dataset_id,
            secrets={"A_GREAT_SECRET": "howdyho"},
            options={},
            project_name="Playground",
            evaluator_rules=[rule_id],
        )

        # Function to parse SSE events from response chunks
        def parse_sse_events(chunk):
            events = []
            # Split by SSE event delimiter (double newline with carriage returns)
            event_strings = chunk.decode("utf-8").split("\n\n")

            for event_str in event_strings:
                if not event_str:
                    continue

                # Split the event into lines
                lines = event_str.split("\n")
                event_type = None
                event_data = None

                for line in lines:
                    if line.startswith("event:"):
                        event_type = line.replace("event:", "").strip()
                    elif line.startswith("data:"):
                        event_data = line.replace("data:", "").strip()

                # Process data events with JSON content
                if event_type == "data" and event_data:
                    try:
                        data = json.loads(event_data)
                        if "patch" in data and isinstance(data["patch"], list):
                            # Extract each patch operation as a separate event
                            for patch_op in data["patch"]:
                                events.append(patch_op)
                    except json.JSONDecodeError:
                        pass

            return events

        # Call the streaming endpoint
        async with client.stream(
            "POST", "/datasets/playground_experiment/stream", data=req.model_dump_json()
        ) as response:
            assert response.status_code == 200, (
                f"Received status code {response.status_code}"
            )

            # Collect and parse all streamed events
            all_events = []
            playground_run_id = None

            async for chunk in response.aiter_bytes():
                events = parse_sse_events(chunk)
                all_events.extend(events)

                # Extract run_id from the first event with op='replace'
                if playground_run_id is None and events:
                    for event in events:
                        if event.get("op") == "replace" and "id" in event.get(
                            "value", {}
                        ):
                            playground_run_id = event["value"]["id"]
                            break

        # Verify we got at least one event and a run_id
        assert len(all_events) > 0, "No SSE events were received"
        assert playground_run_id is not None, (
            "No run ID was found in the streaming response"
        )

        # Wait for feedback processing
        max_retries = 10
        feedback = None

        for _ in range(max_retries):
            await wait_until_task_queue_empty()

            response = await client.get("/feedback", params={"run": playground_run_id})
            assert response.status_code == 200, response.text

            if len(response.json()) > 0:
                feedback = response.json()
                break

            sleep(0.5)

        feedback = await db_asyncpg.fetchrow(
            "SELECT * FROM feedbacks WHERE id = $1",
            feedback[0]["id"],
        )
        run_with_correction_id = feedback["feedback_source"]["metadata"]["__run"][
            "run_id"
        ]

        response = await client.get(f"/runs/{run_with_correction_id}")
        assert response.status_code == 200
        assert response.json()["inputs"]["examples_few_shot"]
        assert (
            "few_shot_explanation" in response.json()["inputs"]["examples_few_shot"][0]
        )
        assert "correctness" in response.json()["inputs"]["examples_few_shot"][0]
        assert response.json()["inputs"]["examples_few_shot"][0]["correctness"] == "2"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_correction_rule_not_created_for_v3_evaluators(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Set up v3 evaluator creation (copying from test_create_alignment_evaluator)
        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        # Create repo
        repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
        response = await client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )
        assert response.status_code == 200, response.text
        repo_full_name = response.json()["repo"]["full_name"]

        # Commit structured prompt
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={"manifest": dumpd(STRUCTURED_PROMPT)},
        )
        assert response.status_code == 200, response.text
        await wait_until_task_queue_empty()

        # Create dataset
        response = await client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        # Create secret
        response = await client.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )
        assert response.status_code == 200, response.text

        # Create v3 evaluator with single boolean key (following test_create_alignment_evaluator)
        raw_prompt_evaluator, _ = get_evaluators(repo_full_name)
        raw_prompt_evaluator["structured"]["schema"] = {
            "title": "extract",
            "description": "Extract information from the user's response.",
            "type": "object",
            "properties": {
                "correctness": {
                    "type": "boolean",
                    "description": "Is the submission correct, accurate, and factual?",
                }
            },
            "required": ["correctness"],
        }
        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "v3 correctness evaluator",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
                "use_corrections_dataset": True,
            },
        )
        assert response.status_code == 200, response.text

        response = await client.get("/runs/rules")
        assert response.status_code == 200
        assert not any([r["session_id"] for r in response.json()])


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_v3_evaluator_creates_ace_rule_on_corrections_dataset(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Set up v3 evaluator creation (copying from test_create_alignment_evaluator)
        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        # Create repo
        repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
        response = await client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )
        assert response.status_code == 200, response.text
        repo_full_name = response.json()["repo"]["full_name"]

        # Commit structured prompt
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={"manifest": dumpd(STRUCTURED_PROMPT)},
        )
        assert response.status_code == 200, response.text
        await wait_until_task_queue_empty()

        # Create dataset
        response = await client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        # Create secret
        response = await client.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )
        assert response.status_code == 200, response.text

        # Create v3 evaluator with single boolean key (following test_create_alignment_evaluator)
        raw_prompt_evaluator, _ = get_evaluators(repo_full_name)
        raw_prompt_evaluator["structured"]["schema"] = {
            "title": "extract",
            "description": "Extract information from the user's response.",
            "type": "object",
            "properties": {
                "correctness": {
                    "type": "boolean",
                    "description": "Is the submission correct, accurate, and factual?",
                }
            },
            "required": ["correctness"],
        }
        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "v3 correctness evaluator",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
                "use_corrections_dataset": True,
            },
        )
        assert response.status_code == 200
        corrections_dataset_id = response.json()["corrections_dataset_id"]

        example_ids = [uuid4() for _ in range(2)]
        examples = [
            {
                "inputs": {"input": "foobar"},
                "outputs": {"correctness": bool(i), "few_shot_explanation": "foobar"},
                "dataset_id": str(corrections_dataset_id),
                "id": str(example_ids[i]),
            }
            for i in range(2)
        ]

        response = await client.post("/examples/bulk", json=examples)
        assert response.status_code == 200

        response = await client.post(
            "/sessions",
            json={
                "name": "correctness_session_1",
                "reference_dataset_id": str(corrections_dataset_id),
            },
        )
        assert response.status_code == 200
        session_id_1 = response.json()["id"]

        response = await client.post(
            "/sessions",
            json={
                "name": "correctness_session_2",
                "reference_dataset_id": str(corrections_dataset_id),
            },
        )
        assert response.status_code == 200
        session_id_2 = response.json()["id"]

        # Create runs for the experiment (following test_creating_rule_from_existing_prompt pattern)
        start_time = datetime.now(timezone.utc) + timedelta(days=1)
        end_time = start_time + timedelta(seconds=8)

        runs = []
        run_ids = []
        session_ids = [session_id_1, session_id_2]
        for i, example_id in enumerate(example_ids):
            run_id = uuid4()
            run_ids.append(run_id)
            session_id = session_ids[i]
            runs.append(
                {
                    "name": "Target",
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Target"},
                    "inputs": {"input": f"test input {i}"},
                    "outputs": {
                        "generations": [
                            [
                                {
                                    "generation_info": {
                                        "finish_reason": "stop",
                                        "logprobs": None,
                                    },
                                    "type": "ChatGeneration",
                                    "message": {
                                        "lc": 1,
                                        "type": "constructor",
                                        "id": [
                                            "langchain",
                                            "schema",
                                            "messages",
                                            "AIMessage",
                                        ],
                                        "kwargs": {
                                            "content": {
                                                "correctness": True,
                                                "comment": "foobar",
                                            },
                                            "additional_kwargs": {"refusal": None},
                                            "response_metadata": {
                                                "token_usage": {
                                                    "completion_tokens": 10,
                                                    "prompt_tokens": 5,
                                                    "total_tokens": 15,
                                                    "completion_tokens_details": {
                                                        "reasoning_tokens": 0
                                                    },
                                                    "prompt_tokens_details": {
                                                        "cached_tokens": 0
                                                    },
                                                },
                                                "model_name": "gpt-4o-2024-08-06",
                                                "system_fingerprint": "fp_45cf54deae",
                                                "finish_reason": "stop",
                                            },
                                            "type": "ai",
                                            "id": f"run-{run_id}-0",
                                            "usage_metadata": {
                                                "input_tokens": 5,
                                                "output_tokens": 10,
                                                "total_tokens": 15,
                                                "input_token_details": {
                                                    "cache_read": 0
                                                },
                                                "output_token_details": {
                                                    "reasoning": 0
                                                },
                                            },
                                            "tool_calls": [],
                                            "invalid_tool_calls": [],
                                        },
                                    },
                                }
                            ]
                        ],
                        "llm_output": {
                            "token_usage": {
                                "completion_tokens": 10,
                                "prompt_tokens": 5,
                                "total_tokens": 15,
                                "completion_tokens_details": {"reasoning_tokens": 0},
                                "prompt_tokens_details": {"cached_tokens": 0},
                            },
                            "model_name": "gpt-4o-2024-08-06",
                            "system_fingerprint": "fp_45cf54deae",
                        },
                        "run": None,
                        "type": "LLMResult",
                    },
                    "session_id": str(session_id),
                    "reference_example_id": str(example_id),
                    "parent_run_id": None,
                    "run_type": "llm",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%f')}Z{run_id}",
                },
            )

        # Submit runs
        response = await client.post(
            "/runs/batch",
            json={
                "post": runs[:1],
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        response = await client.get("/runs/rules")
        assert response.status_code == 200
        alignment_run_rule = [
            r for r in response.json() if r["dataset_id"] == corrections_dataset_id
        ][0]
        assert alignment_run_rule["corrections_dataset_id"] is None
        await cron_schedule_apply_rules(rule_ids=[alignment_run_rule["id"]])
        await wait_until_task_queue_empty()

        response = await client.get(f"/runs/{run_ids[0]}")
        assert response.status_code == 200
        assert response.json()["feedback_stats"]["alignment"]["n"] == 1

        response = await client.post(
            "/runs/batch",
            json={
                "post": runs[1:],
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        await cron_schedule_apply_rules(rule_ids=[alignment_run_rule["id"]])
        await wait_until_task_queue_empty()

        response = await client.get(f"/runs/{run_ids[1]}")
        assert response.status_code == 200
        assert response.json()["feedback_stats"]["alignment"]["n"] == 1


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_cannot_update_schema_for_v3_evaluator(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Set up v3 evaluator creation (copying from test_create_alignment_evaluator)
        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        # Create repo
        repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
        response = await client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )
        assert response.status_code == 200, response.text
        repo_full_name = response.json()["repo"]["full_name"]

        structured_prompt = StructuredPrompt.from_messages_and_schema(
            [
                (
                    "system",
                    "You are evaluating the following value for toxicity, topic, and whether it's long.",
                ),
                (
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ),
            ],
            {
                "title": "extract",
                "description": "Extract information from the user's response.",
                "type": "object",
                "properties": {
                    "correctness2": {
                        "type": "boolean",
                        "description": "Is the submission correct, accurate, and factual?",
                    }
                },
                "required": ["correctness"],
            },
        )
        # Commit structured prompt
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={"manifest": dumpd(structured_prompt)},
        )
        assert response.status_code == 200, response.text
        parent_commit_hash = response.json()["commit"]["commit_hash"]
        await wait_until_task_queue_empty()

        # Create dataset
        response = await client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        # Create secret
        response = await client.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )
        assert response.status_code == 200, response.text

        # Create v3 evaluator with single boolean key (following test_create_alignment_evaluator)
        raw_prompt_evaluator, hub_ref_evaluator = get_evaluators(repo_full_name)
        raw_prompt_evaluator["structured"]["schema"] = {
            "title": "extract",
            "description": "Extract information from the user's response.",
            "type": "object",
            "properties": {
                "correctness": {
                    "type": "boolean",
                    "description": "Is the submission correct, accurate, and factual?",
                },
                "comment": {"type": "string", "description": "the reasoning"},
            },
            "required": ["correctness"],
        }
        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "v3 correctness evaluator",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
                "use_corrections_dataset": True,
            },
        )
        assert response.status_code == 200, response.text
        rule_id = response.json()["id"]

        raw_prompt_evaluator["structured"]["schema"] = {
            "title": "extract",
            "description": "Extract information from the user's response.",
            "type": "object",
            "properties": {
                "correctness2": {
                    "type": "boolean",
                    "description": "Is the submission correct, accurate, and factual?",
                }
            },
            "required": ["correctness"],
        }
        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
            },
        )
        assert response.status_code == 422, response.text
        assert "Cannot change the structure of a boolean evaluator" in response.text

        raw_prompt_evaluator["structured"]["schema"] = {
            "title": "extract",
            "description": "Extract information from the user's response.",
            "type": "object",
            "properties": {
                "correctness": {
                    "type": "string",
                    "description": "Is the submission correct, accurate, and factual?",
                }
            },
            "required": ["correctness"],
        }
        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
            },
        )
        assert response.status_code == 422, response.text
        assert "Cannot change the output type of a boolean evaluator" in response.text

        raw_prompt_evaluator["structured"]["schema"] = {
            "title": "extract",
            "description": "foobarbaz.",
            "type": "object",
            "properties": {
                "correctness": {
                    "type": "boolean",
                    "description": "barbazfoo?",
                }
            },
            "required": ["correctness"],
        }
        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [raw_prompt_evaluator],
            },
        )
        assert response.status_code == 200

        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [hub_ref_evaluator],
            },
        )
        assert response.status_code == 422, response.text
        assert "Cannot change the structure of a boolean evaluator" in response.text

        structured_prompt = StructuredPrompt.from_messages_and_schema(
            [
                (
                    "system",
                    "You are evaluating the following value for toxicity, topic, and whether it's long.",
                ),
                (
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ),
            ],
            {
                "title": "extract",
                "description": "Extract information from the user's response.",
                "type": "object",
                "properties": {
                    "correctness": {
                        "type": "string",
                        "description": "Is the submission correct, accurate, and factual?",
                    }
                },
                "required": ["correctness"],
            },
        )
        # Commit structured prompt
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={
                "manifest": dumpd(structured_prompt),
                "parent_commit": parent_commit_hash,
            },
        )
        assert response.status_code == 200, response.text
        parent_commit_hash = response.json()["commit"]["commit_hash"]
        await wait_until_task_queue_empty()

        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [hub_ref_evaluator],
            },
        )
        assert response.status_code == 422, response.text
        assert "Cannot change the output type of a boolean evaluator" in response.text

        structured_prompt = StructuredPrompt.from_messages_and_schema(
            [
                (
                    "system",
                    "You are evaluating the following value for toxicity, topic, and whether it's long.",
                ),
                (
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ),
            ],
            {
                "title": "extract",
                "description": "barbazfoo.",
                "type": "object",
                "properties": {
                    "correctness": {
                        "type": "boolean",
                        "description": "something something",
                    }
                },
                "required": ["correctness"],
            },
        )
        # Commit structured prompt
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={
                "manifest": dumpd(structured_prompt),
                "parent_commit": parent_commit_hash,
            },
        )
        assert response.status_code == 200, response.text
        await wait_until_task_queue_empty()

        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [hub_ref_evaluator],
            },
        )
        assert response.status_code == 200


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_commit_hash_shorthand_works(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Set up v3 evaluator creation (copying from test_create_alignment_evaluator)
        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        # Create repo
        repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
        response = await client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )
        assert response.status_code == 200, response.text
        repo_full_name = response.json()["repo"]["full_name"]

        structured_prompt = StructuredPrompt.from_messages_and_schema(
            [
                (
                    "system",
                    "You are evaluating the following value for toxicity, topic, and whether it's long.",
                ),
                (
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ),
            ],
            {
                "title": "extract",
                "description": "Extract information from the user's response.",
                "type": "object",
                "properties": {
                    "correctness": {
                        "type": "boolean",
                        "description": "Is the submission correct, accurate, and factual?",
                    }
                },
                "required": ["correctness"],
            },
        )
        # Commit structured prompt
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={"manifest": dumpd(structured_prompt)},
        )
        assert response.status_code == 200, response.text
        parent_commit_hash = response.json()["commit"]["commit_hash"]
        await wait_until_task_queue_empty()

        # Create dataset
        response = await client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        # Create secret
        response = await client.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )
        assert response.status_code == 200, response.text

        _, hub_ref_evaluator = get_evaluators(repo_full_name)
        hub_ref_evaluator["structured"]["hub_ref"] = (
            repo_full_name + f":{parent_commit_hash[:8]}"
        )
        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "v3 correctness evaluator",
                "sampling_rate": 1,
                "evaluators": [hub_ref_evaluator],
                "use_corrections_dataset": False,
            },
        )
        assert response.status_code == 200, response.text
        rule_id = response.json()["id"]

        structured_prompt = StructuredPrompt.from_messages_and_schema(
            [
                (
                    "system",
                    "You are evaluating the following value for toxicity, topic.",
                ),
                (
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ),
            ],
            {
                "title": "extract",
                "description": "Extract information from the user's response.",
                "type": "object",
                "properties": {
                    "correctness": {
                        "type": "boolean",
                        "description": "Is the submission correct, accurate, and factual?",
                    }
                },
                "required": ["correctness"],
            },
        )
        # Commit structured prompt
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={
                "manifest": dumpd(structured_prompt),
                "parent_commit": parent_commit_hash,
            },
        )
        assert response.status_code == 200, response.text
        commit_hash = response.json()["commit"]["commit_hash"]
        await wait_until_task_queue_empty()

        hub_ref_evaluator["structured"]["hub_ref"] = repo_full_name + f":{commit_hash}"
        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [hub_ref_evaluator],
            },
        )
        assert response.status_code == 200, response.text


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_latest_tag_works(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Set up v3 evaluator creation (copying from test_create_alignment_evaluator)
        await db_asyncpg.execute(
            """UPDATE organizations SET config = config || '{"new_rule_evaluator_creation_version": 3}' WHERE id = $1""",
            authed_client.auth.organization_id,
        )

        # Create repo
        repo_handle = "repo_" + "".join(random.choices(string.ascii_lowercase, k=20))
        response = await client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )
        assert response.status_code == 200, response.text
        repo_full_name = response.json()["repo"]["full_name"]

        structured_prompt = StructuredPrompt.from_messages_and_schema(
            [
                (
                    "system",
                    "You are evaluating the following value for toxicity, topic, and whether it's long.",
                ),
                (
                    "user",
                    "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                ),
            ],
            {
                "title": "extract",
                "description": "Extract information from the user's response.",
                "type": "object",
                "properties": {
                    "correctness": {
                        "type": "boolean",
                        "description": "Is the submission correct, accurate, and factual?",
                    }
                },
                "required": ["correctness"],
            },
        )
        # Commit structured prompt
        response = await client.post(
            f"/commits/{repo_full_name}",
            json={"manifest": dumpd(structured_prompt)},
        )
        assert response.status_code == 200, response.text
        await wait_until_task_queue_empty()

        # Create dataset
        response = await client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        # Create secret
        response = await client.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )
        assert response.status_code == 200, response.text

        _, hub_ref_evaluator = get_evaluators(repo_full_name)
        hub_ref_evaluator["structured"]["hub_ref"] = repo_full_name + ":latest"
        response = await client.post(
            "/runs/rules",
            json={
                "dataset_id": dataset_id,
                "filter": "eq(is_root, true)",
                "display_name": "v3 correctness evaluator",
                "sampling_rate": 1,
                "evaluators": [hub_ref_evaluator],
                "use_corrections_dataset": False,
            },
        )
        assert response.status_code == 200, response.text
        rule_id = response.json()["id"]

        response = await client.patch(
            f"/runs/rules/{rule_id}",
            json={
                "dataset_id": dataset_id,
                "display_name": "modified first rule",
                "sampling_rate": 1,
                "evaluators": [hub_ref_evaluator],
            },
        )
        assert response.status_code == 200, response.text
