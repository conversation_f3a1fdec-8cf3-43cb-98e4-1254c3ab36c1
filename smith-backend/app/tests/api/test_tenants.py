"""Test correct functionality of tenants endpoints."""

import uuid
from typing import Any, Awaitable, Callable
from uuid import UUID, uuid4, uuid5

import asyncpg
import pytest
from httpx import ASGITransport, AsyncClient
from lc_config.tenant_config import OrganizationConfig, TenantConfig

from app import config, crud, schemas
from app.api.auth import TenantlessAuthInfo
from app.main import app
from app.models.identities.crud import get_org_members
from app.models.organizations.payment import get_organization
from app.models.tenants.secrets import list_secrets
from app.tests.ensure import DecodedUserInfo, ensure_user
from app.tests.utils import (
    fresh_tenant_client,
    jwt_for_user,
    random_lower_string,
)

pytestmark = pytest.mark.anyio

_EXPECTED_PERSONAL_ORG_CONFIG = {
    "max_identities": 1,
    "max_workspaces": 1,
    "max_langgraph_cloud_deployments": 3,
    "max_free_langgraph_cloud_deployments": 0,
    "can_use_rbac": False,
    "can_use_saml_sso": False,
    "can_add_seats": False,
    "can_serve_datasets": False,
    "kv_dataset_message_support": True,
    "can_use_bulk_export": False,
    "demo_lgp_new_graph_enabled": False,
    "can_disable_public_sharing": False,
    "datadog_rum_session_sample_rate": 20,
    "can_use_langgraph_cloud": True,
    "startup_plan_approval_date": None,
    "premier_plan_approval_date": None,
    "partner_plan_approval_date": None,
    "use_python_playground_service": False,
    "show_updated_sidenav": False,
    "show_updated_resource_tags": False,
    "show_playground_prompt_canvas": False,
    "allow_custom_iframes": False,
    "enable_langgraph_pricing": False,
    "enable_thread_view_playground": False,
    "enable_org_usage_charts": False,
    "enable_select_all_traces": False,
    "playground_evaluator_strategy": "sync",
    "use_exact_search_for_prompts": False,
    "langgraph_deploy_own_cloud_enabled": False,
    "enable_k8s_vanilla_platform": False,
    "prompt_optimization_jobs_enabled": False,
    "langgraph_remote_reconciler_enabled": False,
    "langsmith_alerts_poc_enabled": True,
    "enable_align_evaluators": False,
    "tenant_skip_topk_facets": False,
    "lgp_templates_enabled": False,
    "langsmith_alerts_legacy_poc_enabled": False,
    "langsmith_experimental_search_enabled": False,
    "max_prompt_webhooks": 1,
    "enable_monthly_usage_charts": False,
    "enable_lgp_metrics_charts": False,
    "new_rule_evaluator_creation_version": 2,
    "enable_tracing_project_redesign": False,
    "org_scoped_service_accounts_enabled": False,
}


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth"],
    reason="single tenant",
)
async def test_read_tenants(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that tenants can be listed."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth2 = authed_client.auth

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        response = await client.get("/tenants")

        assert response.status_code == 200

        tenants = response.json()
        tenant_ids = [UUID(t["id"]) for t in tenants]
        assert auth.tenant_id in tenant_ids, (
            "User should have access to tenant they created"
        )
        assert auth2.tenant_id not in tenant_ids, (
            "User should not have access to tenant they didn't create"
        )
        tenant_one = next(t for t in tenants if UUID(t["id"]) == auth.tenant_id)
        assert tenant_one["read_only"] is False


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth", "mixed"],
    reason="single tenant",
)
async def test_read_tenants_read_only(
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that tenants can be listed."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth2 = authed_client.auth

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        new_user_email = "<EMAIL>"
        stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
        new_user_jwt = jwt_for_user(
            user_id=stored_user_id,
            user_email=new_user_email,
        )
        new_user_headers = {
            "Authorization": f"Bearer {new_user_jwt}",
        }

        org_user_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
        )
        workspace_viewer_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
        )

        # invite user to org as an org user & claim invite
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth.tenant_id)],
                "workspace_role_id": str(workspace_viewer_role_id),
            },
        )
        assert response.status_code == 200, response.text
        response = await http_no_auth.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200, response.text

        response = await http_no_auth.get(
            "/tenants",
            headers=new_user_headers,
        )

        assert response.status_code == 200

        tenants = response.json()
        tenant_ids = [UUID(t["id"]) for t in tenants]
        tenant_one = next(t for t in tenants if UUID(t["id"]) == auth.tenant_id)
        assert tenant_one["read_only"] is True

        assert auth.tenant_id in tenant_ids
        assert auth2.tenant_id not in tenant_ids


@pytest.mark.skipif(config.settings.AUTH_TYPE != "none", reason="tenantless auth")
async def test_tenants_create_tenant_no_auth(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that tenants can be created through list tenants endpoint."""

    await wait_until_task_queue_empty()

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.get("/tenants")
        assert response.status_code == 200

    # FETCH the singleton tenant
    result = await db_asyncpg.fetchrow(
        """
        SELECT * FROM tenants
        WHERE id = '00000000-0000-0000-0000-000000000000'
        """
    )
    assert result is not None


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth"],
    reason="tenantless auth",
)
async def test_list_tenants_create_tenant_and_org_jwt_auth(
    use_api_key: bool,
) -> None:
    """Test that tenants can be listed through list tenants endpoint."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")
    user_email = "<EMAIL>"
    user_full_name = "Test User"
    user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(user_email))
    headers = {
        "Authorization": f"Bearer {jwt_for_user(user_id, user_email, user_full_name)}",
    }
    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.get("/tenants", headers=headers)

        assert response.status_code == 200
        available_tenants = response.json()
        assert len(available_tenants) == 1
        tenant = available_tenants[0]
        assert tenant["id"] == str(uuid5(user_id, "personal"))
        org = await get_organization(tenant["organization_id"])
        assert org is not None
        assert org.created_by_user_id == user_id

    # Fetching again shouldn't create another tenant
    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.get("/tenants", headers=headers)

        assert response.status_code == 200
        available_tenants = response.json()
        assert len(available_tenants) == 1
        tenant = available_tenants[0]
        assert tenant["id"] == str(uuid5(user_id, "personal"))
        org = await get_organization(tenant["organization_id"])
        assert org is not None
        assert org.created_by_user_id == user_id


@pytest.mark.skipif(
    config.settings.BASIC_AUTH_ENABLED or config.settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
async def test_list_tenants_skip_create(
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test skipping org and workspace creation when listing."""
    if use_api_key:
        pytest.skip("Api keys are not supported")

    user_id = uuid4()

    jwt = jwt_for_user(
        user_id=user_id,
        user_email=f"test+{user_id}@langchain.dev",
        user_full_name="",
    )
    auth_headers = {"Authorization": f"Bearer {jwt}"}

    response = await http_no_auth.get("/tenants?skip_create=true", headers=auth_headers)
    assert response.status_code == 200
    workspaces = response.json()
    assert len(workspaces) == 0


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth", "mixed"],
    reason="single tenant",
)
async def test_delete_tenant_members_not_allowed(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that deleting a tenant member fails for non-allowed cases like deleting yourself."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, include_read_only=True
    ) as authed_client:
        client = authed_client.client
        read_only_client = authed_client.read_only_client
        assert read_only_client is not None
        auth = authed_client.auth
        new_user_email = "<EMAIL>"
        stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
        new_user_jwt = jwt_for_user(
            user_id=stored_user_id,
            user_email=new_user_email,
        )
        new_user_headers = {
            "Authorization": f"Bearer {new_user_jwt}",
        }

        org_user_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
        )
        workspace_viewer_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
        )

        # invite user to org as an org user & claim invite
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth.tenant_id)],
                "workspace_role_id": str(workspace_viewer_role_id),
            },
        )
        assert response.status_code == 200, response.text
        response = await http_no_auth.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200, response.text

        # tests with readonly user (to avoid org admin deletion constraint)
        response = await client.get(
            "/workspaces/current/members",
        )

        assert response.status_code == 200
        readonly_identity_id = next(
            m["id"]
            for m in response.json()["members"]
            if m["user_id"] == str(stored_user_id)
        )

        # User in another tenant cannot delete
        async with fresh_tenant_client(
            db_asyncpg, use_api_key, include_read_only=True
        ) as authed_client2:
            new_tenant_client = authed_client2.client
            response = await new_tenant_client.delete(
                f"/workspaces/current/members/{readonly_identity_id}",
            )
        assert response.status_code == 404, (
            f"Some other user cannot delete the user, {response.text}"
        )

        # Readonly user cannot delete themselves
        response = await read_only_client.delete(
            f"/workspaces/current/members/{readonly_identity_id}",
            headers={
                "X-Tenant-Id": str(auth.tenant_id),
            },
        )
        assert response.status_code == 403


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth"],
    reason="single tenant; basic auth tests create more members",
)
async def test_read_tenant_members(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that tenants can be listed."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    new_user_jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {new_user_jwt}",
    }

    # test readwrite
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        response = await client.get("/workspaces/current/members")
        assert response.status_code == 200

        # check owner is in members
        tenant_members = schemas.TenantMembers(**response.json())
        assert tenant_members.tenant_id == auth.tenant_id
        assert len(tenant_members.members) == 1
        assert len(tenant_members.pending) == 0
        assert auth.user_id in [m.user_id for m in tenant_members.members]
        owner = next(m for m in tenant_members.members if m.user_id == auth.user_id)
        assert owner.email == auth.user_email
        assert owner.read_only is False

        # invite a new member as Viewer and claim
        viewer_role_id = await db_asyncpg.fetchval(
            "SELECT id from roles WHERE name = 'WORKSPACE_VIEWER'"
        )
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "workspace_role_id": str(viewer_role_id),
                "workspace_ids": [str(auth.tenant_id)],
            },
        )
        assert response.status_code == 200
        response = await http_no_auth.post(
            f"/workspaces/pending/{auth.tenant_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200

        # confirm readonly user can list tenant members
        response = await client.get(
            "/workspaces/current/members",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        assert len(response.json()["members"]) == 2


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth"],
    reason="needs user_id; no auto-org in basic auth",
)
async def test_read_tenants_new_user(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that tenants can be listed."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth2 = authed_client.auth

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client1 = authed_client.client
        auth1 = authed_client.auth

        new_user_email = "<EMAIL>"
        stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
        jwt = jwt_for_user(
            user_id=stored_user_id,
            user_email=new_user_email,
        )
        headers = {
            "Authorization": f"Bearer {jwt}",
        }
        response = await client1.get("/tenants", headers=headers)

        assert response.status_code == 200, response.text

        tenants = response.json()
        tenant_ids = [UUID(t["id"]) for t in tenants]

        assert len(tenant_ids) == 1, "A new personal tenant should have been created"
        tenant = tenants[0]
        assert auth1.tenant_id not in tenant_ids, "No access to tenant one"
        assert auth2.tenant_id not in tenant_ids, "No access to tenant two"
        assert tenant["is_personal"] is True

        # test tenant stats
        jwt = jwt_for_user(
            user_id=stored_user_id,
            user_email=new_user_email,
        )
        headers = {
            "Authorization": f"Bearer {jwt}",
            "X-Tenant-Id": str(tenant_ids[0]),
            "X-Organization-Id": str(tenant["organization_id"]),
        }
        response = await client1.get("/workspaces/current/stats", headers=headers)
        assert response.status_code == 200
        stats = response.json()
        assert stats == {
            "annotation_queue_count": 0,
            "tenant_id": str(tenant_ids[0]),
            "dataset_count": 0,
            "deployment_count": 0,
            "tracer_session_count": 1,  # default session
            "repo_count": 0,
            "dashboards_count": 0,
        }

        # ensure that an organization was created with the correct config
        response = await client1.get("/orgs/current", headers=headers)
        assert response.status_code == 200
        config_new_tenant = response.json()["config"]
        assert config_new_tenant == _EXPECTED_PERSONAL_ORG_CONFIG

        # Test personal tenant doesn't accept invites
        response = await client1.post(
            "/workspaces/current/members",
            headers=headers,
            json={
                "email": "<EMAIL>",
            },
        )
        assert response.status_code == 400, response.text
        response = await client1.get(
            "/workspaces/current/members",
            headers=headers,
        )
        assert response.status_code == 200
        assert len(response.json()["pending"]) == 0
        assert len(response.json()["members"]) == 1
        assert response.json()["members"][0]["user_id"] == str(stored_user_id)


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth"],
    reason="single tenant",
)
@pytest.mark.parametrize("endpoint_prefix", ["/tenants", "/workspaces"])
async def test_create_tenant_and_invites(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    endpoint_prefix: str,
) -> None:
    """Test that a tenant can be created."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # Check that we cannot create a tenant with an invalid name
        response = await client.post(
            endpoint_prefix,
            json={
                "display_name": "<123> invalid> display name",
            },
        )

        assert response.status_code == 422
        assert "display_name: String should match pattern" in response.text

        existing_tenants_response = await client.get(
            "/tenants",
        )
        assert existing_tenants_response.status_code == 200
        num_existing_tenants = len(existing_tenants_response.json())

        response = await client.post(
            "/tenants",
            json={
                "display_name": random_lower_string(),
            },
        )

        # Tenant was created as non-personal org
        assert response.status_code == 200
        tenant = response.json()
        new_tenant_id = UUID(tenant["id"])
        assert tenant["is_personal"] is False

        new_tenant_headers = {
            "Authorization": client.headers["Authorization"],
            "X-Tenant-Id": str(new_tenant_id),
            "X-Organization-Id": str(tenant["organization_id"]),
        }
        response = await client.get("/orgs/current", headers=new_tenant_headers)
        org = response.json()
        assert response.status_code == 200
        new_tenant_config = org["config"]
        assert new_tenant_config["max_identities"] > 1
        expected_tenant_config = OrganizationConfig(
            max_identities=10,
            max_workspaces=3,
            can_use_rbac=False,
            can_add_seats=True,
        )
        assert expected_tenant_config.model_dump() == new_tenant_config

        # New tenant is in list of tenants
        response = await client.get("/tenants", headers=client.headers)
        assert response.status_code == 200
        assert len(response.json()) == num_existing_tenants + 1
        assert str(new_tenant_id) in [t["id"] for t in response.json()]

        # New tenant accepts invites
        response = await client.post(
            "/orgs/current/members",
            headers=new_tenant_headers,
            json={
                "email": "<EMAIL>",
                "workspace_ids": [str(new_tenant_id)],
            },
        )
        assert response.status_code == 200, response.text
        assert response.json()["email"] == "<EMAIL>"

        # Invite is pending
        response = await client.get(
            "/workspaces/current/members",
            headers=new_tenant_headers,
        )
        assert response.status_code == 200
        assert len(response.json()["pending"]) == 1
        assert response.json()["pending"][0]["email"] == "<EMAIL>"
        assert len(response.json()["members"]) == 1
        assert response.json()["members"][0]["user_id"] == str(auth.user_id)

        # New tenant doesn't accept invites if max identities reached
        # 1. First we exhaust the identities
        # -1 because the owner is already there
        # -1 because we created a pending invite above
        for i in range(new_tenant_config["max_identities"] - 2):
            response = await client.post(
                "/orgs/current/members",
                headers=new_tenant_headers,
                json={
                    "email": f"{i}@langchain.dev",
                    "workspace_ids": [str(new_tenant_id)],
                },
            )
            assert response.status_code == 200, response.text
        response = await client.get(
            "/workspaces/current/members",
            headers=new_tenant_headers,
        )
        assert response.status_code == 200
        assert len(response.json()["members"]) == 1
        assert len(response.json()["pending"]) == (
            new_tenant_config["max_identities"] - 1
        )
        # 2. Then we try to add one more, which should fail
        response = await client.post(
            "/orgs/current/members",
            headers=new_tenant_headers,
            json={"email": "<EMAIL>"},
        )
        assert response.status_code == 400


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth", "mixed"],
    reason="single tenant",
)
async def test_create_tenant_already_exists(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        tenant_id = uuid4()

        response = await client.post(
            "/tenants",
            json={"display_name": random_lower_string(), "id": tenant_id.hex},
        )
        assert response.status_code == 200
        response = await client.post(
            "/tenants",
            json={"display_name": random_lower_string(), "id": tenant_id.hex},
        )
        assert response.status_code == 409


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth", "mixed"],
    reason="single tenant",
)
async def test_create_tenants_fails_if_limit_reached(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that the maximum workspaces limit is enforced."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        org_config = auth.tenant_config.organization_config
        max_workspaces = (
            org_config.max_workspaces
            if org_config
            else config.settings.SHARED_ORG_DEFAULT_CONFIG.max_workspaces
        )
        # we already have one workspace
        workspaces = 1
        while workspaces < max_workspaces:
            response = await client.post(
                "/tenants",
                json={
                    "display_name": random_lower_string(),
                    "organization_id": str(auth.organization_id),
                },
            )
            assert response.status_code == 200
            workspaces += 1

        # next one should fail
        response = await client.post(
            "/tenants",
            json={
                "display_name": random_lower_string(),
                "organization_id": str(auth.organization_id),
            },
        )
        assert response.status_code == 400, (
            "Should not be able to create more workspaces than allowed"
        )

        # should be able to create one more if one is deleted
        await db_asyncpg.execute(
            "UPDATE tenants SET is_deleted = true WHERE id = $1", auth.tenant_id
        )
        response = await client.post(
            "/tenants",
            json={
                "display_name": random_lower_string(),
                "organization_id": str(auth.organization_id),
            },
        )
        assert response.status_code == 200, (
            "Should be able to create one more workspace since one was deleted"
        )
        response = await client.post(
            "/tenants",
            json={
                "display_name": random_lower_string(),
                "organization_id": str(auth.organization_id),
            },
        )
        assert response.status_code == 400, (
            "Should not be able to create more workspaces than allowed"
        )


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth", "mixed"],
    reason="single tenant",
)
async def test_create_tenant_with_user_id(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a tenant can be created."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    user_email = "<EMAIL>"
    user_id = uuid.uuid5(uuid.NAMESPACE_OID, user_email)
    user = await ensure_user(
        DecodedUserInfo(
            sub=str(user_id),
            id=str(user_id),
            email=user_email,
        )
    )

    tenant = await crud.create_tenant(
        TenantlessAuthInfo(
            available_tenants=[], user_id=user_id, ls_user_id=user["ls_user_id"]
        ),
        schemas.TenantCreatePrivileged(
            display_name="With user",
            config=TenantConfig(),
            is_personal=True,
        ),
    )

    # test that tenant was created
    fetched_tenant = await db_asyncpg.fetchrow(
        "select * from tenants where id = $1", tenant.id
    )
    assert fetched_tenant["id"] == tenant.id

    # test that workspace and organization identities were created
    identity = await db_asyncpg.fetchrow(
        "select * from identities where tenant_id = $1 and user_id = $2 and access_scope = 'workspace'",
        tenant.id,
        user_id,
    )
    assert identity["user_id"] == user_id
    # single org constraint
    if not config.settings.BASIC_AUTH_ENABLED:
        identity = await db_asyncpg.fetchrow(
            "select * from identities where organization_id = $1 and user_id = $2 and access_scope = 'organization'",
            tenant.organization_id,
            user_id,
        )
        assert identity["user_id"] == user_id

    # test that session was created
    session = await db_asyncpg.fetchrow(
        "select * from tracer_session where tenant_id = $1 and name = 'default'",
        tenant.id,
    )
    assert session["name"] == "default"


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth"],
    reason="needs user_id; new user flow is different in basic auth",
)
async def test_user_invite_flow(
    use_api_key: bool,
    db_asyncpg: asyncpg.Connection,
) -> None:
    """Test that users can be invited."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth2 = authed_client.auth
        client2 = authed_client.client

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth1 = authed_client.auth
        client1 = authed_client.client

        new_user_email = "<EMAIL>"
        stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
        new_user_jwt = jwt_for_user(
            user_id=stored_user_id,
            user_email=new_user_email,
        )
        new_user_headers = {
            "Authorization": f"Bearer {new_user_jwt}",
        }

        # test new user only has access to their own personal tenant
        response = await client1.get("/tenants", headers=new_user_headers)

        assert response.status_code == 200

        tenants = response.json()
        tenant_ids = [UUID(t["id"]) for t in tenants]

        assert len(tenant_ids) == 1, "A new personal tenant should have been created"
        assert auth1.tenant_id not in tenant_ids, "No access to tenant one"
        assert auth2.tenant_id not in tenant_ids, "No access to tenant two"
        tenant = schemas.Tenant(**tenants[0])

        jwt = jwt_for_user(
            user_id=stored_user_id,
            user_email=new_user_email,
        )
        headers = {
            "Authorization": f"Bearer {jwt}",
            "X-Tenant-Id": str(tenant_ids[0]),
            "X-Organization-Id": str(tenant.organization_id),
        }
        response = await client1.get("/orgs/current", headers=headers)
        assert response.status_code == 200
        config_new_tenant = response.json()["config"]
        assert config_new_tenant == _EXPECTED_PERSONAL_ORG_CONFIG
        assert tenant.is_personal is True

        # add unknown flag to tenant config, which should be ignored
        await db_asyncpg.execute(
            """
        UPDATE tenants
        SET config = config || '{"flags": {"unknown_flag": true}}'
        WHERE id = $1
        """,
            auth1.tenant_id,
        )

        response = await client1.get("/orgs/current", headers=headers)

        assert response.status_code == 200

        response = await client1.get("/tenants", headers=new_user_headers)

        assert response.status_code == 200

        tenants = response.json()
        tenant_ids = [UUID(t["id"]) for t in tenants]

        assert len(tenant_ids) == 1, "A new personal tenant should have been created"
        assert auth1.tenant_id not in tenant_ids, "No access to tenant one"
        assert auth2.tenant_id not in tenant_ids, "No access to tenant two"
        new_tenant = schemas.Tenant(**tenants[0])

        jwt = jwt_for_user(
            user_id=stored_user_id,
            user_email=new_user_email,
        )
        headers = {
            "Authorization": f"Bearer {jwt}",
            "X-Tenant-Id": str(tenant_ids[0]),
            "X-Organization-Id": str(new_tenant.organization_id),
        }

        response = await client1.get("/orgs/current", headers=headers)
        assert response.status_code == 200
        config_new_tenant = response.json()["config"]
        assert config_new_tenant == _EXPECTED_PERSONAL_ORG_CONFIG
        assert new_tenant.is_personal is True

        # test new user has no pending invites (for now)
        response = await client1.get("/workspaces/pending", headers=new_user_headers)
        assert response.status_code == 200
        assert len(response.json()) == 0

        # test new user can't claim a pending invite that doesn't exist
        response = await client1.post(
            f"/workspaces/pending/{uuid4()}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 404

        # create a pending invite for someone else
        other_new_user_email = "<EMAIL>"
        response = await client1.post(
            "/orgs/current/members",
            json={
                "email": other_new_user_email,
            },
        )
        assert response.status_code == 200, response.text
        other_new_user_pending_identity = schemas.PendingIdentity(**response.json())
        assert other_new_user_pending_identity.organization_id == auth1.organization_id
        other_new_user_pending_identity_id = other_new_user_pending_identity.id

        # pending organization identity should exist with Org User b/c RBAC is enabled
        # so we don't infer Org Admin role
        response = await client1.get("/orgs/current/members")
        assert response.status_code == 200
        org_members = schemas.OrganizationMembers(**response.json())
        assert other_new_user_email in [m.email for m in org_members.pending]
        org_pending_identity = next(
            m for m in org_members.pending if m.email == other_new_user_email
        )
        assert org_pending_identity.organization_id == auth1.organization_id
        assert org_pending_identity.user_id == other_new_user_pending_identity.user_id
        assert org_pending_identity.role_name == "Organization User"
        assert org_pending_identity.read_only is True
        assert org_pending_identity.tenant_id is None

        # test new user can't see a pending invite that isn't theirs
        response = await client1.get("/workspaces/pending", headers=new_user_headers)
        assert response.status_code == 200
        assert len(response.json()) == 0

        # test new user can't claim a pending invite that isn't theirs
        response = await client1.post(
            f"/workspaces/pending/{other_new_user_pending_identity_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 404

        # test new user can't delete a pending invite that isn't theirs
        response = await client1.delete(
            f"/workspaces/pending/{other_new_user_pending_identity_id}",
            headers=new_user_headers,
        )
        assert response.status_code == 404

        org_user_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
        )
        ws_viewer_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
        )
        ws_editor_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'WORKSPACE_USER'"
        )

        # create a pending invite for this user
        response = await client1.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth1.tenant_id)],
                "workspace_role_id": str(ws_editor_role_id),
            },
        )
        assert response.status_code == 200, response.text

        # test that if a user is invited twice, the second invite fails
        # and does not create a duplicate invite
        response = await client1.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth1.tenant_id)],
                "workspace_role_id": str(ws_viewer_role_id),
            },
        )
        assert response.status_code == 409
        response = await client1.get("/workspaces/current/members")
        assert response.status_code == 200
        assert (
            len([p for p in response.json()["pending"] if p["email"] == new_user_email])
            == 1
        )

        # test new user can see a pending invite that is theirs
        response = await client1.get("/workspaces/pending", headers=new_user_headers)
        assert response.status_code == 200
        assert len(response.json()) == 1
        assert response.json()[0]["id"] == str(auth1.tenant_id)

        # test that an org-level pending identity exists
        org_members = await get_org_members(auth1)
        assert new_user_email in [m.email for m in org_members.pending]

        # test new user cannot use a tenant until they claim the invite
        response = await client1.get(
            "/datasets",
            headers={
                **new_user_headers,
                "X-Tenant-Id": str(auth1.tenant_id),
            },
        )
        assert response.status_code == 403

        # test new user can delete a pending invite that is theirs
        response = await client1.delete(
            f"/workspaces/pending/{auth1.tenant_id}",
            headers=new_user_headers,
        )
        assert response.status_code == 200

        # test that the invite is gone from the tenant members list
        response = await client1.get("/workspaces/current/members")
        assert response.status_code == 200
        assert (
            len([p for p in response.json()["pending"] if p["email"] == new_user_email])
            == 0
        )

        # test that the invite is gone from the pending list for the user
        response = await client1.get(
            "/workspaces/pending",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        assert len(response.json()) == 0

        # test that the org-level pending identity still exists
        org_members = await get_org_members(auth1)
        assert new_user_email in [m.email for m in org_members.pending]

        # create a pending invite for this user
        response = await client1.post(
            "/orgs/current/members",
            headers=client2.headers,
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth2.tenant_id)],
                "workspace_role_id": str(ws_editor_role_id),
            },
        )
        assert response.status_code == 200

        # test new user can claim a pending invite that is theirs
        response = await client1.post(
            f"/workspaces/pending/{auth2.tenant_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        claimed_identity = schemas.Identity(**response.json())

        # test new user can use a tenant after they claim the invite
        response = await client1.get(
            "/datasets",
            headers={
                **new_user_headers,
                "X-Tenant-Id": str(auth2.tenant_id),
            },
        )
        assert response.status_code == 200

        # test new user can create a dataset (has write permissions)
        response = await client1.post(
            "/datasets",
            headers={
                **new_user_headers,
                "X-Tenant-Id": str(auth2.tenant_id),
            },
            json={
                "name": random_lower_string(),
            },
        )
        assert response.status_code == 200

        # confirm new user's workspace identity
        assert claimed_identity.access_scope == schemas.AccessScope.workspace
        assert claimed_identity.organization_id == auth2.organization_id
        assert claimed_identity.tenant_id == auth2.tenant_id
        assert claimed_identity.read_only is False

        # confirm new user's organization identity and no pending identity
        org_members = await get_org_members(auth2)
        assert claimed_identity.user_id in [m.user_id for m in org_members.members]
        assert new_user_email not in [m.email for m in org_members.pending]
        org_identity_full = next(
            m for m in org_members.members if m.user_id == claimed_identity.user_id
        )
        # organization identity should exist with Org User b/c RBAC is enabled
        # so we don't infer Org Admin role
        assert org_identity_full.role_name == "Organization User"
        assert org_identity_full.role_id == await db_asyncpg.fetchval(
            "SELECT id from roles WHERE name = 'ORGANIZATION_USER'"
        )
        assert org_identity_full.access_scope == schemas.AccessScope.organization
        assert org_identity_full.read_only is True
        assert org_identity_full.tenant_id is None
        assert org_identity_full.organization_id == auth2.organization_id


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth"],
    reason="needs user_id",
)
async def test_user_invite_flow_case_sensitive(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that user invites are not case sensitive."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client2 = authed_client.client
        auth2 = authed_client.auth

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        client = authed_client.client

        new_user_email = "<EMAIL>"
        new_user_email_upper = "<EMAIL>"
        new_user_id = uuid.uuid5(uuid.NAMESPACE_OID, new_user_email)
        new_user_jwt = jwt_for_user(
            user_id=new_user_id,
            user_email=new_user_email,
        )
        new_user_headers = {
            "Authorization": f"Bearer {new_user_jwt}",
        }

        # test new user only has access to their own personal tenant
        response = await client.get("/tenants", headers=new_user_headers)

        assert response.status_code == 200

        tenants = response.json()
        tenant_ids = [UUID(t["id"]) for t in tenants]

        assert len(tenant_ids) == 1, "A new personal tenant should have been created"
        assert auth.tenant_id not in tenant_ids, "No access to tenant one"
        assert auth2.tenant_id not in tenant_ids, "No access to tenant two"

        jwt = jwt_for_user(
            user_id=new_user_id,
            user_email=new_user_email,
        )
        headers = {
            "Authorization": f"Bearer {jwt}",
            "X-Tenant-Id": str(tenant_ids[0]),
            "X-Organization-Id": str(tenants[0]["organization_id"]),
        }

        response = await client.get("/orgs/current", headers=headers)
        assert response.status_code == 200
        config_new_tenant = response.json()["config"]
        assert config_new_tenant == _EXPECTED_PERSONAL_ORG_CONFIG
        assert tenants[0]["is_personal"] is True

        # test new user has no pending invites (for now)
        response = await client.get("/workspaces/pending", headers=new_user_headers)
        assert response.status_code == 200
        assert len(response.json()) == 0

        # test new user can't claim a pending invite that doesn't exist
        response = await client.post(
            f"/workspaces/pending/{uuid4()}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 404

        org_user_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
        )
        ws_editor_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'WORKSPACE_USER'"
        )

        # create a pending invite for this user using lower case email
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth.tenant_id)],
                "workspace_role_id": str(ws_editor_role_id),
            },
        )
        assert response.status_code == 200, response.text

        # test that if a user is invited twice (even with a different cased email), the second invite fails
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email_upper,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth.tenant_id)],
                "workspace_role_id": str(ws_editor_role_id),
            },
        )
        assert response.status_code == 409
        response = await client.get("/workspaces/current/members")
        assert response.status_code == 200
        assert (
            len(
                [
                    p
                    for p in response.json()["pending"]
                    if p["email"].lower() == new_user_email
                ]
            )
            == 1
        )

        # test new user can see a pending invite that is theirs
        response = await client.get("/workspaces/pending", headers=new_user_headers)
        assert response.status_code == 200
        assert len(response.json()) == 1
        assert response.json()[0]["id"] == str(auth.tenant_id)

        # test new user cannot use a tenant until they claim the invite
        response = await client.get(
            "/datasets",
            headers={
                **new_user_headers,
                "X-Tenant-Id": str(auth.tenant_id),
            },
        )
        assert response.status_code == 403

        # test new user can delete a pending invite that is theirs
        response = await client.delete(
            f"/workspaces/pending/{auth.tenant_id}",
            headers=new_user_headers,
        )
        assert response.status_code == 200

        # test that the invite is gone from the tenant members list
        response = await client.get("/workspaces/current/members")
        assert response.status_code == 200
        assert (
            len([p for p in response.json()["pending"] if p["email"] == new_user_email])
            == 0
        )

        # test that the invite is gone from the pending list for the user
        response = await client.get(
            "/workspaces/pending",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        assert len(response.json()) == 0

        # test new user can delete a pending invite that is theirs
        response = await client.delete(
            f"/orgs/pending/{auth.organization_id}",
            headers=new_user_headers,
        )
        assert response.status_code == 200

        # create another pending invite for this user
        response = await client.post(
            "/orgs/current/members",
            headers=client2.headers,
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth2.tenant_id)],
                "workspace_role_id": str(ws_editor_role_id),
            },
        )
        assert response.status_code == 200, response.text

        # test new user can claim a pending invite that is theirs
        response = await client.post(
            f"/workspaces/pending/{auth2.tenant_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200

        # test new user can use a tenant after they claim the invite
        response = await client.get(
            "/datasets",
            headers={
                **new_user_headers,
                "X-Tenant-Id": str(auth2.tenant_id),
            },
        )
        assert response.status_code == 200

        # test new user can create a dataset (has write permissions)
        response = await client.post(
            "/datasets",
            headers={
                **new_user_headers,
                "X-Tenant-Id": str(auth2.tenant_id),
            },
            json={
                "name": random_lower_string(),
            },
        )
        assert response.status_code == 200


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth"],
    reason="needs user_id",
)
async def test_user_invite_flow_read_only(
    use_api_key: bool,
    db_asyncpg: asyncpg.Connection,
) -> None:
    """Test that tenants can be listed."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client2:
        auth2 = authed_client2.auth
        client2 = authed_client2.client

        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
            auth = authed_client.auth
            client = authed_client.client

            new_user_email = "<EMAIL>"
            new_user_id = uuid.uuid5(uuid.NAMESPACE_OID, new_user_email)
            new_user_jwt = jwt_for_user(
                user_id=new_user_id,
                user_email=new_user_email,
            )
            new_user_headers = {
                "Authorization": f"Bearer {new_user_jwt}",
            }

            # test new user only has access to their own personal tenant
            response = await client.get("/tenants", headers=new_user_headers)

            assert response.status_code == 200

            tenants = response.json()
            tenant_ids = [UUID(t["id"]) for t in tenants]

            assert len(tenant_ids) == 1, (
                "A new personal tenant should have been created"
            )
            assert auth.tenant_id not in tenant_ids, "No access to tenant one"
            assert auth2.tenant_id not in tenant_ids, "No access to tenant two"

            jwt = jwt_for_user(
                user_id=new_user_id,
                user_email=new_user_email,
            )
            headers = {
                "Authorization": f"Bearer {jwt}",
                "X-Tenant-Id": str(tenant_ids[0]),
                "X-Organization-Id": str(tenants[0]["organization_id"]),
            }
            response = await client.get("/orgs/current", headers=headers)
            assert response.status_code == 200
            config_new_tenant = response.json()["config"]
            assert config_new_tenant == _EXPECTED_PERSONAL_ORG_CONFIG
            assert tenants[0]["is_personal"] is True

            # test new user has no pending invites (for now)
            response = await client.get("/workspaces/pending", headers=new_user_headers)
            assert response.status_code == 200
            assert len(response.json()) == 0

            ws_viewer_role_id = await db_asyncpg.fetchval(
                "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
            )
            org_user_role_id = await db_asyncpg.fetchval(
                "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
            )

            # create a pending invite for this user
            response = await client.post(
                "/orgs/current/members",
                json={
                    "email": new_user_email,
                    "workspace_role_id": str(ws_viewer_role_id),
                    "workspace_ids": [str(auth.tenant_id)],
                },
            )
            assert response.status_code == 200

            # test new user can see a pending invite that is theirs
            response = await client.get("/workspaces/pending", headers=new_user_headers)
            assert response.status_code == 200
            assert len(response.json()) == 1
            assert response.json()[0]["id"] == str(auth.tenant_id)

            # test new user cannot use a tenant until they claim the invite
            response = await client.get(
                "/datasets",
                headers={
                    **new_user_headers,
                    "X-Tenant-Id": str(auth.tenant_id),
                },
            )
            assert response.status_code == 403

            # test new user can delete a pending invite that is theirs
            response = await client.delete(
                f"/workspaces/pending/{auth.tenant_id}",
                headers=new_user_headers,
            )
            assert response.status_code == 200

            # test that the invite is gone from the tenant members list
            response = await client.get("/workspaces/current/members")
            assert response.status_code == 200
            assert (
                len(
                    [
                        p
                        for p in response.json()["pending"]
                        if p["email"] == new_user_email
                    ]
                )
                == 0
            )

            # test that the invite is gone from the pending list for the user
            response = await client.get(
                "/workspaces/pending",
                headers=new_user_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) == 0

            # create a pending invite for this user
            response = await client.post(
                "/orgs/current/members",
                headers=client2.headers,
                json={
                    "email": new_user_email,
                    "role_id": str(org_user_role_id),
                    "workspace_ids": [str(auth2.tenant_id)],
                    "workspace_role_id": str(ws_viewer_role_id),
                },
            )
            assert response.status_code == 200

            # test new user can claim a pending invite that is theirs
            response = await client.post(
                f"/workspaces/pending/{auth2.tenant_id}/claim",
                headers=new_user_headers,
            )
            assert response.status_code == 200
            claimed_identity = schemas.Identity(**response.json())

            # test new user can use a tenant after they claim the invite
            response = await client.get(
                "/datasets",
                headers={
                    **new_user_headers,
                    "X-Tenant-Id": str(auth2.tenant_id),
                },
            )
            assert response.status_code == 200

            # test new user cannot create a dataset (has read-only permissions)
            response = await client.post(
                "/datasets",
                headers={
                    **new_user_headers,
                    "X-Tenant-Id": str(auth2.tenant_id),
                },
                json={
                    "name": random_lower_string(),
                },
            )
            assert response.status_code == 403

            # confirm new user's workspace identity
            assert claimed_identity.access_scope == schemas.AccessScope.workspace
            assert claimed_identity.organization_id == auth2.organization_id
            assert claimed_identity.tenant_id == auth2.tenant_id
            assert claimed_identity.read_only is True

            # confirm new user's organization identity
            org_members = await get_org_members(auth2)
            assert claimed_identity.user_id in [m.user_id for m in org_members.members]
            org_identity_full = next(
                m for m in org_members.members if m.user_id == claimed_identity.user_id
            )
            assert org_identity_full.role_name == "Organization User"
            assert org_identity_full.role_id == await db_asyncpg.fetchval(
                "SELECT id from roles WHERE name = 'ORGANIZATION_USER'"
            )
            assert org_identity_full.access_scope == schemas.AccessScope.organization
            assert org_identity_full.read_only is True
            assert org_identity_full.tenant_id is None
            assert org_identity_full.organization_id == auth2.organization_id


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_tenant_secrets(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    # use a fresh tenant to avoid other tests inserting secrets from interfering
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_org_id=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        response = await client.get("/workspaces/current/secrets")
        assert response.status_code == 200
        assert response.json() == []

        response = await client.post(
            "/workspaces/current/secrets",
            json=[
                {"key": "OH_SO_SECRET", "value": "VERY_SECRET"},
            ],
        )
        assert response.status_code == 200
        assert response.json() is None

        response = await client.get("/workspaces/current/secrets")
        assert response.status_code == 200
        assert response.json() == [{"key": "OH_SO_SECRET"}]

        stored = await db_asyncpg.fetchval(
            "SELECT secrets FROM tenants WHERE id = $1", auth.tenant_id
        )
        assert stored is not None
        assert isinstance(stored, bytes)
        assert "VERY_SECRET" not in stored.decode()

        decrypted = await list_secrets(auth)
        assert decrypted == [schemas.Secret(key="OH_SO_SECRET", value="VERY_SECRET")]

        response = await client.post(
            "/workspaces/current/secrets",
            json=[
                {"key": "THE_SECOND_ONE", "value": "VERY_VERY_SECRET"},
            ],
        )
        assert response.status_code == 200
        assert response.json() is None

        decrypted = await list_secrets(auth)
        assert decrypted == [
            schemas.Secret(key="OH_SO_SECRET", value="VERY_SECRET"),
            schemas.Secret(key="THE_SECOND_ONE", value="VERY_VERY_SECRET"),
        ]

        response = await client.post(
            "/workspaces/current/secrets",
            json=[
                {"key": "OH_SO_SECRET", "value": None},
            ],
        )
        assert response.status_code == 200

        decrypted = await list_secrets(auth)
        assert decrypted == [
            schemas.Secret(key="THE_SECOND_ONE", value="VERY_VERY_SECRET")
        ]


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
async def test_workspace_stats(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    key1 = random_lower_string()

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client2:
        client2 = authed_client2.client
        auth2 = authed_client2.auth

        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
            client = authed_client.client
            auth = authed_client.auth

            response = await client.post(
                "/workspaces/current/tag-keys",
                json={"key": key1},
            )

            assert response.status_code == 200
            key_id = response.json()["id"]

            value1 = random_lower_string()
            response = await client.post(
                f"/workspaces/current/tag-keys/{key_id}/tag-values",
                json={"value": value1},
            )

            assert response.status_code == 200
            value_id = response.json()["id"]

            # Create some resources to tag: annotation queue, dataset, project
            response = await client.post(
                "/annotation-queues",
                json={
                    "name": random_lower_string(),
                    "description": "test",
                },
            )
            assert response.status_code == 200
            queue_id = response.json()["id"]

            response = await client.post(
                "/datasets",
                json={
                    "name": random_lower_string(),
                    "description": "test",
                },
            )
            assert response.status_code == 200
            dataset_id = response.json()["id"]

            response = await client.post(
                "/sessions",
                json={"name": random_lower_string()},
            )
            assert response.status_code == 200
            session_id = response.json()["id"]

            response = await client.post(
                "/charts/section",
                json={"title": random_lower_string()},
            )
            assert response.status_code == 200
            section_id = response.json()["id"]

            response = await client.post(
                "/charts/create",
                json={
                    "title": random_lower_string(),
                    "description": "test",
                    "chart_type": "line",
                    "metadata": {"key": "value"},
                    "section_id": section_id,
                    "series": [
                        {
                            "name": random_lower_string(),
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                            },
                            "metric": "median_tokens",
                        }
                    ],
                },
            )
            assert response.status_code == 200

            response = await client.post(
                "/charts/create",
                json={
                    "title": random_lower_string(),
                    "description": "test",
                    "chart_type": "line",
                    "metadata": {"key": "value"},
                    "section_id": section_id,
                    "series": [
                        {
                            "name": random_lower_string(),
                            "filters": {
                                "session": [session_id],
                                "filter": "eq(is_root, true)",
                            },
                            "metric": "median_tokens",
                        }
                    ],
                },
            )
            assert response.status_code == 200

            response = await client.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "queue",
                    "resource_id": queue_id,
                    "tag_value_id": value_id,
                },
            )
            assert response.status_code == 200

            response = await client.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "dataset",
                    "resource_id": dataset_id,
                    "tag_value_id": value_id,
                },
            )
            assert response.status_code == 200

            response = await client.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "project",
                    "resource_id": session_id,
                    "tag_value_id": value_id,
                },
            )
            assert response.status_code == 200

            response = await client.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "dashboard",
                    "resource_id": section_id,
                    "tag_value_id": value_id,
                },
            )
            assert response.status_code == 200

            # Create another tag value
            value2 = random_lower_string()
            response = await client.post(
                f"/workspaces/current/tag-keys/{key_id}/tag-values",
                json={"value": value2},
            )

            assert response.status_code == 200
            value_id2 = response.json()["id"]

            response = await client.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "queue",
                    "resource_id": queue_id,
                    "tag_value_id": value_id2,
                },
            )
            assert response.status_code == 200

            response = await client.get(
                "/workspaces/current/taggings",
            )
            assert response.status_code == 200
            assert len(response.json()) >= 2

            # fetch stats with no tag
            response = await client.get(
                "/workspaces/current/stats",
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["tenant_id"] == str(auth.tenant_id)
            assert resp["dataset_count"] >= 1
            assert resp["tracer_session_count"] >= 1
            assert resp["annotation_queue_count"] >= 1
            assert resp["repo_count"] >= 0
            assert resp["dashboards_count"] >= 1

            # try filtering by tag_value_id
            response = await client.get(
                f"/workspaces/current/stats?tag_value_id={value_id}",
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["tenant_id"] == str(auth.tenant_id)
            assert resp["dataset_count"] == 1
            assert resp["tracer_session_count"] == 1
            assert resp["annotation_queue_count"] == 1
            assert resp["repo_count"] == 0
            assert resp["dashboards_count"] == 1

            # try filtering by tag_value_id_2
            response = await client.get(
                f"/workspaces/current/stats?tag_value_id={value_id2}",
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["tenant_id"] == str(auth.tenant_id)
            assert resp["dataset_count"] == 0
            assert resp["tracer_session_count"] == 0
            assert resp["annotation_queue_count"] == 1
            assert resp["repo_count"] == 0
            assert resp["dashboards_count"] == 0

            # try filtering by tag_value_id_2 and tag_value_id
            response = await client.get(
                f"/workspaces/current/stats?tag_value_id={value_id}&tag_value_id={value_id2}",
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["tenant_id"] == str(auth.tenant_id)
            assert resp["dataset_count"] == 0
            assert resp["tracer_session_count"] == 0
            assert resp["annotation_queue_count"] == 1
            assert resp["repo_count"] == 0
            assert resp["dashboards_count"] == 0

            response = await client.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "dataset",
                    "resource_id": dataset_id,
                    "tag_value_id": value_id2,
                },
            )

            assert response.status_code == 200

            response = await client.get(
                f"/workspaces/current/stats?tag_value_id={value_id}&tag_value_id={value_id2}",
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["tenant_id"] == str(auth.tenant_id)
            assert resp["dataset_count"] == 1
            assert resp["tracer_session_count"] == 0
            assert resp["annotation_queue_count"] == 1
            assert resp["repo_count"] == 0
            assert resp["dashboards_count"] == 0

            response = await client.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "dashboard",
                    "resource_id": section_id,
                    "tag_value_id": value_id2,
                },
            )

            assert response.status_code == 200

            response = await client.get(
                f"/workspaces/current/stats?tag_value_id={value_id}&tag_value_id={value_id2}",
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["tenant_id"] == str(auth.tenant_id)
            assert resp["dataset_count"] == 1
            assert resp["tracer_session_count"] == 0
            assert resp["annotation_queue_count"] == 1
            assert resp["repo_count"] == 0
            assert resp["dashboards_count"] == 1

            # test that tenant two has no stats with tag filter
            response = await client2.get(
                f"/workspaces/current/stats?tag_value_id={value_id}",
            )
            assert response.status_code == 200

            resp = response.json()
            assert resp["tenant_id"] == str(auth2.tenant_id)
            assert resp["dataset_count"] == 0
            assert resp["tracer_session_count"] == 0
            assert resp["annotation_queue_count"] == 0
            assert resp["repo_count"] == 0
            assert resp["dashboards_count"] == 0

            response = await client.delete(
                f"/charts/section/{section_id}",
            )
            assert response.status_code == 200
