"""Test correct behavior of conversation view"""

import math
from typing import Any, Awaitable, Callable
from uuid import UUID, uuid4

import pytest
from httpx import AsyncClient

from app import config

pytestmark = [
    pytest.mark.anyio,
    pytest.mark.skipif(
        config.settings.AUTH_TYPE == "oauth", reason="oauth not support in queue"
    ),
]


async def test_list_conversations(
    http_tenant_one: AsyncClient,
    tenant_one_tracer_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    conversation_id = uuid4()
    run_id_1 = uuid4()
    run_id_2 = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Run 1",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:14:24.571809",
            "extra": {
                "foo": "bar",
                "metadata": {"conversation_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "what's the weather in SF"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_1),
        },
    )

    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Run 2",
            "start_time": "2023-05-05T05:15:24.571809",
            "end_time": "2023-05-05T05:16:24.571809",
            "extra": {
                "foo": "bar",
                # use thread_id instead of conversation_id
                "metadata": {"thread_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "is this hot or is this cold?"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "This is quite typical of SF weather",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "This is quite typical of SF weather",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
                "usage_metadata": {
                    "input_tokens": 10,
                    "output_tokens": 20,
                    "total_tokens": 30,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # add feedback to runs
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "foo",
            "score": 100,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "foo",
            "score": 0,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "foo",
            "score": 50,
            "value": "green",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    await wait_until_task_queue_empty()

    # test with and without start time
    for start_time in [None, "2023-05-01T00:00:00.571809Z"]:
        # test before and after cutoff date
        response = await http_tenant_one.post(
            "/runs/group",
            json={
                "session_id": str(tenant_one_tracer_session_id),
                "group_by": "conversation",
                "start_time": start_time if start_time else None,
            },
        )
        assert response.status_code == 200
        json = response.json()

        assert len(json["groups"]) >= 1, (
            f"Groups should be >= 1 for start_time: {start_time}"
        )
        assert json["total"] >= 1, f"Total should be >= 1 for start_time: {start_time}"

        group = next(
            (g for g in json["groups"] if g["group_key"] == str(conversation_id)),
            None,
        )
        assert group is not None
        assert group["count"] == 2
        assert group["min_start_time"] == "2023-05-05T05:13:24.571809"
        assert group["max_start_time"] == "2023-05-05T05:15:24.571809"
        assert group["first_inputs"] == "human: what's the weather in SF"
        assert group["last_outputs"] == "ai: This is quite typical of SF weather"
        assert group["total_tokens"] == 28 + 30
        assert group["feedback_stats"]["foo"]["values"] == {
            "blue": 2,
            "green": 1,
        }
        assert group["feedback_stats"]["foo"]["avg"] == 50.0
        assert math.isclose(
            group["feedback_stats"]["foo"]["stdev"], 40.************, rel_tol=1e-9
        )
        assert group["feedback_stats"]["foo"]["n"] == 3


async def test_list_conversations_filtered(
    http_tenant_one: AsyncClient,
    tenant_one_tracer_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    conversation_id = uuid4()
    run_id_1 = uuid4()
    run_id_2 = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Run 1",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:14:24.571809",
            "extra": {
                "foo": "bar",
                "metadata": {"conversation_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "what's the weather in SF"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_1),
        },
    )

    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Run 2",
            "start_time": "2023-05-05T05:15:24.571809",
            "end_time": "2023-05-05T05:16:24.571809",
            "extra": {
                "foo": "bar",
                # use thread_id instead of conversation_id
                "metadata": {"thread_id": str(conversation_id)},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {"input": ["human", "is this hot or is this cold?"]},
            "outputs": {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "This is quite typical of SF weather",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "This is quite typical of SF weather",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
                "usage_metadata": {
                    "input_tokens": 10,
                    "output_tokens": 20,
                    "total_tokens": 30,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # add feedback to runs
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "foo",
            "score": 100,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "foo",
            "score": 0,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "foo",
            "score": 50,
            "value": "green",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    await wait_until_task_queue_empty()

    # test with and without start time
    for start_time in [None, "2023-05-01T00:00:00.571809Z"]:
        # test before and after cutoff date
        response = await http_tenant_one.post(
            "/runs/group",
            json={
                "session_id": str(tenant_one_tracer_session_id),
                "group_by": "conversation",
                "start_time": start_time if start_time else None,
                "filter": 'and(eq(name, "Run 1"))',
            },
        )
        assert response.status_code == 200
        json = response.json()

        assert len(json["groups"]) >= 1, (
            f"Groups should be >= 1 for start_time: {start_time}"
        )
        assert json["total"] >= 1, f"Total should be >= 1 for start_time: {start_time}"

        group = next(
            (g for g in json["groups"] if g["group_key"] == str(conversation_id)),
            None,
        )
        assert group is not None
        assert group["count"] == 2
        assert group["min_start_time"] == "2023-05-05T05:13:24.571809"
        assert group["max_start_time"] == "2023-05-05T05:15:24.571809"
        assert group["first_inputs"] == "human: what's the weather in SF"
        assert group["last_outputs"] == "ai: This is quite typical of SF weather"
        assert group["total_tokens"] == 28 + 30
        assert group["feedback_stats"]["foo"]["values"] == {
            "blue": 2,
            "green": 1,
        }
        assert group["feedback_stats"]["foo"]["avg"] == 50.0
        assert math.isclose(
            group["feedback_stats"]["foo"]["stdev"], 40.************, rel_tol=1e-9
        )
        assert group["feedback_stats"]["foo"]["n"] == 3

    # test filter does not return any groups
    response = await http_tenant_one.post(
        "/runs/group",
        json={
            "session_id": str(tenant_one_tracer_session_id),
            "group_by": "conversation",
            "filter": 'and(eq(name, "SomeOtherRun"))',
        },
    )
    assert response.status_code == 200
    json = response.json()
    assert len(json["groups"]) == 0
    assert json["total"] == 0
