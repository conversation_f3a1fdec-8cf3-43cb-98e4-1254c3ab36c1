"""Test correct functionality of annotation queue endpoints."""

import asyncio
import random
from datetime import datetime, timedelta, timezone
from uuid import UUID, uuid4

import asyncpg
import pytest
from httpx import AsyncClient
from lc_database.database import asyncpg_conn

from app import config, crud, schemas
from app.api.auth import AuthInfo
from app.tests.utils import (
    create_test_runs,
    fresh_tenant_client,
    post_runs,
    random_lower_string,
    wait_for_runs_to_exist,
)


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="need tenants/orgs")
async def test_create_and_read_annotation_queue(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that an annotation queue can be created and read, and that the pagination works as expected."""
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, include_read_only=True
    ) as authed_tenant:
        authed_client = authed_tenant.client
        read_only_client = authed_tenant.read_only_client
        assert read_only_client is not None
        tenant_id = authed_tenant.auth.tenant_id
        queue_data = {
            "name": random_lower_string(),
            "description": "test",
            "tenant_id": str(tenant_id),
        }
        response = await authed_client.post("/annotation-queues", json=queue_data)
        assert response.status_code == 200

        response = await authed_client.get("/annotation-queues")
        assert response.status_code == 200
        response_names = [q["name"] for q in response.json()]
        assert queue_data["name"] in response_names

        queue_data = {
            "name": random_lower_string(),
            "description": "test2",
            "tenant_id": str(tenant_id),
        }
        response = await authed_client.post("/annotation-queues", json=queue_data)
        assert response.status_code == 200

        queue_data = {
            "name": random_lower_string(),
            "description": "test3",
            "tenant_id": str(tenant_id),
        }
        response = await authed_client.post("/annotation-queues", json=queue_data)
        assert response.status_code == 200

        response = await authed_client.get("/annotation-queues?limit=2&offset=0")
        assert response.status_code == 200
        assert len(response.json()) == 2
        assert response.headers["X-Pagination-Total"] == "3"

        id_to_update = response.json()[0]["id"]

        response = await authed_client.post(
            "/sessions",
            json={"name": random_lower_string()},
        )

        session_obj = response.json()
        session_id = session_obj["id"]
        assert response.status_code == 200

        run_id = uuid4()
        await post_runs(
            "/runs/batch",
            authed_client,
            post=[
                {
                    "name": "AgentExecutor",
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "run_type": "chain",
                    "session_id": str(session_id),
                    "id": str(run_id),
                    "start_time": "2024-06-05T05:14:30.000000Z",
                    "dotted_order": f"20240605T051430000000Z{run_id}",
                    "trace_id": str(run_id),
                }
            ],
        )
        await wait_for_runs_to_exist(authed_client, run_id)

        response = await authed_client.post(
            f"/annotation-queues/{id_to_update}/runs",
            json=[str(run_id)],
        )
        assert response.status_code == 200, response.text

        # Check that the user can read runs from the annotation queue
        response = await authed_client.get(f"/annotation-queues/{id_to_update}/run/0")
        assert response.status_code == 200
        assert response.json()["id"] == str(run_id)

        # Update the queue
        response = await authed_client.patch(
            f"/annotation-queues/{id_to_update}", json={"name": "NEW NAME"}
        )
        assert response.status_code == 200

        # Check queue is updated
        response = await authed_client.get("/annotation-queues")
        assert response.status_code == 200
        response_names = [q["name"] for q in response.json()]
        assert "NEW NAME" in response_names

        # Check that the read-only user can read the annotation queue
        response = await read_only_client.get("/annotation-queues")
        assert response.status_code == 200
        response_names = [q["name"] for q in response.json()]
        assert "NEW NAME" in response_names

        # Check that the read-only user can read runs from the annotation queue
        response = await read_only_client.get(
            f"/annotation-queues/{id_to_update}/run/0"
        )
        assert response.status_code == 200
        assert response.json()["id"] == str(run_id)

        response = await authed_client.delete(f"/annotation-queues/{id_to_update}")
        assert response.status_code == 200
        response = await authed_client.get("/annotation-queues")
        assert response.status_code == 200
        response_names = [q["name"] for q in response.json()]
        assert "NEW NAME" not in response_names


async def test_read_annotation_queue_by_name(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that an annotation queue can be read by name."""
    queue = await crud.create_annotation_queue(
        db_asyncpg,
        auth_tenant_one,
        schemas.AnnotationQueueCreateSchema(
            name=random_lower_string(),
            description="test",
        ),
    )
    response = await http_tenant_one.get(f"/annotation-queues?name={queue.name}")

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == queue.name
    assert response.json()[0]["description"] == queue.description


async def test_annotation_queue_rubric_items(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that an annotation queue rubric items can be created and read."""
    queue_data = {
        "name": random_lower_string(),
        "description": "test",
        "tenant_id": str(auth_tenant_one.tenant_id),
        "rubric_instructions": "test instructions",
        "rubric_items": [
            {
                "feedback_key": "test",
                "description": "test",
                "value_descriptions": {"test": "test"},
            },
            {
                "feedback_key": "test2",
                "description": "test2",
                "score_descriptions": {"test2": "test2"},
            },
        ],
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}")
    assert response.status_code == 200

    assert response.json()["rubric_instructions"] == "test instructions"
    assert len(response.json()["rubric_items"]) == 2
    assert response.json()["rubric_items"][0] == {
        "feedback_key": "test",
        "description": "test",
        "value_descriptions": {"test": "test"},
        "score_descriptions": {},
    } or response.json()["rubric_items"][1] == {
        "feedback_key": "test",
        "description": "test",
        "value_descriptions": {"test": "test"},
        "score_descriptions": {},
    }

    assert response.json()["rubric_items"][0] == {
        "feedback_key": "test2",
        "description": "test2",
        "value_descriptions": {},
        "score_descriptions": {"test2": "test2"},
    } or response.json()["rubric_items"][1] == {
        "feedback_key": "test2",
        "description": "test2",
        "value_descriptions": {},
        "score_descriptions": {"test2": "test2"},
    }

    response = await http_tenant_one.patch(
        f"/annotation-queues/{queue_id}",
        json={
            "rubric_instructions": "test NEW instructions 2",
            "rubric_items": [
                {
                    "feedback_key": "test NEW",
                    "description": "test NEW",
                    "value_descriptions": {"test NEW": "test NEW"},
                },
                {
                    "feedback_key": "test2 NEW",
                    "description": "test2 NEW",
                    "score_descriptions": {"test2 NEW": "test2 NEW"},
                },
            ],
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}")
    assert response.status_code == 200

    assert response.json()["rubric_instructions"] == "test NEW instructions 2"
    assert len(response.json()["rubric_items"]) == 2
    assert response.json()["rubric_items"][0] == {
        "feedback_key": "test NEW",
        "description": "test NEW",
        "value_descriptions": {"test NEW": "test NEW"},
        "score_descriptions": {},
    } or response.json()["rubric_items"][1] == {
        "feedback_key": "test NEW",
        "description": "test NEW",
        "value_descriptions": {"test NEW": "test NEW"},
        "score_descriptions": {},
    }

    assert response.json()["rubric_items"][0] == {
        "feedback_key": "test2 NEW",
        "description": "test2 NEW",
        "value_descriptions": {},
        "score_descriptions": {"test2 NEW": "test2 NEW"},
    } or response.json()["rubric_items"][1] == {
        "feedback_key": "test2 NEW",
        "description": "test2 NEW",
        "value_descriptions": {},
        "score_descriptions": {"test2 NEW": "test2 NEW"},
    }

    response = await http_tenant_one.patch(
        f"/annotation-queues/{queue_id}",
        json={
            "rubric_instructions": "test NEW NEW instructions 2",
            "rubric_items": [
                {
                    "feedback_key": "test NEW",
                    "description": "test NEW NEW",
                    "value_descriptions": {"test NEW NEW": "test NEW NEW"},
                }
            ],
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}")
    assert response.status_code == 200

    assert response.json()["rubric_instructions"] == "test NEW NEW instructions 2"
    assert len(response.json()["rubric_items"]) == 1
    assert response.json()["rubric_items"][0] == {
        "feedback_key": "test NEW",
        "description": "test NEW NEW",
        "value_descriptions": {"test NEW NEW": "test NEW NEW"},
        "score_descriptions": {},
    }


async def test_read_annotation_queue_by_name_contains(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that an annotation queue can be searched by name_contains."""
    queue_name = random_lower_string()
    query = queue_name[1:-1]
    queue = await crud.create_annotation_queue(
        db_asyncpg,
        auth_tenant_one,
        schemas.AnnotationQueueCreateSchema(
            name=queue_name,
            description="test",
        ),
    )

    response = await http_tenant_one.get(f"/annotation-queues?name_contains={query}")

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == queue.name
    assert response.json()[0]["description"] == queue.description

    response = await http_tenant_one.get(f"/annotation-queues?ids={queue.id}")

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == queue.name
    assert response.json()[0]["description"] == queue.description


@pytest.mark.flaky
@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_crud_annotation_queue(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
) -> None:
    """Test that an annotation queue can be created and read."""
    queue_data = {
        "name": random_lower_string(),
        "description": "test",
        "tenant_id": str(auth_tenant_one.tenant_id),
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]
    queue_updated_at = response.json()["updated_at"]

    queue_data2 = {
        "name": random_lower_string(),
        "description": "test2",
        "tenant_id": str(auth_tenant_one.tenant_id),
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data2)
    assert response.status_code == 200
    queue_id2 = response.json()["id"]

    response = await http_tenant_one.get(
        f"/annotation-queues?name={queue_data['name']}"
    )
    assert response.status_code == 200
    assert response.json()[0]["name"] == queue_data["name"]
    assert response.json()[0]["description"] == queue_data["description"]

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")

    assert response.status_code == 404

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    run_id2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id2),
        },
    )
    assert response.status_code == 202

    await wait_for_runs_to_exist(http_tenant_one, run_id)
    await wait_for_runs_to_exist(http_tenant_one, run_id2)

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id)],
    )
    assert response.status_code == 200, response.text

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id2)],
    )
    assert response.status_code == 200, response.text

    response = await http_tenant_one.get("/annotation-queues")
    assert response.status_code == 200
    queue_1 = [q for q in response.json() if q["id"] == queue_id][0]
    assert queue_1["updated_at"] > queue_updated_at

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.status_code == 200
    assert response.json()["size"] == 2

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id2}/runs",
        json=[str(run_id)],
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id2}/runs",
        json=[str(run_id)],  # test adding the same run again
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")

    assert response.status_code == 200
    run = response.json()
    assert str(run_id) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["outputs"]["output"] == "39,566,248"
    id_to_update = run["id"]

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/1")

    assert response.status_code == 200
    run = response.json()
    assert str(run_id2) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["outputs"] is None

    response = await http_tenant_one.get(f"/annotation-queues/{run_id}/queues")
    response_ids = [r["id"] for r in response.json()]
    assert queue_id in response_ids and queue_id2 in response_ids

    response = await http_tenant_one.get(f"/annotation-queues/{run_id2}/queues")
    response_ids = [r["id"] for r in response.json()]
    assert queue_id in response_ids and queue_id2 not in response_ids

    # Test Updating a run in the queue

    # Create a random date:
    def random_date(start, end):
        return start + timedelta(
            seconds=random.randint(0, int((end - start).total_seconds()))
        )

    start = datetime(2020, 1, 1)
    end = datetime.now()
    queue_id_to_update = queue_id
    updated_last_review_time = random_date(start, end).isoformat()
    updated_added_at_time = random_date(start, end).isoformat()
    response = await http_tenant_one.patch(
        f"/annotation-queues/{queue_id_to_update}/runs/{id_to_update}",
        json={
            "last_reviewed_time": updated_last_review_time,
            "added_at": updated_added_at_time,
        },
    )

    assert response.status_code == 200
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    updated_run = response.json()
    assert updated_run["id"] == id_to_update
    assert updated_run["last_reviewed_time"] == updated_last_review_time
    assert updated_run["added_at"] == updated_added_at_time

    # Test Deleting a run from the queue
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    assert response.json()["id"] == str(id_to_update)

    response = await http_tenant_one.delete(
        f"/annotation-queues/{queue_id_to_update}/runs/{id_to_update}",
    )
    assert response.status_code == 200
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    assert response.json()["id"] == str(run_id2)


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["oauth", "none"], reason="write queue/single tenant"
)
async def test_bulk_delete_annotation_queue(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that an annotation queue can be created and read."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        queue_data = {
            "name": random_lower_string(),
            "description": "test",
            "tenant_id": str(authed_client.auth.tenant_id),
        }
        response = await authed_client.client.post(
            "/annotation-queues", json=queue_data
        )
        assert response.status_code == 200
        queue_id = response.json()["id"]
        queue_updated_at = response.json()["updated_at"]

        response = await authed_client.client.get(
            f"/annotation-queues?name={queue_data['name']}"
        )
        assert response.status_code == 200
        assert response.json()[0]["name"] == queue_data["name"]
        assert response.json()[0]["description"] == queue_data["description"]

        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/0"
        )

        assert response.status_code == 404

        run_id, run_id2, run_id3, run_id4 = uuid4(), uuid4(), uuid4(), uuid4()
        await create_test_runs(authed_client.auth, [run_id, run_id2, run_id3, run_id4])

        await wait_for_runs_to_exist(
            authed_client.auth, run_id, run_id2, run_id3, run_id4
        )

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs",
            json=[str(run_id)],
        )
        assert response.status_code == 200, response.text

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs",
            json=[str(run_id2)],
        )
        assert response.status_code == 200, response.text

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs",
            json=[str(run_id3)],
        )
        assert response.status_code == 200, response.text

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs",
            json=[str(run_id4)],
        )
        assert response.status_code == 200, response.text

        response = await authed_client.client.get("/annotation-queues")
        assert response.status_code == 200
        queue_1 = [q for q in response.json() if q["id"] == queue_id][0]
        assert queue_1["updated_at"] > queue_updated_at

        response = await authed_client.client.get(f"/annotation-queues/{queue_id}/size")
        assert response.status_code == 200
        assert response.json()["size"] == 4

        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/0"
        )
        assert response.status_code == 200
        run = response.json()
        assert str(run_id) == run["id"]
        assert run["added_at"] is not None

        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/1"
        )
        assert response.status_code == 200
        run = response.json()
        assert str(run_id2) == run["id"]
        assert run["added_at"] is not None

        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/2"
        )
        assert response.status_code == 200
        run = response.json()
        assert str(run_id3) == run["id"]
        assert run["added_at"] is not None

        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/3"
        )
        assert response.status_code == 200
        run = response.json()
        assert str(run_id4) == run["id"]
        assert run["added_at"] is not None

        # Test Deleting a run from the queue
        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs/delete",
            json={"run_ids": [str(run_id), str(run_id2)], "delete_all": True},
        )
        assert response.status_code == 422

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs/delete",
            json={"exclude_run_ids": [str(run_id), str(run_id2)]},
        )
        assert response.status_code == 422

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs/delete",
            json={
                "exclude_run_ids": [str(run_id), str(run_id2)],
                "run_ids": [str(run_id3)],
            },
        )
        assert response.status_code == 422

        response = await authed_client.client.get(f"/annotation-queues/{queue_id}/size")
        assert response.status_code == 200
        assert response.json()["size"] == 4

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs/delete",
            json={"exclude_run_ids": [str(run_id), str(run_id2)], "delete_all": True},
        )
        assert response.status_code == 200, response.text
        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/0"
        )
        assert response.status_code == 200
        assert response.json()["id"] == str(run_id)

        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/1"
        )
        assert response.status_code == 200
        assert response.json()["id"] == str(run_id2)

        response = await authed_client.client.get(f"/annotation-queues/{queue_id}/size")
        assert response.status_code == 200
        assert response.json()["size"] == 2

        archived = await db_asyncpg.fetch(
            "SELECT * FROM annotation_queue_runs_archive WHERE queue_id = $1",
            queue_id,
        )
        assert len(archived) == 2
        all_archived_ids = [a["run_id"] for a in archived]
        assert all(run_id in all_archived_ids for run_id in [run_id3, run_id4])

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs/delete",
            json={"run_ids": [str(run_id), str(run_id2), str(uuid4())]},
        )
        assert response.status_code == 404, (
            "Should not be able to delete runs that are not in the queue"
        )

        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/0"
        )
        assert response.status_code == 200
        assert response.json()["id"] == str(run_id)

        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/1"
        )
        assert response.status_code == 200
        assert response.json()["id"] == str(run_id2)

        response = await authed_client.client.get(f"/annotation-queues/{queue_id}/size")
        assert response.status_code == 200
        assert response.json()["size"] == 2

        archived = await db_asyncpg.fetch(
            "SELECT * FROM annotation_queue_runs_archive WHERE queue_id = $1",
            queue_id,
        )
        assert len(archived) == 2
        all_archived_ids = [a["run_id"] for a in archived]
        assert all(run_id in all_archived_ids for run_id in [run_id3, run_id4])

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs/delete",
            json={"run_ids": [str(run_id), str(run_id2)]},
        )
        assert response.status_code == 200, response.text
        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/run/0"
        )
        assert response.status_code == 404

        response = await authed_client.client.get(f"/annotation-queues/{queue_id}/size")
        assert response.status_code == 200
        assert response.json()["size"] == 0

        archived = await db_asyncpg.fetch(
            "SELECT * FROM annotation_queue_runs_archive WHERE queue_id = $1",
            queue_id,
        )
        assert len(archived) == 4
        all_archived_ids = [a["run_id"] for a in archived]
        assert all(
            run_id in all_archived_ids for run_id in [run_id, run_id2, run_id3, run_id4]
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_fetch_annotation_queue_by_tag(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that an annotation queue can be read by tag."""
    queue_data = {
        "name": f"super_unique_prefix_{random_lower_string()}",
        "description": "test",
        "tenant_id": str(auth_tenant_one.tenant_id),
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]

    queue_data2 = {
        "name": f"super_unique_prefix_{random_lower_string()}",
        "description": "test2",
        "tenant_id": str(auth_tenant_one.tenant_id),
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data2)
    assert response.status_code == 200
    queue_id2 = response.json()["id"]

    queue_data3 = {
        "name": random_lower_string(),
        "description": "test2",
        "tenant_id": str(auth_tenant_one.tenant_id),
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data3)
    assert response.status_code == 200
    queue_id3 = response.json()["id"]

    response = await http_tenant_one.get(
        f"/annotation-queues?name={queue_data['name']}"
    )
    assert response.status_code == 200
    assert response.json()[0]["name"] == queue_data["name"]
    assert response.json()[0]["description"] == queue_data["description"]
    assert len(response.json()) == 1

    key1 = random_lower_string()
    response = await http_tenant_one.post(
        "/workspaces/current/tag-keys",
        json={"key": key1},
    )

    assert response.status_code == 200
    key1_id = response.json()["id"]

    value1 = random_lower_string()
    response = await http_tenant_one.post(
        f"/workspaces/current/tag-keys/{key1_id}/tag-values",
        json={"value": value1},
    )

    assert response.status_code == 200
    value1_id = response.json()["id"]

    value2 = random_lower_string()
    response = await http_tenant_one.post(
        f"/workspaces/current/tag-keys/{key1_id}/tag-values",
        json={"value": value2},
    )

    assert response.status_code == 200
    value2_id = response.json()["id"]

    # tag sections
    responses_taggings = await asyncio.gather(
        http_tenant_one.post(
            "/workspaces/current/taggings",
            json={
                "resource_type": "queue",
                "resource_id": queue_id,
                "tag_value_id": value1_id,
            },
        ),
        http_tenant_one.post(
            "/workspaces/current/taggings",
            json={
                "resource_type": "queue",
                "resource_id": queue_id2,
                "tag_value_id": value2_id,
            },
        ),
        http_tenant_one.post(
            "/workspaces/current/taggings",
            json={
                "resource_type": "queue",
                "resource_id": queue_id3,
                "tag_value_id": value1_id,
            },
        ),
    )

    assert all(response.status_code == 200 for response in responses_taggings)

    response = await http_tenant_one.get(
        f"/annotation-queues?name={queue_data['name']}&tag_value_id={value1_id}"
    )
    assert response.status_code == 200
    assert response.json()[0]["name"] == queue_data["name"]
    assert response.json()[0]["description"] == queue_data["description"]
    assert len(response.json()) == 1

    response = await http_tenant_one.get(
        f"/annotation-queues?name={queue_data3['name']}&tag_value_id={value1_id}"
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["name"] == queue_data3["name"]
    assert response.json()[0]["description"] == queue_data3["description"]

    response = await http_tenant_one.get(
        f"/annotation-queues?name={queue_data2['name']}&tag_value_id={value1_id}"
    )
    assert response.status_code == 200
    assert len(response.json()) == 0

    response = await http_tenant_one.get(f"/annotation-queues?tag_value_id={value1_id}")
    assert response.status_code == 200
    assert len(response.json()) == 2
    queue_1 = [q for q in response.json() if q["id"] == queue_id][0]
    assert queue_1["name"] == queue_data["name"]
    assert queue_1["description"] == queue_data["description"]

    queue_3 = [q for q in response.json() if q["id"] == queue_id3][0]
    assert queue_3["name"] == queue_data3["name"]
    assert queue_3["description"] == queue_data3["description"]

    response = await http_tenant_one.get(f"/annotation-queues?tag_value_id={value2_id}")
    assert response.status_code == 200
    assert len(response.json()) == 1
    queue_2 = response.json()[0]
    assert queue_2["name"] == queue_data2["name"]
    assert queue_2["description"] == queue_data2["description"]

    response = await http_tenant_one.get(
        f"/annotation-queues?tag_value_id={value2_id}&name_contains=super_unique_prefix"
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    queue_2 = response.json()[0]
    assert queue_2["name"] == queue_data2["name"]
    assert queue_2["description"] == queue_data2["description"]

    response = await http_tenant_one.get(
        f"/annotation-queues?tag_value_id={value1_id}&name_contains=super_unique_prefix"
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    queue_1 = response.json()[0]
    assert queue_1["name"] == queue_data["name"]
    assert queue_1["description"] == queue_data["description"]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_read_annotation_queue_from_other_user(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_two: AuthInfo,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    tenant_one_tracer_session_id: UUID,
    tenant_two_tracer_session_id: UUID,
) -> None:
    """Test that a annotation queue cannot be read by another tenant, and that runs from tenant A can't be added to an annotation queue owned by tenant B."""
    tenant_two_queue = await crud.create_annotation_queue(
        db_asyncpg,
        auth_tenant_two,
        schemas.AnnotationQueueCreateSchema(
            name=random_lower_string(),
            description="test",
        ),
    )

    response = await http_tenant_one.get("/annotation-queues")
    assert response.status_code == 200
    assert tenant_two_queue.id not in [q["id"] for q in response.json()]

    tenant_one_run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(tenant_one_run_id),
        },
    )

    assert response.status_code == 202

    tenant_two_run_id = uuid4()
    response = await http_tenant_two.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_two_tracer_session_id),
            "id": str(tenant_two_run_id),
        },
    )
    assert response.status_code == 202

    await wait_for_runs_to_exist(auth_tenant_one, tenant_one_run_id)
    await wait_for_runs_to_exist(auth_tenant_two, tenant_two_run_id)

    # First, ensure that tenant one can't insert one of their own runs into tenant two's queue
    response = await http_tenant_one.post(
        f"/annotation-queues/{tenant_two_queue.id}/runs",
        json=[str(tenant_one_run_id)],
    )
    assert response.status_code == 404

    # Then, ensure that tenant two can't add one of tenant one's runs to their queue (even if a valid run is included)
    response = await http_tenant_two.post(
        f"/annotation-queues/{tenant_two_queue.id}/runs",
        json=[str(tenant_one_run_id), str(tenant_two_run_id)],
    )
    assert response.status_code == 404

    # Next, ensure that tenant one can't insert one of tenant two's runs into tenant two's queue for them
    response = await http_tenant_one.post(
        f"/annotation-queues/{tenant_two_queue.id}/runs",
        json=[str(tenant_two_run_id)],
    )

    await http_tenant_two.post(
        f"/annotation-queues/{tenant_two_queue.id}/runs",
        json=[str(tenant_two_run_id)],
    )

    # Ensure that tenant one can't delete or update runs in tenant two's queue

    response = await http_tenant_one.patch(
        f"/annotation-queues/{tenant_two_queue.id}/runs/{str(tenant_two_run_id)}",
        json={"last_reviewed_time": "2020-01-01T00:00:00.000000"},
    )
    assert response.status_code == 404

    response = await http_tenant_two.patch(
        f"/annotation-queues/{tenant_two_queue.id}/runs/{str(tenant_two_run_id)}",
        json={"last_reviewed_time": "2020-01-01T00:00:00.000000"},
    )
    assert response.status_code == 200

    response = await http_tenant_one.delete(
        f"/annotation-queues/{tenant_two_queue.id}/runs/{str(tenant_two_run_id)}",
    )
    assert response.status_code == 404

    response = await http_tenant_two.delete(
        f"/annotation-queues/{tenant_two_queue.id}/runs/{str(tenant_two_run_id)}",
    )
    assert response.status_code == 200


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_annotation_queue_deleted_runs(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    tenant_one_tracer_session_id_2: UUID,
) -> None:
    """Test that deleted runs are cleaned up from the queue correctly."""
    queue_data = {
        "name": random_lower_string(),
        "description": "test",
        "tenant_id": str(auth_tenant_one.tenant_id),
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    run_id4 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor4",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id4),
        },
    )
    assert response.status_code == 202

    await wait_for_runs_to_exist(auth_tenant_one, run_id, run_id4)

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id)],
    )
    assert response.status_code == 200, response.text

    async with asyncpg_conn() as db:
        await db.fetch(
            """
            INSERT INTO annotation_queue_runs (run_id, queue_id, added_at)
            SELECT run_id, $2, now()
            FROM UNNEST($1::uuid[]) as run_id
            ON CONFLICT (run_id, queue_id) DO NOTHING
            RETURNING *
            """,
            [str(uuid4()), str(uuid4())],
            queue_id,
        )

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id4)],
    )
    assert response.status_code == 200, response.text

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.status_code == 200
    assert response.json()["size"] == 4

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")

    assert response.status_code == 200
    run = response.json()
    assert str(run_id) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["outputs"] is None

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/1")

    assert response.status_code == 200
    run = response.json()
    assert str(run_id4) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor4"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["outputs"] is None

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/2")
    assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_annotation_queue_deleted_runs_bulk(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
) -> None:
    """Test that deleted runs are cleaned up from the queue correctly."""
    queue_data = {
        "name": random_lower_string(),
        "description": "test",
        "tenant_id": str(auth_tenant_one.tenant_id),
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    run_id4 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor4",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id4),
        },
    )
    assert response.status_code == 202

    await wait_for_runs_to_exist(auth_tenant_one, run_id, run_id4)

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id)],
    )
    assert response.status_code == 200, response.text

    async with asyncpg_conn() as db:
        await db.fetch(
            """
            INSERT INTO annotation_queue_runs (run_id, queue_id, added_at)
            SELECT run_id, $2, now()
            FROM UNNEST($1::uuid[]) as run_id
            ON CONFLICT (run_id, queue_id) DO NOTHING
            RETURNING *
            """,
            [str(uuid4()), str(uuid4())],
            queue_id,
        )

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id4)],
    )
    assert response.status_code == 200, response.text

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.status_code == 200
    assert response.json()["size"] == 4

    response = await http_tenant_one.get(
        f"/annotation-queues/{queue_id}/runs", params={"limit": 2}
    )

    assert response.status_code == 200
    runs = response.json()

    run = runs[0]
    assert str(run_id) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["outputs"] is None

    run = runs[1]
    assert str(run_id4) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor4"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["outputs"] is None

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/2")
    assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_annotation_queue_read_runs_bulk(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    tenant_one_tracer_session_id_2: UUID,
) -> None:
    """Test that deleted runs are cleaned up from the queue correctly."""
    queue_data = {
        "name": random_lower_string(),
        "description": "test",
        "tenant_id": str(auth_tenant_one.tenant_id),
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    run_id2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor2",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id_2),
            "id": str(run_id2),
        },
    )
    assert response.status_code == 202

    run_id3 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor3",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id_2),
            "id": str(run_id3),
            "parent_id": str(run_id),
        },
    )
    assert response.status_code == 202

    run_id4 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor4",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id4),
        },
    )
    assert response.status_code == 202

    await wait_for_runs_to_exist(auth_tenant_one, run_id, run_id2, run_id3, run_id4)

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id)],
    )
    assert response.status_code == 200, response.text

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id2)],
    )
    assert response.status_code == 200, response.text

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id3)],
    )
    assert response.status_code == 200, response.text

    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id4)],
    )
    assert response.status_code == 200, response.text

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.status_code == 200
    assert response.json()["size"] == 4

    response = await http_tenant_one.get(
        f"/annotation-queues/{queue_id}/runs", params={"limit": 2}
    )

    assert response.status_code == 200
    assert response.headers["X-Pagination-Total"] == "3"
    runs = response.json()

    run = runs[0]
    assert str(run_id) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["outputs"] is None

    run = runs[1]
    assert str(run_id2) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor2"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id_2)
    assert run["outputs"] is None

    response = await http_tenant_one.get(
        f"/annotation-queues/{queue_id}/runs", params={"limit": 2, "offset": 2}
    )

    assert response.status_code == 200
    assert response.headers["X-Pagination-Total"] == "4"
    runs = response.json()

    run = runs[0]
    assert str(run_id3) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor3"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id_2)
    assert run["outputs"] is None

    run = runs[1]
    assert str(run_id4) == run["id"]
    assert run["added_at"] is not None
    assert run["inputs"]["input"] == "How many people live in canada as of 2023?"
    assert run["name"] == "AgentExecutor4"
    assert run["run_type"] == "chain"
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["outputs"] is None


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_all_workspace_members_review(
    http_tenant_one: AsyncClient,
    http_tenant_one_user_two: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
):
    # test multiple users interacting with an annotation queue
    # where everyone can review all items
    queue_data = {
        "name": random_lower_string(),
        "description": "test_multiple_users",
        "tenant_id": str(auth_tenant_one.tenant_id),
        "num_reviewers_per_item": None,
        "enable_reservations": True,
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]

    # INSERT TWO RUNS INTO THE QUEUE
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    run_id2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor4",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id2),
        },
    )
    assert response.status_code == 202

    await wait_for_runs_to_exist(auth_tenant_one, run_id, run_id2)

    run_ids = [str(run_id), str(run_id2)]
    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=run_ids,
    )
    assert response.status_code == 200, response.text

    # NOW CHECK THE SIZE OF THE QUEUE AND ENSURE IT'S 2 LONG FOR BOTH TENANTS SINCE THEY BOTH CAN VIEW ALL ITEMS
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.status_code == 200
    assert response.json()["size"] == len(run_ids)

    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.status_code == 200
    assert response.json()["size"] == len(run_ids)

    # NOW GO THROUGH THE QUEUE AS USER 1
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    run = response.json()
    assert run["queue_run_id"]
    index_zero_run_id = run["id"]
    # mark as completed
    response = await http_tenant_one.post(
        f"/annotation-queues/status/{run['queue_run_id']}",
        json={"status": "completed"},
    )

    # check that the queue size has decreased by 1
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids) - 1
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.json()["id"] in run_ids

    # check that the queue size has not changed for user two
    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids)

    # try to insert the same run again, it should not change the size of the queue for user two
    # but it should bring the run back into the queue for user one
    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(index_zero_run_id)],
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids)
    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids)

    # mark all runs as completed for user 1
    for i in range(len(run_ids)):
        response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
        run = response.json()
        assert run["queue_run_id"]
        response = await http_tenant_one.post(
            f"/annotation-queues/status/{run['queue_run_id']}",
            json={"status": "viewed"},
        )
        response = await http_tenant_one.post(
            f"/annotation-queues/status/{run['queue_run_id']}",
            json={"status": "completed"},
        )

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == 0
    response = await http_tenant_one_user_two.get(
        f"/annotation-queues/{queue_id}/run/0"
    )
    run = response.json()
    assert run["queue_run_id"]

    # check that we get an error if we try to mark a run as completed that is not in the queue
    random_run_id = uuid4()
    response = await http_tenant_one.post(
        f"/annotation-queues/status/{random_run_id}",
        json={"status": "completed"},
    )
    assert response.status_code == 404

    # check that we persist the override_added_at field when viewing a run after it has been moved to end
    # move to end
    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids)
    response = await http_tenant_one_user_two.get(
        f"/annotation-queues/{queue_id}/run/0"
    )
    run = response.json()
    assert run["queue_run_id"]
    now = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    response = await http_tenant_one_user_two.post(
        f"/annotation-queues/status/{run['queue_run_id']}",
        json={"status": None, "override_added_at": now},
    )
    assert response.status_code == 200
    response = await http_tenant_one_user_two.post(
        f"/annotation-queues/status/{run['queue_run_id']}",
        json={"status": "viewed"},
    )
    assert response.status_code == 200
    response = await http_tenant_one_user_two.get(
        f"/annotation-queues/{queue_id}/run/1"
    )
    assert response.status_code == 200
    run = response.json()
    run["effective_added_at"] == now

    # check size of tenant 1 user 2 queue
    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids)
    # check total number of runs
    response = await http_tenant_one_user_two.get(
        f"/annotation-queues/{queue_id}/total_size"
    )
    assert response.status_code == 200
    assert response.json()["size"] == len(run_ids)

    # mark runs as completed for user 2
    for i in range(len(run_ids)):
        response = await http_tenant_one_user_two.get(
            f"/annotation-queues/{queue_id}/run/0"
        )
        run = response.json()
        assert run["queue_run_id"]
        response = await http_tenant_one_user_two.post(
            f"/annotation-queues/status/{run['queue_run_id']}",
            json={"status": "completed"},
        )

    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == 0

    # check total number of runs
    workspace = await http_tenant_one_user_two.get("/workspaces/current/members")
    if workspace.status_code == 200:
        if len(workspace.json()["members"]) == 2:
            response = await http_tenant_one_user_two.get(
                f"/annotation-queues/{queue_id}/total_size"
            )
            assert response.status_code == 200
            assert response.json()["size"] == 0
        else:
            response = await http_tenant_one_user_two.get(
                f"/annotation-queues/{queue_id}/total_size"
            )
            assert response.status_code == 200
            assert response.json()["size"] == 2


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_multiple_reviewers_with_reservations(
    http_tenant_one: AsyncClient,
    http_tenant_one_user_two: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
):
    # test multiple users interacting with an annotation queue
    # where 1 person must review each item
    queue_data = {
        "name": random_lower_string(),
        "description": "test_multiple_users_with_reservations",
        "tenant_id": str(auth_tenant_one.tenant_id),
        "num_reviewers_per_item": 1,
        "enable_reservations": True,
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]

    # INSERT TWO RUNS INTO THE QUEUE
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    run_id2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor4",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id2),
        },
    )
    assert response.status_code == 202

    await wait_for_runs_to_exist(auth_tenant_one, run_id, run_id2)

    run_ids = [str(run_id), str(run_id2)]
    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=run_ids,
    )
    assert response.status_code == 200, response.text

    # Now check the size of the queue and make sure it's two items long for both users
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.status_code == 200
    assert response.json()["size"] == len(run_ids)

    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.status_code == 200
    assert response.json()["size"] == len(run_ids)

    # Go through the queue as user 1
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    user_1_first_run = response.json()
    assert user_1_first_run["queue_run_id"]

    # check that the queue size has decreased by 1 for user 2
    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids) - 1

    # check that the first item we're seeing is different from user 1
    response = await http_tenant_one_user_two.get(
        f"/annotation-queues/{queue_id}/run/0"
    )
    assert response.status_code == 200
    user_2_first_run = response.json()
    assert user_2_first_run["queue_run_id"]
    assert user_2_first_run["id"] != user_1_first_run["id"]
    assert user_2_first_run["id"] in run_ids

    # check that user 1's queue also decreased to 1
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids) - 1

    # now unmark the run as viewed for user 1 to emulate losing a reservation
    response = await http_tenant_one.post(
        f"/annotation-queues/status/{user_1_first_run['queue_run_id']}",
        json={"status": None},
    )

    # check that the queue size has increased by 1 for user 2
    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids)

    # now let's make sure user 1 can't mark user 2's reserved run as reserved
    assert response.status_code == 200
    response = await http_tenant_one.post(
        f"/annotation-queues/status/{user_2_first_run['queue_run_id']}",
        json={"status": "viewed"},
    )
    assert response.status_code == 409

    # now let's mark a run as completed for user 2 and ensure it's removed from the queue
    response = await http_tenant_one_user_two.post(
        f"/annotation-queues/status/{user_2_first_run['queue_run_id']}",
        json={"status": "completed"},
    )
    assert response.status_code == 200
    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids) - 1
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids) - 1

    # now let's mark a run as completed for user 1 and ensure it's removed from the queue
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    user_2_first_run = response.json()
    assert user_2_first_run["queue_run_id"]

    # size of user 2 queue should be 0
    response = await http_tenant_one_user_two.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == 0

    response = await http_tenant_one.post(
        f"/annotation-queues/status/{user_2_first_run['queue_run_id']}",
        json={"status": "completed"},
    )
    assert response.status_code == 200
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/size")
    assert response.json()["size"] == len(run_ids) - 2

    # make sure the whole queue is empty for everyone
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/total_size")
    assert response.status_code == 200
    assert response.json()["size"] == 0

    # add runs back to the queue
    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=run_ids,
    )
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/total_size")
    assert response.status_code == 200
    assert response.json()["size"] == 2

    # Test move to end repeatedly

    # User 1 reserve
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    user_1_first_run = response.json()

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/1")
    assert response.status_code == 200
    user_1_second_run = response.json()

    # move to end
    now = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    response = await http_tenant_one.post(
        f"/annotation-queues/status/{user_1_first_run['queue_run_id']}",
        json={"status": None, "override_added_at": now},
    )
    assert response.status_code == 200

    # reserve again
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    user_1_first_run_after = response.json()

    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/1")
    assert response.status_code == 200

    # assert the run is different from the first one
    assert user_1_first_run_after["id"] != user_1_first_run["id"]
    # assert the run is the same as the second one
    assert user_1_first_run_after["id"] == user_1_second_run["id"]

    # move to end
    now = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    response = await http_tenant_one.post(
        f"/annotation-queues/status/{user_1_second_run['queue_run_id']}",
        json={"status": None, "override_added_at": now},
    )
    assert response.status_code == 200
    # reserve again
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    user_1_third_run = response.json()
    # assert the run is the same as the first one
    assert user_1_third_run["id"] == user_1_first_run["id"]
    # move to end
    now = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

    response = await http_tenant_one.post(
        f"/annotation-queues/status/{user_1_third_run['queue_run_id']}",
        json={"status": None, "override_added_at": now},
    )
    assert response.status_code == 200
    # reserve again
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    user_1_fourth_run = response.json()
    # assert the run is the same as the second one
    assert user_1_fourth_run["id"] == user_1_second_run["id"]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_three_reviewers_with_reservations(
    http_tenant_one: AsyncClient,
    http_tenant_one_user_two: AsyncClient,
    http_tenant_one_user_three: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
):
    queue_data = {
        "name": random_lower_string(),
        "description": "test_three_reviewers_with_reservations",
        "tenant_id": str(auth_tenant_one.tenant_id),
        "num_reviewers_per_item": 2,
        "enable_reservations": True,
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]

    # INSERT TWO RUNS INTO THE QUEUE
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    run_id2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor4",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id2),
        },
    )
    assert response.status_code == 202

    await wait_for_runs_to_exist(auth_tenant_one, run_id, run_id2)

    run_ids = [str(run_id), str(run_id2)]
    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=run_ids,
    )
    assert response.status_code == 200, response.text

    # The difference here is we need to make sure two people can reserve the same run

    # User 1 reserve
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    user_1_first_run = response.json()
    assert user_1_first_run["queue_run_id"]

    # User 2 reserve
    response = await http_tenant_one_user_two.get(
        f"/annotation-queues/{queue_id}/run/0"
    )
    assert response.status_code == 200
    user_2_first_run = response.json()
    assert user_2_first_run["queue_run_id"]
    assert user_2_first_run["id"] == user_1_first_run["id"]

    # User 3 tries to reserve the same run
    response = await http_tenant_one_user_three.post(
        f"/annotation-queues/status/{user_1_first_run['queue_run_id']}",
        json={"status": "viewed"},
    )
    assert response.status_code == 409

    # User 3 try to view a run (should be able to view run 2)
    response = await http_tenant_one_user_three.get(
        f"/annotation-queues/{queue_id}/run/0"
    )
    assert response.status_code == 200
    user_3_first_run = response.json()
    assert user_3_first_run["queue_run_id"]
    assert user_3_first_run["id"] != user_1_first_run["id"]

    # User 2 reserve run 2
    response = await http_tenant_one_user_two.get(
        f"/annotation-queues/{queue_id}/run/1"
    )
    assert response.status_code == 200
    user_2_second_run = response.json()
    assert user_2_second_run["queue_run_id"]
    assert user_2_second_run["id"] == user_3_first_run["id"]

    # User 1 try for another run
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/1")
    assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_experiment_queue(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that an annotation queue created from an experiment is populated as expected."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # Setup initial rule
        response = await authed_client.client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        response = await authed_client.client.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "inputs": {"input": "How many people live in Canada?"},
                "outputs": {"output": "38 million"},
            },
        )
        assert response.status_code == 200, response.text
        example_1_id = response.json()["id"]

        response = await authed_client.client.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "inputs": {"input": "How many people live in the United States?"},
                "outputs": {"output": "334 million"},
            },
        )
        assert response.status_code == 200, response.text
        example_2_id = response.json()["id"]

        response = await authed_client.client.post(
            "/sessions",
            json={
                "name": random_lower_string(),
                "trace_tier": "shortlived",
                "reference_dataset_id": dataset_id,
            },
        )
        assert response.status_code == 200, response.text
        session_id = response.json()["id"]

        # Send runs that match the rule
        run_one = uuid4()
        run_two = uuid4()
        response = await authed_client.client.post(
            "/runs/batch",
            json={
                "post": [
                    {
                        "name": "Search",
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Search"},
                        "inputs": {"input": "How many people live in Canada?"},
                        "outputs": {"output": "38 million"},
                        "session_id": session_id,
                        "parent_run_id": None,
                        "run_type": "tool",
                        "id": str(run_one),
                        "trace_id": str(run_one),
                        "dotted_order": f"20230505T051324571809Z{run_one}",
                        "reference_example_id": example_1_id,
                    },
                    {
                        "name": "Search",
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Search"},
                        "inputs": {"input": "What's the capital of France?"},
                        "outputs": {"output": "Paris"},
                        "session_id": session_id,
                        "parent_run_id": None,
                        "run_type": "tool",
                        "id": str(run_two),
                        "trace_id": str(run_two),
                        "dotted_order": f"20230505T051324571809Z{run_two}",
                        "reference_example_id": example_2_id,
                    },
                ]
            },
        )
        assert response.status_code == 202, response.text
        await wait_for_runs_to_exist(authed_client.auth, run_one, run_two)

        response = await authed_client.client.post(
            "/annotation-queues",
            json={
                "session_ids": [session_id],
                "name": random_lower_string(),
            },
        )
        assert response.status_code == 200, response.text
        annotation_queue_id = response.json()["id"]

        response = await authed_client.client.get(
            f"/annotation-queues/{annotation_queue_id}/size"
        )
        assert response.status_code == 200
        assert response.json()["size"] == 2

        response = await authed_client.client.get(
            f"/annotation-queues/{annotation_queue_id}/run/0"
        )
        assert response.status_code == 200
        run = response.json()
        assert run["id"] == str(run_one) or run["id"] == str(run_two)
        assert (
            run["inputs"]["input"] == "How many people live in Canada?"
            or run["inputs"]["input"] == "What's the capital of France?"
        )
        assert (
            run["outputs"]["output"] == "38 million"
            or run["outputs"]["output"] == "Paris"
        )

        response = await authed_client.client.get(
            f"/annotation-queues/{annotation_queue_id}/run/1"
        )
        assert response.status_code == 200
        run = response.json()
        assert run["id"] == str(run_two) or run["id"] == str(run_one)
        assert (
            run["inputs"]["input"] == "What's the capital of France?"
            or run["inputs"]["input"] == "How many people live in Canada?"
        )
        assert (
            run["outputs"]["output"] == "Paris"
            or run["outputs"]["output"] == "38 million"
        )

        annotation_queue_sessions = await db_asyncpg.fetch(
            """
            SELECT session_id
            FROM annotation_queue_sessions
            WHERE annotation_queue_id = $1
            """,
            annotation_queue_id,
        )
        assert len(annotation_queue_sessions) == 1
        assert str(annotation_queue_sessions[0]["session_id"]) == session_id


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_populate_existing_queue(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that an existing annotation queue that is populated from an experiment is populated as expected."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # Setup initial rule
        response = await authed_client.client.post(
            "/datasets",
            json={"name": random_lower_string()},
        )
        assert response.status_code == 200, response.text
        dataset_id = response.json()["id"]

        response = await authed_client.client.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "inputs": {"input": "How many people live in Canada?"},
                "outputs": {"output": "38 million"},
            },
        )
        assert response.status_code == 200, response.text
        example_1_id = response.json()["id"]

        response = await authed_client.client.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "inputs": {"input": "How many people live in the United States?"},
                "outputs": {"output": "334 million"},
            },
        )
        assert response.status_code == 200, response.text
        example_2_id = response.json()["id"]

        response = await authed_client.client.post(
            "/sessions",
            json={
                "name": random_lower_string(),
                "trace_tier": "shortlived",
                "reference_dataset_id": dataset_id,
            },
        )
        assert response.status_code == 200, response.text
        session_id = response.json()["id"]

        # Send runs that match the rule
        run_one = uuid4()
        run_two = uuid4()
        response = await authed_client.client.post(
            "/runs/batch",
            json={
                "post": [
                    {
                        "name": "Search",
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Search"},
                        "inputs": {"input": "How many people live in Canada?"},
                        "outputs": {"output": "38 million"},
                        "session_id": session_id,
                        "parent_run_id": None,
                        "run_type": "tool",
                        "id": str(run_one),
                        "trace_id": str(run_one),
                        "dotted_order": f"20230505T051324571809Z{run_one}",
                        "reference_example_id": example_1_id,
                    },
                    {
                        "name": "Search",
                        "start_time": datetime.now(timezone.utc).isoformat(),
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Search"},
                        "inputs": {"input": "What's the capital of France?"},
                        "outputs": {"output": "Paris"},
                        "session_id": session_id,
                        "parent_run_id": None,
                        "run_type": "tool",
                        "id": str(run_two),
                        "trace_id": str(run_two),
                        "dotted_order": f"20230505T051324571809Z{run_two}",
                        "reference_example_id": example_2_id,
                    },
                ]
            },
        )
        assert response.status_code == 202, response.text
        await wait_for_runs_to_exist(authed_client.auth, run_one, run_two)

        response = await authed_client.client.post(
            "/annotation-queues",
            json={
                "name": random_lower_string(),
            },
        )
        assert response.status_code == 200, response.text
        annotation_queue_id = response.json()["id"]

        response = await authed_client.client.get(
            f"/annotation-queues/{annotation_queue_id}/size"
        )
        assert response.status_code == 200
        assert response.json()["size"] == 0

        response = await authed_client.client.post(
            "/annotation-queues/populate",
            json={
                "queue_id": annotation_queue_id,
                "session_ids": [session_id],
            },
        )
        assert response.status_code == 200, response.text

        response = await authed_client.client.get(
            f"/annotation-queues/{annotation_queue_id}/run/0"
        )
        assert response.status_code == 200
        run = response.json()
        assert run["id"] == str(run_one) or run["id"] == str(run_two)
        assert (
            run["inputs"]["input"] == "How many people live in Canada?"
            or run["inputs"]["input"] == "What's the capital of France?"
        )
        assert (
            run["outputs"]["output"] == "38 million"
            or run["outputs"]["output"] == "Paris"
        )

        response = await authed_client.client.get(
            f"/annotation-queues/{annotation_queue_id}/run/1"
        )
        assert response.status_code == 200
        run = response.json()
        assert run["id"] == str(run_two) or run["id"] == str(run_one)
        assert (
            run["inputs"]["input"] == "What's the capital of France?"
            or run["inputs"]["input"] == "How many people live in Canada?"
        )
        assert (
            run["outputs"]["output"] == "Paris"
            or run["outputs"]["output"] == "38 million"
        )

        annotation_queue_sessions = await db_asyncpg.fetch(
            """
            SELECT session_id
            FROM annotation_queue_sessions
            WHERE annotation_queue_id = $1
            """,
            annotation_queue_id,
        )
        assert len(annotation_queue_sessions) == 1
        assert str(annotation_queue_sessions[0]["session_id"]) == session_id


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_export_annotated_runs_csv(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that dataset can be exported to CSV"""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        score_key = "my_score_feedback"
        response = await authed_client.client.post(
            "/feedback-configs/",
            json={
                "feedback_key": score_key,
                "feedback_config": {"type": "continuous", "min": 0, "max": 1},
            },
        )
        assert response.status_code == 200

        value_key = "my_value_feedback"
        response = await authed_client.client.post(
            "/feedback-configs/",
            json={
                "feedback_key": value_key,
                "feedback_config": {
                    "type": "categorical",
                    "categories": [
                        {"value": 0, "label": "a"},
                        {"value": 0.5, "label": "b"},
                        {"value": 1, "label": "c"},
                    ],
                },
            },
        )
        assert response.status_code == 200

        freeform_key = "my_freeform_feedback"
        response = await authed_client.client.post(
            "/feedback-configs/",
            json={
                "feedback_key": freeform_key,
                "feedback_config": {"type": "freeform"},
            },
        )
        assert response.status_code == 200

        queue_data = {
            "name": random_lower_string(),
            "description": "test_multiple_users_with_reservations",
            "tenant_id": str(authed_client.auth.tenant_id),
            "num_reviewers_per_item": 1,
            "enable_reservations": True,
            "rubric_items": [
                {
                    "feedback_key": score_key,
                },
                {
                    "feedback_key": value_key,
                },
                {
                    "feedback_key": freeform_key,
                },
            ],
        }
        response = await authed_client.client.post(
            "/annotation-queues", json=queue_data
        )
        assert response.status_code == 200
        queue_id = response.json()["id"]

        # INSERT TWO RUNS INTO THE QUEUE
        session_id = uuid4()
        response = await authed_client.client.post(
            "/sessions",
            json={
                "name": "test",
                "trace_tier": "shortlived",
                "reference_dataset_id": None,
                "id": str(session_id),
            },
        )

        run_id = uuid4()
        start_time_1 = (datetime.now(timezone.utc) - timedelta(minutes=1)).isoformat()
        end_time_1 = datetime.now(timezone.utc).isoformat()
        response = await authed_client.client.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "38 million"},
                "run_type": "chain",
                "extra": {"foo": "bar"},
                "start_time": start_time_1,
                "end_time": end_time_1,
                "session_id": str(session_id),
                "id": str(run_id),
            },
        )
        assert response.status_code == 202

        run_id2 = uuid4()

        start_time_2 = (datetime.now(timezone.utc) - timedelta(minutes=1)).isoformat()
        end_time_2 = datetime.now(timezone.utc).isoformat()
        response = await authed_client.client.post(
            "/runs",
            json={
                "name": "AgentExecutor4",
                "inputs": {"input": "How many people live in paris as of 2023?"},
                "outputs": {"output": "1 million"},
                "run_type": "chain",
                "error": "error",
                "extra": {"qux": "baz"},
                "start_time": start_time_2,
                "end_time": end_time_2,
                "session_id": str(session_id),
                "id": str(run_id2),
            },
        )
        assert response.status_code == 202

        await wait_for_runs_to_exist(authed_client.auth, run_id, run_id2)

        response = await authed_client.client.post(
            "/feedback",
            json={
                "run_id": str(run_id),
                "key": score_key,
                "score": 0.5,
            },
        )
        assert response.status_code == 200

        response = await authed_client.client.post(
            "/feedback",
            json={
                "run_id": str(run_id),
                "key": value_key,
                "value": "a",
                "score": 0,
            },
        )
        assert response.status_code == 200

        response = await authed_client.client.post(
            "/feedback",
            json={
                "run_id": str(run_id),
                "key": freeform_key,
                "comment": "hello",
            },
        )
        assert response.status_code == 200

        response = await authed_client.client.post(
            "/feedback",
            json={
                "run_id": str(run_id2),
                "key": score_key,
                "score": 1,
            },
        )
        assert response.status_code == 200

        response = await authed_client.client.post(
            "/feedback",
            json={
                "run_id": str(run_id2),
                "key": value_key,
                "value": "b",
                "score": 0.5,
            },
        )
        assert response.status_code == 200

        response = await authed_client.client.post(
            "/feedback",
            json={
                "run_id": str(run_id2),
                "key": freeform_key,
                "comment": "hola again",
            },
        )
        assert response.status_code == 200

        run_ids = [str(run_id)]
        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs",
            json=run_ids,
        )
        assert response.status_code == 200, response.text

        run_ids = [str(run_id2)]
        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/runs",
            json=run_ids,
        )
        assert response.status_code == 200, response.text

        run_ids = [str(run_id), str(run_id2)]
        # mark runs as completed
        for i in range(len(run_ids)):
            response = await authed_client.client.get(
                f"/annotation-queues/{queue_id}/run/0"
            )
            run = response.json()
            assert run["queue_run_id"]
            response = await authed_client.client.post(
                f"/annotation-queues/status/{run['queue_run_id']}",
                json={"status": "completed"},
            )

        response = await authed_client.client.get(f"/annotation-queues/{queue_id}/size")
        assert response.status_code == 200
        assert response.json()["size"] == 0

        response = await authed_client.client.get(
            f"/annotation-queues/{queue_id}/total_archived"
        )
        assert response.status_code == 200
        assert response.json()["size"] == 2

        response = await authed_client.client.post(
            f"/annotation-queues/{queue_id}/export", json={}
        )
        assert response.status_code == 200
        body = response.content.decode("utf-8")
        # Contains header row
        assert body.startswith(
            "id,status,inputs,outputs,start_time,end_time,reviewed_at,extra"
        )
        split_body = (
            body.split("id,status,inputs,outputs,start_time,end_time,reviewed_at,extra")
            .pop()
            .split("\n")
            .pop(0)
        )
        assert f"feedback.{score_key}" in split_body
        assert f"feedback.{value_key}" in split_body
        assert f"feedback.{freeform_key}" in split_body

        # Parse CSV content to check individual rows
        csv_rows = body.split("\n")
        header = csv_rows[0]
        data_rows = [row for row in csv_rows[1:] if row]

        # Verify we have exactly 2 data rows
        assert len(data_rows) == 2

        # Helper function to parse CSV row into dict
        def parse_csv_row(row: str) -> dict:
            import csv
            from io import StringIO

            reader = csv.reader(StringIO(row), quotechar='"', escapechar="\\")
            values = next(reader)
            return dict(zip(header.split(","), values))

        # Get both rows as dictionaries - run 2 will always be first because it was completed last
        row2, row1 = map(parse_csv_row, data_rows)

        # Verify first run
        assert row1["id"] == str(run_id)
        assert row1["status"] == "success"
        assert '"input": "How many people live in canada as of 2023?"' in row1["inputs"]
        assert '"output": "38 million"' in row1["outputs"]
        assert '"foo": "bar"' in row1["extra"]
        assert row1[f"feedback.{score_key}"] == "0.5"
        assert row1[f"feedback.{value_key}"] == "a"
        assert row1[f"feedback.{freeform_key}"] == "hello"
        assert start_time_1.startswith(row1["start_time"])
        assert end_time_1.startswith(row1["end_time"])

        # Verify second run
        assert row2["id"] == str(run_id2)
        assert row2["status"] == "error"
        assert '"input": "How many people live in paris as of 2023?"' in row2["inputs"]
        assert '"output": "1 million"' in row2["outputs"]
        assert '"qux": "baz"' in row2["extra"]
        assert row2[f"feedback.{score_key}"] == "1.0"
        assert row2[f"feedback.{value_key}"] == "b"
        assert row2[f"feedback.{freeform_key}"] == "hola again"
        assert start_time_2.startswith(row2["start_time"])
        assert end_time_2.startswith(row2["end_time"])


@pytest.mark.asyncio
async def test_annotation_queue_run_include_extra(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
):
    # Create an annotation queue with a random name
    queue_data = {
        "name": random_lower_string(),
        "description": "test",
        "tenant_id": str(auth_tenant_one.tenant_id),
    }
    response = await http_tenant_one.post("/annotation-queues", json=queue_data)
    assert response.status_code == 200
    queue_id = response.json()["id"]

    # Create a run with an extra field
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "TestRun",
            "inputs": {"input": "test"},
            "outputs": {"output": "result"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
            "extra": {"metadata": {"foo": "bar"}},
        },
    )
    assert response.status_code == 202

    # Wait for the run to exist
    await wait_for_runs_to_exist(auth_tenant_one, run_id)

    # Add the run to the annotation queue
    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id)],
    )
    assert response.status_code == 200

    # Create a run with an extra field
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "TestRun",
            "inputs": {"input": "test"},
            "outputs": {"output": "result"},
            "run_type": "chain",
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
            "extra": {"metadata": {"foo": "bar"}},
        },
    )
    assert response.status_code == 202

    # Wait for the run to exist
    await wait_for_runs_to_exist(auth_tenant_one, run_id)

    # Add the run to the annotation queue
    response = await http_tenant_one.post(
        f"/annotation-queues/{queue_id}/runs",
        json=[str(run_id)],
    )
    assert response.status_code == 200

    # Fetch the run from the queue without include_extra (should NOT have 'extra')
    response = await http_tenant_one.get(f"/annotation-queues/{queue_id}/run/0")
    assert response.status_code == 200
    run = response.json()
    assert run["name"] == "TestRun"
    assert "extra" not in run or run["extra"] is None

    # Fetch the run from the queue with include_extra=true (should have 'extra')
    response = await http_tenant_one.get(
        f"/annotation-queues/{queue_id}/run/0?include_extra=true"
    )
    assert response.status_code == 200
    run = response.json()
    assert run["name"] == "TestRun"
    assert "extra" in run and run["extra"]["metadata"]["foo"] == "bar"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="need tenants/orgs")
async def test_cannot_delete_queue_with_evaluator(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, include_read_only=True
    ) as authed_tenant:
        authed_client = authed_tenant.client
        tenant_id = authed_tenant.auth.tenant_id
        queue_data = {
            "name": random_lower_string(),
            "description": "test",
            "tenant_id": str(tenant_id),
        }
        response = await authed_client.post("/annotation-queues", json=queue_data)
        assert response.status_code == 200
        annotation_queue_id_1 = response.json()["id"]

        queue_data = {
            "name": random_lower_string(),
            "description": "test",
            "tenant_id": str(tenant_id),
        }
        response = await authed_client.post("/annotation-queues", json=queue_data)
        assert response.status_code == 200
        annotation_queue_id_2 = response.json()["id"]

        repo_handle = "repo_" + random_lower_string()
        response = await authed_client.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": False},
        )
        assert response.status_code == 200, response.text
        repo_id = response.json()["repo"]["id"]

        evaluator_id = uuid4()

        await db_asyncpg.execute(
            """INSERT INTO evaluators (id, name) VALUES ($1, $2)""",
            evaluator_id,
            "foobar",
        )

        await db_asyncpg.execute(
            """INSERT INTO llm_evaluators (evaluator_id, prompt_id, annotation_queue_id) VALUES ($1, $2, $3)""",
            evaluator_id,
            repo_id,
            annotation_queue_id_1,
        )

        await db_asyncpg.execute(
            """INSERT INTO run_rules (evaluator_id, tenant_id, display_name, sampling_rate) VALUES ($1, $2, $3, $4)""",
            evaluator_id,
            tenant_id,
            "foobar",
            1.0,
        )

        response = await authed_client.delete(
            f"/annotation-queues/{annotation_queue_id_1}"
        )
        assert response.status_code == 409
        assert (
            "Cannot delete annotation queue attached to an evaluator" in response.text
        )

        await db_asyncpg.execute(
            """
            UPDATE llm_evaluators SET annotation_queue_id = $1 where evaluator_id = $2
            """,
            annotation_queue_id_2,
            evaluator_id,
        )

        response = await authed_client.delete(
            f"/annotation-queues/{annotation_queue_id_1}"
        )
        assert response.status_code == 200
