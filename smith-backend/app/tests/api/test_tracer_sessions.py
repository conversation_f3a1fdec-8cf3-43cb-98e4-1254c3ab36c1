"""Test correct functionality of session endpoints."""

import asyncio
import json
import math
import random
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import Any, Awaitable, Callable, cast
from unittest.mock import AsyncMock, call, patch
from urllib.parse import urlencode
from uuid import UUID, uuid4

import asyncpg
import jsonpatch
import pytest
import requests
from fastapi import HTTPException
from host.models import deployment_type, host_metadata_crud, projects
from httpx import AsyncClient
from httpx_sse import aconnect_sse
from lc_config.settings import shared_settings as settings
from lc_database import clickhouse, redis
from lc_database.clickhouse import ClickhouseClient

from app import config, crud, models, schemas
from app.api.auth import AuthInfo
from app.models import ttl_settings
from app.models.runs.fetch_ch import fetch_runs
from app.models.runs.ingest import (
    ERROR_S3_KEY,
    EVENTS_S3_KEY,
    EXTRA_S3_KEY,
    ROOT_S3_KEY,
)
from app.tests.utils import fresh_tenant_client, random_lower_string

pytestmark = pytest.mark.anyio


async def _create_run(
    auth: AuthInfo,
    session_id: UUID,
    run_id: UUID,
) -> UUID:
    """Create a run."""
    start_time = datetime.utcnow()
    payload = dict(
        id=str(run_id),
        session_id=str(session_id),
        name=random_lower_string(),
        start_time=start_time.isoformat(),
        run_type="llm",
        end_time=(start_time + timedelta(seconds=1)).isoformat(),
        extra={},
        error=None,
        serialized={},
        inputs={},
        outputs={},
        parent_run_id=None,
        reference_example_id=None,
        session_name=None,
        events=None,
        tags=None,
    )
    await models.runs.ingest.upsert_runs(
        [
            models.runs.ingest.RunInsert(
                payload=payload,
                trace_id=str(run_id),
                dotted_order=f"{start_time.isoformat(timespec='microseconds').replace(':', '').replace('.', '').replace('-', '')}Z{run_id.hex}",
                received_at=datetime.now(timezone.utc).isoformat(),
                modified_at=datetime.now(timezone.utc).isoformat(),
                hash_key="",
                should_insert=True,
                done=False,
            )
        ],
        defaultdict(
            lambda: models.runs.ingest.TokenTracker(
                prompt_tokens=None,
                completion_tokens=None,
                total_tokens=None,
                first_token_time=None,
                prompt_cost=None,
                completion_cost=None,
                total_cost=None,
            )
        ),
        auth,
        asyncio.Semaphore(),
    )

    return run_id


async def test_create_session(
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a session can be created."""
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string()},
    )

    assert response.status_code == 200


async def test_create_session_with_existing_id(
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a session cannot be created with an existing ID."""
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string()},
    )
    id = response.json()["id"]

    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/sessions?upsert=true",
        json={"name": response.json()["name"]},
    )

    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": response.json()["name"]},
    )

    assert response.status_code == 409

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "id": id},
    )

    assert response.status_code == 409


async def test_create_session_with_existing_name(
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a session cannot be created with an existing name."""
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string()},
    )

    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/sessions?upsert=true",
        json={"name": response.json()["name"]},
    )

    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": response.json()["name"]},
    )

    assert response.status_code == 409


async def test_read_session(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that a session can be read."""
    test_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(
            name=random_lower_string(),
            trace_tier=schemas.TraceTier.shortlived,
        ),
    )
    response = await http_tenant_one.get(
        f"/sessions/{test_session.id}?include_stats=true"
    )

    assert response.status_code == 200
    session_response = response.json()
    assert session_response["id"] == str(test_session.id)
    assert session_response["name"] == test_session.name
    assert session_response["tenant_id"] == str(auth_tenant_one.tenant_id)
    assert session_response["start_time"] == test_session.start_time.isoformat()
    assert session_response["end_time"] is None
    assert session_response["run_count"] == 0
    assert session_response["trace_tier"] == schemas.TraceTier.shortlived.value

    async with aconnect_sse(
        http_tenant_one, "GET", f"/sessions/{test_session.id}?include_stats=true"
    ) as sse:
        patch: list[dict] = []
        async for event in sse.aiter_sse():
            if event.event == "data":
                patch += event.json()["patch"]
            elif event.event == "end":
                pass
            else:
                assert False, f"Unexpected event type {event}"

        assert {
            k: v for k, v in jsonpatch.apply_patch({}, patch).items() if v is not None
        } == {k: v for k, v in session_response.items() if v is not None}


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_read_session_diff_tenant(
    db_asyncpg: asyncpg.Connection,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
) -> None:
    """Test that a session can be read."""
    test_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(name=random_lower_string()),
    )
    response = await http_tenant_one.get(f"/sessions/{test_session.id}")

    assert response.status_code == 200

    if config.settings.AUTH_TYPE != "none":
        with pytest.raises(HTTPException):
            await crud.get_tracer_session_asyncpg(
                db_asyncpg,
                auth_tenant_two,
                cast(UUID, test_session.id),
                schemas.QueryParamsForSingleTracerSessionSchema(),
            )


async def test_read_session_not_found(http_tenant_one: AsyncClient) -> None:
    """Test that a session cannot be read if it does not exist."""
    response = await http_tenant_one.get(f"/sessions/{uuid4()}")

    assert response.status_code == 404


@pytest.mark.parametrize(
    "patch_topk_aggregated_merge_tree", [False, True], indirect=True
)
@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_read_sessions(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    patch_topk_aggregated_merge_tree: bool,
    patch_session_aggregation: bool,
) -> None:
    """Test that sessions can be read."""
    test_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(name=random_lower_string()),
    )
    response = await http_tenant_one.get("/sessions?facets=true")

    assert response.status_code == 200
    sessions = response.json()
    assert len(sessions) >= 1
    this_session = next(
        session for session in sessions if session["id"] == str(test_session.id)
    )
    assert this_session["id"] == str(test_session.id)

    async with aconnect_sse(
        http_tenant_one, "GET", f"/sessions?facets=true&id={test_session.id}"
    ) as sse:
        patch: list[dict] = []
        async for event in sse.aiter_sse():
            if event.event == "data":
                patch += event.json()["patch"]
            elif event.event == "end":
                pass
            else:
                assert False, f"Unexpected event type {event}"

        streamed_sessions = jsonpatch.apply_patch({}, patch)["rows"]
        # remove run_facets from comparison as the order may not be stable
        for session in streamed_sessions:
            del session["run_facets"]
            for key in list(session):
                if session[key] is None:
                    del session[key]
        del this_session["run_facets"]
        for key in list(this_session):
            if this_session[key] is None:
                del this_session[key]
        assert streamed_sessions == [this_session]


@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_read_sessions_by_name(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    patch_session_aggregation: bool,
) -> None:
    """Test that sessions can be read by name."""
    test_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(name=random_lower_string()),
    )
    response = await http_tenant_one.get(
        f"/sessions?name={test_session.name}",
    )

    assert response.status_code == 200
    assert len(response.json()) == 1


@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_read_sessions_by_name_contains(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    patch_session_aggregation: bool,
) -> None:
    query = random_lower_string()
    session_names = [
        random_lower_string() + query + random_lower_string() for _ in range(2)
    ]
    """Test that sessions can be read by name."""
    for name in session_names:
        await crud.create_tracer_session(
            auth_tenant_one,
            schemas.TracerSessionCreate(name=name),
        )
    response = await http_tenant_one.get(
        f"/sessions?name_contains={query}",
    )

    assert response.status_code == 200
    assert len(response.json()) == 2


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_read_sessions_by_metadata(
    fresh_tenant: tuple[AsyncClient, AuthInfo],
    patch_session_aggregation: bool,
) -> None:
    """Test that sessions can be read by metadata."""
    http_tenant_one, auth_tenant_one = fresh_tenant
    test_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(
            name=random_lower_string(), extra={"metadata": {"foo": "bar", "baz": "qux"}}
        ),
    )
    response = await http_tenant_one.get(
        f"/sessions?metadata={json.dumps({'foo': 'bar'})}",
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(test_session.id)


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_read_sessions_by_column_sort(
    fresh_tenant: tuple[AsyncClient, AuthInfo],
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    patch_session_aggregation: bool,
) -> None:
    """Test that sessions can be sorted by column"""
    http_tenant_one, auth_tenant_one = fresh_tenant
    dataset_id = uuid4()
    await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            id=dataset_id,
            name=random_lower_string(),
            description="test",
            data_type="kv",
        ),
    )

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": "input_val_1", "input_2": "input_val_2"},
            "outputs": {"output_1": "output_val_1", "output_2": "output_val_2"},
            "dataset_id": str(dataset_id),
        },
    )

    assert response.status_code == 200
    example_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": "input_val_1asdf", "input_2": "input_val_2asdf"},
            "outputs": {"output_1": "output_val_1asdf", "output_2": "output_val_2asdf"},
            "dataset_id": str(dataset_id),
        },
    )

    assert response.status_code == 200
    example_id_2 = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {
                "input_1": "input_val_1asdfasdf",
                "input_2": "input_val_2asdfasdf",
            },
            "outputs": {
                "output_1": "output_val_1asdfasdf",
                "output_2": "output_val_2asdfasdf",
            },
            "dataset_id": str(dataset_id),
        },
    )

    assert response.status_code == 200
    example_id_3 = response.json()["id"]

    session_names = [
        "000000000session_" + chr(x)
        for x in range(
            ord("a"), ord("z") + 1
        )  # Make sure it comes first alphabetically
    ]
    random.shuffle(session_names)

    for name in session_names:
        await crud.create_tracer_session(
            auth_tenant_one,
            schemas.TracerSessionCreate(name=name, reference_dataset_id=dataset_id),
        )

    """Share the dataset in order to test public endpoints too"""
    response = await http_tenant_one.put(f"/datasets/{dataset_id}/share")
    assert response.status_code == 200
    share_token = response.json()["share_token"]

    response = await http_tenant_one.get(
        f"/sessions?sort_by=name&sort_by_desc=false&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 5
    assert response.json()[0]["name"] == "000000000session_a"
    assert response.json()[1]["name"] == "000000000session_b"
    assert response.json()[2]["name"] == "000000000session_c"
    assert response.json()[3]["name"] == "000000000session_d"
    assert response.json()[4]["name"] == "000000000session_e"

    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/sessions?sort_by=name&sort_by_desc=false&limit=5&offset=0",
    )
    assert response.status_code == 200
    assert len(response.json()) == 5
    assert response.json()[0]["name"] == "000000000session_a"
    assert response.json()[1]["name"] == "000000000session_b"
    assert response.json()[2]["name"] == "000000000session_c"
    assert response.json()[3]["name"] == "000000000session_d"
    assert response.json()[4]["name"] == "000000000session_e"

    """Test that sessions can't be read by an invalid column name (which would allow sql injection)"""
    response = await http_tenant_one.get(
        "/sessions?sort_by=KJAHSLDFJHASLKD&sort_by_desc=false&limit=5&offset=0",
    )
    assert response.status_code == 422

    """Test that we don't accept sort_by = feedback without specifying a feedback key"""
    response = await http_tenant_one.get(
        f"/sessions?sort_by=feedback&sort_by_desc=false&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 422
    assert (
        response.json()["detail"]
        == "Must specify sort_by_feedback_key when sorting by feedback"
    )

    """Test that you can't sort by feedback if you don't specify a reference dataset"""
    response = await http_tenant_one.get(
        "/sessions?sort_by=feedback&sort_by_feedback_key=asdf&sort_by_desc=false&limit=5&offset=0",
    )
    assert response.status_code == 422
    assert (
        response.json()["detail"]
        == "sort_by must be one of last_run_start_time_live, start_time, error_rate, latency, or name when reference_dataset is not provided"
    )

    """Test that latency ordering works"""

    current_time = datetime.now()
    time_minus_10_seconds = current_time - timedelta(seconds=10)
    time_minus_20_seconds = current_time - timedelta(seconds=20)
    formatted_time = current_time.strftime("%Y-%m-%dT%H:%M:%S")
    formatted_time_minus_10_seconds = time_minus_10_seconds.strftime(
        "%Y-%m-%dT%H:%M:%S"
    )
    formatted_time_minus_20_seconds = time_minus_20_seconds.strftime(
        "%Y-%m-%dT%H:%M:%S"
    )

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": formatted_time_minus_10_seconds,
            "end_time": formatted_time,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": "000000000session_c",
            "reference_example_id": str(example_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    run_id_2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": formatted_time_minus_20_seconds,
            "end_time": formatted_time,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": "000000000session_e",
            "reference_example_id": str(example_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.get(
        f"/sessions?sort_by=latency_p99&sort_by_desc=false&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 2  # Only returns sessions with runs
    assert response.json()[0]["name"] == "000000000session_c"
    assert response.json()[1]["name"] == "000000000session_e"

    response = await http_tenant_one.get(
        f"/sessions?sort_by=latency_p50&sort_by_desc=true&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 2  # Only returns sessions with runs
    assert response.json()[0]["name"] == "000000000session_e"
    assert response.json()[1]["name"] == "000000000session_c"

    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/sessions?sort_by=latency_p50&sort_by_desc=false&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 2  # Only returns sessions with runs
    assert response.json()[0]["name"] == "000000000session_c"
    assert response.json()[1]["name"] == "000000000session_e"

    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/sessions?sort_by=latency_p99&sort_by_desc=true&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 2  # Only returns sessions with runs
    assert response.json()[0]["name"] == "000000000session_e"
    assert response.json()[1]["name"] == "000000000session_c"

    run_id_3 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": formatted_time_minus_10_seconds,
            "end_time": formatted_time,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": "000000000session_a",
            "reference_example_id": str(example_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_3),
        },
    )

    run_id_4 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": formatted_time_minus_20_seconds,
            "end_time": formatted_time,
            "extra": {"foo": "bar"},
            "error": "ERROR",
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": "000000000session_b",
            "reference_example_id": str(example_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_4),
        },
    )

    run_id_5 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": formatted_time_minus_10_seconds,
            "end_time": formatted_time,
            "extra": {"foo": "bar"},
            "error": "ERROR",
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": "000000000session_c",
            "reference_example_id": str(example_id_2),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_5),
        },
    )

    run_id_6 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": formatted_time_minus_20_seconds,
            "end_time": formatted_time,
            "extra": {"foo": "bar"},
            "error": "ERROR",
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": "000000000session_e",
            "reference_example_id": str(example_id_2),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_6),
        },
    )

    run_id_7 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": formatted_time_minus_20_seconds,
            "end_time": formatted_time,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": "000000000session_e",
            "reference_example_id": str(example_id_3),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_7),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # session b has 100% error rate, session c has 50% error rate, session e has 33% error rate, session a has 0% error rate
    response = await http_tenant_one.get(
        f"/sessions?sort_by=error_rate&sort_by_desc=true&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 4
    assert response.json()[0]["name"] == "000000000session_b"
    assert response.json()[1]["name"] == "000000000session_c"
    assert response.json()[2]["name"] == "000000000session_e"
    assert response.json()[3]["name"] == "000000000session_a"

    assert response.json()[0]["error_rate"] == 1.0
    assert response.json()[1]["error_rate"] == 0.5
    assert response.json()[2]["error_rate"] == (1.0 / 3.0)
    assert response.json()[3]["error_rate"] == 0.0

    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/sessions?sort_by=error_rate&sort_by_desc=true&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 4
    assert response.json()[0]["name"] == "000000000session_b"
    assert response.json()[1]["name"] == "000000000session_c"
    assert response.json()[2]["name"] == "000000000session_e"
    assert response.json()[3]["name"] == "000000000session_a"

    assert response.json()[0]["error_rate"] == 1.0
    assert response.json()[1]["error_rate"] == 0.5
    assert response.json()[2]["error_rate"] == (1.0 / 3.0)
    assert response.json()[3]["error_rate"] == 0.0

    """Test that feedback ordering works"""
    feedback_id = uuid4()
    feedback_key = random_lower_string()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=feedback_id,
            run_id=run_id,  # session c
            score=0.5,
            key=feedback_key,
        ),
    )

    feedback_id_2 = uuid4()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=feedback_id_2,
            run_id=run_id_2,  # session e
            score=0.1,
            key=feedback_key,
        ),
    )

    response = await http_tenant_one.get(
        f"/sessions?sort_by=feedback&sort_by_desc=false&sort_by_feedback_key={feedback_key}&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.json()[0]["name"] == "000000000session_e"
    assert response.json()[1]["name"] == "000000000session_c"

    response = await http_tenant_one.get(
        f"/sessions?sort_by=feedback&sort_by_desc=true&sort_by_feedback_key={feedback_key}&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.json()[0]["name"] == "000000000session_c"
    assert response.json()[1]["name"] == "000000000session_e"

    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/sessions?sort_by=feedback&sort_by_desc=false&sort_by_feedback_key={feedback_key}&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.json()[0]["name"] == "000000000session_e"
    assert response.json()[1]["name"] == "000000000session_c"

    response = await http_tenant_one.get(
        f"/public/{share_token}/datasets/sessions?sort_by=feedback&sort_by_desc=true&sort_by_feedback_key={feedback_key}&limit=5&offset=0&reference_dataset={dataset_id}",
    )
    assert response.status_code == 200
    assert len(response.json()) == 2
    assert response.json()[0]["name"] == "000000000session_c"
    assert response.json()[1]["name"] == "000000000session_e"


async def test_delete_session(
    fresh_tenant: tuple[AsyncClient, AuthInfo],
    db_asyncpg: asyncpg.Connection,
) -> None:
    """Test that a session can be deleted."""
    client, auth = fresh_tenant
    test_session = await crud.create_tracer_session(
        auth,
        schemas.TracerSessionCreate(name=random_lower_string()),
    )
    response = await client.delete(
        f"/sessions/{test_session.id}",
    )

    assert response.status_code == 202
    with pytest.raises(HTTPException) as exc:
        await crud.get_tracer_session_asyncpg(
            db_asyncpg,
            auth,
            cast(UUID, test_session.id),
            schemas.QueryParamsForSingleTracerSessionSchema(),
        )
    assert exc.value.status_code == 404

    sessions, total = await crud.get_tracer_sessions(
        auth,
        schemas.FilterQueryParamsForTracerSessionSchema(),
    )

    assert len(sessions) == 0
    assert total == 0

    # test API as well to verify cache eviction
    response = await client.get("/sessions")
    assert response.status_code == 200
    assert response.json() == []


@pytest.mark.parametrize("is_s3_payload", [True, False])
async def test_delete_multiple_sessions(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    is_s3_payload: bool,
) -> None:
    """Test deleting multiple sessions along with ch cleanup."""
    test_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(name=random_lower_string()),
    )
    test_session2 = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(name=random_lower_string()),
    )
    run_ids = [uuid4(), uuid4()]
    pre_signed_urls = []
    for session, run_id in zip([test_session, test_session2], run_ids):
        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248"}
        error = "an error"
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ]
        extra = {"foo": "bar", "metadata": {"conversation_id": "112233"}}

        if is_s3_payload:
            large_data = "x" * (settings.MIN_BLOB_STORAGE_SIZE_KB * 1024)
            inputs["large_input"] = large_data
            outputs["large_output"] = large_data
            events[0]["data"] = large_data
            error = "e" * (settings.MIN_BLOB_STORAGE_SIZE_KB * 1024)
            extra["data"] = large_data

        response = await http_tenant_one.post(
            "/runs",
            json={
                "name": "LLM",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": extra,
                "error": error,
                "events": events,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": inputs,
                "outputs": outputs,
                "session_id": str(session.id),
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
            },
        )

    await wait_until_task_queue_empty()

    for run_id in run_ids:
        res = await fetch_runs(
            auth_tenant_one,
            schemas.BodyParamsForRunSchema(
                id=[run_id],
                select=[
                    schemas.RunSelect.inputs_s3_urls,
                    schemas.RunSelect.outputs_s3_urls,
                    schemas.RunSelect.s3_urls,
                ],
            ),
        )
        run = res["runs"][0]
        if is_s3_payload:
            assert run["inputs_s3_urls"] and run["outputs_s3_urls"] and run["s3_urls"]
            pre_signed_urls.append(run["inputs_s3_urls"][ROOT_S3_KEY]["presigned_url"])
            pre_signed_urls.append(run["outputs_s3_urls"][ROOT_S3_KEY]["presigned_url"])
            pre_signed_urls.append(run["s3_urls"][ERROR_S3_KEY]["presigned_url"])
            pre_signed_urls.append(run["s3_urls"][EVENTS_S3_KEY]["presigned_url"])
            pre_signed_urls.append(run["s3_urls"][EXTRA_S3_KEY]["presigned_url"])
        else:
            assert (
                not run["inputs_s3_urls"]
                and not run["outputs_s3_urls"]
                and not run["s3_urls"]
            )

    if is_s3_payload:
        for presigned_url in pre_signed_urls:
            response = requests.get(presigned_url)
            assert response.status_code == 200

    response = await http_tenant_one.delete(
        "/sessions",
        params={"session_ids": [test_session.id, test_session2.id]},
    )

    assert response.status_code == 202

    # these endpoints should fail after projects are deleted
    response = await http_tenant_one.post(
        "/runs/group",
        json={
            "session_id": str(test_session.id),
            "group_by": "conversation",
            "limit": 10,
            "offset": 0,
        },
    )
    assert response.status_code == 404
    response = await http_tenant_one.post(
        "/runs/query",
        json={
            "session": [str(test_session.id)],
            "limit": 1,
        },
    )
    assert response.status_code == 404

    # delete is in background task
    await wait_until_task_queue_empty()

    for session_id in [test_session.id, test_session2.id]:
        with pytest.raises(HTTPException):
            await crud.get_tracer_session_asyncpg(
                db_asyncpg,
                auth_tenant_one,
                cast(UUID, session_id),
                schemas.QueryParamsForSingleTracerSessionSchema(),
            )

    if is_s3_payload:
        for presigned_url in pre_signed_urls:
            # check that assets were deleted
            response = requests.get(presigned_url)
            assert response.status_code == 404

    # check clickhouse tables to make sure they got deleted including MV
    async with clickhouse.clickhouse_client(ClickhouseClient.INGESTION) as ch:
        rows = await asyncio.gather(
            *[
                ch.fetchrow(
                    "this_name_isnt_used",
                    "select COUNT(1) as count from {table} WHERE tenant_id = {tenant_id} AND session_id IN {session_ids}",
                    params={
                        "table": search_table,
                        "tenant_id": str(auth_tenant_one.tenant_id),
                        "session_ids": [str(test_session.id), str(test_session2.id)],
                    },
                )
                for search_table in ["runs"]
                + clickhouse.RELATED_MATERIALIZED_VIEWS["runs"]
            ]
        )
        results = [r["count"] for r in rows]
        assert results == [0] * len(rows)

    run_ids_after_delete = [uuid4(), uuid4()]
    for session, run_id in zip([test_session, test_session2], run_ids_after_delete):
        response = await http_tenant_one.post(
            "/runs",
            json={
                "name": "LLM",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": str(session.id),
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
            },
        )
        assert response.status_code == 202 or response.status_code == 404

    await wait_until_task_queue_empty()

    for run_id in run_ids_after_delete:
        with pytest.raises(HTTPException):
            await crud.get_run(auth_tenant_one, run_id)


async def test_delete_session_cascade_runs(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a session can be deleted."""
    test_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(name=random_lower_string()),
    )
    run_id = uuid4()
    await _create_run(auth_tenant_one, test_session.id, run_id)
    response = await http_tenant_one.delete(f"/sessions/{test_session.id}")

    assert response.status_code == 202

    # delete is in background task
    await wait_until_task_queue_empty()

    with pytest.raises(HTTPException):
        await crud.get_tracer_session_asyncpg(
            db_asyncpg,
            auth_tenant_one,
            cast(UUID, test_session.id),
            schemas.QueryParamsForSingleTracerSessionSchema(),
        )
    with pytest.raises(HTTPException):
        await crud.get_run(auth_tenant_one, run_id)  # type: ignore

    response = await http_tenant_one.post(
        "/runs/query",
        json={"id": [str(run_id)]},
    )
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 0


async def test_cannot_delete_host_project_tracer_session(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that a session cannot be deleted if it has an existing deployment attached."""

    test_host_project_metadata = (
        await host_metadata_crud.create_project_metadata(
            auth_tenant_one,
            host_metadata_crud.CreateProjectMetadataRequest(
                name="test-cannot-delete-host-project-tracer-session" + uuid4().hex,
                repo_url="https://github.com/langchain-ai/chat-langchain",
                repo_path="langgraph.json",
                repo_commit="main",
                platform=projects._get_default_project_platform(
                    image_source=deployment_type.ImageSourceId.github,
                    organization_id=auth_tenant_one.organization_id,
                    deployment_type=deployment_type.DeploymentType.prod,
                    tenant_id=auth_tenant_one.tenant_id,
                    remote_reconciled=False,
                    deployment_platform=None,
                    region=None,
                    aws_account_id=None,
                    public=None,
                ),
                created_by={},
                container_spec={},
                database_platform=deployment_type.DatabasePlatformId.gcp_cloud_sql,
                secrets={},
            ),
        )
    ).project_metadata

    del_response = await http_tenant_one.delete(
        f"/sessions/{test_host_project_metadata.tracer_session_id}",
    )
    assert del_response.status_code == 409

    # Ensure the session still exists
    get_response = await http_tenant_one.get(
        f"/sessions/{test_host_project_metadata.tracer_session_id}",
    )

    assert get_response.status_code == 200


async def test_update_session(http_tenant_one: AsyncClient) -> None:
    """Test that a session cannot be created with an existing ID."""

    original_name = random_lower_string()
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": original_name},
    )
    session_obj = response.json()
    assert response.status_code == 200
    id = session_obj["id"]
    original_extra = session_obj["extra"]
    assert not session_obj["description"]
    assert not original_extra

    response = await http_tenant_one.patch(
        f"/sessions/{id}",
        json={
            "description": "test description",
            "extra": {"metadata": {"test": 1}, "other": 5},
        },
    )
    assert response.status_code == 200
    session_obj = response.json()
    assert session_obj["description"] == "test description"
    assert session_obj["extra"] == {"metadata": {"test": 1}, "other": 5}

    response = await http_tenant_one.patch(
        f"/sessions/{id}",
        json={"description": None, "extra": {"metadata": None}},
    )
    assert response.status_code == 200
    session_obj = response.json()
    assert session_obj["description"] == "test description"
    assert session_obj["extra"] == {"other": 5}

    # Check that updating the name of the tracer session is allowed
    new_name = random_lower_string()
    response = await http_tenant_one.patch(
        f"/sessions/{id}",
        json={"name": new_name},
    )
    assert response.status_code == 200
    session_obj = response.json()
    assert session_obj["name"] == new_name

    # Confirm that you can read the session by the new name
    response = await http_tenant_one.get(
        f"/sessions?name={new_name}",
    )
    assert response.status_code == 200
    sessions = response.json()
    assert len(sessions) == 1
    assert sessions[0]["id"] == id
    assert sessions[0]["name"] == new_name

    # Confirm you can't read the session by the old name
    response = await http_tenant_one.get(
        f"/sessions?name={original_name}",
    )
    assert response.status_code == 200
    sessions = response.json()
    assert len(sessions) == 0

    # set trace_tier
    response = await http_tenant_one.patch(
        f"/sessions/{id}",
        json={"trace_tier": "shortlived"},
    )
    assert response.status_code == 200
    session_obj = response.json()
    assert session_obj["trace_tier"] == "shortlived"

    # Add an end_time
    response = await http_tenant_one.patch(
        f"/sessions/{id}",
        json={"end_time": "2023-05-05T05:13:32.022361+01:30"},
    )
    assert response.status_code == 200
    session_obj = response.json()
    # End time is stored in UTC
    assert session_obj["end_time"] == "2023-05-05T03:43:32.022361+00:00"


async def test_update_session_not_found(
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a session cannot be created with an existing ID."""

    response = await http_tenant_one.patch(
        f"/sessions/{uuid4()}",
        json={"description": "test description"},
    )
    assert response.status_code == 404


async def test_update_tracer_session_default_dataset_id_zero(
    http_tenant_one: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    auth_tenant_one: AuthInfo,
) -> None:
    # Create a new session
    session_name = random_lower_string()
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": session_name},
    )
    assert response.status_code == 200
    session = response.json()
    session_id = session["id"]

    # Create a dataset
    dataset_name = random_lower_string()
    response = await http_tenant_one.post(
        "/datasets",
        json={"name": dataset_name, "description": "Test dataset"},
    )
    assert response.status_code == 200
    dataset = response.json()
    dataset_id = dataset["id"]

    # Update session with a non-zero default_dataset_id
    response = await http_tenant_one.patch(
        f"/sessions/{session_id}",
        json={"default_dataset_id": str(dataset_id)},
    )
    assert response.status_code == 200
    updated_session = response.json()
    assert updated_session["default_dataset_id"] == str(dataset_id)

    # Update session with default_dataset_id = UUID(int=0)
    response = await http_tenant_one.patch(
        f"/sessions/{session_id}",
        json={"default_dataset_id": "00000000-0000-0000-0000-000000000000"},
    )
    assert response.status_code == 200
    updated_session = response.json()
    assert updated_session["default_dataset_id"] is None

    # Verify the update in the database
    db_session = await crud.get_tracer_session_asyncpg(
        db_asyncpg,
        auth_tenant_one,
        cast(UUID, session_id),
        schemas.QueryParamsForSingleTracerSessionSchema(),
    )
    assert db_session.default_dataset_id is None


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_update_session_different_tenant(
    http_tenant_one: AsyncClient,
    tenant_two_headers: dict[str, str],
) -> None:
    """Test that a session cannot be created with an existing ID."""

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string()},
    )
    session_obj = response.json()
    assert response.status_code == 200
    id = session_obj["id"]

    response = await http_tenant_one.patch(
        f"/sessions/{id}",
        headers=tenant_two_headers,
        json={"description": "test description"},
    )
    assert response.status_code == 404


async def test_tracer_session_with_langserve_host_can_change_name(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    host_project = (
        await host_metadata_crud.create_project_metadata(
            auth_tenant_one,
            host_metadata_crud.CreateProjectMetadataRequest(
                name="test_tracer_session_name_change" + uuid4().hex,
                repo_url="https://github.com/langchain-ai/chat-langchain",
                repo_path="langgraph.json",
                repo_commit="main",
                platform=projects._get_default_project_platform(
                    image_source=deployment_type.ImageSourceId.github,
                    organization_id=auth_tenant_one.organization_id,
                    deployment_type=deployment_type.DeploymentType.prod,
                    tenant_id=auth_tenant_one.tenant_id,
                    remote_reconciled=False,
                    deployment_platform=None,
                    region=None,
                    aws_account_id=None,
                    public=None,
                ),
                created_by={},
                container_spec={},
                database_platform=deployment_type.DatabasePlatformId.gcp_cloud_sql,
                secrets={},
            ),
        )
    ).project_metadata

    response = await http_tenant_one.get(
        "/sessions?name=" + host_project.name,
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    session_id = response.json()[0]["id"]

    new_name = "test_tracer_session_name_change 2"
    response = await http_tenant_one.patch(
        f"/sessions/{session_id}",
        json={"name": new_name},
    )
    assert response.status_code == 200


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_update_session_different_tier(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client
        response = await aclient.post(
            "/sessions",
            json={"name": random_lower_string()},
        )

        session_obj = response.json()
        assert response.status_code == 200
        assert session_obj["trace_tier"] == "shortlived"
        id = session_obj["id"]

        response = await aclient.patch(
            f"/sessions/{id}",
            json={"trace_tier": "longlived"},
        )
        assert response.status_code == 200
        assert response.json()["trace_tier"] == "longlived"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_ttl_new_session_defaults_based_on_org_config(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        response = await aclient.put(
            "/orgs/ttl-settings",
            json={"default_trace_tier": "shortlived"},
        )

        assert response.status_code == 200
        result = ttl_settings.crud.TTLSettings(**response.json())
        assert result.default_trace_tier == schemas.TraceTier.shortlived

        response = await aclient.post(
            "/sessions",
            json={"name": random_lower_string()},
        )

        session_obj = response.json()
        assert response.status_code == 200
        assert session_obj["trace_tier"] == "shortlived"

        response = await aclient.post(
            "/sessions",
            json={"name": random_lower_string()},
        )

        session_obj = response.json()
        assert response.status_code == 200
        assert session_obj["trace_tier"] == "shortlived"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_ttl_tenant_config_overrides_org_config(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client
        auth = authed_client.auth

        response = await aclient.put(
            "/orgs/ttl-settings",
            json={"default_trace_tier": "longlived"},
        )
        assert response.status_code == 200
        assert response.json()["default_trace_tier"] == "longlived"

        response = await aclient.put(
            "/ttl-settings",
            json={
                "default_trace_tier": "shortlived",
                "tenant_id": str(auth.tenant_id),
            },
        )
        assert response.status_code == 200
        assert response.json()["default_trace_tier"] == "shortlived"
        tenant_level_settings = ttl_settings.crud.TTLSettings(**response.json())

        settings = await ttl_settings.crud.get_trace_tier_settings_for_tenant(
            db_asyncpg, tenant_id=auth.tenant_id
        )

        assert settings and settings.default_trace_tier == schemas.TraceTier.shortlived
        assert settings.id == tenant_level_settings.id

        response = await aclient.post(
            "/sessions",
            json={"name": random_lower_string()},
        )

        session_obj = response.json()
        assert response.status_code == 200
        assert session_obj["trace_tier"] == "shortlived"

        # Test that updating the default tier doesn't update the existing projects
        response = await aclient.put(
            "/ttl-settings",
            json={
                "default_trace_tier": "shortlived",
                "tenant_id": str(auth.tenant_id),
            },
        )
        assert response.status_code == 200
        assert response.json()["default_trace_tier"] == "shortlived"

        response = await aclient.get(
            f"/sessions/{session_obj['id']}",
        )
        assert response.status_code == 200
        assert response.json()["trace_tier"] == "shortlived"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def use_default_trace_tier_if_none_defined_for_tenant(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        response = await aclient.post(
            "/sessions",
            json={"name": random_lower_string()},
        )

        session_obj = response.json()
        assert response.status_code == 200

        # Longlived is the default value
        assert session_obj["trace_tier"] == "longlived"


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["oauth", "none"],
    reason="write queue, single tenant gets a sample run",
)
@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_tracer_session_stats(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    patch_session_aggregation: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client
        # Give a reference dataset id to the session so we
        # calculate all time stats
        now = datetime.now(timezone.utc).isoformat()
        later = (datetime.now(timezone.utc) + timedelta(seconds=6)).isoformat()
        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()
        dataset_id = response.json()["id"]

        response = await aclient.post(
            "/sessions",
            json={
                "name": random_lower_string(),
                "start_time": now,
                "reference_dataset_id": dataset_id,
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()
        session_id = response.json()["id"]

        run_id_1 = uuid4()
        response = await aclient.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": now,
                "end_time": later,
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": session_id,
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id_1),
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        run_id_2 = uuid4()
        response = await aclient.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": now,
                "end_time": later,
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": session_id,
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id_2),
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id_1),
                "key": "test",
                "score": 10,
                "value": "blue",
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id_1),
                "key": "foo",
                "score": 10,
                "value": "green",
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id_2),
                "key": "test",
                "score": 0,
                "value": "blue",
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id_2),
                "key": "test",
                "score": 10,
                "value": "green",
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id_2),
                "key": "test",
                "score": 0,
                "value": "blue",
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id_2),
                "key": "foo",
                "score": 10,
                "value": "green",
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id_2),
                "key": "foo",
                "score": 10,
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id_2),
                "key": "foo",
                "score": 10,
                "value": "green",
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id_2),
                "key": "foo",
                "score": None,
                "feedback_source": {"type": "api"},
                "error": True,
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await aclient.get("/sessions?sort_by=last_run_start_time")
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) >= 1
        assert sessions[0]["id"] == session_id

        response = await aclient.get(
            "/sessions?sort_by=last_run_start_time&sort_by_desc=false"
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) >= 1
        assert sessions[-1]["id"] == session_id

        response = await aclient.get("/sessions")
        assert response.status_code == 200
        sessions = response.json()

        session = next(s for s in sessions if s["id"] == session_id)
        assert session["id"] == session_id
        assert session.get("latency_p50")
        assert session.get("latency_p99")
        assert session.get("feedback_stats").get("test")
        assert session.get("feedback_stats").get("test").get("avg") == 5
        assert session.get("feedback_stats").get("test").get("stdev") == 5
        assert session.get("feedback_stats").get("test").get("n") == 4
        assert session.get("feedback_stats").get("test").get("values") == {
            "blue": 3,
            "green": 1,
        }
        assert session.get("feedback_stats").get("foo")
        assert session.get("feedback_stats").get("foo").get("avg") == 10
        assert session.get("feedback_stats").get("foo").get("stdev") == 0
        assert session.get("feedback_stats").get("foo").get("n") == 5
        assert session.get("feedback_stats").get("foo").get("errors") == 1
        assert session.get("feedback_stats").get("foo").get("values") == {"green": 3}

        response = await aclient.get(f"/sessions/{session_id}?include_stats=true")
        assert response.status_code == 200
        session = response.json()
        assert session["id"] == session_id
        assert session.get("latency_p50")
        assert session.get("latency_p99")
        assert session.get("feedback_stats").get("test")
        assert session.get("feedback_stats").get("test").get("avg") == 5
        assert session.get("feedback_stats").get("test").get("stdev") == 5
        assert session.get("feedback_stats").get("test").get("n") == 4
        assert session.get("feedback_stats").get("test").get("values") == {
            "blue": 3,
            "green": 1,
        }
        assert session.get("feedback_stats").get("foo")
        assert session.get("feedback_stats").get("foo").get("avg") == 10
        assert session.get("feedback_stats").get("foo").get("stdev") == 0
        assert session.get("feedback_stats").get("foo").get("n") == 5
        assert session.get("feedback_stats").get("foo").get("errors") == 1
        assert session.get("feedback_stats").get("foo").get("values") == {"green": 3}


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "none", reason="specific single tenant test"
)
async def test_tracer_session_stats_single_tenant(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    # Give a reference dataset id to the session so we
    # calculate all time stats
    now = datetime.now(timezone.utc).isoformat()
    later = (datetime.now(timezone.utc) + timedelta(seconds=6)).isoformat()
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "start_time": now,
            "reference_dataset_id": dataset_id,
        },
    )
    assert response.status_code == 200
    session_id = response.json()["id"]

    run_id_1 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": now,
            "end_time": later,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "test",
            "score": 10,
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "foo",
            "score": 10,
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.get("/sessions?sort_by=last_run_start_time")
    assert response.status_code == 200
    sessions = response.json()
    assert len(sessions) >= 2

    response = await http_tenant_one.get(
        "/sessions?sort_by=last_run_start_time&sort_by_desc=false"
    )
    assert response.status_code == 200
    sessions = response.json()
    assert len(sessions) >= 2

    response = await http_tenant_one.get("/sessions")
    assert response.status_code == 200
    sessions = response.json()

    session = next(s for s in sessions if s["id"] == session_id)
    assert session["id"] == session_id
    assert session.get("latency_p50")
    assert session.get("latency_p99")
    assert session.get("feedback_stats").get("test")
    assert session.get("feedback_stats").get("foo")

    response = await http_tenant_one.get(f"/sessions/{session_id}?include_stats=true")
    assert response.status_code == 200
    session = response.json()
    assert session["id"] == session_id
    assert session.get("latency_p50")
    assert session.get("latency_p99")
    assert session.get("feedback_stats").get("test")
    assert session.get("feedback_stats").get("foo")


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_tracer_session_start_time(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "start_time": "2022-06-05T05:13:24",
        },
    )
    assert response.status_code == 200
    session = response.json()
    session_id = session["id"]
    assert session["start_time"] == "2022-06-05T05:13:24"

    run_id_1 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2021-05-05T05:13:24",
            "end_time": "2023-05-05T05:13:32",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.get(f"/sessions/{session_id}")
    assert response.status_code == 200
    session = response.json()
    assert session["id"] == session_id
    assert session["start_time"] == "2021-05-05T05:13:24"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_tracer_session_last_session_start_time_live(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "start_time": "2022-06-05T05:13:24",
        },
    )
    assert response.status_code == 200
    session = response.json()
    session_id = session["id"]

    run_id_1 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2022-06-05T05:14:24",
            "end_time": "2022-06-05T05:20:32",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.get(f"/sessions/{session_id}")
    assert response.status_code == 200
    session = response.json()
    assert session["id"] == session_id
    # assert session["last_run_start_time_live"] == "2022-06-05T05:14:24"

    run_id_2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2022-06-05T05:14:34",
            "end_time": "2022-06-05T05:20:32",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.get(f"/sessions/{session_id}")
    assert response.status_code == 200
    session = response.json()
    assert session["id"] == session_id

    # should not be updated since comparison is on minute granularity
    # assert session["last_run_start_time_live"] == "2022-06-05T05:14:24"

    run_id_3 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2022-06-05T05:15:24",
            "end_time": "2022-06-05T05:20:32",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_3),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.get(f"/sessions/{session_id}")
    assert response.status_code == 200
    session = response.json()
    assert session["id"] == session_id

    # should be updated since comparison is on minute granularity
    # assert session["last_run_start_time_live"] == "2022-06-05T05:15:24"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
async def test_tracer_session_top_run_metadata(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "start_time": "2022-06-05T05:13:24",
        },
    )
    assert response.status_code == 200
    session = response.json()
    session_id = session["id"]

    run_id_1 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2022-06-05T05:14:24",
            "end_time": "2022-06-05T05:20:32",
            "extra": {"metadata": {"bar": "foo", "bar2": "foo"}},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    run_id_2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2022-06-05T05:14:34",
            "end_time": "2022-06-05T05:20:32",
            "extra": {"metadata": {"bar": "foo"}},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )

    assert response.status_code == 202

    run_id_3 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2022-06-05T05:14:34",
            "end_time": "2022-06-05T05:20:32",
            "extra": {"metadata": {"bar": "foo2"}},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_3),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.get(
        f"/sessions/{session_id}/metadata?start_time=2022-06-05T03:14:34"
    )
    assert response.status_code == 200
    common_metadata = response.json()
    assert common_metadata == {
        "bar": ['"foo"', '"foo2"'],
        "bar2": ['"foo"'],
        "ls_run_depth": ["0"],
    }

    # we default start_time to 30 days ago if not provided so nothing should show up
    response = await http_tenant_one.get(f"/sessions/{session_id}/metadata")
    assert response.status_code == 200
    common_metadata = response.json()
    assert common_metadata == {}

    response = await http_tenant_one.get(
        f"/sessions/{session_id}/metadata?start_time=2022-06-05T03:14:34&metadata_keys=bar"
    )
    assert response.status_code == 200
    common_metadata = response.json()
    assert common_metadata == {"bar": ['"foo"', '"foo2"']}

    response = await http_tenant_one.get(
        f"/sessions/{session_id}/metadata?start_time=2022-06-05T03:14:34&metadata_keys=bar2"
    )
    assert response.status_code == 200
    common_metadata = response.json()
    assert common_metadata == {"bar2": ['"foo"']}

    response = await http_tenant_one.get(
        f"/sessions/{session_id}/metadata?start_time=2022-06-05T03:14:34&metadata_keys=bar&k=1"
    )
    assert response.status_code == 200
    common_metadata = response.json()
    assert common_metadata == {"bar": ['"foo"']}


async def test_crud_filter_views(
    http_tenant_one: AsyncClient,
) -> None:
    # Create a session
    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
        },
    )
    tenant_one_tracer_session_id = UUID(response.json()["id"])

    response = await http_tenant_one.post(
        f"/sessions/{tenant_one_tracer_session_id}/views",
        json={
            "display_name": "test",
            "filter_string": "eq(run_type, 'chain')",
        },
    )

    assert response.status_code == 200
    view = response.json()
    assert view["display_name"] == "test"
    assert view["filter_string"] == "eq(run_type, 'chain')"
    assert view["session_id"] == str(tenant_one_tracer_session_id)
    assert view["id"]
    assert view["created_at"]
    assert view["updated_at"]

    # try one with a description
    response = await http_tenant_one.post(
        f"/sessions/{tenant_one_tracer_session_id}/views",
        json={
            "display_name": "test2",
            "filter_string": "eq(run_type, 'chain')",
            "description": "this is a test",
        },
    )

    assert response.status_code == 200
    view = response.json()
    assert view["display_name"] == "test2"
    assert view["filter_string"] == "eq(run_type, 'chain')"
    assert view["session_id"] == str(tenant_one_tracer_session_id)
    assert view["id"]
    assert view["created_at"]
    assert view["updated_at"]
    assert view["description"] == "this is a test"

    # one with trace and tree filters
    response = await http_tenant_one.post(
        f"/sessions/{tenant_one_tracer_session_id}/views",
        json={
            "display_name": "test",
            "filter_string": "eq(run_type, 'chain')",
            "trace_filter_string": "eq(is_root, true)",
            "tree_filter_string": "eq(is_root, true)",
        },
    )

    assert response.status_code == 200
    view = response.json()
    assert view["display_name"] == "test"
    assert view["filter_string"] == "eq(run_type, 'chain')"
    assert view["trace_filter_string"] == "eq(is_root, true)"
    assert view["tree_filter_string"] == "eq(is_root, true)"
    assert view["session_id"] == str(tenant_one_tracer_session_id)
    assert view["id"]
    assert view["created_at"]
    assert view["updated_at"]

    # try one with a bad filter
    response = await http_tenant_one.post(
        f"/sessions/{tenant_one_tracer_session_id}/views",
        json={
            "display_name": "test3",
            "filter_string": "eq(run_type, 'chain'",
        },
    )

    assert response.status_code == 400

    # try one with a bad session id
    response = await http_tenant_one.post(
        f"/sessions/{uuid4()}/views",
        json={
            "display_name": "test4",
            "filter_string": "eq(run_type, 'chain')",
        },
    )

    assert response.status_code == 404

    # try fetching the views
    response = await http_tenant_one.get(
        f"/sessions/{tenant_one_tracer_session_id}/views"
    )

    assert response.status_code == 200
    views = response.json()
    assert len(views) == 3
    view = next(v for v in views if v["display_name"] == "test")
    assert view
    assert view["filter_string"] == "eq(run_type, 'chain')"
    assert view["session_id"] == str(tenant_one_tracer_session_id)

    view = next(v for v in views if v["display_name"] == "test2")
    assert view
    assert view["filter_string"] == "eq(run_type, 'chain')"
    assert view["session_id"] == str(tenant_one_tracer_session_id)

    # try fetching a single view
    response = await http_tenant_one.get(
        f"/sessions/{tenant_one_tracer_session_id}/views/{views[0]['id']}"
    )
    assert response.status_code == 200
    view = response.json()
    assert view["display_name"] == "test"
    assert view["filter_string"] == "eq(run_type, 'chain')"
    assert view["session_id"] == str(tenant_one_tracer_session_id)

    # try updating a view
    response = await http_tenant_one.patch(
        f"/sessions/{tenant_one_tracer_session_id}/views/{views[0]['id']}",
        json={
            "display_name": "test5",
        },
    )

    assert response.status_code == 200
    view = response.json()
    assert view["display_name"] == "test5"
    assert view["filter_string"] == "eq(run_type, 'chain')"
    assert view["session_id"] == str(tenant_one_tracer_session_id)

    response = await http_tenant_one.patch(
        f"/sessions/{tenant_one_tracer_session_id}/views/{views[0]['id']}",
        json={
            "filter_string": "eq(run_type, 'chain') and eq(run_type, 'chain')",
        },
    )

    assert response.status_code == 400

    response = await http_tenant_one.patch(
        f"/sessions/{tenant_one_tracer_session_id}/views/{views[0]['id']}",
        json={
            "filter_string": "eq(run_type, 'llm')",
        },
    )

    assert response.status_code == 200
    view = response.json()
    assert view["display_name"] == "test5"
    assert view["filter_string"] == "eq(run_type, 'llm')"
    assert view["session_id"] == str(tenant_one_tracer_session_id)

    # try deleting a view
    response = await http_tenant_one.delete(
        f"/sessions/{tenant_one_tracer_session_id}/views/{UUID(views[0]['id'])}"
    )
    assert response.status_code == 204

    response = await http_tenant_one.get(
        f"/sessions/{tenant_one_tracer_session_id}/views"
    )
    assert response.status_code == 200
    views = response.json()
    assert len(views) == 2


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_crud_filter_views_auth(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
) -> None:
    response = await http_tenant_one.post(
        f"/sessions/{tenant_one_tracer_session_id}/views",
        json={
            "display_name": "test",
            "filter_string": "eq(run_type, 'chain')",
        },
    )
    assert response.status_code == 200
    id = response.json()["id"]

    response = await http_tenant_two.post(
        f"/sessions/{tenant_one_tracer_session_id}/views",
        json={
            "display_name": "test",
            "filter_string": "eq(run_type, 'chain')",
        },
    )
    assert response.status_code == 404

    response = await http_tenant_two.get(
        f"/sessions/{tenant_one_tracer_session_id}/views"
    )
    assert response.json() == []

    response = await http_tenant_two.get(
        f"/sessions/{tenant_one_tracer_session_id}/views/{id}"
    )
    assert response.status_code == 404

    response = await http_tenant_two.patch(
        f"/sessions/{tenant_one_tracer_session_id}/views/{id}",
        json={
            "display_name": "test",
        },
    )
    assert response.status_code == 404

    response = await http_tenant_two.delete(
        f"/sessions/{tenant_one_tracer_session_id}/views/{id}"
    )
    assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_tracer_session_tagging(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    patch_session_aggregation: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client_tenant = authed_client.client
        key1 = random_lower_string()
        response = await client_tenant.post(
            "/workspaces/current/tag-keys",
            json={"key": key1},
        )

        assert response.status_code == 200
        key1_id = response.json()["id"]

        value1 = random_lower_string()
        response = await client_tenant.post(
            f"/workspaces/current/tag-keys/{key1_id}/tag-values",
            json={"value": value1},
        )

        assert response.status_code == 200
        value1_id = response.json()["id"]

        value2 = random_lower_string()
        response = await client_tenant.post(
            f"/workspaces/current/tag-keys/{key1_id}/tag-values",
            json={"value": value2},
        )

        assert response.status_code == 200
        value2_id = response.json()["id"]

        # create three sessions concurrently
        responses = await asyncio.gather(
            *[
                client_tenant.post(
                    "/sessions",
                    json={
                        "name": random_lower_string(),
                    },
                )
                for _ in range(3)
            ]
        )

        assert len(responses) == 3
        assert all(response.status_code == 200 for response in responses)
        session1_id, session2_id, session3_id = [
            response.json()["id"] for response in responses
        ]

        # Add some runs with different start times to each session
        run_responses = await asyncio.gather(
            client_tenant.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": "2022-06-05T05:14:24",
                    "end_time": "2022-06-05T05:20:32",
                    "extra": {"metadata": {"bar": "foo", "bar2": "foo"}},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": session1_id,
                    "parent_run_id": None,
                    "run_type": "chain",
                },
            ),
            client_tenant.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": "2022-06-05T05:14:34",
                    "end_time": "2022-06-05T05:20:32",
                    "extra": {"metadata": {"bar": "foo"}},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": session2_id,
                    "parent_run_id": None,
                    "run_type": "chain",
                },
            ),
            client_tenant.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": "2022-06-05T05:14:34",
                    "end_time": "2022-06-05T05:20:32",
                    "extra": {"metadata": {"bar": "foo2"}},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": session3_id,
                    "parent_run_id": None,
                    "run_type": "chain",
                },
            ),
        )

        assert all(response.status_code == 202 for response in run_responses)

        await wait_until_task_queue_empty()

        responses_2 = await asyncio.gather(
            client_tenant.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "project",
                    "resource_id": session1_id,
                    "tag_value_id": value1_id,
                },
            ),
            client_tenant.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "project",
                    "resource_id": session1_id,
                    "tag_value_id": value2_id,
                },
            ),
            client_tenant.post(
                "/workspaces/current/taggings",
                json={
                    "resource_type": "project",
                    "resource_id": session2_id,
                    "tag_value_id": value1_id,
                },
            ),
        )

        assert all(response.status_code == 200 for response in responses_2)

        # fetch the sessions without any tag_value_id query param
        response = await client_tenant.get("/sessions")
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) >= 3

        assert any(session["id"] == session1_id for session in sessions)
        assert any(session["id"] == session2_id for session in sessions)
        assert any(session["id"] == session3_id for session in sessions)

        # fetch the sessions with tag_value_id query param
        response = await client_tenant.get(f"/sessions?tag_value_id={value1_id}")
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 2

        assert any(session["id"] == session1_id for session in sessions)
        assert any(session["id"] == session2_id for session in sessions)

        response = await client_tenant.get(f"/sessions?tag_value_id={value2_id}")
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1

        assert any(session["id"] == session1_id for session in sessions)

        response = await client_tenant.get(
            f"/sessions?tag_value_id={value1_id}&tag_value_id={value2_id}"
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1

        assert any(session["id"] == session1_id for session in sessions)

        # Test the experiment case
        # first create a dataset
        response = await client_tenant.post(
            "/datasets",
            json={
                "name": random_lower_string(),
            },
        )
        assert response.status_code == 200
        dataset_id = response.json()["id"]

        response = await client_tenant.post(
            "/sessions",
            json={"name": random_lower_string(), "reference_dataset_id": dataset_id},
        )
        assert response.status_code == 200
        experiment_id = response.json()["id"]

        # tag it as en experiment
        response = await client_tenant.post(
            "/workspaces/current/taggings",
            json={
                "resource_type": "experiment",
                "resource_id": experiment_id,
                "tag_value_id": value1_id,
            },
        )

        assert response.status_code == 200

        # fetch the sessions with tag_value_id query param
        response = await client_tenant.get(f"/sessions?tag_value_id={value1_id}")
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 3

        assert any(session["id"] == session1_id for session in sessions)
        assert any(session["id"] == session2_id for session in sessions)
        assert any(session["id"] == experiment_id for session in sessions)

        # query by both tag_value_id and reference_dataset
        response = await client_tenant.get(
            f"/sessions?tag_value_id={value1_id}&reference_dataset={dataset_id}"
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1

        assert any(session["id"] == experiment_id for session in sessions)

        # query by tag_value_id and sort_by=last_run_start_time
        response = await client_tenant.get(
            f"/sessions?tag_value_id={value1_id}&tag_value_id={value2_id}&sort_by=last_run_start_time"
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1


@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_reference_free(
    http_tenant_one: AsyncClient,
    patch_session_aggregation: bool,
) -> None:
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "reference_dataset_id": dataset_id},
    )
    assert response.status_code == 200
    experiment_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string()},
    )
    assert response.status_code == 200
    project_id = response.json()["id"]

    response = await http_tenant_one.get("/sessions?reference_free=true")
    assert response.status_code == 200
    sessions = response.json()
    assert all(session["reference_dataset_id"] is None for session in sessions)
    assert any(session["id"] == project_id for session in sessions)
    assert not any(session["id"] == experiment_id for session in sessions)

    response = await http_tenant_one.get("/sessions?reference_free=false")
    assert response.status_code == 200
    sessions = response.json()
    assert all(session["reference_dataset_id"] is not None for session in sessions)
    assert any(session["id"] == experiment_id for session in sessions)
    assert not any(session["id"] == project_id for session in sessions)


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_tracer_session_reference_dataset_auth(
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
) -> None:
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
        },
    )
    assert response.status_code == 200
    t1_dataset_id = response.json()["id"]

    response = await http_tenant_two.post(
        "/datasets",
        json={
            "name": random_lower_string(),
        },
    )
    assert response.status_code == 200
    t2_dataset_id = response.json()["id"]

    response = await http_tenant_two.post(
        "/sessions",
        json={"name": random_lower_string(), "reference_dataset_id": t1_dataset_id},
    )
    assert response.status_code == 404

    response = await http_tenant_two.get("/sessions")
    assert response.status_code == 200
    sessions = response.json()
    assert not any(
        session["reference_dataset_id"] == t1_dataset_id for session in sessions
    )

    response = await http_tenant_two.get(f"/sessions?reference_dataset={t1_dataset_id}")
    assert response.status_code == 200
    sessions = response.json()
    assert not any(
        session["reference_dataset_id"] == t1_dataset_id for session in sessions
    )

    # Add another test case for default_dataset_id
    response = await http_tenant_two.post(
        "/sessions",
        json={"name": random_lower_string(), "default_dataset_id": t1_dataset_id},
    )
    assert response.status_code == 404

    response = await http_tenant_two.get("/sessions")
    assert response.status_code == 200

    sessions = response.json()
    assert not any(
        session["default_dataset_id"] == t1_dataset_id for session in sessions
    )

    # Try both reference_dataset_id and default_dataset_id
    response = await http_tenant_two.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "reference_dataset_id": t1_dataset_id,
            "default_dataset_id": t1_dataset_id,
        },
    )
    assert response.status_code == 404

    # Try a valid reference_dataset_id and invalid default_dataset_id
    response = await http_tenant_two.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "reference_dataset_id": t1_dataset_id,
            "default_dataset_id": t2_dataset_id,
        },
    )
    assert response.status_code == 404

    # Try a valid case
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "reference_dataset_id": t1_dataset_id},
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string(), "default_dataset_id": t1_dataset_id},
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "reference_dataset_id": t1_dataset_id,
            "default_dataset_id": t1_dataset_id,
        },
    )
    assert response.status_code == 200

    # Try updating a session with a different tenant's dataset
    response = await http_tenant_one.patch(
        f"/sessions/{response.json()['id']}",
        json={"default_dataset_id": t2_dataset_id},
    )
    assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_experiment_tag_inheritance(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        key1 = random_lower_string()
        response = await aclient.post(
            "/workspaces/current/tag-keys",
            json={"key": key1},
        )

        assert response.status_code == 200
        key1_id = response.json()["id"]

        value1 = random_lower_string()
        response = await aclient.post(
            f"/workspaces/current/tag-keys/{key1_id}/tag-values",
            json={"value": value1},
        )

        assert response.status_code == 200
        value1_id = response.json()["id"]

        value2 = random_lower_string()
        response = await aclient.post(
            f"/workspaces/current/tag-keys/{key1_id}/tag-values",
            json={"value": value2},
        )

        assert response.status_code == 200
        value2_id = response.json()["id"]

        # create a dataset and tag it with value1 and value2
        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
            },
        )
        assert response.status_code == 200
        dataset_id = response.json()["id"]

        response = await aclient.post(
            "/workspaces/current/taggings",
            json={
                "resource_type": "dataset",
                "resource_id": dataset_id,
                "tag_value_id": value1_id,
            },
        )

        assert response.status_code == 200

        response = await aclient.post(
            "/workspaces/current/taggings",
            json={
                "resource_type": "dataset",
                "resource_id": dataset_id,
                "tag_value_id": value2_id,
            },
        )

        assert response.status_code == 200

        # create an experiment (tracer session with reference dataset)
        response = await aclient.post(
            "/sessions",
            json={"name": random_lower_string(), "reference_dataset_id": dataset_id},
        )

        assert response.status_code == 200

        experiment_id = response.json()["id"]

        # check to see if the taggings are inherited

        response = await aclient.get(
            "/workspaces/current/taggings",
        )
        assert response.status_code == 200
        assert len(response.json()) >= 2

        tagging_value_1 = next(
            tagging
            for tagging in response.json()
            if tagging["tag_value_id"] == value1_id
        )

        t1_datasets = tagging_value_1["resources"]["datasets"]
        assert len(t1_datasets) == 1
        assert t1_datasets[0]["resource_id"] == dataset_id

        t1_experiment = tagging_value_1["resources"]["experiments"]
        assert len(t1_experiment) == 1
        assert t1_experiment[0]["resource_id"] == experiment_id

        tagging_value_2 = next(
            tagging
            for tagging in response.json()
            if tagging["tag_value_id"] == value2_id
        )

        t2_datasets = tagging_value_2["resources"]["datasets"]
        assert len(t2_datasets) == 1
        assert t2_datasets[0]["resource_id"] == dataset_id


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_tracer_session_filter_query_lang(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    patch_session_aggregation: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client
        # create a dataset to reference
        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
            },
        )
        assert response.status_code == 200
        dataset_id = response.json()["id"]

        # add an example to the dataset
        response = await aclient.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
            },
        )
        assert response.status_code == 200
        example_id = response.json()["id"]

        # create 5 sessions concurrently
        session_ids = [uuid4() for _ in range(5)]

        (
            session_1_id,
            session_2_id,
            session_3_id,
            session_4_id,
            session_5_id,
        ) = session_ids

        responses = await asyncio.gather(
            *[
                aclient.post(
                    "/sessions",
                    json={
                        "id": str(session_id),
                        "name": f"session_{i + 1}_{random_lower_string()}",
                        "extra": {"metadata": {"session_id": f"session_{i + 1}"}},
                        "start_time": f"2023-06-05T05:14:2{i}",
                        "reference_dataset_id": dataset_id,
                    },
                )
                for i, session_id in enumerate(session_ids)
            ]
        )

        assert len(responses) == 5
        assert all(response.status_code == 200 for response in responses)

        # Add some runs with different start times to each session
        run_body = {
            "name": "AgentExecutor",
            "end_time": "2024-06-05T05:20:32",
            "extra": {"metadata": {"bar": "foo", "bar2": "foo"}},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "parent_run_id": None,
            "run_type": "chain",
            "reference_example_id": example_id,
        }

        # prepare the request bodies, each session will have a different start time, with session_5 being the latest
        run_ids = [uuid4() for _ in range(5)]
        run_bodies = []
        for i, (run_id, session_id) in enumerate(zip(run_ids, session_ids)):
            run_bodies.append(
                {
                    **run_body,
                    "start_time": f"2024-06-05T05:14:2{i}",
                    "session_id": str(session_id),
                    "id": str(run_id),
                    "dotted_order": f"20240605T05142{i}000000Z{run_id}",
                    "trace_id": str(run_id),
                }
            )

        run_response = await aclient.post("/runs/batch", json={"post": run_bodies})
        assert run_response.status_code == 202
        await wait_until_task_queue_empty()

        hello_feedback_bodies = [
            {
                "run_id": str(run_id),
                "key": "hello",
                "score": round(0.1 * (i + 1), 4),
            }
            for i, run_id in enumerate(run_ids)
        ]
        world_feedback_bodies = [
            {
                "run_id": str(run_id),
                "key": "world",
                "score": round(0.1 * (i + 1), 4),
            }
            for i, run_id in enumerate(run_ids)
        ]
        # don't have batch feedback endpoint yet so use asyncio.gather
        feedback_responses = await asyncio.gather(
            *[
                aclient.post("/feedback", json=body)
                for body in hello_feedback_bodies + world_feedback_bodies
            ]
        )

        assert all(response.status_code == 200 for response in feedback_responses)
        await wait_until_task_queue_empty()

        # fetch the sessions without any filter query param
        response = await aclient.get(
            "/sessions", params={"sort_by": "last_run_start_time"}
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 5
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[-1]["id"] == str(session_1_id)
        # assert that each sessions feedback_stats is correct
        for i, session in enumerate(reversed(sessions)):
            feedback_stats = session["feedback_stats"]
            assert "hello" in feedback_stats
            assert "world" in feedback_stats
            assert feedback_stats["hello"]["n"] == 1
            assert feedback_stats["world"]["n"] == 1
            assert math.isclose(feedback_stats["hello"]["avg"], 0.1 * (i + 1))
            assert math.isclose(feedback_stats["world"]["avg"], 0.1 * (i + 1))

        # fetch the sessions with filter query param
        response = await aclient.get(
            "/sessions",
            params={"filter": 'has("metadata", \'{"foo": "bar"}\')'},
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 0

        response = await aclient.get(
            "/sessions",
            params={"filter": 'has("metadata", \'{"session_id": "session_1"}\')'},
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1
        assert sessions[0]["id"] == str(session_1_id)

        response = await aclient.get(
            "/sessions",
            params={"filter": 'and(has("metadata", \'{"foo": "bar"}\'))'},
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 0

        response = await aclient.get(
            "/sessions",
            params={"filter": 'not(has("metadata", \'{"foo": "bar"}\'))'},
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 5

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'not(has("metadata", \'{"foo": "bar"}\'))',
                "sort_by": "last_run_start_time",
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 5
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[-1]["id"] == str(session_1_id)

        # try exists
        response = await aclient.get(
            "/sessions",
            params={"filter": 'exists("metadata", "foo")'},
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 0

        response = await aclient.get(
            "/sessions",
            params={"filter": 'and(exists("metadata", "session_id"))'},
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 5

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(exists("metadata", "session_id"), has("metadata", \'{"session_id": "session_1"}\'))'
            },
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1
        assert sessions[0]["id"] == str(session_1_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'not(exists("metadata", "foo"), exists("metadata", "bar"), has("metadata", \'{"session_id": "session_1"}\'))'
            },
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 4
        assert all(session["id"] != str(session_1_id) for session in sessions)

        response = await aclient.get("/sessions", params={"filter": "invalid"})
        assert response.status_code == 400

        # cannot use not with feedback filters
        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'not(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.5)))'
            },
        )
        assert response.status_code == 400

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'not(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.5)))'
            },
        )
        assert response.status_code == 400

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.45))'
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1
        assert sessions[0]["id"] == str(session_5_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.45)))'
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1
        assert sessions[0]["id"] == str(session_5_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.45))))'
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1
        assert sessions[0]["id"] == str(session_5_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(eq("feedback_key", "hello"), gte("blah_feedback_score", 0.45))'
            },
        )
        # can't have a filter on something that isn't avg_feedback_score
        assert response.status_code == 400

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(neq("feedback_key", "hello"), gte("avg_feedback_score", 0.45))'
            },
        )
        # can't have a neq filter on feedback key
        assert response.status_code == 400

        response = await aclient.get(
            "/sessions",
            params={"filter": 'eq("feedback_key", "hello")'},
        )
        # can't filter by feedback key without a score
        assert response.status_code == 400

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.45), gte("feedback_score", 0.45))'
            },
        )
        # Can't have two feedback filters on the same key
        assert response.status_code == 400

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.45)), and(eq("feedback_key", "world"), gte("avg_feedback_score", 0.45)))'
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1
        assert sessions[0]["id"] == str(session_5_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(has("metadata", \'{"session_id": "session_1"}\'), and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.45)), and(eq("feedback_key", "world"), gte("avg_feedback_score", 0.45))))'
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 0

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(has("metadata", \'{"session_id": "session_5"}\'), and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.45)), and(eq("feedback_key", "world"), gte("avg_feedback_score", 0.45))))'
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1
        assert sessions[0]["id"] == str(session_5_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(exists("metadata", "session_id"), and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.35)), and(eq("feedback_key", "world"), gte("avg_feedback_score", 0.35))))'
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 2

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(exists("metadata", "session_id"), and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.35)), and(eq("feedback_key", "world"), gte("avg_feedback_score", 0.35))))',
                "sort_by": "last_run_start_time",
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 2
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[1]["id"] == str(session_4_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(exists("metadata", "session_id"), and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.35)), and(eq("feedback_key", "world"), gte("avg_feedback_score", 0.35))))',
                "sort_by": "last_run_start_time",
                "sort_by_desc": False,
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 2
        assert sessions[0]["id"] == str(session_4_id)
        assert sessions[1]["id"] == str(session_5_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(exists("metadata", "session_id"))',
                "sort_by": "feedback",
                "sort_by_feedback_key": "hello",
                "reference_dataset": dataset_id,
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 5
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[-1]["id"] == str(session_1_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(exists("metadata", "session_id"), and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.35)), and(eq("feedback_key", "world"), gte("avg_feedback_score", 0.35))))',
                "sort_by": "feedback",
                "sort_by_feedback_key": "hello",
                "reference_dataset": dataset_id,
            },
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 2
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[1]["id"] == str(session_4_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(exists("metadata", "session_id"), and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.35)), and(eq("feedback_key", "world"), gte("avg_feedback_score", 0.35))))',
                "reference_dataset": dataset_id,
                "name_contains": "session_5",
            },
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 1
        assert sessions[0]["id"] == str(session_5_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(exists("metadata", "session_id"), and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.35))))',
                "sort_by": "feedback",
                "sort_by_feedback_key": "world",
                "reference_dataset": dataset_id,
            },
        )
        # can't sort by feedback key that is not in the filter
        assert response.status_code == 400

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(exists("metadata", "session_id"), and(and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.35))))',
                "metadata": json.dumps({"session_id": "session_5"}),
                "reference_dataset": dataset_id,
            },
        )
        # can't use both metadata and filter
        assert response.status_code == 400

        # test pagination sort by ch column
        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.15))',
                "sort_by": "last_run_start_time",
                "limit": 2,
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 2
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[1]["id"] == str(session_4_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.15))',
                "sort_by": "last_run_start_time",
                "limit": 2,
                "offset": 2,
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 2
        assert sessions[0]["id"] == str(session_3_id)
        assert sessions[1]["id"] == str(session_2_id)

        # test pagination sort by name (non clickhouse column)
        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.15))',
                "sort_by": "name",
                "limit": 2,
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 2
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[1]["id"] == str(session_4_id)

        response = await aclient.get(
            "/sessions",
            params={
                "filter": 'and(eq("feedback_key", "hello"), gte("avg_feedback_score", 0.15))',
                "sort_by": "name",
                "limit": 2,
                "offset": 2,
            },
        )
        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 2
        assert sessions[0]["id"] == str(session_3_id)
        assert sessions[1]["id"] == str(session_2_id)


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_tracer_session_top_k_cache(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    patch_session_aggregation: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        # create 5 sessions concurrently
        session_ids = [uuid4() for _ in range(5)]

        (
            session_1_id,
            session_2_id,
            session_3_id,
            session_4_id,
            session_5_id,
        ) = session_ids

        responses = await asyncio.gather(
            *[
                aclient.post(
                    "/sessions",
                    json={
                        "id": str(session_id),
                        "name": f"session_{i + 1}_{random_lower_string()}",
                        "extra": {"metadata": {"session_id": f"session_{i + 1}"}},
                        "start_time": f"2023-06-05T05:14:2{i}",
                    },
                )
                for i, session_id in enumerate(session_ids)
            ]
        )

        assert len(responses) == 5
        assert all(response.status_code == 200 for response in responses)

        # Add some runs with different start times to each session
        run_body = {
            "name": "AgentExecutor",
            "end_time": "2024-06-05T05:20:32",
            "extra": {"metadata": {"bar": "foo", "bar2": "foo"}},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "parent_run_id": None,
            "run_type": "chain",
        }

        # prepare the request bodies, each session will have a different start time, with session_5 being the latest
        run_ids = [uuid4() for _ in range(5)]
        run_bodies = []
        for i, (run_id, session_id) in enumerate(zip(run_ids, session_ids)):
            run_bodies.append(
                {
                    **run_body,
                    "start_time": f"2024-06-05T05:14:2{i}",
                    "session_id": str(session_id),
                    "id": str(run_id),
                    "dotted_order": f"20240605T05142{i}000000Z{run_id}",
                    "trace_id": str(run_id),
                }
            )

        run_response = await aclient.post("/runs/batch", json={"post": run_bodies})
        assert run_response.status_code == 202
        await wait_until_task_queue_empty()

        # fetch the sessions with these filter params:
        # https://api.smith.langchain.com/sessions?offset=0&limit=5&sort_by=last_run_start_time&sort_by_desc=true&reference_free=true
        response = await aclient.get(
            "/sessions",
            params={
                "offset": 0,
                "limit": 5,
                "sort_by": "last_run_start_time",
                "sort_by_desc": True,
                "reference_free": True,
            },
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 5
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[-1]["id"] == str(session_1_id)

        # add a run with a more recent start time to session_1
        run_id = uuid4()
        run_body = {
            **run_body,
            "start_time": "2025-06-05T05:14:29",
            "session_id": str(session_1_id),
            "id": str(run_id),
            "dotted_order": f"20250605T051429000000Z{run_id}",
            "trace_id": str(run_id),
        }

        run_response = await aclient.post("/runs/batch", json={"post": [run_body]})
        assert run_response.status_code == 202
        await wait_until_task_queue_empty()

        # fetch the sessions with the same filter params
        response = await aclient.get(
            "/sessions",
            params={
                "offset": 0,
                "limit": 5,
                "sort_by": "last_run_start_time",
                "sort_by_desc": True,
                "reference_free": True,
            },
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 5
        # order should not change because of cache
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[-1]["id"] == str(session_1_id)

        # fetch the sessions with the same filter params
        response = await aclient.get(
            "/sessions",
            params={
                "offset": 0,
                "limit": 10,
                "sort_by": "last_run_start_time",
                "sort_by_desc": True,
                "reference_free": True,
            },
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 5
        # order should not change because is still cacheable
        assert sessions[0]["id"] == str(session_5_id)
        assert sessions[-1]["id"] == str(session_1_id)

        # fetch the sessions with the same filter params
        response = await aclient.get(
            "/sessions",
            params={
                "offset": 0,
                "limit": 11,
                "sort_by": "last_run_start_time",
                "sort_by_desc": True,
                "reference_free": True,
            },
        )

        assert response.status_code == 200
        sessions = response.json()
        assert len(sessions) == 5
        # order should change because this is not cacheable
        assert sessions[0]["id"] == str(session_1_id)
        assert sessions[-1]["id"] == str(session_2_id)

        # try streaming
        async with aconnect_sse(
            aclient,
            "GET",
            "/sessions?offset=0&limit=5&sort_by=last_run_start_time&sort_by_desc=true&reference_free=true",
        ) as sse:
            patch: list[dict] = []
            async for event in sse.aiter_sse():
                if event.event == "data":
                    patch += event.json()["patch"]
                elif event.event == "end":
                    pass
                else:
                    assert False, f"Unexpected event type {event}"

            streamed_sessions = jsonpatch.apply_patch({}, patch)["rows"]
            assert len(streamed_sessions) == 5
            assert streamed_sessions[0]["id"] == str(session_5_id)
            assert streamed_sessions[-1]["id"] == str(session_1_id)


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("patch_session_aggregation", [False, True], indirect=True)
async def test_tracer_session_include_stats_false(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    patch_session_aggregation: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # create a session
        session_id = uuid4()
        session_name = random_lower_string()

        aclient = authed_client.client

        response = await aclient.post(
            "/sessions",
            json={
                "id": str(session_id),
                "name": session_name,
                "start_time": datetime.now().isoformat(),
            },
        )
        assert response.status_code == 200

        # add a few runs to the session
        run_body = {
            "name": "AgentExecutor",
            "extra": {"metadata": {"bar": "foo", "bar2": "foo"}},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "parent_run_id": None,
            "run_type": "chain",
        }

        run_ids = [uuid4() for _ in range(5)]
        run_bodies = []

        for i, run_id in enumerate(run_ids):
            run_bodies.append(
                {
                    **run_body,
                    "start_time": datetime.now().isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "session_id": str(session_id),
                    "id": str(run_id),
                    "dotted_order": f"20240605T05142{i}000000Z{run_id}",
                    "trace_id": str(run_id),
                }
            )

        run_response = await aclient.post("/runs/batch", json={"post": run_bodies})
        assert run_response.status_code == 202
        await wait_until_task_queue_empty()

        # fetch the session by name with include_stats=False using the /sessions endpoint
        response = await aclient.get(
            f"/sessions?limit=1&name={session_name}&include_stats=False"
        )
        assert response.status_code == 200

        sessions = response.json()
        assert len(sessions) == 1
        session = sessions[0]
        assert session.get("last_run_start_time") is None
        assert session.get("run_count") is None

        response = await aclient.get(
            f"/sessions?limit=1&name={session_name}&include_stats=True"
        )
        assert response.status_code == 200

        sessions = response.json()
        assert len(sessions) == 1
        session = sessions[0]
        assert session.get("last_run_start_time") is not None
        assert session.get("run_count") == 5

        response = await aclient.get(f"/sessions?limit=1&name={session_name}")
        assert response.status_code == 200

        sessions = response.json()
        assert len(sessions) == 1
        session = sessions[0]
        assert session.get("last_run_start_time") is not None
        assert session.get("run_count") == 5


async def test_ensure_tracer_sessions(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    tenant_one_tracer_session_id_2: UUID,
):
    mock_db = AsyncMock()
    mock_db.fetch.return_value = [{"id": tenant_one_tracer_session_id}]
    mock_conn = AsyncMock()
    mock_conn.__aenter__.return_value = mock_db

    # clear the cache
    async with redis.aredis_caching_pool() as aredis:
        pattern = "smith:cache:auth_tracer_sessions:*"
        keys = await aredis.keys(pattern)
        if keys:
            await aredis.delete(*keys)

    with patch("app.crud.database.asyncpg_conn", return_value=mock_conn):
        with patch(
            "app.crud.ensure_tracer_sessions_from_cache",
            wraps=crud.ensure_tracer_sessions_from_cache,
        ) as mock_cache:
            # first check if we go to pg to check if session exists
            await crud.ensure_tracer_sessions(
                auth_tenant_one, {tenant_one_tracer_session_id}
            )
            mock_db.fetch.assert_called_once_with(
                """SELECT id FROM tracer_session WHERE id = ANY($1) AND tenant_id = $2""",
                [tenant_one_tracer_session_id],
                auth_tenant_one.tenant_id,
            )
            assert mock_cache.call_count == 1
            # now check if we do not go to pg
            await crud.ensure_tracer_sessions(
                auth_tenant_one, [tenant_one_tracer_session_id]
            )
            mock_db.fetch.assert_called_once()
            assert mock_cache.call_count == 2

            # insert new session
            mock_db.fetch.return_value = [{"id": tenant_one_tracer_session_id_2}]
            await crud.ensure_tracer_sessions(
                auth_tenant_one, {tenant_one_tracer_session_id_2}
            )
            assert len(mock_db.fetch.call_args_list) == 2
            assert mock_db.fetch.call_args_list[1] == call(
                """SELECT id FROM tracer_session WHERE id = ANY($1) AND tenant_id = $2""",
                [tenant_one_tracer_session_id_2],
                auth_tenant_one.tenant_id,
            )
            assert mock_cache.call_count == 3

            # check if we do not go to pg
            await crud.ensure_tracer_sessions(
                auth_tenant_one,
                {tenant_one_tracer_session_id, tenant_one_tracer_session_id_2},
            )
            mock_db.fetch.call_count = 2
            assert mock_cache.call_count == 4


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_read_empty_stream(
    fresh_tenant: tuple[AuthInfo, AsyncClient],
):
    client, _ = fresh_tenant
    response = await client.get(
        "/sessions",
        headers={"accept": "text/event-stream"},
    )
    assert response.status_code == 200

    expected_msgs = [
        "event: data",
        'data: {"patch":[{"op":"add","path":"","value":{"rows":[],"total":0,"excluding_empty":false}}]}',
        "event: end",
    ]

    msgs = [msg for msg in response.text.replace("\r", "").split("\n") if msg]
    assert len(msgs) == 3
    assert msgs == expected_msgs

    # test cacheable request
    expected_msgs_cacheable = [
        "event: data",
        'data: {"patch":[{"op":"add","path":"","value":{"rows":[],"total":0}}]}',
        "event: end",
    ]
    response = await client.get(
        "/sessions?offset=0&limit=1&sort_by=last_run_start_time&sort_by_desc=true&reference_free=true",
        headers={"accept": "text/event-stream"},
    )
    assert response.status_code == 200
    msgs = [msg for msg in response.text.replace("\r", "").split("\n") if msg]
    assert len(msgs) == 3
    assert msgs == expected_msgs_cacheable


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_cannot_create_dashboard_for_session_with_reference_dataset(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    now = datetime.now(timezone.utc) + timedelta(seconds=1)
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client_tenant = authed_client.client

        response = await client_tenant.post(
            "/datasets",
            json={
                "name": random_lower_string(),
            },
        )
        assert response.status_code == 200
        dataset_id = response.json()["id"]

        response = await client_tenant.post(
            "/sessions",
            json={"name": random_lower_string(), "reference_dataset_id": dataset_id},
        )
        assert response.status_code == 200
        session_id = response.json()["id"]

        response = await client_tenant.post(
            f"/sessions/{session_id}/dashboard",
            json={
                "start_time": (now - timedelta(hours=3)).isoformat(),
                "end_time": now.isoformat(),
                "stride": {
                    "hours": 1,
                },
            },
        )
        assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_feedback_corrections(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    now = datetime.now(timezone.utc).isoformat()
    later = (datetime.now(timezone.utc) + timedelta(seconds=6)).isoformat()
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client
        auth = authed_client.auth

        response = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert response.status_code == 200
        dataset_id = response.json()["id"]

        response = await aclient.post(
            "/sessions",
            json={
                "name": random_lower_string(),
                "start_time": now,
                "reference_dataset_id": dataset_id,
            },
        )
        assert response.status_code == 200
        session_id = response.json()["id"]

        run_id = uuid4()
        response = await aclient.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": now,
                "end_time": later,
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in czechia as of 2023?"},
                "outputs": {"output": "110,498,692"},
                "session_id": str(session_id),
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
            },
        )

        assert response.status_code == 202
        await wait_until_task_queue_empty()

        response = await aclient.post(
            "/feedback",
            json={
                "run_id": str(run_id),
                "key": "foo",
                "score": 0,
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        rows = await db_asyncpg.fetch(
            "SELECT * FROM feedbacks WHERE run_id = $1 AND tenant_id = $2",
            run_id,
            auth.tenant_id,
        )
        # PATCH the feedback
        response = await aclient.patch(
            f"/feedback/{rows[0]['id']}",
            json={"score": 0.6},
        )
        assert response.status_code == 200
        response = await aclient.get(f"/sessions/{session_id}?include_stats=true")
        assert response.status_code == 200
        session = response.json()
        assert session["id"] == session_id
        assert session.get("feedback_stats").get("foo").get("avg") == 0.6


@pytest.mark.anyio
async def test_tracer_session_dataset_version_filter(
    http_tenant_one: AsyncClient, auth_tenant_one: AuthInfo
):
    # 1. Create dataset
    response = await http_tenant_one.post(
        "/datasets",
        json={"name": "test-dataset", "description": "Test dataset"},
    )
    assert response.status_code == 200
    dataset = response.json()
    dataset_id = dataset["id"]

    # 2. Add examples at t1, t2, t3 (creates versions at those times)
    t1 = datetime.now(timezone.utc) - timedelta(hours=2)
    t2 = datetime.now(timezone.utc) - timedelta(hours=1)
    t3 = datetime.now(timezone.utc)
    for t in [t1, t2, t3]:
        response = await http_tenant_one.post(
            "/examples",
            json={
                "created_at": t.isoformat(),
                "inputs": {"input": f"val_{t.isoformat()}"},
                "outputs": {"output": f"out_{t.isoformat()}"},
                "dataset_id": dataset_id,
            },
        )
        assert response.status_code == 200

    # 3. Create a session with start_time between t2 and t3, no dataset_version in extra
    session_time = t2 + timedelta(minutes=30)
    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": "test-session",
            "start_time": session_time.isoformat(),
            "reference_dataset_id": dataset_id,
        },
    )
    assert response.status_code == 200
    session_id = response.json()["id"]

    # 4. Query for sessions with dataset_version=t1, t2, t3
    for version_time, should_find in [(t1, False), (t2, True), (t3, False)]:
        query_params = urlencode(
            {
                "reference_dataset": dataset_id,
                "dataset_version": version_time.isoformat(),
            }
        )
        response = await http_tenant_one.get(f"/sessions?{query_params}")
        assert response.status_code == 200
        session_ids = [s["id"] for s in response.json()]
        if should_find:
            assert session_id in session_ids, (
                f"Session should be found for version {version_time}"
            )
        else:
            assert session_id not in session_ids, (
                f"Session should NOT be found for version {version_time}"
            )
