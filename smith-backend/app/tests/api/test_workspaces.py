"""Test correct functionality of tenants endpoints."""

import uuid
from typing import cast

import asyncpg
import pytest
from httpx import AsyncClient

from app import config, crud, schemas
from app.api.auth.schemas import OrganizationRoles
from app.models.identities.seat_txn import SeatChangeOperation
from app.models.identities.users import get_provider_users_slim_in_txn
from app.models.tracer_sessions import create as tracer_sessions_crud
from app.tests.models.billing.test_transactions import (
    _delete_seat_events_for_org,
    _list_seat_events_for_org,
)
from app.tests.models.identities.test_crud import (
    _assert_reporting_status,
    _assert_valid_transaction_chain_for_org,
)
from app.tests.utils import (
    fresh_tenant_client,
    jwt_for_user,
    random_lower_string,
)

pytestmark = pytest.mark.anyio


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_create_workspace_in_existing_org(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a workspace can be created."""
    # personal org should fail even if max_workspaces is updated
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, organization_is_personal=True, use_pat=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        await db_asyncpg.execute(
            "UPDATE organizations set config = config || '{\"max_workspaces\": 2}' where id = $1",
            auth.organization_id,
        )

        response = await client.post(
            "/workspaces", json={"display_name": "test mukil's (cool) @workspace"}
        )
        assert response.status_code == 400, response.text

    # org with max_workspaces 2 should succeed
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, use_pat=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        await db_asyncpg.execute(
            "UPDATE organizations set config = config || '{\"max_workspaces\": 2}' where id = $1",
            auth.organization_id,
        )

        response = await client.post("/workspaces", json={"display_name": "test"})
        assert response.status_code == 200, response.text
        workspace = schemas.Tenant(**response.json())

        assert workspace.display_name == "test"
        assert workspace.organization_id == authed_client.auth.organization_id
        assert workspace.tenant_handle is None
        assert not workspace.is_personal

        # Can't create 3rd workspace, reached limit
        response = await client.post("/workspaces", json={"display_name": "test 2"})
        assert response.status_code == 400, response.text


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_create_workspace_with_colon(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that you cannot create a workspace with a colon in the display name"""
    if use_api_key:
        pytest.skip("Api keys are single tenant")
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        response = await client.post(
            "/workspaces", json={"display_name": "test:workspace"}
        )
        assert response.status_code == 422, response.text


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_create_workspace_adds_pending_org_admins(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that existing pending org admins are added to a new workspace as pending."""
    new_user_email = "<EMAIL>"
    org_user_email = "<EMAIL>"

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, use_pat=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        await db_asyncpg.execute(
            "UPDATE organizations set config = config || '{\"max_workspaces\": 3}' where id = $1",
            auth.organization_id,
        )

        org_admin_role_id = await db_asyncpg.fetchval(
            f"SELECT id FROM roles WHERE name = '{OrganizationRoles.ADMIN.value}'"
        )

        org_user_role_id = await db_asyncpg.fetchval(
            f"SELECT id FROM roles WHERE name = '{OrganizationRoles.USER.value}'"
        )

        # Invite them to the organization as org admin
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_admin_role_id),
            },
        )
        assert response.status_code == 200, response.text

        # Invite another user as an org user
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": org_user_email,
                "role_id": str(org_user_role_id),
            },
        )
        assert response.status_code == 200, response.text

        # clear seat change events
        await _delete_seat_events_for_org(auth.organization_id)

        response = await client.post("/workspaces", json={"display_name": "test"})
        assert response.status_code == 200, response.text
        workspace = schemas.Tenant(**response.json())

        # verify no seat change events
        seat_events = await _list_seat_events_for_org(auth.organization_id)
        assert len(seat_events) == 0

        response = await client.get(
            "/workspaces/current/members",
            headers={"X-Tenant-Id": str(workspace.id)},
        )
        assert response.status_code == 200, response.text

        members = response.json()["pending"]
        assert any(member["email"] == str(new_user_email) for member in members), (
            "Pending org admin should be added as pending to the new workspace"
        )
        workspace_pending_identity = next(
            m for m in members if m["email"] == str(new_user_email)
        )
        assert workspace_pending_identity["role_name"] == "Admin"

        assert all(member["email"] != str(org_user_email) for member in members), (
            "Pending org user should not be added to the new workspace"
        )


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_create_workspace_adds_existing_org_admins(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that existing org admins are added to a new workspace as admins."""
    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    new_user_jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {new_user_jwt}",
    }
    org_user_email = "<EMAIL>"
    stored_org_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(org_user_email))
    new_org_user_jwt = jwt_for_user(
        user_id=stored_org_user_id,
        user_email=org_user_email,
    )
    new_org_user_headers = {
        "Authorization": f"Bearer {new_org_user_jwt}",
    }

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, use_pat=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        await db_asyncpg.execute(
            "UPDATE organizations set config = config || '{\"max_workspaces\": 3}' where id = $1",
            auth.organization_id,
        )

        org_admin_role_id = await db_asyncpg.fetchval(
            f"SELECT id FROM roles WHERE name = '{OrganizationRoles.ADMIN.value}'"
        )

        org_user_role_id = await db_asyncpg.fetchval(
            f"SELECT id FROM roles WHERE name = '{OrganizationRoles.USER.value}'"
        )

        # Invite them to the organization as admin
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_admin_role_id),
            },
        )
        assert response.status_code == 200, response.text

        # Claim the organization invite
        response = await http_no_auth.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200, response.text
        claimed_org_identity = schemas.Identity(**response.json())

        # Invite another user to the organization as user
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": org_user_email,
                "role_id": str(org_user_role_id),
            },
        )
        assert response.status_code == 200, response.text

        # Claim the organization invite
        response = await http_no_auth.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_org_user_headers,
        )
        assert response.status_code == 200, response.text

        # clear seat change events
        await _delete_seat_events_for_org(auth.organization_id)

        response = await client.post("/workspaces", json={"display_name": "test"})
        assert response.status_code == 200, response.text
        workspace = schemas.Tenant(**response.json())

        # verify no seat change events
        seat_events = await _list_seat_events_for_org(auth.organization_id)
        assert len(seat_events) == 0

        response = await client.get(
            "/workspaces/current/members",
            headers={"X-Tenant-Id": str(workspace.id)},
        )
        assert response.status_code == 200, response.text

        members = response.json()["members"]
        assert any(
            member["user_id"] == str(claimed_org_identity.user_id) for member in members
        ), "Existing org admin should be added to the new workspace"
        workspace_identity = next(
            m for m in members if m["user_id"] == str(claimed_org_identity.user_id)
        )
        assert workspace_identity["role_name"] == "Admin"
        assert workspace_identity["ls_user_id"] is not None
        assert workspace_identity["ls_user_id"] == str(claimed_org_identity.ls_user_id)
        assert all(
            member["user_id"] != str(stored_org_user_id) for member in members
        ), "Existing org user should not be added to the new workspace"


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_list_workspaces(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that workspaces can be listed."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        response = await client.get("/workspaces")
        assert response.status_code == 200, response.text
        workspaces = response.json()
        assert len(workspaces) == 1
        workspace = schemas.TenantForUser(**workspaces[0])

        assert workspace.organization_id == authed_client.auth.organization_id
        assert not workspace.is_personal


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_patch_workspace(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a workspace's display_name can be updated."""
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, use_pat=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations set config = config || '{\"max_workspaces\": 2}' where id = $1",
            auth.organization_id,
        )

        # Create a workspace
        response = await client.post("/workspaces", json={"display_name": "test"})
        assert response.status_code == 200, response.text
        workspace = schemas.Tenant(**response.json())

        # Update the workspace's display_name
        new_display_name = "my_new_name"
        response = await client.patch(
            f"/workspaces/{workspace.id}", json={"display_name": new_display_name}
        )
        assert response.status_code == 200, response.text

        # Verify that the workspace's display_name has been updated
        response = await client.get("/workspaces")
        assert response.status_code == 200, response.text
        workspaces = response.json()
        assert len(workspaces) == 2  # default and
        updated_workspace = schemas.TenantForUser(
            **[w for w in workspaces if w["id"] == str(workspace.id)][0]
        )
        assert updated_workspace.display_name == new_display_name

    # should not allow update outside of org
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client2 = authed_client.client
        response = await client2.patch(
            f"/workspaces/{workspace.id}", json={"display_name": new_display_name}
        )
        assert response.status_code == 404, response.text


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_delete_workspace(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a workspace's display_name can be updated."""
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, use_pat=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations set config = config || '{\"max_workspaces\": 2}' where id = $1",
            auth.organization_id,
        )

        # Create a workspace
        response = await client.post("/workspaces", json={"display_name": "test"})
        assert response.status_code == 200, response.text
        workspace = schemas.Tenant(**response.json())

        # Verify that the workspace has been created
        response = await client.get("/workspaces")
        assert response.status_code == 200, response.text
        workspaces = response.json()
        assert len(workspaces) == 2

        # Datasets
        dataset = await crud.create_dataset(
            auth,
            schemas.DatasetCreate(
                name="test_delete_workspace",
                description="test_delete_workspace",
            ),
        )

        assert dataset is not None
        datasets = await db_asyncpg.fetch(
            "SELECT FROM dataset WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(datasets) == 1

        # Tracer sessions (tracer_session)
        tracer_session = await tracer_sessions_crud.create_tracer_session(
            db_asyncpg,
            auth,
            schemas.TracerSessionCreate(
                name="test_delete_workspace",
                description="test_delete_workspace",
                trace_tier=schemas.TraceTier.longlived,
                reference_dataset_id=dataset.id,
            ),
            upsert=True,
        )
        assert tracer_session is not None

        # Delete the workspace with id different from the current auth - should be forbidden
        response = await client.delete(
            f"/workspaces/{workspace.id}",
        )
        assert response.status_code == 403, response.text

        # Delete the original workspace (since it is mapped to auth)
        response = await client.delete(
            f"/workspaces/{auth.tenant_id}",
        )
        assert response.status_code == 200, response.text

    # Reauthenticate because we just deleted the workspace and the auth in it is no longer valid
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, use_pat=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        # Verify that the workspace has been deleted
        response = await client.get("/workspaces")
        assert response.status_code == 200, response.text
        workspaces = response.json()
        assert len(workspaces) == 1  # default = 1

        # Tracer sessions (tracer_session)
        tracer_sessions = await db_asyncpg.fetch(
            "SELECT FROM tracer_session WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(tracer_sessions) == 0


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_invite_user(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that a user can be invited directly to a workspace if they have an existing org identity."""
    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    new_user_jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {new_user_jwt}",
    }

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # clear seat change events
        await _delete_seat_events_for_org(auth.organization_id)

        await db_asyncpg.execute(
            """
            WITH u AS (
                INSERT INTO users (id, email) VALUES ($1, $2) RETURNING id, ls_user_id
            )
            INSERT INTO provider_users (ls_user_id, email, provider, provider_user_id)
            SELECT u.ls_user_id, $2, 'supabase:non-sso', u.id FROM u
            """,
            stored_user_id,
            new_user_email,
        )
        org_user_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
        )

        # Try inviting a user that does not have an org identity
        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(stored_user_id),
            },
        )
        assert response.status_code == 404, response.text

        # Invite them to the organization (must be Org User otherwise added to all workspaces)
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
            },
        )
        assert response.status_code == 200, response.text

        seat_events = await _list_seat_events_for_org(auth.organization_id)
        assert len(seat_events) == 2
        assert seat_events[0].operation == SeatChangeOperation.INVITE
        assert seat_events[0].seat_type == OrganizationRoles.ADMIN.value
        assert seat_events[0].seats_before == 1
        assert seat_events[0].pending_seats_before == 0
        assert seat_events[0].seats_after == 1
        assert seat_events[0].pending_seats_after == 0

        assert seat_events[1].operation == SeatChangeOperation.INVITE
        assert seat_events[1].seat_type == OrganizationRoles.USER.value
        assert seat_events[1].seats_before == 0
        assert seat_events[1].pending_seats_before == 0
        assert seat_events[1].seats_after == 0
        assert seat_events[1].pending_seats_after == 1

        _assert_valid_transaction_chain_for_org(
            seat_events,
            [SeatChangeOperation.INVITE],
        )
        _assert_reporting_status(seat_events)

        # Claim the organization invite
        response = await http_no_auth.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200, response.text
        claimed_org_identity = schemas.Identity(**response.json())

        seat_events = await _list_seat_events_for_org(auth.organization_id)
        assert len(seat_events) == 4
        assert seat_events[2].operation == SeatChangeOperation.CLAIM_INTO_ORGANIZATION
        assert seat_events[2].seat_type == OrganizationRoles.ADMIN.value
        assert seat_events[2].seats_before == 1
        assert seat_events[2].pending_seats_before == 0
        assert seat_events[2].seats_after == 1
        assert seat_events[2].pending_seats_after == 0

        assert seat_events[3].operation == SeatChangeOperation.CLAIM_INTO_ORGANIZATION
        assert seat_events[3].seat_type == OrganizationRoles.USER.value
        assert seat_events[3].seats_before == 0
        assert seat_events[3].pending_seats_before == 1
        assert seat_events[3].seats_after == 1
        assert seat_events[3].pending_seats_after == 0

        _assert_valid_transaction_chain_for_org(
            seat_events,
            [SeatChangeOperation.INVITE, SeatChangeOperation.CLAIM_INTO_ORGANIZATION],
        )
        _assert_reporting_status(seat_events)

        # Invite them to the workspace, which should succeed now that they have an org identity
        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(stored_user_id),
            },
        )
        assert response.status_code == 200, response.text
        # Duplicate invite should no-op
        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(stored_user_id),
            },
        )
        assert response.status_code == 200, response.text

        response = await client.get("/workspaces/current/members")
        assert response.status_code == 200, response.text
        members = schemas.TenantMembers(**response.json())

        # Verify that the user is not pending
        assert new_user_email not in [m.email for m in members.pending]

        # Verify that the user is now a member of the workspace
        assert new_user_email in [m.email for m in members.members]
        assert len([m for m in members.members if m.email == new_user_email]) == 1
        member = next(m for m in members.members if m.email == new_user_email)
        assert member.role_name == "Admin"
        assert member.user_id == claimed_org_identity.user_id
        assert member.tenant_id == auth.tenant_id
        assert member.organization_id == auth.organization_id


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_default_tag_keys_for_workspace(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a workspace can be created."""
    # check that a personal org has a single workspace with default tag keys
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, organization_is_personal=True
    ) as authed_client:
        client = authed_client.client
        response = await client.get("/workspaces/current/tag-keys")
        assert response.status_code == 200
        tag_keys = response.json()
        assert len(tag_keys) == len(config.settings.DEFAULT_TAG_KEYS)
        for tag_key in tag_keys:
            assert (
                tag_key["key"],
                tag_key["description"],
            ) in config.settings.DEFAULT_TAG_KEYS

    # create a new workspace in an org and check that it has the same default tag keys
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, use_pat=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        await db_asyncpg.execute(
            "UPDATE organizations set config = config || '{\"max_workspaces\": 2}' where id = $1",
            auth.organization_id,
        )

        response = await client.post("/workspaces", json={"display_name": "test"})
        assert response.status_code == 200, response.text
        workspace = schemas.Tenant(**response.json())

        # Fetch tag keys for the workspace
        keys = await db_asyncpg.fetch(
            "SELECT * FROM tag_keys WHERE tenant_id = $1", workspace.id
        )
        assert len(keys) == len(config.settings.DEFAULT_TAG_KEYS)
        for key in keys:
            assert (key["key"], key["description"]) in config.settings.DEFAULT_TAG_KEYS


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="workspaces disabled"
)
async def test_list_workspace_members(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a workspace's members can be listed."""
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, use_pat=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        response = await client.get("/workspaces/current/members")
        assert response.status_code == 200
        workspace_members = schemas.TenantMembers(**response.json())
        assert len(workspace_members.members) == 1  # owner
        assert len(workspace_members.pending) == 0
        assert workspace_members.tenant_id == auth.tenant_id
        member = workspace_members.members[0]
        assert member.role_name == "Admin"
        assert member.access_scope == schemas.AccessScope.workspace
        assert member.organization_id == auth.organization_id
        assert member.tenant_id == auth.tenant_id
        assert member.read_only is False
        assert member.user_id == auth.user_id
        assert member.email == auth.user_email
        assert set([p.provider_user_id for p in member.linked_login_methods]) == {
            auth.user_id,
        }
        expected_login_methods = await get_provider_users_slim_in_txn(
            db_asyncpg, cast(uuid.UUID, auth.ls_user_id)
        )
        assert len(expected_login_methods) == 1
        expected_login_method = expected_login_methods[0]
        login_method = next(
            p for p in member.linked_login_methods if p.provider_user_id == auth.user_id
        )
        assert expected_login_method.model_dump() == login_method.model_dump()
        assert "hashed_password" not in login_method.model_dump()


@pytest.mark.skipif(
    config.settings.AUTH_TYPE != "supabase", reason="new auth only enabled for supabase"
)
async def test_list_ws_members_paginated(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    org_admin_role_id = await db_asyncpg.fetchval(
        f"SELECT id FROM roles WHERE name = '{OrganizationRoles.ADMIN.value}'"
    )
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # Invite users
        num_invites = 10
        num_to_claim = 5
        invite_payload = [
            {
                "email": f"testlistwspg{i}@langchain.dev",
                "role_id": str(org_admin_role_id),
            }
            for i in range(num_invites)
        ]
        response = await client.post(
            "/orgs/current/members/batch",
            json=invite_payload,
        )

        response = await client.get("/workspaces/current/members")
        assert response.status_code == 200
        ws_members = schemas.TenantMembers(**response.json())
        assert len(ws_members.members) == 1  # owner
        assert len(ws_members.pending) == num_invites

        # Check paginated endpoints
        response = await client.get("/workspaces/current/members/active")
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        active_members = [schemas.MemberIdentity(**i) for i in response.json()]
        assert ws_members.members == active_members

        response = await client.get(
            "/workspaces/current/members/pending", params={"limit": num_invites}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites)
        pending_members = [schemas.PendingIdentity(**i) for i in response.json()]
        assert len(pending_members) == num_invites, "Should return exactly the limit"

        response = await client.get(
            "/workspaces/current/members/pending", params={"limit": num_invites // 2}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites)
        pending_members1 = [schemas.PendingIdentity(**i) for i in response.json()]
        response = await client.get(
            "/workspaces/current/members/pending",
            params={"limit": num_invites // 2, "offset": num_invites // 2},
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites)
        pending_members2 = [schemas.PendingIdentity(**i) for i in response.json()]
        assert [p.email for p in pending_members] == [
            p.email for p in pending_members1 + pending_members2
        ]

        response = await client.get(
            "/workspaces/current/members/pending", params={"limit": 1}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites)
        assert len(response.json()) == 1, "Should return exactly the limit"

        # Claim half of the invites
        for i in range(num_to_claim):
            payload = invite_payload[i]
            email = payload["email"]
            token = jwt_for_user(
                user_id=uuid.uuid4(), user_email=email, user_full_name=""
            )
            response = await client.post(
                f"/orgs/pending/{auth.organization_id}/claim",
                headers={"Authorization": f"Bearer {token}"},
            )
            assert response.status_code == 200

        # Check endpoints again
        response = await client.get("/workspaces/current/members")
        assert response.status_code == 200
        ws_members = schemas.TenantMembers(**response.json())
        assert len(ws_members.members) == num_to_claim + 1
        assert len(ws_members.pending) == num_invites - num_to_claim

        # Verify organization roles are present in workspace scope.
        # This test propagates to paginated endpoints via equality checks
        for member in ws_members.members:
            assert member.org_role_id == org_admin_role_id
            assert member.org_role_name == "Organization Admin"
        for member in ws_members.pending:
            assert member.org_role_id == org_admin_role_id
            assert member.org_role_name == "Organization Admin"

        # Default pagination params should get all users
        response = await client.get("/workspaces/current/members/active")
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_to_claim + 1)
        active_members = [schemas.MemberIdentity(**i) for i in response.json()]
        assert ws_members.members == active_members
        for member in active_members:
            assert member.org_role_id == org_admin_role_id
            assert member.org_role_name == "Organization Admin"

        response = await client.get("/workspaces/current/members/pending")
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites - num_to_claim)
        pending_members = [schemas.PendingIdentity(**i) for i in response.json()]
        assert len(pending_members) == num_invites - num_to_claim
        assert pending_members == ws_members.pending
        for member in pending_members:
            assert member.org_role_id == org_admin_role_id
            assert member.org_role_name == "Organization Admin"

        # Get fewer, confirm stable ordering
        limit = 2
        response = await client.get(
            "/workspaces/current/members/active", params={"limit": limit}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_to_claim + 1)
        active_members_few = [schemas.MemberIdentity(**i) for i in response.json()]
        assert len(active_members_few) == limit
        assert active_members_few == ws_members.members[: len(active_members_few)]

        response = await client.get(
            "/workspaces/current/members/pending", params={"limit": limit}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites - num_to_claim)
        pending_members_few = [schemas.PendingIdentity(**i) for i in response.json()]
        assert len(pending_members_few) == limit
        assert pending_members_few == ws_members.pending[: len(pending_members_few)]

        # Filter active by email, user_id, and ls_user_id
        response = await client.get(
            "/workspaces/current/members/active",
            params={"ls_user_ids": active_members[0].ls_user_id},
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        assert len(response.json()) == 1
        active_member = [schemas.MemberIdentity(**i) for i in response.json()][0]
        assert active_members[0] == active_member
        response = await client.get(
            "/workspaces/current/members/active",
            params={"emails": active_members[0].email},
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        assert len(response.json()) == 1
        active_member = [schemas.MemberIdentity(**i) for i in response.json()][0]
        assert active_members[0] == active_member
        response = await client.get(
            "/workspaces/current/members/active",
            params={"user_ids": active_members[0].user_id},
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        assert len(response.json()) == 1
        active_member = [schemas.MemberIdentity(**i) for i in response.json()][0]
        assert active_members[0] == active_member

        # Filter pending by email
        response = await client.get(
            "/workspaces/current/members/pending",
            params={"emails": pending_members[0].email},
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        assert len(response.json()) == 1
        pending_member = [schemas.PendingIdentity(**i) for i in response.json()][0]
        assert pending_members[0] == pending_member


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="none auth")
async def test_deleted_workspace(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a deleted workspace correctly returns 403 and can be optionally listed."""
    if use_api_key:
        pytest.skip("tenantless auth not supported with api key")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE tenants SET is_deleted = true WHERE id = $1", auth.tenant_id
        )

        response = await client.get(
            "/workspaces/current/members",
        )
        assert response.status_code == 403

        response = await client.post(
            "/runs",
            json={
                "name": "LLM",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_name": random_lower_string(),
                "parent_run_id": None,
                "run_type": "chain",
            },
        )
        assert response.status_code == 403

        response = await client.get(
            "/datasets",
        )
        assert response.status_code == 403

        # workspace should only be listed if requested
        for endpoint in ["/workspaces", "/tenants"]:
            response = await client.get(
                endpoint,
            )
            assert response.status_code == 200, f"Got {response.text} for {endpoint}"
            assert len(response.json()) == 0

            response = await client.get(
                endpoint,
                params={"include_deleted": "true"},
            )
            assert response.status_code == 200
            res = response.json()
            assert len(res) == 1, f"Got {res} for {endpoint}"
            assert res[0]["id"] == str(auth.tenant_id)
            assert res[0]["is_deleted"] is True

        await db_asyncpg.execute(
            "UPDATE tenants SET is_deleted = false WHERE id = $1", auth.tenant_id
        )

        response = await client.get(
            "/workspaces/current/members",
        )
        assert response.status_code == 200
