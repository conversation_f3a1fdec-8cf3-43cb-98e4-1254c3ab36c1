"""Test correct functionality of examples endpoints."""

import asyncio
import datetime
import json
import os
import tempfile
import urllib.parse
from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator, Literal, cast
from uuid import UUID, uuid4

import asyncpg
import pytest
from fastapi import HTTPException
from httpx import AsyncClient
from langchain_core.load import dumpd
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.tools import tool
from langchain_core.utils.function_calling import convert_to_openai_tool
from lc_database.curl import internal_platform_request, platform_request
from requests_toolbelt import MultipartEncoder

from app import config, crud, schemas
from app.api.auth import AuthInfo
from app.tests.api.test_runs import post_runs
from app.tests.utils import (
    FreshTenantClient,
    chat_run_1_payload,
    create_chat_preset_dataset,
    create_dataset,
    create_session,
    fresh_tenant_client,
    random_lower_string,
    wait_for_runs_to_end,
)

pytestmark = pytest.mark.anyio


async def _create_example(
    auth: AuthInfo,
    create_example: schemas.ExampleCreate,
) -> schemas.Example:
    """Helper function to create an example"""
    example_dict = create_example.model_dump()
    created_examples_json = await crud.create_examples(
        auth, [example_dict], ignore_conflicts=False
    )
    assert len(created_examples_json) == 1
    return schemas.Example.model_validate_json(created_examples_json[0])


async def _create_examples(
    auth: AuthInfo,
    create_examples: list[schemas.ExampleCreate],
) -> list[schemas.Example]:
    """Helper function to create examples"""
    example_dicts = [example.model_dump() for example in create_examples]
    created_examples_json = await crud.create_examples(auth, example_dicts)
    return [
        schemas.Example.model_validate_json(example)
        for example in created_examples_json
    ]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_update_example_attachments(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    use_multipart: bool,
) -> None:
    """Test updating an example's attachments."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        session_id = await create_session(aclient)
        # Add transformations to dataset
        req_json: dict[str, Any] = {
            "name": "transformations dataset " + str(uuid4()),
            "description": "A test dataset",
            "data_type": "kv",
            "inputs_schema_definition": {
                "type": "object",
                "properties": {
                    "input": {"type": "string"},
                },
            },
            "transformations": [
                {
                    "path": ["inputs"],
                    "transformation_type": "remove_extra_fields",
                }
            ],
        }
        resp = await aclient.post(
            "/datasets",
            json=req_json,
        )
        assert resp.status_code == 200
        dataset_id = resp.json()["id"]
        run_id_1 = uuid4()
        run_id_2 = uuid4()

        # Create a run with 2 attachments
        post = [
            {
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "THESE ARE THE INPUTS"},
                "outputs": {"output": "THESE ARE THE OUTPUTS"},
                "session_id": session_id,
                "run_type": "chain",
                "id": str(run_id_1),
                "trace_id": str(run_id_1),
                "dotted_order": f"20230505T051324571809Z{run_id_1}",
            },
            {
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "THESE ARE THE INPUTS"},
                "outputs": {"output": "THESE ARE THE OUTPUTS"},
                "session_id": session_id,
                "run_type": "chain",
                "id": str(run_id_2),
                "trace_id": str(run_id_2),
                "dotted_order": f"20230505T051324571809Z{run_id_2}",
            },
        ]

        attachments = {
            (str(run_id_1), "doc1.txt"): ("text/plain", b"First document"),
            (str(run_id_1), "doc2.txt"): ("text/plain", b"Second document"),
            (str(run_id_2), "doc1.txt"): ("text/plain", b"First document"),
            (str(run_id_2), "doc2.txt"): ("text/plain", b"Second document"),
        }

        await post_runs("/runs/multipart", aclient, post, [], attachments)
        await wait_for_runs_to_end(authed_client.auth, run_id_1, run_id_2)

        # Create example with both attachments
        example_1_req_body = {
            "inputs": {"input": "Test input"},
            "outputs": {"output": "Test output"},
            "source_run_id": str(run_id_1),
            "dataset_id": str(dataset_id),
            "use_source_run_attachments": ["doc1.txt", "doc2.txt"],
        }

        response = await aclient.post("/examples", json=example_1_req_body)
        assert response.status_code == 200
        example_1_id = response.json()["id"]

        # Verify both attachments exist
        response = await aclient.get(f"/examples/{example_1_id}")
        assert response.status_code == 200
        assert sorted(response.json()["attachment_urls"].keys()) == sorted(
            ["attachment.doc1.txt", "attachment.doc2.txt"]
        )
        original_attachment_url = response.json()["attachment_urls"][
            "attachment.doc2.txt"
        ]
        # Update example to only keep first attachment
        update_req_body_1 = {
            "attachments_operations": {
                "retain": ["doc1.txt"],
                "rename": {"doc2.txt": "doc3.txt"},
            }
        }

        response = await aclient.patch(
            f"/examples/{example_1_id}", json=update_req_body_1
        )
        assert response.status_code == 200

        response = await aclient.get(f"/examples/{example_1_id}")
        assert response.status_code == 200
        assert sorted(response.json()["attachment_urls"].keys()) == sorted(
            ["attachment.doc1.txt", "attachment.doc3.txt"]
        )
        assert (
            response.json()["attachment_urls"]["attachment.doc3.txt"]["storage_url"]
            == original_attachment_url["storage_url"]
        )
        assert all(
            a["presigned_url"].startswith("http")
            for a in response.json()["attachment_urls"].values()
        )
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": response.json()["attachment_urls"]["attachment.doc3.txt"][
                    "storage_url"
                ]
            },
        )
        assert result.body == b"Second document"
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": response.json()["attachment_urls"]["attachment.doc1.txt"][
                    "storage_url"
                ]
            },
        )
        assert result.body == b"First document"

        # Create example with both attachments
        example_2_req_body = {
            "inputs": {"input": "Test input"},
            "outputs": {"output": "Test output"},
            "source_run_id": str(run_id_2),
            "dataset_id": str(dataset_id),
            "use_source_run_attachments": ["doc1.txt", "doc2.txt"],
        }

        response = await aclient.post("/examples", json=example_2_req_body)
        assert response.status_code == 200
        example_2_id = response.json()["id"]

        update_req_body_2 = {"inputs": {"input": "foo"}}
        response = await aclient.patch(
            f"/examples/{example_2_id}", json=update_req_body_2
        )
        assert response.status_code == 200
        response = await aclient.get(f"/examples/{example_2_id}")
        assert response.status_code == 200
        # Test that attachments are unchanged if attachments_operations is empty
        assert sorted(response.json()["attachment_urls"].keys()) == sorted(
            ["attachment.doc1.txt", "attachment.doc2.txt"]
        )
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": response.json()["attachment_urls"]["attachment.doc1.txt"][
                    "storage_url"
                ]
            },
        )
        assert result.body == b"First document"
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": response.json()["attachment_urls"]["attachment.doc2.txt"][
                    "storage_url"
                ]
            },
        )
        assert result.body == b"Second document"

        # Name conflict
        update_req_body_3 = {
            "attachments_operations": {
                "rename": {"doc1.txt": "doc4.txt"},
                "retain": ["doc1.txt"],
            }
        }
        response = await aclient.patch(
            f"/examples/{example_2_id}", json=update_req_body_3
        )
        assert response.status_code == 400

        # Name conflict
        update_req_body_4 = {
            "attachments_operations": {
                "rename": {"doc2.txt": "doc1.txt"},
                "retain": ["doc1.txt"],
            }
        }
        response = await aclient.patch(
            f"/examples/{example_2_id}", json=update_req_body_4
        )
        assert response.status_code == 400

        # Renaming nonexistent attachment
        update_req_body_5 = {
            "attachments_operations": {
                "rename": {"doc3.txt": "doc1.txt"},
            }
        }
        response = await aclient.patch(
            f"/examples/{example_2_id}", json=update_req_body_5
        )
        assert response.status_code == 400

        # Retaining nonexistent attachment
        update_req_body_6 = {"attachments_operations": {"retain": ["doc3.txt"]}}
        response = await aclient.patch(
            f"/examples/{example_2_id}", json=update_req_body_6
        )
        assert response.status_code == 400

        bulk_update_req_json = [
            {
                "id": example_1_id,
                "attachments_operations": {
                    "retain": ["doc1.txt"],
                    "rename": {"doc3.txt": "doc4.txt"},
                },
            },
            {
                "id": example_2_id,
                "attachments_operations": {"rename": {"doc2.txt": "doc1.txt"}},
            },
        ]

        response = await _patch_examples(
            bulk_update_req_json, aclient, str(dataset_id), use_multipart=use_multipart
        )
        assert response.success(), (response.status_code, response.json())

        response = await aclient.get(f"/examples/{example_1_id}")
        assert response.status_code == 200
        assert sorted(response.json()["attachment_urls"].keys()) == sorted(
            ["attachment.doc1.txt", "attachment.doc4.txt"]
        )

        response = await aclient.get(f"/examples/{example_2_id}")
        assert response.status_code == 200
        assert list(response.json()["attachment_urls"].keys()) == [
            "attachment.doc1.txt"
        ]

        # Retain non-existent attachment
        bulk_update_error_one = [
            {"id": example_1_id, "attachments_operations": {"retain": ["doc2.txt"]}},
        ]
        response = await _patch_examples(
            bulk_update_error_one, aclient, str(dataset_id), use_multipart=use_multipart
        )
        expected_code = 422 if use_multipart else 400
        assert response.status_code == expected_code, response.json()

        # Rename non-existent attachment
        bulk_update_error_two = [
            {
                "id": example_1_id,
                "attachments_operations": {"rename": {"doc2.txt": "doc1.txt"}},
            },
        ]
        response = await _patch_examples(
            bulk_update_error_two, aclient, str(dataset_id), use_multipart=use_multipart
        )
        expected_code = 422 if use_multipart else 400
        assert response.status_code == expected_code, response.json()

        # Rename and retain conflift on key
        bulk_update_error_three = [
            {
                "id": example_1_id,
                "attachments_operations": {
                    "retain": ["doc1.txt"],
                    "rename": {"doc1.txt": "doc2.txt"},
                },
            },
        ]

        response = await _patch_examples(
            bulk_update_error_three,
            aclient,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        expected_code = 422 if use_multipart else 400
        assert response.status_code == expected_code, response.json()

        # Rename and retain conflift on value
        bulk_update_error_four = [
            {
                "id": example_1_id,
                "attachments_operations": {
                    "retain": ["doc1.txt"],
                    "rename": {"doc4.txt": "doc1.txt"},
                },
            },
        ]
        response = await _patch_examples(
            bulk_update_error_four,
            aclient,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        expected_code = 422 if use_multipart else 400
        assert response.status_code == expected_code, response.json()


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_split_metadata_behavior(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that passing split in metadata actually works"""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        dataset_id = await create_dataset(aclient)

        response = await aclient.post(
            "/examples",
            json={
                "inputs": {"input": "Test input"},
                "outputs": {"output": "Test output"},
                "dataset_id": str(dataset_id),
                "metadata": {"dataset_split": ["train"]},
            },
        )
        assert response.status_code == 200

        example_id = response.json()["id"]

        response = await aclient.get(f"/examples/{example_id}")
        assert response.status_code == 200
        assert response.json()["metadata"] == {"dataset_split": ["train"]}

        response = await aclient.post(
            "/examples",
            json={
                "inputs": {"input": "Test input"},
                "outputs": {"output": "Test output"},
                "dataset_id": str(dataset_id),
                "metadata": {"dataset_split": ["train"]},
                "split": "test",
            },
        )
        assert response.status_code == 200

        example_id = response.json()["id"]

        response = await aclient.get(f"/examples/{example_id}")
        assert response.status_code == 200
        assert response.json()["metadata"] == {"dataset_split": ["test"]}


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_example_with_attachments_from_run(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        session_id = await create_session(aclient)
        dataset_id = await create_dataset(aclient)

        inputs_outputs = [
            {
                "input": "THESE ARE THE INPUTS FOR RUN ONE",
                "output": "THESE ARE THE OUTPUTS FOR RUN ONE",
            },
            {
                "input": "THESE ARE THE INPUTS FOR RUN TWO",
                "output": "THESE ARE THE OUTPUTS FOR RUN TWO",
            },
            {
                "input": "THESE ARE THE INPUTS FOR RUN THREE",
                "output": "THESE ARE THE OUTPUTS FOR RUN THREE",
            },
            {
                "input": "THESE ARE THE INPUTS FOR RUN FOUR",
                "output": "THESE ARE THE OUTPUTS FOR RUN FOUR",
            },
        ]

        post = []
        run_ids = [uuid4() for _ in range(4)]
        run_id_1, run_id_2, run_id_3, run_id_4 = run_ids
        for io, run_id in zip(inputs_outputs, run_ids):
            post.append(
                {
                    "name": "AgentExecutor",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "end_time": "2023-05-05T05:13:32.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": io["input"]},
                    "outputs": {"output": io["output"]},
                    "session_id": session_id,
                    "run_type": "chain",
                    "id": str(run_id),
                    "trace_id": str(run_id),
                    "dotted_order": f"20230505T051324571809Z{run_id}",
                }
            )

        sample_text_data = ("This is a test document").encode("utf-8")
        sample_image_data = b"fake image data"

        attachments = {
            (str(run_id_1), "document.txt"): (
                "text/plain",
                sample_text_data,
            ),
            (str(run_id_1), "image.jpg"): ("image/jpeg", sample_image_data),
            (str(run_id_2), "document.txt"): (
                "text/plain",
                sample_text_data,
            ),
            (str(run_id_2), "image.jpg"): ("image/jpeg", sample_image_data),
            (str(run_id_3), "document.txt"): (
                "text/plain",
                sample_text_data,
            ),
            (str(run_id_3), "image.jpg"): ("image/jpeg", sample_image_data),
            (str(run_id_4), "document.txt"): (
                "text/plain",
                sample_text_data,
            ),
            (str(run_id_4), "image.jpg"): ("image/jpeg", sample_image_data),
        }

        await post_runs("/runs/multipart", aclient, post, [], attachments)
        await wait_for_runs_to_end(
            authed_client.auth, run_id_1, run_id_2, run_id_3, run_id_4
        )

        # Only copy values in use_source_run_attachments
        example_1_req_body = {
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "source_run_id": str(run_id_1),
            "dataset_id": str(dataset_id),
            "use_source_run_attachments": ["document.txt"],
        }

        response = await aclient.post(
            "/examples",
            json=example_1_req_body,
        )
        assert response.status_code == 200
        example_1_id = response.json()["id"]

        # try fetching through read path
        response = await aclient.get(f"/examples/{example_1_id}")
        assert response.status_code == 200

        returned_example_1 = response.json()
        assert list(returned_example_1["attachment_urls"].keys()) == [
            "attachment.document.txt"
        ]

        mime_type = returned_example_1["attachment_urls"]["attachment.document.txt"][
            "mime_type"
        ]
        assert mime_type == "text/plain"
        storage_url = returned_example_1["attachment_urls"]["attachment.document.txt"][
            "storage_url"
        ]
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={"path": storage_url},
        )
        assert result.body == sample_text_data

        # use_source_run_io overrules use_source_run_attachments
        example_2_req_body = {
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "source_run_id": str(run_id_2),
            "dataset_id": str(dataset_id),
            "use_source_run_io": True,
            "use_source_run_attachments": ["document.txt"],
        }

        response = await aclient.post(
            "/examples",
            json=example_2_req_body,
        )
        assert response.status_code == 200
        example_2_id = response.json()["id"]

        # try fetching through read path
        response = await aclient.get(f"/examples/{example_2_id}")
        assert response.status_code == 200

        returned_example_2 = response.json()
        assert sorted(returned_example_2["attachment_urls"].keys()) == sorted(
            ["attachment.document.txt", "attachment.image.jpg"]
        )

        mime_type = returned_example_2["attachment_urls"]["attachment.document.txt"][
            "mime_type"
        ]
        assert mime_type == "text/plain"
        storage_url = returned_example_2["attachment_urls"]["attachment.document.txt"][
            "storage_url"
        ]
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={"path": storage_url},
        )
        assert result.body == sample_text_data

        mime_type = returned_example_2["attachment_urls"]["attachment.image.jpg"][
            "mime_type"
        ]
        assert mime_type == "image/jpeg"
        storage_url = returned_example_2["attachment_urls"]["attachment.image.jpg"][
            "storage_url"
        ]
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={"path": storage_url},
        )
        assert result.body == sample_image_data

        # use_source_run_io copies attachments
        example_3_req_body = {
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "source_run_id": str(run_id_3),
            "dataset_id": str(dataset_id),
            "use_source_run_io": True,
        }

        response = await aclient.post(
            "/examples",
            json=example_3_req_body,
        )
        assert response.status_code == 200
        example_3_id = response.json()["id"]

        # try fetching through read path
        response = await aclient.get(f"/examples/{example_3_id}")
        assert response.status_code == 200

        returned_example_3 = response.json()
        assert sorted(returned_example_3["attachment_urls"].keys()) == sorted(
            ["attachment.document.txt", "attachment.image.jpg"]
        )

        mime_type = returned_example_3["attachment_urls"]["attachment.document.txt"][
            "mime_type"
        ]
        assert mime_type == "text/plain"
        storage_url = returned_example_3["attachment_urls"]["attachment.document.txt"][
            "storage_url"
        ]
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={"path": storage_url},
        )
        assert result.body == sample_text_data

        mime_type = returned_example_3["attachment_urls"]["attachment.image.jpg"][
            "mime_type"
        ]
        assert mime_type == "image/jpeg"
        storage_url = returned_example_3["attachment_urls"]["attachment.image.jpg"][
            "storage_url"
        ]
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={"path": storage_url},
        )
        assert result.body == sample_image_data

        # Attachments are not copied by default
        example_4_req_body = {
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "source_run_id": str(run_id_4),
            "dataset_id": str(dataset_id),
        }

        response = await aclient.post(
            "/examples",
            json=example_4_req_body,
        )
        assert response.status_code == 200
        example_4_id = response.json()["id"]

        # try fetching through read path
        response = await aclient.get(f"/examples/{example_4_id}")
        assert response.status_code == 200

        returned_example_4 = response.json()
        assert returned_example_4["attachment_urls"] is None

        # Using use_source_run_io without a run_id throws a 422
        example_5_req_body = {
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "dataset_id": str(dataset_id),
            "use_source_run_io": True,
        }

        response = await aclient.post(
            "/examples",
            json=example_5_req_body,
        )
        assert response.status_code == 422

        # Using use_source_run_attachments without a run_id throws a 422
        example_6_req_body = {
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "dataset_id": str(dataset_id),
            "use_source_run_attachments": [],
        }

        response = await aclient.post(
            "/examples",
            json=example_6_req_body,
        )
        assert response.status_code == 422

        # Passing False for use_source_run_io is fine
        example_7_req_body = {
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "dataset_id": str(dataset_id),
            "use_source_run_io": False,
        }

        response = await aclient.post(
            "/examples",
            json=example_7_req_body,
        )
        assert response.status_code == 200


async def test_create_example(
    tenant_one_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that an example can be created."""
    input_val_1 = random_lower_string()
    input_val_2 = random_lower_string()

    output_val_1 = random_lower_string()
    output_val_2 = random_lower_string()

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": input_val_1, "input_2": input_val_2},
            "outputs": {"output_1": output_val_1, "output_2": output_val_2},
            "dataset_id": str(tenant_one_dataset_id),
            "metadata": {"foo": "bar"},
        },
    )

    assert response.status_code == 200

    # Check that the example was created for the correct dataset
    example = await crud.get_example(auth_tenant_one, response.json()["id"])
    assert example.inputs == {"input_1": input_val_1, "input_2": input_val_2}
    # type: ignore
    assert example.outputs == {"output_1": output_val_1, "output_2": output_val_2}
    # type: ignore
    assert example.dataset_id == tenant_one_dataset_id  # type: ignore
    assert example.metadata == {"foo": "bar", "dataset_split": ["base"]}


@pytest.mark.parametrize("use_multipart", [True, False])
async def test_create_example_null_output(
    tenant_one_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    use_multipart: bool,
) -> None:
    """Test that an example can be created."""
    input_val_1 = random_lower_string()
    input_val_2 = random_lower_string()

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": input_val_1, "input_2": input_val_2},
            "outputs": None,
            "dataset_id": str(tenant_one_dataset_id),
            "metadata": {"foo": "bar"},
        },
    )

    assert response.status_code == 200

    # Check that the example was created for the correct dataset
    example = await crud.get_example(auth_tenant_one, response.json()["id"])
    assert example.inputs == {"input_1": input_val_1, "input_2": input_val_2}
    assert example.outputs is None
    assert example.dataset_id == tenant_one_dataset_id
    assert example.metadata == {"foo": "bar", "dataset_split": ["base"]}

    # Test bulk create
    examples = [
        {
            "inputs": {"input_1": input_val_1, "input_2": input_val_2},
            "outputs": None,
            "dataset_id": str(tenant_one_dataset_id),
            "metadata": {"foo": "bar"},
        },
    ]
    response = await _post_examples(
        examples,
        http_tenant_one,
        str(tenant_one_dataset_id),
        use_multipart=use_multipart,
    )

    assert response.success(), response.json()
    example_ids = (
        response.json().get("example_ids")
        if isinstance(response.json(), dict)
        else [e["id"] for e in response.json()]
    )
    assert len(example_ids) == 1
    example = await crud.get_example(auth_tenant_one, example_ids[0])
    assert example.inputs == {"input_1": input_val_1, "input_2": input_val_2}
    assert example.outputs is None
    assert example.dataset_id == tenant_one_dataset_id
    assert example.metadata == {"foo": "bar", "dataset_split": ["base"]}


@pytest.mark.parametrize("use_multipart", [True, False])
async def test_create_example_array_inputs_and_outputs(
    tenant_one_dataset_id: UUID,
    http_tenant_one: AsyncClient,
    use_multipart: bool,
) -> None:
    """Test that an example can be created."""
    input_val_1 = random_lower_string()
    input_val_2 = random_lower_string()

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": [{"input_1": input_val_1, "input_2": input_val_2}],
            "outputs": None,
            "dataset_id": str(tenant_one_dataset_id),
            "metadata": {"foo": "bar"},
        },
    )

    assert response.status_code == 422

    # Test bulk create
    examples = [
        {
            "inputs": [{"input_1": input_val_1, "input_2": input_val_2}],
            "outputs": None,
            "dataset_id": str(tenant_one_dataset_id),
            "metadata": {"foo": "bar"},
        },
    ]
    response = await _post_examples(
        examples,
        http_tenant_one,
        str(tenant_one_dataset_id),
        use_multipart=use_multipart,
    )

    assert response.status_code == 422


async def test_create_example_invalid_dataset(
    http_tenant_one: AsyncClient,
) -> None:
    """Test that an example cannot be created with a non-existent dataset."""
    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": "value_1", "input_2": "value_2"},
            "outputs": {"output_1": "value_1", "output_2": "value_2"},
            "dataset_id": str(uuid4()),
        },
    )

    assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_example_other_tenant_dataset(
    auth_tenant_one: AuthInfo,
    tenant_two_dataset_id: UUID,
) -> None:
    """Test that an example cannot be created with a dataset from another user."""
    with pytest.raises(HTTPException):
        await _create_example(
            auth_tenant_one,
            schemas.ExampleCreate(
                inputs={"input_1": "value_1", "input_2": "value_2"},
                outputs={"output_1": "value_1", "output_2": "value_2"},
                dataset_id=tenant_two_dataset_id,
            ),
        )


async def test_read_example(
    tenant_one_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that an example can be read."""

    input_val = random_lower_string()
    output_val = random_lower_string()
    random_example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": input_val},
            outputs={"output_1": output_val},
            dataset_id=tenant_one_dataset_id,
        ),
    )
    response = await http_tenant_one.get(f"/examples/{random_example.id}")

    assert response.status_code == 200, response.text
    assert response.json()["id"] == str(random_example.id)
    assert response.json()["inputs"] == {"input_1": input_val}
    assert response.json()["outputs"] == {"output_1": output_val}


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_read_example_other_tenant_dataset(
    tenant_two_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
) -> None:
    """Test that an example cannot be read if it belongs to another tenant."""
    random_example = await _create_example(
        auth_tenant_two,
        schemas.ExampleCreate(
            inputs={"input_1": random_lower_string()},
            outputs={"output_1": random_lower_string()},
            dataset_id=tenant_two_dataset_id,
        ),
    )

    with pytest.raises(HTTPException):
        await crud.get_example(
            auth_tenant_one,
            cast(UUID, random_example.id),
        )


async def test_read_example_invalid_id(
    http_tenant_one: AsyncClient,
) -> None:
    """Test that an example cannot be read if the id is invalid."""
    response = await http_tenant_one.get(f"/examples/{uuid4()}")

    assert response.status_code == 404


async def test_read_all_examples(
    http_tenant_one: AsyncClient,
    tenant_one_dataset_id: UUID,
) -> None:
    """Test that all examples can be read for a dataset."""
    response = await http_tenant_one.get(f"/examples?dataset={tenant_one_dataset_id}")
    assert response.status_code == 200


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_read_all_no_tenant(
    aclient: AsyncClient,
    tenant_one_dataset_id: UUID,
) -> None:
    """Test that all examples cannot be read if the user is not authorized."""
    response = await aclient.get(f"/examples?dataset={tenant_one_dataset_id}")
    assert response.status_code == 401


async def test_delete_example(
    tenant_one_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that an example can be deleted."""
    random_example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": random_lower_string()},
            outputs={"output_1": random_lower_string()},
            dataset_id=tenant_one_dataset_id,
        ),
    )

    response = await http_tenant_one.delete(f"/examples/{random_example.id}")

    assert response.status_code == 200

    response = await http_tenant_one.get(f"/examples/{random_example.id}")

    assert response.status_code == 404

    response = await http_tenant_one.get(
        f"/examples/{random_example.id}?as_of={urllib.parse.quote(datetime.datetime.now(datetime.timezone.utc).isoformat())}",
    )

    assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_delete_example_other_tenant_dataset(
    tenant_two_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
) -> None:
    """Test that an example cannot be deleted if it belongs to another tenant."""
    random_example = await _create_example(
        auth_tenant_two,
        schemas.ExampleCreate(
            inputs={"input_1": random_lower_string()},
            outputs={"output_1": random_lower_string()},
            dataset_id=tenant_two_dataset_id,
        ),
    )

    with pytest.raises(HTTPException):
        await crud.delete_example(
            auth_tenant_one,
            cast(UUID, random_example.id),
        )


async def test_update_example(
    tenant_one_dataset_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that an example can be updated."""
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    other_dataset_id = response.json()["id"]

    random_example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": random_lower_string()},
            outputs={"output_1": random_lower_string()},
            dataset_id=tenant_one_dataset_id,
            metadata={"foo": "bar"},
            split="test",
        ),
    )

    random_example_id = cast(UUID, random_example.id)
    input_val = random_lower_string()
    output_val = random_lower_string()
    await crud.update_example(
        auth_tenant_one,
        random_example_id,
        schemas.ExampleUpdate(
            inputs={"input_1": input_val},
            outputs={"output_1": output_val},
            split="train",
        ),
    )

    response = await crud.get_example(auth_tenant_one, random_example_id)
    assert response.inputs == {"input_1": input_val}  # type: ignore
    assert response.outputs == {"output_1": output_val}  # type: ignore
    assert response.metadata == {"foo": "bar", "dataset_split": ["train"]}

    input_val = random_lower_string()
    output_val = random_lower_string()
    await crud.update_example(
        auth_tenant_one,
        random_example_id,
        schemas.ExampleUpdate(
            inputs={"input_1": input_val},
            outputs={"output_1": output_val},
            split=["train", "other"],
        ),
    )

    response = await crud.get_example(auth_tenant_one, random_example_id)
    assert response.inputs == {"input_1": input_val}  # type: ignore
    assert response.outputs == {"output_1": output_val}  # type: ignore
    assert response.metadata == {"foo": "bar", "dataset_split": ["train", "other"]}

    response = await http_tenant_one.patch(
        f"/examples/{random_example_id}",
        json={
            "inputs": {"input_1": "test"},
            "outputs": {"output_2": "test"},
            "split": "train",
            "dataset_id": str(other_dataset_id),
        },
    )
    assert response.status_code == 400, response.json()

    response = await http_tenant_one.post(
        "/examples",
        json={
            "id": str(random_example_id),
            "inputs": {"input_1": "test"},
            "outputs": {"output_2": "test"},
            "split": "train",
            "dataset_id": str(other_dataset_id),
        },
    )
    assert response.status_code == 409


@pytest.mark.parametrize("use_multipart", [True, False])
async def test_update_examples(
    tenant_one_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    use_multipart: bool,
) -> None:
    """Test that examples can be updated in bulk."""
    random_example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": random_lower_string()},
            outputs={"output_1": random_lower_string()},
            dataset_id=tenant_one_dataset_id,
            metadata={"foo": "bar"},
            split="test",
        ),
    )
    random_example_2 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": random_lower_string()},
            outputs={"output_1": random_lower_string()},
            dataset_id=tenant_one_dataset_id,
            metadata={"foo": "bar"},
            split="test",
        ),
    )
    random_example_3 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": random_lower_string()},
            outputs={"output_1": random_lower_string()},
            dataset_id=tenant_one_dataset_id,
            metadata={"foo": "bar"},
            split="test",
        ),
    )

    new_input_val = random_lower_string()
    new_output_val = random_lower_string()
    new_input_val_2 = random_lower_string()
    new_output_val_2 = random_lower_string()
    new_input_val_3 = random_lower_string()
    new_output_val_3 = random_lower_string()
    example_updates = [
        {
            "id": str(random_example.id),
            "inputs": {"input_1": new_input_val},
            "outputs": {"output_2": new_output_val},
            "split": "train",
        },
        {
            "id": str(random_example_2.id),
            "inputs": {"input_1": new_input_val_2},
            "outputs": {"output_2": new_output_val_2},
            "split": ["train", "base"],
        },
        {
            "id": str(random_example_3.id),
            "inputs": {"input_1": new_input_val_3},
            "outputs": {"output_2": new_output_val_3},
            "split": "train",
        },
    ]
    response = await _patch_examples(
        example_updates,
        http_tenant_one,
        str(tenant_one_dataset_id),
        use_multipart=use_multipart,
    )
    assert response.success(), (response.status_code, response.json())

    response = await crud.get_example(auth_tenant_one, random_example.id)
    assert response.inputs == {"input_1": new_input_val}  # type: ignore
    assert response.outputs == {"output_2": new_output_val}  # type: ignore
    assert response.metadata == {"foo": "bar", "dataset_split": ["train"]}  # type: ignore

    response = await crud.get_example(auth_tenant_one, random_example_2.id)
    assert response.inputs == {"input_1": new_input_val_2}  # type: ignore
    assert response.outputs == {"output_2": new_output_val_2}  # type: ignore
    assert response.metadata == {"foo": "bar", "dataset_split": ["train", "base"]}  # type: ignore

    response = await crud.get_example(auth_tenant_one, random_example_3.id)
    assert response.inputs == {"input_1": new_input_val_3}  # type: ignore
    assert response.outputs == {"output_2": new_output_val_3}  # type: ignore
    assert response.metadata == {"foo": "bar", "dataset_split": ["train"]}  # type: ignore

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    other_dataset_id = response.json()["id"]

    # bad update to the wrong dataset
    example_updates = [
        {
            "id": str(random_example.id),
            "inputs": {"input_1": new_input_val},
            "outputs": {"output_2": new_output_val},
            "split": "train",
            "dataset_id": str(other_dataset_id),
        },
        {
            "id": str(random_example_2.id),
            "inputs": {"input_1": new_input_val_2},
            "outputs": {"output_2": new_output_val_2},
            "split": ["train", "base"],
            "dataset_id": str(other_dataset_id),
        },
        {
            "id": str(random_example_3.id),
            "inputs": {"input_1": new_input_val_3},
            "outputs": {"output_2": new_output_val_3},
            "split": "train",
            "dataset_id": str(other_dataset_id),
        },
    ]
    response = await _patch_examples(
        example_updates,
        http_tenant_one,
        str(other_dataset_id),
        use_multipart=use_multipart,
    )
    error_code = 409 if use_multipart else 400
    assert response.status_code == error_code, response.json()


async def test_update_example_change_metadata(
    tenant_one_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that an example can be updated."""
    random_example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": random_lower_string()},
            outputs={"output_1": random_lower_string()},
            dataset_id=tenant_one_dataset_id,
        ),
    )

    random_example_id = cast(UUID, random_example.id)
    input_val = random_lower_string()
    output_val = random_lower_string()
    await crud.update_example(
        auth_tenant_one,
        random_example_id,
        schemas.ExampleUpdate(
            inputs={"input_1": input_val},
            outputs={"output_1": output_val},
            metadata={"foo": "bar"},
            split="test",
        ),
    )

    response = await crud.get_example(auth_tenant_one, random_example_id)
    assert response.inputs == {"input_1": input_val}  # type: ignore
    assert response.outputs == {"output_1": output_val}  # type: ignore
    assert response.metadata == {"foo": "bar", "dataset_split": ["test"]}

    random_example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": random_lower_string()},
            outputs={"output_1": random_lower_string()},
            dataset_id=tenant_one_dataset_id,
        ),
    )

    random_example_id = cast(UUID, random_example.id)
    input_val = random_lower_string()
    output_val = random_lower_string()
    await crud.update_example(
        auth_tenant_one,
        random_example_id,
        schemas.ExampleUpdate(
            inputs={"input_1": input_val},
            outputs={"output_1": output_val},
            split="test",
        ),
    )

    response = await crud.get_example(auth_tenant_one, random_example_id)
    assert response.inputs == {"input_1": input_val}  # type: ignore
    assert response.outputs == {"output_1": output_val}  # type: ignore
    assert response.metadata == {"dataset_split": ["test"]}


@pytest.mark.flaky
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_bulk_create_examples(
    http_tenant_one: AsyncClient,
    tenant_one_dataset_id: UUID,
    use_multipart: bool,
) -> None:
    """Test that examples can be created in bulk."""
    metadata_one = {"foo": random_lower_string()}
    metadata_two = {"bar": random_lower_string()}
    input_1 = random_lower_string()
    output_1 = random_lower_string()
    input_2 = random_lower_string()
    output_2 = random_lower_string()
    example_id_1 = uuid4()
    example_id_2 = uuid4()
    examples = [
        {
            "id": str(example_id_1),
            "inputs": {"input_1_asdf": input_1},
            "outputs": {"output_1_asdf": output_1},
            "dataset_id": str(tenant_one_dataset_id),
            "metadata": {**metadata_one, "yo": "hi"},
        },
        {
            "id": str(example_id_2),
            "inputs": {"input_2_asdf": input_2},
            "outputs": {"output_2_asdf": output_2},
            "dataset_id": str(tenant_one_dataset_id),
            "metadata": {**metadata_two, "yo": "hi"},
        },
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(tenant_one_dataset_id),
        use_multipart=use_multipart,
    )
    assert response.success(), response.text

    # Check that examples can be filtered by metadata key-value pairs
    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": tenant_one_dataset_id, "metadata": json.dumps(metadata_one)},
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_one,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": tenant_one_dataset_id, "metadata": json.dumps(metadata_one)},
    )
    assert response.status_code == 200
    assert response.json() == 1

    # Check that examples can be filtered by full text
    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_1_asdf"],
        },
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_one,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_1_asdf"],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_1_asdf", "output_1_asdf"],
        },
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_one,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_1_asdf", "output_1_asdf"],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_1_asdf", input_1],
        },
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_one,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_1_asdf", input_1],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["output_1_asdf", input_1],
        },
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_one,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["output_1_asdf", input_1],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": tenant_one_dataset_id, "full_text_contains": [output_1]},
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_one,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": [output_1],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_2_asdf"],
        },
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_two,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_2_asdf"],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_2_asdf", "output_2_asdf"],
        },
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_two,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_2_asdf", "output_2_asdf"],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_2_asdf", input_2],
        },
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_two,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["input_2_asdf", input_2],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["output_2_asdf", input_2],
        },
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_two,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "full_text_contains": ["output_2_asdf", input_2],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": tenant_one_dataset_id, "full_text_contains": [output_2]},
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_two,
        "yo": "hi",
        "dataset_split": ["base"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": tenant_one_dataset_id, "full_text_contains": [output_2]},
    )
    assert response.status_code == 200
    assert response.json() == 1

    # Test case where one example's dataset_id is invalid
    examples = [
        {
            "inputs": {"input_1": random_lower_string()},
            "outputs": {"output_1": random_lower_string()},
            "dataset_id": str(tenant_one_dataset_id),
        },
        {
            "inputs": {"input_1": random_lower_string()},
            "outputs": {"output_1": random_lower_string()},
            "dataset_id": str(tenant_one_dataset_id),
        },
        {
            "inputs": {"input_1": random_lower_string()},
            "outputs": {"output_1": random_lower_string()},
            "dataset_id": str(uuid4()),
        },
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(tenant_one_dataset_id),
        use_multipart=False,
    )
    assert response.status_code == 400

    examples = [
        {
            "inputs": {"input_1": random_lower_string()},
            "outputs": {"output_1": random_lower_string()},
            "dataset_id": str(tenant_one_dataset_id),
            "metadata": {**metadata_one, "yo": "hi"},
        },
    ] * 30

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(tenant_one_dataset_id),
        use_multipart=use_multipart,
    )

    assert response.success()

    # Sort by recent

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "order": "recent",
            "metadata": json.dumps(metadata_one),
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 31
    assert response.json()[0]["modified_at"] > response.json()[-1]["modified_at"]

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "metadata": json.dumps(metadata_one),
        },
    )

    assert response.status_code == 200
    assert response.json() == 31

    # Sort by random with seed 0.25

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "order": "random",
            "random_seed": 0.25,
            "metadata": json.dumps(metadata_one),
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 31
    random_sorted = response.json()

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "metadata": json.dumps(metadata_one),
        },
    )

    assert response.status_code == 200
    assert response.json() == 31

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": tenant_one_dataset_id,
            "order": "random",
            "random_seed": 0.25,
            "metadata": json.dumps(metadata_one),
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 31
    assert response.json() == random_sorted

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": tenant_one_dataset_id,
            "order": "random",
            "random_seed": 0.25,
            "metadata": json.dumps(metadata_one),
        },
    )

    assert response.status_code == 200
    assert response.json() == 31

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    other_dataset_id = response.json()["id"]

    examples = [
        {
            "id": str(example_id_1),
            "inputs": {"input_1_asdf": input_1},
            "outputs": {"output_1_asdf": output_1},
            "metadata": {**metadata_one, "yo": "hi"},
            "dataset_id": str(other_dataset_id),
        },
        {
            "id": str(example_id_2),
            "inputs": {"input_2_asdf": input_2},
            "outputs": {"output_2_asdf": output_2},
            "metadata": {**metadata_two, "yo": "hi"},
            "dataset_id": str(other_dataset_id),
        },
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(tenant_one_dataset_id),
        use_multipart=use_multipart,
    )
    assert response.status_code == 409

    example = {
        "id": str(example_id_1),
        "inputs": {"input_1_asdf": input_1},
        "outputs": {"output_1_asdf": output_1},
        "metadata": {**metadata_one, "yo": "hi"},
        "dataset_id": str(other_dataset_id),
    }
    response = await http_tenant_one.post("/examples", json=example)
    assert response.status_code == 409


@pytest.mark.parametrize("use_multipart", [True, False])
async def test_bulk_create_examples_with_splits(
    http_tenant_one: AsyncClient,
    tenant_one_dataset_id: UUID,
    use_multipart: bool,
) -> None:
    """Test that examples can be created in bulk."""
    metadata_one = {"foo": random_lower_string()}
    input_1 = random_lower_string()
    output_1 = random_lower_string()
    input_2 = random_lower_string()
    output_2 = random_lower_string()
    examples = [
        {
            "inputs": {"input_1_abcd1": input_1},
            "outputs": {"output_1_abcd1": output_1},
            "dataset_id": str(tenant_one_dataset_id),
            "metadata": {**metadata_one, "yo": "hi"},
            "split": "testasdf",
        },
        {
            "inputs": {"input_2_abcd2": input_2},
            "outputs": {"output_2_abcd2": output_2},
            "dataset_id": str(tenant_one_dataset_id),
            "split": "trainasdf",
        },
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(tenant_one_dataset_id),
        use_multipart=use_multipart,
    )

    assert response.success()
    example_ids: list = cast(
        list,
        (
            response.json().get("example_ids")
            if isinstance(response.json(), dict)
            else [e["id"] for e in response.json()]
        ),
    )
    assert len(example_ids) == 2

    # Check that examples can be filtered by split
    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": tenant_one_dataset_id, "splits": ["testasdf"]},
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {
        **metadata_one,
        "yo": "hi",
        "dataset_split": ["testasdf"],
    }

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": tenant_one_dataset_id, "splits": ["testasdf"]},
    )
    assert response.status_code == 200, response.text
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": tenant_one_dataset_id, "splits": ["trainasdf"]},
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["metadata"] == {"dataset_split": ["trainasdf"]}

    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": tenant_one_dataset_id, "splits": ["trainasdf", "testasdf"]},
    )
    assert response.status_code == 200, response.text
    assert len(response.json()) == 2

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": tenant_one_dataset_id, "splits": ["trainasdf", "testasdf"]},
    )
    assert response.status_code == 200
    assert response.json() == 2


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_bulk_create_examples_other_tenant(
    tenant_two_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that examples cannot be created in bulk for other tenants."""
    with pytest.raises(HTTPException):
        await _create_examples(
            auth_tenant_one,
            [
                schemas.ExampleCreate(
                    inputs={"input_1": random_lower_string()},
                    outputs={"output_1": random_lower_string()},
                    dataset_id=tenant_two_dataset_id,
                ),
                schemas.ExampleCreate(
                    inputs={"input_1": random_lower_string()},
                    outputs={"output_1": random_lower_string()},
                    dataset_id=tenant_two_dataset_id,
                ),
            ],
        )


async def test_bulk_delete_examples(
    tenant_one_headers: dict,
    tenant_one_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that examples can be created in bulk."""

    db_examples = await _create_examples(
        auth_tenant_one,
        [
            schemas.ExampleCreate(
                inputs={"input_1": random_lower_string()},
                outputs={"output_1": random_lower_string()},
                dataset_id=tenant_one_dataset_id,
            ),
            schemas.ExampleCreate(
                inputs={"input_1": random_lower_string()},
                outputs={"output_1": random_lower_string()},
                dataset_id=tenant_one_dataset_id,
            ),
        ],
    )

    example_1_id, example_2_id = db_examples[0].id, db_examples[1].id

    response = await http_tenant_one.delete(
        "/examples",
        params={"example_ids": [str(example_1_id), str(example_2_id)]},
    )
    assert response.status_code == 200


async def test_upload_examples(
    http_tenant_one: AsyncClient,
    tenant_one_dataset_id: UUID,
) -> None:
    """Test that a dataset can be uploaded."""
    # Fetch the examples
    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": tenant_one_dataset_id},
    )
    assert response.status_code == 200
    example_ids = (
        response.json().get("example_ids")
        if isinstance(response.json(), dict)
        else [e["id"] for e in response.json()]
    )
    if example_ids:
        response = await http_tenant_one.delete(
            "/examples",
            params={"example_ids": example_ids},
        )
        assert response.status_code == 200, response.text

    test_dataset_bytes = open(
        f"{os.path.dirname(os.path.realpath(__file__))}/test_dataset.csv", "rb"
    ).read()
    with tempfile.NamedTemporaryFile(suffix=".csv") as tmp_file:
        with open(tmp_file.name, "w") as f:
            # Write the contents of "test_dataset.csv" to the temporary file
            f.write(test_dataset_bytes.decode("utf-8"))
        # Upload the temporary file, passing the input keys and output keys in the form
        response = await http_tenant_one.post(
            f"/examples/upload/{tenant_one_dataset_id}",
            files={"file": (tmp_file.name, open(tmp_file.name, "rb"))},
            data={
                "input_keys": ["input1", "input2", "input3"],
                "output_keys": ["output1"],
            },
        )

        assert response.status_code == 200
        assert len(response.json()) == 12

    # Fetch the examples
    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": tenant_one_dataset_id},
    )
    assert response.status_code == 200
    assert len(response.json()) == 12

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": tenant_one_dataset_id},
    )
    assert response.status_code == 200
    assert response.json() == 12


@pytest.mark.skipif(config.settings.AUTH_TYPE == "oauth", reason="write queue")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_list_shared_examples(
    fresh_tenant: tuple[AsyncClient, AuthInfo],
    use_multipart: bool,
) -> None:
    http_tenant_one, _ = fresh_tenant
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "comparison view test",
        },
    )
    dataset_id_2 = response.json()["id"]

    example_id = response.json()["id"]

    # Add run and session so we can
    # check that the source_run_id is scrubbed
    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "reference_dataset_id": str(dataset_id),
        },
    )
    assert response.status_code == 200
    session_id = response.json()["id"]

    run_id_1 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": session_id,
            "reference_example_id": example_id,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202
    # this wait fails
    # await wait_for_runs_to_exist(auth_tenant_one, run_id_1)

    response = await http_tenant_one.put(f"/datasets/{dataset_id}/share")
    assert response.status_code == 200
    share_token = response.json()["share_token"]
    response = await http_tenant_one.get(f"/public/{share_token}/examples")
    assert response.status_code == 200
    # Assert no examples are shared
    assert len(response.json()) == 0

    response = await http_tenant_one.get(f"/public/{share_token}/examples/count")
    assert response.status_code == 200
    # Assert no examples are shared
    assert response.json() == 0

    examples = [
        {
            "inputs": {"input_1": random_lower_string()},
            "outputs": {"output_1": random_lower_string()},
            "dataset_id": str(dataset_id),
        },
        {
            "inputs": {"input_1": random_lower_string()},
            "outputs": {"output_1": random_lower_string()},
            "dataset_id": str(dataset_id),
        },
        {
            "inputs": {"input_1": random_lower_string()},
            "outputs": {"output_1": random_lower_string()},
            "dataset_id": str(dataset_id),
            "metadata": {"foo": "bar"},
        },
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )
    assert response.success(), response.json()

    response = await http_tenant_one.get(f"/public/{share_token}/examples")
    assert response.status_code == 200
    assert len(response.json()) == 3
    assert all([example.get("source_run_id") is None for example in response.json()])

    count_response = await http_tenant_one.get(f"/public/{share_token}/examples/count")
    assert count_response.status_code == 200
    assert count_response.json() == 3

    # AND with the ID
    params = {"id": response.json()[0]["id"]}
    response = await http_tenant_one.get(
        f"/public/{share_token}/examples", params=params
    )
    assert response.status_code == 200
    assert len(response.json()) == 1

    # AND with a limit of 1
    params = {"limit": 1}
    response = await http_tenant_one.get(
        f"/public/{share_token}/examples", params=params
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.headers["X-Pagination-Total"] == "2"

    count_response = await http_tenant_one.get(
        f"/public/{share_token}/examples/count", params=params
    )
    assert count_response.json() == 3

    # With metadata
    params = {"metadata": json.dumps({"foo": "bar"})}
    response = await http_tenant_one.get(
        f"/public/{share_token}/examples", params=params
    )
    assert response.status_code == 200
    assert len(response.json()) == 1

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples/count", params=params
    )
    assert response.status_code == 200
    assert response.json() == 1

    # With filter
    params = {"filter": 'has("metadata", \'{"foo": "bar"}\')'}
    response = await http_tenant_one.get(
        f"/public/{share_token}/examples", params=params
    )
    assert response.status_code == 200
    assert len(response.json()) == 1

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples/count", params=params
    )
    assert response.status_code == 200
    assert response.json() == 1

    # With filter and metadata
    params = {
        "filter": 'has("metadata", \'{"foo": "bar"}\')',
        "metadata": json.dumps({"foo": "bar"}),
    }
    response = await http_tenant_one.get(
        f"/public/{share_token}/examples", params=params
    )
    assert response.status_code == 400

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    dataset_2_id = response.json()["id"]
    new_examples = [
        {**example, "dataset_id": str(dataset_2_id)} for example in examples
    ]
    response = await _post_examples(
        new_examples,
        http_tenant_one,
        str(dataset_id_2),
        use_multipart=use_multipart,
    )

    assert response.success()
    example_ids = (
        response.json().get("example_ids")
        if isinstance(response.json(), dict)
        else [e["id"] for e in response.json()]
    )
    params = {"id": example_ids}

    # Assert that the examples in the other dataset are not
    # inappropriately shared
    response = await http_tenant_one.get(
        f"/public/{share_token}/examples", params=params
    )
    assert response.status_code == 404

    response = await http_tenant_one.get(
        f"/public/{share_token}/examples/count", params=params
    )
    assert response.status_code == 404, response.json()


async def test_create_example_chat_dataset(
    tenant_one_chat_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that an example can be created."""
    input_val_1 = [
        {
            "data": {"content": random_lower_string(), "additional_kwargs": {}},
            "type": "human",
        }
    ]

    output_val_1 = {
        "data": {"content": random_lower_string(), "additional_kwargs": {}},
        "type": "ai",
    }
    functions = [
        {
            "name": "get_current_weather",
            "description": "Get the current weather in a given location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA",
                    },
                    "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]},
                },
                "required": ["location"],
            },
        }
    ]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input": input_val_1},
            "outputs": {"output": output_val_1},
            "dataset_id": str(tenant_one_chat_dataset_id),
        },
    )

    assert response.status_code == 200

    # Check that the example was created for the correct dataset
    example = await crud.get_example(auth_tenant_one, response.json()["id"])
    assert example.inputs == {"input": input_val_1}
    # type: ignore
    assert example.outputs == {"output": output_val_1}
    # type: ignore
    assert example.dataset_id == tenant_one_chat_dataset_id  # type: ignore

    # Test updating example
    output_val_2 = {
        "data": {"content": random_lower_string(), "additional_kwargs": {}},
        "type": "ai",
    }
    response = await http_tenant_one.patch(
        f"/examples/{str(example.id)}",
        json={
            "outputs": {"output": output_val_2},
        },
    )
    assert response.status_code == 200, response.json()

    # Check that the example was updated
    example = await crud.get_example(auth_tenant_one, example.id)
    assert example.inputs == {"input": input_val_1}
    assert example.outputs == {"output": output_val_2}

    input_val_2 = [
        {
            "data": {"content": random_lower_string(), "additional_kwargs": {}},
            "type": "human",
        }
    ]
    response = await http_tenant_one.patch(
        f"/examples/{str(example.id)}",
        json={
            "inputs": {"input": input_val_2},
        },
    )
    assert response.status_code == 200

    # Check that the example was updated
    example = await crud.get_example(auth_tenant_one, example.id)
    assert example.inputs == {"input": input_val_2}
    assert example.outputs == {"output": output_val_2}

    # Test with functions
    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input": input_val_1, "functions": functions},
            "outputs": {"output": output_val_1},
            "dataset_id": str(tenant_one_chat_dataset_id),
        },
    )

    assert response.status_code == 200

    example = await crud.get_example(auth_tenant_one, response.json()["id"])
    assert example.inputs == {"input": input_val_1, "functions": functions}
    # type: ignore
    assert example.outputs == {"output": output_val_1}
    # type: ignore
    assert example.dataset_id == tenant_one_chat_dataset_id  # type: ignore


async def test_create_example_llm_dataset(
    tenant_one_llm_dataset_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that an example can be created."""
    input_val_1 = {
        "input": random_lower_string(),
    }

    output_val_1 = {
        "output": random_lower_string(),
    }

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": input_val_1,
            "outputs": output_val_1,
            "dataset_id": str(tenant_one_llm_dataset_id),
        },
    )

    assert response.status_code == 200, response.json()

    # Check that the example was created for the correct dataset
    example = await crud.get_example(auth_tenant_one, response.json()["id"])
    assert example.inputs == input_val_1
    # type: ignore
    assert example.outputs == output_val_1
    # type: ignore
    assert example.dataset_id == tenant_one_llm_dataset_id  # type: ignore

    # Test updating example
    output_val_2 = {
        "output": random_lower_string(),
    }
    response = await http_tenant_one.patch(
        f"/examples/{str(example.id)}",
        json={
            "outputs": output_val_2,
        },
    )
    assert response.status_code == 200, response.json()

    # Check that the example was updated
    example = await crud.get_example(auth_tenant_one, example.id)
    assert example.inputs == input_val_1
    assert example.outputs == output_val_2

    input_val_2 = {
        "input": random_lower_string(),
    }
    response = await http_tenant_one.patch(
        f"/examples/{str(example.id)}",
        json={
            "inputs": input_val_2,
        },
    )
    assert response.status_code == 200, response.json()

    # Check that the example was updated
    example = await crud.get_example(auth_tenant_one, example.id)
    assert example.inputs == input_val_2
    assert example.outputs == output_val_2


async def test_read_example_version(
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that an example can be updated."""
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
            data_type=schemas.DataType.kv,
        ),
    )
    random_example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": f"original_input_{random_lower_string()}"},
            outputs={"output_1": f"original_output_{random_lower_string()}"},
            dataset_id=dataset.id,
        ),
    )

    updated_example = await crud.update_example(
        auth_tenant_one,
        cast(UUID, random_example.id),
        schemas.ExampleUpdate(
            inputs={"input_1": f"updated_input_{random_lower_string()}"},
            outputs={"output_1": f"updated_output_{random_lower_string()}"},
        ),
    )

    response = await crud.get_example(auth_tenant_one, cast(UUID, random_example.id))
    assert response.inputs["input_1"].startswith("updated_input_")
    assert response.outputs["output_1"].startswith("updated_output_")

    # Update just the outputs

    _ = await crud.update_example(
        auth_tenant_one,
        cast(UUID, random_example.id),
        schemas.ExampleUpdate(
            outputs={"output_1": f"newly_updated_output_{random_lower_string()}"},
        ),
    )

    response = await crud.get_example(auth_tenant_one, cast(UUID, random_example.id))
    assert response.inputs["input_1"].startswith("updated_input_")
    assert response.outputs["output_1"].startswith("newly_updated_output_")

    response = await crud.get_example(
        auth_tenant_one, cast(UUID, random_example.id), as_of=random_example.created_at
    )
    assert response.inputs["input_1"].startswith("original_input_")

    between_time = (
        random_example.created_at
        + (updated_example.modified_at - random_example.created_at) / 2
    )

    response = await crud.get_example(
        auth_tenant_one,
        cast(UUID, random_example.id),
        as_of=between_time,
    )

    assert response.inputs["input_1"].startswith("original_input_")
    response = await crud.get_example(
        auth_tenant_one,
        cast(UUID, random_example.id),
        as_of=updated_example.modified_at + datetime.timedelta(milliseconds=1),
    )

    assert response.inputs["input_1"].startswith("updated_input_")

    await crud.delete_example(
        auth_tenant_one,
        cast(UUID, random_example.id),
    )

    with pytest.raises(HTTPException):
        await crud.get_example(
            auth_tenant_one,
            cast(UUID, random_example.id),
        )

    response = await crud.get_example(
        auth_tenant_one,
        cast(UUID, random_example.id),
        as_of=random_example.created_at,
    )
    assert response.inputs["input_1"].startswith("original_input_")
    assert response.outputs["output_1"].startswith("original_output_")


async def test_read_examples_at_version(
    auth_tenant_one: AuthInfo,
) -> None:
    """Test list endpoints for examples."""
    created_at = datetime.datetime(2021, 1, 1, 0, 0, 0).astimezone(
        tz=datetime.timezone.utc
    )
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
            created_at=created_at,
            data_type=schemas.DataType.kv,
        ),
    )
    random_example_1 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            created_at=created_at,
            inputs={"input_1": f"original_input_{random_lower_string()}"},
            outputs={"output_1": f"original_output_{random_lower_string()}"},
            dataset_id=dataset.id,
        ),
    )

    await crud.update_example(
        auth_tenant_one,
        cast(UUID, random_example_1.id),
        schemas.ExampleUpdate(
            inputs={"input_1": f"updated_input_{random_lower_string()}"},
            outputs={"output_1": f"updated_output_{random_lower_string()}"},
        ),
    )

    random_example_2 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            created_at=created_at,
            inputs={"input_1": f"original_input_{random_lower_string()}"},
            outputs={"output_1": f"original_output_{random_lower_string()}"},
            dataset_id=dataset.id,
        ),
    )

    await crud.update_example(
        auth_tenant_one,
        cast(UUID, random_example_2.id),
        schemas.ExampleUpdate(
            inputs={"input_1": f"updated_input_{random_lower_string()}"},
            outputs={"output_1": f"updated_output_{random_lower_string()}"},
        ),
    )

    response, num_examples = await crud.get_examples(
        auth_tenant_one,
        schemas.FilterQueryParamsForExampleSchema(
            dataset=dataset.id,
            as_of=created_at,
        ),
    )
    assert len(response) == num_examples == 2
    assert response[0].inputs["input_1"].startswith("original_input_")
    assert response[1].inputs["input_1"].startswith("original_input_")

    response, num_examples = await crud.get_examples(
        auth_tenant_one,
        schemas.FilterQueryParamsForExampleSchema(
            dataset=dataset.id,
            as_of=created_at + datetime.timedelta(seconds=1),
        ),
    )
    assert len(response) == num_examples == 2
    assert response[0].inputs["input_1"].startswith("original_input_")
    assert response[1].inputs["input_1"].startswith("original_input_")

    response, num_examples = await crud.get_examples(
        auth_tenant_one,
        schemas.FilterQueryParamsForExampleSchema(
            dataset=dataset.id,
            as_of=datetime.datetime.now(datetime.timezone.utc)
            + datetime.timedelta(seconds=1),
        ),
    )
    assert len(response) == num_examples == 2
    assert response[0].inputs["input_1"].startswith("updated_input_")
    assert response[1].inputs["input_1"].startswith("updated_input_")

    # Test after deleting one data point
    await crud.delete_example(
        auth_tenant_one,
        cast(UUID, random_example_1.id),
    )

    response, num_examples = await crud.get_examples(
        auth_tenant_one,
        schemas.FilterQueryParamsForExampleSchema(
            dataset=dataset.id,
            as_of=created_at,
        ),
    )
    assert len(response) == num_examples == 2

    response, num_examples = await crud.get_examples(
        auth_tenant_one,
        schemas.FilterQueryParamsForExampleSchema(
            dataset=dataset.id,
            as_of=created_at + datetime.timedelta(seconds=1),
        ),
    )

    assert len(response) == num_examples == 2

    response, num_examples = await crud.get_examples(
        auth_tenant_one,
        schemas.FilterQueryParamsForExampleSchema(
            dataset=dataset.id,
            as_of=datetime.datetime.now(datetime.timezone.utc)
            + datetime.timedelta(seconds=1),
        ),
    )

    assert len(response) == num_examples == 1


async def test_splits(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that examples can be split."""
    # Create a dataset
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
            data_type=schemas.DataType.kv,
        ),
    )

    example_ids = []
    modified_at: datetime.datetime | None = None
    for i in range(10):
        example = await _create_example(
            auth_tenant_one,
            schemas.ExampleCreate(
                inputs={"input_1": random_lower_string()},
                outputs={"output_1": random_lower_string()},
                dataset_id=dataset.id,
            ),
        )
        example_ids.append(str(example.id))
        if i == 9:
            modified_at = example.modified_at

    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": dataset.id, "splits": ["base"]},
    )
    assert response.status_code == 200
    assert len(response.json()) == 10

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": dataset.id, "splits": ["base"]},
    )
    assert response.status_code == 200
    assert response.json() == 10

    response = await http_tenant_one.get(
        "/examples",
        params={"id": [example_ids[0]]},
    )
    assert response.status_code == 200
    assert response.json()[0]["metadata"]["dataset_split"] == ["base"]

    first_five_example_ids = example_ids[:5]

    response = await http_tenant_one.put(
        f"/datasets/{dataset.id}/splits",
        json={
            "examples": first_five_example_ids,
            "split_name": "train",
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 5

    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": dataset.id, "splits": ["train"]},
    )
    assert response.status_code == 200
    assert len(response.json()) == 5
    newest_modified_at: str | None = None
    for example in response.json():
        assert example["id"] in first_five_example_ids
        assert sorted(example["metadata"]["dataset_split"]) == sorted(["base", "train"])
        newest_modified_at = example["modified_at"]

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": dataset.id, "splits": ["train"]},
    )
    assert response.status_code == 200
    assert response.json() == 5

    response = await http_tenant_one.get(
        f"/datasets/{dataset.id}/splits",
    )
    assert response.status_code == 200
    assert sorted(response.json()) == sorted(["train", "base"])

    response = await http_tenant_one.get(
        f"/datasets/{dataset.id}/splits",
        params={"as_of": cast(datetime.datetime, modified_at).isoformat()},
    )
    assert response.status_code == 200
    assert response.json() == ["base"]

    response = await http_tenant_one.put(
        f"/datasets/{dataset.id}/splits",
        json={
            "examples": first_five_example_ids,
            "split_name": "validation",
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 5

    response = await http_tenant_one.get(
        f"/datasets/{dataset.id}/splits",
    )
    assert response.status_code == 200
    assert sorted(response.json()) == sorted(["validation", "base", "train"])

    response = await http_tenant_one.get(
        f"/datasets/{dataset.id}/splits",
        params={"as_of": newest_modified_at},
    )
    assert response.status_code == 200
    assert sorted(response.json()) == sorted(["train", "base"])

    response = await http_tenant_one.put(
        f"/datasets/{dataset.id}/splits",
        json={
            "examples": first_five_example_ids,
            "split_name": "validation",
            "remove": True,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 5

    response = await http_tenant_one.get(
        f"/datasets/{dataset.id}/splits",
    )
    assert response.status_code == 200
    assert sorted(response.json()) == sorted(["base", "train"])


async def test_read_examples_with_filter(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    # Create a dataset
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test",
            data_type=schemas.DataType.kv,
        ),
    )

    # Create examples
    example_1 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_1": "input_1"},
            outputs={"output_1": "output_1"},
            dataset_id=dataset.id,
        ),
    )

    example_2 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_2": "input_2"},
            outputs={"output_2": "output_2"},
            dataset_id=dataset.id,
            metadata={"foo": "bar"},
        ),
    )

    example_3 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input_3": "input_3"},
            outputs={"output_3": "output_3"},
            dataset_id=dataset.id,
            metadata={"baz": "qux"},
        ),
    )

    now = datetime.datetime.now(datetime.timezone.utc)

    # Fetch the examples
    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": dataset.id},
    )
    assert response.status_code == 200
    assert len(response.json()) == 3

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": dataset.id},
    )
    assert response.status_code == 200
    assert response.json() == 3

    # Filter by metadata
    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "metadata": json.dumps({"foo": "bar"}),
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_2.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "metadata": json.dumps({"foo": "bar"}),
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    # Use filters
    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'has("metadata", \'{"foo": "bar"}\')',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_2.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'has("metadata", \'{"foo": "bar"}\')',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'has("metadata", \'{"baz": "qux"}\')',
            "as_of": now,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_3.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'has("metadata", \'{"baz": "qux"}\')',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "baz")',
            "as_of": now,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_3.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "baz")',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_2.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'not(exists("metadata", "bar"))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 3

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'not(exists("metadata", "bar"))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 3

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'not(exists("metadata", "baz"))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 2

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'not(exists("metadata", "baz"))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 2

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'not(has("metadata", \'{"foo": "bar"}\'))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 2

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'not(has("metadata", \'{"foo": "bar"}\'))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 2

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'not(exists("metadata", "foo"), has("metadata", \'{"baz": "qux"}\'))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_1.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'not(exists("metadata", "foo"), has("metadata", \'{"baz": "qux"}\'))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'and(not(exists("metadata", "foo")), not(exists("metadata", "baz")))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_1.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'and(not(exists("metadata", "foo")), not(exists("metadata", "baz")))',
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    # Try using filter with invalid syntax
    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": dataset.id, "filter": "invalid_syntax", "as_of": now},
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": dataset.id, "filter": "invalid_syntax", "as_of": now},
    )
    assert response.status_code == 400

    # Try using filter and metadata together, should result in 400
    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "metadata": json.dumps({"foo": "bar"}),
            "as_of": now,
        },
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "metadata": json.dumps({"foo": "bar"}),
            "as_of": now,
        },
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "metadata": json.dumps({"foo": "bar"}),
            "as_of": now,
        },
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "metadata": json.dumps({"foo": "bar"}),
        },
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "metadata": json.dumps({"foo": "bar"}),
        },
    )
    assert response.status_code == 400

    # Try using filter along with full text search
    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "full_text_contains": ["input_1"],
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 0

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "full_text_contains": ["input_1"],
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 0

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "full_text_contains": ["input_2"],
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_2.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "full_text_contains": ["input_2"],
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    # Try using filter along with splits
    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "splits": ["base"],
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_2.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "splits": ["base"],
            "as_of": now,
        },
    )
    assert response.status_code == 200
    assert response.json() == 1

    # Try using with as_of=tag (default of as_of is "latest")
    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": dataset.id, "filter": 'has("metadata", \'{"foo": "bar"}\')'},
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_2.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": dataset.id, "filter": 'has("metadata", \'{"foo": "bar"}\')'},
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": dataset.id, "filter": 'has("metadata", \'{"baz": "qux"}\')'},
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_3.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": dataset.id, "filter": 'has("metadata", \'{"baz": "qux"}\')'},
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": dataset.id, "filter": 'exists("metadata", "baz")'},
    )

    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_3.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": dataset.id, "filter": 'exists("metadata", "baz")'},
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={"dataset": dataset.id, "filter": 'exists("metadata", "foo")'},
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_2.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={"dataset": dataset.id, "filter": 'exists("metadata", "foo")'},
    )
    assert response.status_code == 200
    assert response.json() == 1

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "full_text_contains": ["input_1"],
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 0

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "full_text_contains": ["input_1"],
        },
    )
    assert response.status_code == 200
    assert response.json() == 0

    response = await http_tenant_one.get(
        "/examples",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "full_text_contains": ["input_2"],
        },
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == str(example_2.id)

    response = await http_tenant_one.get(
        "/examples/count",
        params={
            "dataset": dataset.id,
            "filter": 'exists("metadata", "foo")',
            "full_text_contains": ["input_2"],
        },
    )
    assert response.status_code == 200
    assert response.json() == 1


@pytest.mark.parametrize("use_multipart", [True, False])
async def test_example_schema(
    http_tenant_one: AsyncClient, tenant_one_dataset_id: UUID, use_multipart: bool
) -> None:
    input_schema_definition = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "Address",
        "description": "An address book entry",
        "type": "object",
        "properties": {
            "name": {"description": "The person's full name", "type": "string"},
            "age": {
                "description": "The person's age",
                "type": "integer",
                "minimum": 0,
            },
            "sex": {
                "description": "The person's sex",
                "type": "string",
                "enum": ["male", "female"],
            },
            "email": {
                "description": "The person's email address",
                "type": "string",
                "format": "email",
            },
            "address": {
                "description": "The person's physical address",
                "type": "object",
                "properties": {
                    "streetAddress": {
                        "description": "The street address",
                        "type": "string",
                    },
                    "city": {"description": "The city", "type": "string"},
                    "state": {"description": "The state", "type": "string"},
                    "postalCode": {
                        "description": "The postal code",
                        "type": "string",
                        "pattern": "^[0-9]{5}(?:-[0-9]{4})?$",
                    },
                },
                "required": ["streetAddress", "city", "state", "postalCode"],
            },
            "phoneNumbers": {
                "description": "The person's phone numbers",
                "type": "array",
                "items": {
                    "type": "string",
                    "pattern": "^[2-9]{1}[0-9]{2}-[0-9]{3}-[0-9]{4}$",
                },
            },
        },
        "required": ["name", "email", "address"],
    }
    output_schema_definition = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "Stock",
        "description": "A stock market entry",
        "type": "object",
        "properties": {
            "name": {"description": "The company's name", "type": "string"},
            "price": {
                "description": "The company's stock price",
                "type": "number",
                "minimum": 0,
            },
            "ticker": {
                "description": "The company's ticker, if publicly traded",
                "type": "string",
            },
        },
        "required": ["name"],
        "additionalProperties": False,
    }
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "$schema": "http://json-schema.org/draft-07/schema#",
            "name": random_lower_string(),
            "description": "test",
            "inputs_schema_definition": input_schema_definition,
            "outputs_schema_definition": output_schema_definition,
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    example_with_all_fields = {
        "inputs": {
            "name": "John Doe",
            "age": 30,
            "sex": "male",
            "email": "<EMAIL>",
            "address": {
                "streetAddress": "123 Main St",
                "city": "Springfield",
                "state": "IL",
                "postalCode": "62701",
            },
            "phoneNumbers": ["************", "************"],
        },
        "outputs": {"name": "Company Inc.", "price": 100.00, "ticker": "CMI"},
        "dataset_id": str(dataset_id),
    }
    example_with_minimum_fields = {
        "inputs": {
            "name": "Jane Doe",
            "email": "<EMAIL>",
            "address": {
                "streetAddress": "123 Main St",
                "city": "Springfield",
                "state": "IL",
                "postalCode": "62701",
            },
        },
        "outputs": {"name": "LangChain Inc."},
        "dataset_id": str(dataset_id),
    }
    example_with_incompatible_inputs = {
        "inputs": {
            "name": "John Doe",
            "age": 30,
            "address": {
                "streetAddress": "123 Main St",
                "city": "Springfield",
                "state": "IL",
                "postalCode": "62701",
            },
        },
        "outputs": {"name": "Company Inc.", "price": 100.00, "ticker": "CMI"},
        "dataset_id": str(dataset_id),
    }
    example_2_with_incompatible_inputs = {
        "inputs": {
            "name": "Jane Doe",
            "email": "<EMAIL>",
            "address": {
                "streetAddress": "123 Main St",
                "city": "Springfield",
                "state": "IL",
                "postalCode": "62701",
            },
            "phoneNumbers": ["asdf"],
        },
        "outputs": {"name": "Company Inc."},
        "dataset_id": str(dataset_id),
    }
    example_with_incompatible_outputs = {
        "inputs": {
            "name": "John Doe",
            "email": "<EMAIL>",
            "address": {
                "streetAddress": "123 Main St",
                "city": "Springfield",
                "state": "IL",
                "postalCode": "62701",
            },
            "phoneNumbers": ["************", "************"],
        },
        "outputs": {
            "name": "Company Inc.",
            "price": "100.00",
        },
        "dataset_id": str(dataset_id),
    }
    example_2_with_incompatible_outputs = {
        "inputs": {
            "name": "Jane Doe",
            "email": "<EMAIL>",
            "address": {
                "streetAddress": "123 Main St",
                "city": "Springfield",
                "state": "IL",
                "postalCode": "62701",
            },
            "phoneNumbers": ["************", "************"],
        },
        "outputs": {"name": "Company Inc.", "ticket": "CMI"},
        "dataset_id": str(dataset_id),
    }

    examples: list = [
        example_with_all_fields,
        example_with_minimum_fields,
        example_with_incompatible_inputs,
        example_2_with_incompatible_inputs,
        example_with_incompatible_outputs,
        example_2_with_incompatible_outputs,
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )

    expected_code = 422 if use_multipart else 400
    assert response.status_code == expected_code, response.json()

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 0

    response = await http_tenant_one.get(f"/examples/count?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert response.json() == 0

    examples = [
        example_with_all_fields,
        example_with_minimum_fields,
        example_with_incompatible_inputs,
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )
    expected_code = 422 if use_multipart else 400
    assert response.status_code == expected_code, response.json()

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 0

    response = await http_tenant_one.get(f"/examples/count?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert response.json() == 0

    examples = [
        example_with_all_fields,
        example_with_minimum_fields,
        example_with_incompatible_outputs,
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )
    expected_code = 422 if use_multipart else 400
    assert response.status_code == expected_code, response.json()

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 0

    response = await http_tenant_one.get(f"/examples/count?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert response.json() == 0

    examples = [
        example_with_all_fields,
        example_with_minimum_fields,
        example_2_with_incompatible_inputs,
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )
    expected_code = 422 if use_multipart else 400
    assert response.status_code == expected_code, response.json()

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 0

    response = await http_tenant_one.get(f"/examples/count?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert response.json() == 0

    examples = [
        example_with_all_fields,
        example_with_minimum_fields,
        example_2_with_incompatible_outputs,
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )
    expected_code = 422 if use_multipart else 400
    assert response.status_code == expected_code, response.json()

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 0

    response = await http_tenant_one.get(f"/examples/count?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert response.json() == 0

    examples = [
        example_with_all_fields,
        example_with_minimum_fields,
    ]

    response = await _post_examples(
        examples,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )
    assert response.success(), response.json()

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 2

    count_response = await http_tenant_one.get(
        f"/examples/count?dataset={str(dataset_id)}"
    )
    assert count_response.status_code == 200
    assert count_response.json() == 2

    john_doe_example = next(
        example
        for example in response.json()
        if example["inputs"]["name"] == "John Doe"
    )
    jane_doe_example = next(
        example
        for example in response.json()
        if example["inputs"]["name"] == "Jane Doe"
    )

    assert john_doe_example["outputs"] == example_with_all_fields["outputs"]
    assert jane_doe_example["outputs"] == example_with_minimum_fields["outputs"]

    assert john_doe_example["inputs"] == example_with_all_fields["inputs"]
    assert jane_doe_example["inputs"] == example_with_minimum_fields["inputs"]

    john_doe_id = john_doe_example["id"]
    jane_doe_id = jane_doe_example["id"]

    response = await http_tenant_one.post(
        "/examples", json=example_with_incompatible_inputs
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 2

    response = await http_tenant_one.get(f"/examples/count?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert response.json() == 2

    response = await http_tenant_one.post(
        "/examples", json=example_2_with_incompatible_inputs
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 2

    response = await http_tenant_one.get(f"/examples/count?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert response.json() == 2

    response = await http_tenant_one.post(
        "/examples", json=example_with_incompatible_outputs
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 2

    response = await http_tenant_one.get(f"/examples/count?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert response.json() == 2

    response = await http_tenant_one.post(
        "/examples", json=example_2_with_incompatible_outputs
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 2

    response = await http_tenant_one.post("/examples", json=example_with_all_fields)
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 3
    assert john_doe_id in [example["id"] for example in response.json()]
    assert jane_doe_id in [example["id"] for example in response.json()]

    count_response = await http_tenant_one.get(
        f"/examples/count?dataset={str(dataset_id)}"
    )
    assert count_response.status_code == 200
    assert count_response.json() == 3

    new_example = next(
        example
        for example in response.json()
        if example["id"] != john_doe_id and example["id"] != jane_doe_id
    )
    assert new_example["outputs"] == example_with_all_fields["outputs"]
    assert new_example["inputs"] == example_with_all_fields["inputs"]

    new_example_id = new_example["id"]

    response = await http_tenant_one.post("/examples", json=example_with_minimum_fields)
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/examples?dataset={str(dataset_id)}")
    assert response.status_code == 200
    assert len(response.json()) == 4
    assert john_doe_id in [example["id"] for example in response.json()]
    assert jane_doe_id in [example["id"] for example in response.json()]
    assert new_example_id in [example["id"] for example in response.json()]

    count_response = await http_tenant_one.get(
        f"/examples/count?dataset={str(dataset_id)}"
    )
    assert count_response.status_code == 200
    assert count_response.json() == 4

    new_example = next(
        example
        for example in response.json()
        if example["id"] != john_doe_id
        and example["id"] != jane_doe_id
        and example["id"] != new_example_id
    )
    assert new_example["outputs"] == example_with_minimum_fields["outputs"]
    assert new_example["inputs"] == example_with_minimum_fields["inputs"]

    new_example_id_2 = new_example["id"]

    response = await http_tenant_one.patch(
        f"/examples/{new_example_id}", json={"inputs": {"name": "John Doe"}}
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(f"/examples/{new_example_id}")
    assert response.status_code == 200
    assert response.json()["inputs"] == example_with_all_fields["inputs"]
    assert response.json()["outputs"] == example_with_all_fields["outputs"]

    response = await http_tenant_one.patch(
        f"/examples/{new_example_id_2}",
        json={"outputs": example_2_with_incompatible_outputs["outputs"]},
    )
    assert response.status_code == 400

    response = await http_tenant_one.get(f"/examples/{new_example_id_2}")
    assert response.status_code == 200
    assert response.json()["inputs"] == example_with_minimum_fields["inputs"]
    assert response.json()["outputs"] == example_with_minimum_fields["outputs"]

    response = await http_tenant_one.patch(
        f"/examples/{new_example_id_2}",
        json={"outputs": example_with_all_fields["outputs"]},
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/examples/{new_example_id_2}")
    assert response.status_code == 200
    assert response.json()["inputs"] == example_with_minimum_fields["inputs"]
    assert response.json()["outputs"] == example_with_all_fields["outputs"]

    response = await http_tenant_one.patch(
        f"/examples/{new_example_id_2}",
        json={"inputs": example_with_all_fields["inputs"]},
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/examples/{new_example_id_2}")
    assert response.status_code == 200
    assert response.json()["inputs"] == example_with_all_fields["inputs"]
    assert response.json()["outputs"] == example_with_all_fields["outputs"]

    json_schema_with_less_requirements = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "Address",
        "description": "An address book entry",
        "type": "object",
        "properties": {
            "name": {"description": "The person's full name", "type": "string"},
            "age": {
                "description": "The person's age",
                "type": "integer",
                "minimum": 0,
            },
            "email": {
                "description": "The person's email address",
                "type": "string",
                "format": "email",
            },
            "address": {
                "description": "The person's physical address",
                "type": "object",
                "properties": {
                    "streetAddress": {
                        "description": "The street address",
                        "type": "string",
                    },
                    "city": {"description": "The city", "type": "string"},
                    "state": {"description": "The state", "type": "string"},
                    "postalCode": {
                        "description": "The postal code",
                        "type": "string",
                        "pattern": "^[0-9]{5}(?:-[0-9]{4})?$",
                    },
                },
                "required": ["city", "state", "postalCode"],  # removed streetAddress
            },
            "phoneNumbers": {
                "description": "The person's phone numbers",
                "type": "array",
                "items": {
                    "type": "string",
                    "pattern": "^[2-9]{1}[0-9]{2}-[0-9]{3}-[0-9]{4}$",
                },
            },
            "newField": {
                "description": "A new field",
                "type": "string",
            },
        },
        "required": ["name", "email", "address"],
    }

    # Test updating dataset schema and ensure it is only allowed if the existing examples are compatible
    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "inputs_schema_definition": json_schema_with_less_requirements,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/datasets/{dataset_id}")
    assert response.status_code == 200
    assert (
        response.json()["inputs_schema_definition"]
        == json_schema_with_less_requirements
    )
    assert response.json()["outputs_schema_definition"] == output_schema_definition

    output_schema_with_added_required_field = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "Stock",
        "description": "A stock market entry",
        "type": "object",
        "properties": {
            "name": {"description": "The company's name", "type": "string"},
            "price": {
                "description": "The company's stock price",
                "type": "number",
                "minimum": 0,
            },
            "ticker": {
                "description": "The company's ticker, if publicly traded",
                "type": "string",
            },
        },
        "required": ["name", "price"],
        "additionalProperties": False,
    }

    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "outputs_schema_definition": output_schema_with_added_required_field,
        },
    )
    assert response.status_code == 400
    assert len(response.json()["detail"]["non_conforming_examples"]) == 1
    assert (
        jane_doe_example["id"] in response.json()["detail"]["non_conforming_examples"]
    )

    response = await http_tenant_one.get(f"/datasets/{dataset_id}")
    assert response.status_code == 200
    assert (
        response.json()["inputs_schema_definition"]
        == json_schema_with_less_requirements
    )
    assert response.json()["outputs_schema_definition"] == output_schema_definition

    # Try updating the schema, and simultaneously update the outputs to be compatible but make the inputs incompatible
    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "outputs_schema_definition": output_schema_with_added_required_field,
            "patch_examples": {
                jane_doe_id: {
                    "inputs": {"name": "Jane Doe", "email": "<EMAIL>"},
                    "outputs": {
                        "name": "Company Inc.",
                        "price": 100.00,
                    },
                }
            },
        },
    )
    assert response.status_code == 400

    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "outputs_schema_definition": output_schema_with_added_required_field,
            "patch_examples": {
                jane_doe_id: {
                    "outputs": {
                        "name": "Company Inc.",
                        "price": "100.00",
                    }
                }
            },
        },
    )
    assert response.status_code == 400

    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "outputs_schema_definition": output_schema_with_added_required_field,
            "patch_examples": {
                jane_doe_id: {
                    "outputs": {
                        "name": "Company Inc.",
                        "price": 100.00,
                    }
                }
            },
        },
    )
    assert response.status_code == 200
    assert response.headers["X-Updated-Examples-Count"] == "1"

    response = await http_tenant_one.get(f"/datasets/{dataset_id}")
    assert response.status_code == 200
    assert (
        (response.json()["inputs_schema_definition"])
        == json_schema_with_less_requirements
    )
    assert (
        response.json()["outputs_schema_definition"]
        == output_schema_with_added_required_field
    )

    response = await http_tenant_one.get(f"/examples/{jane_doe_id}")
    assert response.status_code == 200
    assert response.json()["outputs"] == {
        "name": "Company Inc.",
        "price": 100.00,
    }
    assert response.json()["inputs"] == example_with_minimum_fields["inputs"]

    response = await http_tenant_one.get(f"/examples/{john_doe_id}")
    assert response.status_code == 200
    assert response.json()["outputs"] == example_with_all_fields["outputs"]
    assert response.json()["inputs"] == example_with_all_fields["inputs"]

    output_schema_with_added_optional_field = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "Stock",
        "description": "A stock market entry",
        "type": "object",
        "properties": {
            "name": {"description": "The company's name", "type": "string"},
            "price": {
                "description": "The company's stock price",
                "type": "number",
                "minimum": 0,
            },
            "ticker": {
                "description": "The company's ticker, if publicly traded",
                "type": "string",
            },
            "newField": {
                "description": "A new field",
                "type": "string",
            },
        },
        "required": ["name"],
        "additionalProperties": False,
    }

    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "outputs_schema_definition": output_schema_with_added_optional_field,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(f"/datasets/{dataset_id}")
    assert response.status_code == 200
    assert (
        response.json()["inputs_schema_definition"]
        == json_schema_with_less_requirements
    )
    assert (
        response.json()["outputs_schema_definition"]
        == output_schema_with_added_optional_field
    )

    json_schema_with_new_required_field = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "title": "Address",
        "description": "An address book entry",
        "type": "object",
        "properties": {
            "name": {"description": "The person's full name", "type": "string"},
            "age": {
                "description": "The person's age",
                "type": "integer",
                "minimum": 0,
            },
            "email": {
                "description": "The person's email address",
                "type": "string",
                "format": "email",
            },
            "address": {
                "description": "The person's physical address",
                "type": "object",
                "properties": {
                    "streetAddress": {
                        "description": "The street address",
                        "type": "string",
                    },
                    "city": {"description": "The city", "type": "string"},
                    "state": {"description": "The state", "type": "string"},
                    "postalCode": {
                        "description": "The postal code",
                        "type": "string",
                        "pattern": "^[0-9]{5}(?:-[0-9]{4})?$",
                    },
                },
                "required": ["city", "state", "postalCode"],  # removed streetAddress
            },
            "phoneNumbers": {
                "description": "The person's phone numbers",
                "type": "array",
                "items": {
                    "type": "string",
                    "pattern": "^[2-9]{1}[0-9]{2}-[0-9]{3}-[0-9]{4}$",
                },
            },
            "newField": {
                "description": "A new field",
                "type": "string",
            },
        },
        "required": ["name", "email", "address", "newField"],
    }

    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "inputs_schema_definition": json_schema_with_new_required_field,
        },
    )
    assert response.status_code == 400
    assert len(response.json()["detail"]["non_conforming_examples"]) == 4

    assert john_doe_id in response.json()["detail"]["non_conforming_examples"]
    assert jane_doe_id in response.json()["detail"]["non_conforming_examples"]
    assert new_example_id in response.json()["detail"]["non_conforming_examples"]
    assert new_example_id_2 in response.json()["detail"]["non_conforming_examples"]

    new_conforming_inputs = {
        "name": "John Doe",
        "age": 30,
        "email": "<EMAIL>",
        "address": {
            "city": "Springfield",
            "state": "IL",
            "postalCode": "62701",
        },
        "newField": "new field",
    }

    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "inputs_schema_definition": json_schema_with_new_required_field,
            "patch_examples": {
                john_doe_id: {
                    "inputs": new_conforming_inputs,
                },
                jane_doe_id: {
                    "inputs": new_conforming_inputs,
                },
                new_example_id: {
                    "inputs": new_conforming_inputs,
                },
            },
        },
    )
    assert response.status_code == 400

    response = await http_tenant_one.patch(
        f"/datasets/{dataset_id}",
        json={
            "inputs_schema_definition": json_schema_with_new_required_field,
            "patch_examples": {
                john_doe_id: {
                    "inputs": new_conforming_inputs,
                },
                jane_doe_id: {
                    "inputs": new_conforming_inputs,
                },
                new_example_id: {
                    "inputs": new_conforming_inputs,
                },
                new_example_id_2: {
                    "inputs": new_conforming_inputs,
                },
            },
        },
    )
    assert response.status_code == 200
    assert response.headers["X-Updated-Examples-Count"] == "4"

    response = await http_tenant_one.get(f"/datasets/{dataset_id}")
    assert response.status_code == 200
    assert (
        response.json()["inputs_schema_definition"]
        == json_schema_with_new_required_field
    )
    assert (
        response.json()["outputs_schema_definition"]
        == output_schema_with_added_optional_field
    )

    response = await http_tenant_one.get(f"/examples/{john_doe_id}")
    assert response.status_code == 200
    assert response.json()["inputs"] == new_conforming_inputs
    assert response.json()["outputs"] == example_with_all_fields["outputs"]

    response = await http_tenant_one.get(f"/examples/{jane_doe_id}")
    assert response.status_code == 200
    assert response.json()["inputs"] == new_conforming_inputs
    assert response.json()["outputs"] == {
        "name": "Company Inc.",
        "price": 100.00,
    }

    response = await http_tenant_one.get(f"/examples/{new_example_id}")
    assert response.status_code == 200
    assert response.json()["inputs"] == new_conforming_inputs
    assert response.json()["outputs"] == example_with_all_fields["outputs"]

    response = await http_tenant_one.get(f"/examples/{new_example_id_2}")
    assert response.status_code == 200
    assert response.json()["inputs"] == new_conforming_inputs
    assert response.json()["outputs"] == example_with_all_fields["outputs"]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_combined_errors_on_example_validation(
    http_tenant_one: AsyncClient,
) -> None:
    # Test combined error on validation for chat dataset
    chat_dataset_id = await create_dataset(
        http_tenant_one, "chat-dataset", schemas.DataType.chat
    )

    example_with_no_inner_input_key = {
        "inputs": {"not_input": "Hello, world!"},
        "outputs": {"not_output": "Hello, world!"},
        "dataset_id": chat_dataset_id,
    }
    response = await http_tenant_one.post(
        "/examples", json=example_with_no_inner_input_key
    )
    assert response.status_code == 400
    assert response.json()["detail"]["message"] == "Failed example validation"
    assert response.json()["detail"]["input_errors"] == [
        "Chat Example inputs must have a single key 'input' or two keys 'input' and 'functions'. Got keys: dict_keys(['not_input'])"
    ]
    assert response.json()["detail"]["output_errors"] == [
        "Chat Example outputs must have a single key 'output'."
    ]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_use_source_run_io(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    use_multipart: bool,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        session_id = await create_session(aclient)

        dataset_id = await create_dataset(aclient)
        dataset_id_2 = await create_dataset(aclient)

        run_id_1 = uuid4()
        run_id_2 = uuid4()
        run_id_3 = uuid4()

        post = [
            {
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "THESE ARE THE INPUTS FROM THE FIRST RUN"},
                "outputs": {"output": "THESE ARE THE OUTPUTS FROM MY FIRST RUN"},
                "session_id": session_id,
                "run_type": "chain",
                "id": str(run_id_1),
                "trace_id": str(run_id_1),
                "dotted_order": f"20230505T051324571809Z{run_id_1}",
            },
            {
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "THESE ARE THE INPUTS FROM THE SECOND RUN"},
                "outputs": {"output": "THESE ARE THE OUTPUTS FROM MY SECOND RUN"},
                "session_id": session_id,
                "run_type": "chain",
                "id": str(run_id_2),
                "trace_id": str(run_id_2),
                "dotted_order": f"20230505T051324571809Z{run_id_2}",
            },
            {
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "THESE ARE THE INPUTS FROM THE THIRD RUN"},
                "outputs": {"output": "THESE ARE THE OUTPUTS FROM MY THIRD RUN"},
                "session_id": session_id,
                "run_type": "chain",
                "id": str(run_id_3),
                "trace_id": str(run_id_3),
                "dotted_order": f"20230505T051324571809Z{run_id_3}",
            },
        ]

        attachments = {
            (str(run_id_1), "fish"): ("application/octet-stream", b"blub"),
            (str(run_id_1), "fox"): (
                "application/octet-stream",
                b"what does the fox say?",
            ),
        }

        await post_runs("/runs/multipart", aclient, post, [], attachments)
        await wait_for_runs_to_end(authed_client.auth, run_id_1, run_id_2, run_id_3)

        async def _example_1_assertions(example, verify_attachment_urls=True):
            assert example["inputs"] == {
                "input": "THESE ARE THE INPUTS FROM THE FIRST RUN"
            }
            assert example["outputs"] == {
                "output": "THESE ARE THE OUTPUTS FROM MY FIRST RUN"
            }
            assert example["source_run_id"] == str(run_id_1)
            if verify_attachment_urls:
                attachment_urls = example["attachment_urls"]
                assert len(attachment_urls) == 2
                presigned_url = attachment_urls["attachment.fish"]["presigned_url"]
                assert presigned_url.startswith("http")
                storage_url = attachment_urls["attachment.fish"]["storage_url"]
                result = await internal_platform_request(
                    "GET",
                    "/internal/download",
                    params={"path": storage_url},
                )
                assert result.body == b"blub"

                presigned_url = attachment_urls["attachment.fox"]["presigned_url"]
                assert presigned_url.startswith("http")
                storage_url = attachment_urls["attachment.fox"]["storage_url"]
                result = await internal_platform_request(
                    "GET",
                    "/internal/download",
                    params={"path": storage_url},
                )
                assert result.body == b"what does the fox say?"
            else:
                assert example.get("attachment_urls") is None

        def _example_2_assertions(example):
            assert example["inputs"] == {
                "input": "THESE ARE THE INPUTS FROM THE SECOND RUN"
            }
            assert example["outputs"] == {
                "output": "THESE ARE THE OUTPUTS FROM MY SECOND RUN"
            }
            assert example["source_run_id"] == str(run_id_2)
            assert example.get("attachment_urls") is None

        def _example_3_assertions(example):
            assert example["inputs"] == {
                "input": "THESE ARE THE INPUTS FROM THE THIRD RUN"
            }
            assert example["outputs"] == {
                "output": "THESE ARE THE OUTPUTS FROM MY THIRD RUN"
            }
            assert example["source_run_id"] == str(run_id_3)

        example_1_req_body = {
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "source_run_id": str(run_id_1),
            "dataset_id": str(dataset_id),
            "use_source_run_io": True,
        }

        validate_response = await aclient.post(
            "/examples/validate",
            json=example_1_req_body,
        )
        assert validate_response.status_code == 200

        response = await aclient.post(
            "/examples",
            json=example_1_req_body,
        )
        assert response.status_code == 200
        example_id = response.json()["id"]

        assert validate_response.json()["inputs"] == response.json()["inputs"]
        assert validate_response.json()["outputs"] == response.json()["outputs"]

        # try fetching through read path
        response = await aclient.get(f"/examples/{example_id}")
        assert response.status_code == 200
        example = response.json()
        _example_1_assertions(example)

        # try fetching through read path with asof
        response = await aclient.get(
            f"/examples/{example_id}?asof=2023-05-05T05:13:24.571809"
        )
        assert response.status_code == 200
        example = response.json()
        await _example_1_assertions(example)

        example_2_req_body = {
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "source_run_id": str(run_id_2),
            "dataset_id": str(dataset_id),
            "use_source_run_io": True,
        }

        validate_response = await aclient.post(
            "/examples/validate",
            json=example_2_req_body,
        )
        assert validate_response.status_code == 200

        response = await aclient.post(
            "/examples",
            json=example_2_req_body,
        )
        assert response.status_code == 200
        example_id_2 = response.json()["id"]

        assert validate_response.json()["inputs"] == response.json()["inputs"]
        assert validate_response.json()["outputs"] == response.json()["outputs"]

        response = await aclient.get(f"/examples/{example_id_2}")
        assert response.status_code == 200
        example = response.json()
        _example_2_assertions(example)

        response = await aclient.get(f"/examples?id={example_id}")
        assert response.status_code == 200

        example = response.json()[0]
        await _example_1_assertions(example, verify_attachment_urls=False)

        response = await aclient.get(
            f"/examples?id={example_id}&select=attachment_urls&select=inputs&select=outputs&select=source_run_id"
        )
        assert response.status_code == 200

        example = response.json()[0]
        _example_1_assertions(example)

        examples: list = [
            {
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "source_run_id": str(run_id_1),
                "dataset_id": str(dataset_id_2),
                "metadata": {"number": "1"},
                "use_source_run_io": True,
            },
            {
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "source_run_id": str(run_id_2),
                "dataset_id": str(dataset_id_2),
                "metadata": {"number": "2"},
                "use_source_run_io": False,
            },
            {
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "source_run_id": str(run_id_3),
                "dataset_id": str(dataset_id_2),
                "metadata": {"number": "3"},
                "use_source_run_io": True,
            },
        ]
        response = await _post_examples(
            examples,
            aclient,
            str(dataset_id_2),
            use_multipart=use_multipart,
        )
        assert response.success(), response.json()

        response = await aclient.get(f"/examples?dataset={dataset_id_2}")
        assert response.status_code == 200
        examples = response.json()
        assert len(examples) == 3

        response = await aclient.get(f"/examples/count?dataset={dataset_id_2}")
        assert response.status_code == 200
        examples_count = response.json()
        assert examples_count == 3

        example_1 = next(
            example for example in examples if example["metadata"]["number"] == "1"
        )
        example_2 = next(
            example for example in examples if example["metadata"]["number"] == "2"
        )
        example_3 = next(
            example for example in examples if example["metadata"]["number"] == "3"
        )

        await _example_1_assertions(example_1, verify_attachment_urls=False)

        assert example_2["inputs"] == {
            "input": "How many people live in canada as of 2023?"
        }
        assert example_2["outputs"] == {"output": "39,566,248"}
        assert example_2["source_run_id"] == str(run_id_2)

        _example_3_assertions(example_3)


def llm_run_payload(session_id, llm_run_id):
    return {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "inputs": {"prompt": "a"},
        "outputs": {
            "generations": [
                {
                    "text": ")\n\n        self.__a = a\n        self.__b = b\n        self.__c = c\n\n    @property\n    def a(self):\n        return self.__a\n\n    @a.setter\n    def a(self, a):\n        if not self.__is_valid_triangle_side(a):\n            raise ValueError('Invalid value for side a: {}'.format(a))\n\n        self.__a = a\n\n    @property\n    def b(self):\n        return self.__b\n\n    @b.setter\n    def b(self, b):\n        if not self.__is_valid_triangle_side(b):\n            raise ValueError('Invalid value for side b: {}'.format(b))\n\n        self.__b = b\n\n    @property\n    def c(self):\n        return self.__c\n\n    @c.setter\n    def c(self, c):\n        if not self.__is_valid_triangle_side(c):\n            raise ValueError('Invalid value for side c: {}'.format(c))\n\n        self.__c = c\n\n    def __repr__(self):\n        return 'Triangle(a={}, b={}, c={}, area={})'.format(\n            self.a, self.b, self.c, self.area)\n\n    def __eq__(self, other):\n        return self.a == other.a and self.b == other",
                    "generationInfo": {"finishReason": "length"},
                }
            ]
        },
        "session_id": session_id,
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(llm_run_id),
    }


def llm_run_nested_generations_payload(session_id, llm_run_id):
    return {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "inputs": {"prompt": "a"},
        "outputs": {
            "generations": [
                {
                    "text": ")\n\n        self.__a = a\n        self.__b = b\n        self.__c = c\n\n    @property\n    def a(self):\n        return self.__a\n\n    @a.setter\n    def a(self, a):\n        if not self.__is_valid_triangle_side(a):\n            raise ValueError('Invalid value for side a: {}'.format(a))\n\n        self.__a = a\n\n    @property\n    def b(self):\n        return self.__b\n\n    @b.setter\n    def b(self, b):\n        if not self.__is_valid_triangle_side(b):\n            raise ValueError('Invalid value for side b: {}'.format(b))\n\n        self.__b = b\n\n    @property\n    def c(self):\n        return self.__c\n\n    @c.setter\n    def c(self, c):\n        if not self.__is_valid_triangle_side(c):\n            raise ValueError('Invalid value for side c: {}'.format(c))\n\n        self.__c = c\n\n    def __repr__(self):\n        return 'Triangle(a={}, b={}, c={}, area={})'.format(\n            self.a, self.b, self.c, self.area)\n\n    def __eq__(self, other):\n        return self.a == other.a and self.b == other",
                    "generationInfo": {"finishReason": "length"},
                }
            ]
        },
        "session_id": session_id,
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(llm_run_id),
    }


def llm_run_payload_2(session_id, llm_run_id):
    return {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "inputs": {"prompt": "a"},
        "outputs": None,
        "session_id": session_id,
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(llm_run_id),
    }


def chat_run_1_nested_generations_payload(session_id, chat_run_id):
    return {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {
            "invocation_params": {
                "model": "gpt-4o",
                "model_name": "gpt-4o",
                "stream": False,
                "n": 1,
                "temperature": 0.0,
                "_type": "openai-chat",
                "stop": None,
                "tools": [
                    {
                        "type": "function",
                        "function": {
                            "name": "eval",
                            "description": "Submit your evaluation for this run.",
                            "parameters": {
                                "type": "object",
                                "required": ["about_langchain"],
                                "properties": {
                                    "about_langchain": {
                                        "type": "boolean",
                                        "description": "Is the user input about LangChain? Or is it about other arbitrary information?",
                                    }
                                },
                            },
                        },
                    }
                ],
                "tool_choice": {"type": "function", "function": {"name": "eval"}},
            },
            "options": {"stop": None},
            "batch_size": 1,
            "metadata": {
                "run_id": "*****",
                "rule_id": "*****",
                "source_project_id": "*****",
                "ls_provider": "openai",
                "ls_model_name": "gpt-4o",
                "ls_model_type": "chat",
                "ls_temperature": 0.0,
                "system_fingerprint": "*******",
            },
        },
        "inputs": {
            "messages": [
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": "You are assessing whether a user's input is about LangChain. LangChain is a developer framework for building LLM applications. If the inputs seem to be about a developer framework (or LLMs) then it's likely about LangChain. \n\nHere is the data:\n[BEGIN DATA]\n***\n[User Query]: [{'id': '0.09573682871622125', 'type': 'human', 'content': \"I've asked exact question!\\nHow from this: C:\\\\1og\\\\output_dir\\\\uis_log_audit_full_rules_202408010653\\\\uis_log_audit_full_rules_202408010653\\\\deus-command-control\\\\deus-command-control-1.gz\\\\deus-command-control\\\\deus-command-control-1.gz\\nby using os.path.split()\\nI can make this: C:\\\\1og\\\\output_dir\\\\uis_log_audit_full_rules_202408010653\\\\uis_log_audit_full_rules_202408010653\\\\deus-command-control\\\\deus-command-control-1.gz\\\\deus-command-control\\\\deus-command-control-1\"}]\n***\n[END DATA]",
                        "type": "human",
                    },
                }
            ]
        },
        "outputs": {
            "llm_output": {
                "model_name": "gpt-4o",
                "system_fingerprint": "*******",
            },
            "run": None,
            "generations": [
                [
                    {
                        "text": "",
                        "generation_info": {"finish_reason": "stop", "logprobs": None},
                        "type": "ChatGeneration",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain", "schema", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "",
                                "additional_kwargs": {
                                    "tool_calls": [
                                        {
                                            "id": "*******",
                                            "function": {
                                                "arguments": '{"about_langchain":False}',
                                                "name": "eval",
                                            },
                                            "type": "function",
                                        }
                                    ]
                                },
                                "response_metadata": {
                                    "token_usage": {
                                        "completion_tokens": 7,
                                        "prompt_tokens": 298,
                                        "total_tokens": 305,
                                    },
                                    "model_name": "gpt-4o",
                                    "system_fingerprint": "*******",
                                    "finish_reason": "stop",
                                    "logprobs": None,
                                },
                                "type": "ai",
                                "id": "run-*******",
                                "tool_calls": [
                                    {
                                        "name": "eval",
                                        "args": {"about_langchain": False},
                                        "id": "*******",
                                    }
                                ],
                                "usage_metadata": {
                                    "input_tokens": 298,
                                    "output_tokens": 7,
                                    "total_tokens": 305,
                                },
                                "invalid_tool_calls": [],
                            },
                        },
                    }
                ]
            ],
        },
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "session_id": session_id,
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(chat_run_id),
    }


def chat_run_2_payload(session_id, chat_run_id):
    return {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "inputs": {
            "messages": [
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": "Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question.\n\nChat History:\n[{'content': 'Compute embeddings for a given text using openai', 'role': 'human'}, {'content': 'Here is how you can compute embeddings for a given text using OpenAI in LangChain:\\n\\n• First, you need to install the required package:\\n\\n```bash\\npip install langchain-openai\\n```\\n\\n• Then, you need to set the OPENAI_API_KEY environment variable with your OpenAI API key.\\n\\n• Next, you can create an instance of the OpenAIEmbeddings class and use its `embed_query()` and `embed_documents()` methods to compute embeddings for a single text or multiple texts, respectively:\\n\\n```python\\nfrom langchain_openai import OpenAIEmbeddings\\n\\nembeddings = OpenAIEmbeddings()\\n\\n# Compute embedding for a single text\\ntext = \"What is deep learning?\"\\nsingle_vector = embeddings.embed_query(text)\\nprint(single_vector[:3])  # Print the first 3 elements of the embedding vector\\n\\n# Compute embeddings for multiple texts\\ntexts = [\\n    \"Data science involves extracting insights from data.\",\\n    \"Artificial intelligence is transforming various industries.\"\\n]\\nmultiple_vectors = embeddings.embed_documents(texts)\\nfor vector in multiple_vectors:\\n    print(vector[:3])  # Print the first 3 elements of each embedding vector\\n```\\n\\n[0]', 'role': 'ai'}]\nFollow Up Input: I have text . i want to convert into embeddings .\nStandalone Question:",
                        "type": "human",
                    },
                }
            ]
        },
        "extra": {
            "invocation_params": {
                "model": "claude-3-haiku-20240307",
                "max_tokens": 4096,
                "temperature": 0.0,
                "top_k": None,
                "top_p": None,
                "model_kwargs": {},
                "streaming": None,
                "max_retries": 2,
                "default_request_timeout": None,
                "_type": "anthropic-chat",
                "stop": None,
            },
            "options": {"stop": None},
            "batch_size": 1,
            "metadata": {
                "name": "Compute embeddings f...",
                "userId": "*****",
                "graph_id": "chat",
                "created_by": "system",
                "run_id": "*****",
                "thread_id": "*****",
                "x-api-key": "*****",
                "model_name": "anthropic_claude_3_haiku",
                "x-vercel-id": "*****",
                "assistant_id": "*****",
                "x-request-id": "*****",
                "x-forwarded-for": "******",
                "x-forwarded-proto": "https",
                "x-middleware-subrequest": "*****",
                "langgraph_step": 5,
                "langgraph_node": "retriever_with_chat_history",
                "langgraph_triggers": [
                    "branch:__start__:route_to_retriever:retriever_with_chat_history"
                ],
                "langgraph_task_idx": 0,
                "checkpoint_id": "*****",
                "checkpoint_ns": "retriever_with_chat_history",
                "ls_provider": "anthropic",
                "ls_model_name": "claude-3-haiku-20240307",
                "ls_model_type": "chat",
                "ls_temperature": 0.0,
                "ls_max_tokens": 4096,
                "LANGSMITH_LANGGRAPH_API_VARIANT": "cloud",
                "LANGSMITH_LANGGRAPH_API_REVISION": "*****",
                "LANGSMITH_HOST_PROJECT_ID": "*****",
                "LANGSMITH_AUTH_VERIFY_TENANT_ID": "True",
                "LANGSMITH_TENANT_ID": "*****",
                "LANGSMITH_AUTH_ENDPOINT": "https://api.smith.langchain.com",
            },
            "runtime": {
                "sdk": "langsmith-py",
                "sdk_version": "0.1.100",
                "library": "langsmith",
                "platform": "*****",
                "runtime": "python",
                "py_implementation": "CPython",
                "runtime_version": "3.11.9",
                "langchain_version": "0.2.14",
                "langchain_core_version": "0.2.33",
            },
        },
        "outputs": {
            "llm_output": None,
            "run": None,
            "generations": [
                {
                    "text": "How can I convert a given text into embeddings using OpenAI in LangChain?",
                    "generation_info": None,
                    "type": "ChatGeneration",
                    "message": {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "schema", "messages", "AIMessage"],
                        "kwargs": {
                            "content": "How can I convert a given text into embeddings using OpenAI in LangChain?",
                            "response_metadata": {
                                "stop_reason": "end_turn",
                                "stop_sequence": None,
                            },
                            "type": "ai",
                            "id": "run-*******",
                            "usage_metadata": {
                                "input_tokens": 431,
                                "output_tokens": 23,
                                "total_tokens": 454,
                            },
                            "tool_calls": [],
                            "invalid_tool_calls": [],
                        },
                    },
                }
            ],
        },
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "session_id": session_id,
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(chat_run_id),
    }


def chat_run_2_nested_generations_payload(session_id, chat_run_id):
    return {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "inputs": {
            "messages": [
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": "Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question.\n\nChat History:\n[{'content': 'Compute embeddings for a given text using openai', 'role': 'human'}, {'content': 'Here is how you can compute embeddings for a given text using OpenAI in LangChain:\\n\\n• First, you need to install the required package:\\n\\n```bash\\npip install langchain-openai\\n```\\n\\n• Then, you need to set the OPENAI_API_KEY environment variable with your OpenAI API key.\\n\\n• Next, you can create an instance of the OpenAIEmbeddings class and use its `embed_query()` and `embed_documents()` methods to compute embeddings for a single text or multiple texts, respectively:\\n\\n```python\\nfrom langchain_openai import OpenAIEmbeddings\\n\\nembeddings = OpenAIEmbeddings()\\n\\n# Compute embedding for a single text\\ntext = \"What is deep learning?\"\\nsingle_vector = embeddings.embed_query(text)\\nprint(single_vector[:3])  # Print the first 3 elements of the embedding vector\\n\\n# Compute embeddings for multiple texts\\ntexts = [\\n    \"Data science involves extracting insights from data.\",\\n    \"Artificial intelligence is transforming various industries.\"\\n]\\nmultiple_vectors = embeddings.embed_documents(texts)\\nfor vector in multiple_vectors:\\n    print(vector[:3])  # Print the first 3 elements of each embedding vector\\n```\\n\\n[0]', 'role': 'ai'}]\nFollow Up Input: I have text . i want to convert into embeddings .\nStandalone Question:",
                        "type": "human",
                    },
                }
            ]
        },
        "extra": {
            "invocation_params": {
                "model": "claude-3-haiku-20240307",
                "max_tokens": 4096,
                "temperature": 0.0,
                "top_k": None,
                "top_p": None,
                "model_kwargs": {},
                "streaming": None,
                "max_retries": 2,
                "default_request_timeout": None,
                "_type": "anthropic-chat",
                "stop": None,
            },
            "options": {"stop": None},
            "batch_size": 1,
            "metadata": {
                "name": "Compute embeddings f...",
                "userId": "*****",
                "graph_id": "chat",
                "created_by": "system",
                "run_id": "*****",
                "thread_id": "*****",
                "x-api-key": "*****",
                "model_name": "anthropic_claude_3_haiku",
                "x-vercel-id": "*****",
                "assistant_id": "*****",
                "x-request-id": "*****",
                "x-forwarded-for": "******",
                "x-forwarded-proto": "https",
                "x-middleware-subrequest": "*****",
                "langgraph_step": 5,
                "langgraph_node": "retriever_with_chat_history",
                "langgraph_triggers": [
                    "branch:__start__:route_to_retriever:retriever_with_chat_history"
                ],
                "langgraph_task_idx": 0,
                "checkpoint_id": "*****",
                "checkpoint_ns": "retriever_with_chat_history",
                "ls_provider": "anthropic",
                "ls_model_name": "claude-3-haiku-20240307",
                "ls_model_type": "chat",
                "ls_temperature": 0.0,
                "ls_max_tokens": 4096,
                "LANGSMITH_LANGGRAPH_API_VARIANT": "cloud",
                "LANGSMITH_LANGGRAPH_API_REVISION": "*****",
                "LANGSMITH_HOST_PROJECT_ID": "*****",
                "LANGSMITH_AUTH_VERIFY_TENANT_ID": "True",
                "LANGSMITH_TENANT_ID": "*****",
                "LANGSMITH_AUTH_ENDPOINT": "https://api.smith.langchain.com",
            },
            "runtime": {
                "sdk": "langsmith-py",
                "sdk_version": "0.1.100",
                "library": "langsmith",
                "platform": "*****",
                "runtime": "python",
                "py_implementation": "CPython",
                "runtime_version": "3.11.9",
                "langchain_version": "0.2.14",
                "langchain_core_version": "0.2.33",
            },
        },
        "outputs": {
            "llm_output": None,
            "run": None,
            "generations": [
                [
                    {
                        "text": "How can I convert a given text into embeddings using OpenAI in LangChain?",
                        "generation_info": None,
                        "type": "ChatGeneration",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain", "schema", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "How can I convert a given text into embeddings using OpenAI in LangChain?",
                                "response_metadata": {
                                    "stop_reason": "end_turn",
                                    "stop_sequence": None,
                                },
                                "type": "ai",
                                "id": "run-*******",
                                "usage_metadata": {
                                    "input_tokens": 431,
                                    "output_tokens": 23,
                                    "total_tokens": 454,
                                },
                                "tool_calls": [],
                                "invalid_tool_calls": [],
                            },
                        },
                    }
                ]
            ],
        },
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "session_id": session_id,
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(chat_run_id),
    }


def chat_run_3_payload(session_id, chat_run_id):
    return {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "inputs": {
            "messages": [
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "SystemMessage"],
                    "kwargs": {
                        "content": 'You are an expert programmer and problem-solver, tasked with answering any question about Langchain.\n\nGenerate a comprehensive and informative answer of 80 words or less for the given question based solely on the provided search results (URL and content). You must only use information from the provided search results. Use an unbiased and journalistic tone. Combine search results together into a coherent answer. Do not repeat text. Cite search results using [${number}] notation. Only cite the most relevant results that answer the question accurately. Place these citations at the end of the sentence or paragraph that reference them - do not put them all at the end. If different results refer to different entities within the same name, write separate answers for each entity.\n\nYou should use bullet points in your answer for readability. Put citations where they apply\nrather than putting them all at the end.\n\nIf there is nothing in the context relevant to the question at hand, just say "Hmm, I\'m not sure." Don\'t try to make up an answer.\n\nAnything between the following `context`  html blocks is retrieved from a knowledge bank, not part of the conversation with the user. \n\n<context>\n    <doc id=\'0\'>If you are inside this directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/rag-redis-multi-modal-multi-vector/playground](http://127.0.0.1:8000/rag-redis-multi-modal-multi-vector/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/rag-redis-multi-modal-multi-vector")\n```\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Input](#input)\n\n- [Storage](#storage)- [Redis](#redis)\n\n- [LLM](#llm)\n\n- [Environment Setup](#environment-setup)\n\n- [Usage](#usage)</doc>\n<doc id=\'1\'>🦜️🏓 LangServe | 🦜️🔗 LangChain\n\n[Skip to main content](#__docusaurus_skipToContent_fallback)LangChain 0.2 is out! Leave feedback on the v0.2 docs [here](https://github.com/langchain-ai/langchain/discussions/21716). You can view the v0.1 docs [here](/v0.1/docs/get_started/introduction/).# 🦜️🏓 LangServe\n\n[](https://github.com/langchain-ai/langserve/releases)\n[](https://pepy.tech/project/langserve)\n[](https://github.com/langchain-ai/langserve/issues)\n[](https://discord.com/channels/1038097195422978059/1170024642245832774)\n\n## Overview​\n\n[LangServe](https://github.com/langchain-ai/langserve) helps developers\ndeploy `LangChain` [runnables and chains](https://python.langchain.com/docs/expression_language/)\nas a REST API.\n\nThis library is integrated with [FastAPI](https://fastapi.tiangolo.com/) and\nuses [pydantic](https://docs.pydantic.dev/latest/) for data validation.\n\nIn addition, it provides a client that can be used to call into runnables deployed on a\nserver.\nA JavaScript client is available\nin [LangChain.js](https://js.langchain.com/docs/ecosystem/langserve).\n\n## Features​\n\n- Input and Output schemas automatically inferred from your LangChain object, and\nenforced on every API call, with rich error messages\n\n- API docs page with JSONSchema and Swagger (insert example link)\n\n- Efficient `/invoke`, `/batch` and `/stream` endpoints with support for many\nconcurrent requests on a single server\n\n- `/stream_log` endpoint for streaming all (or some) intermediate steps from your\nchain/agent\n\n- **new** as of 0.0.40, supports `/stream_events` to make it easier to stream without needing to parse the output of `/stream_log`.\n\n- Playground page at `/playground/` with streaming output and intermediate steps\n\n- Built-in (optional) tracing to [LangSmith](https://www.langchain.com/langsmith), just\nadd your API key (see [Instructions](https://docs.smith.langchain.com/))\n\n- All built with battle-tested open-source Python libraries like FastAPI, Pydantic,\nuvloop and asyncio.\n\n- Use the client SDK to call a LangServe server as if it was a Runnable running\nlocally (or call the HTTP API directly)\n\n- [LangServe Hub](https://github.com/langchain-ai/langchain/blob/master/templates/README.md)\n\n## ⚠️ LangGraph Compatibility​\n\nLangServe is designed to primarily deploy simple Runnables and wok with well-known primitives in langchain-core.\n\nIf you need a deployment option for LangGraph, you should instead be looking at [LangGraph Cloud (beta)](https://langchain-ai.github.io/langgraph/cloud/) which will\nbe better suited for deploying LangGraph applications.\n\n## Limitations​\n\n- Client callbacks are not yet supported for events that originate on the server\n\n- OpenAPI docs will not be generated when using Pydantic V2. Fast API does not\nsupport [mixing pydantic v1 and v2 namespaces](https://github.com/tiangolo/fastapi/issues/10360).\nSee section below for more details.\n\n## Security​\n\n- Vulnerability in Versions 0.0.13 - 0.0.15 -- playground endpoint allows accessing\narbitrary files on\nserver. [Resolved in 0.0.16](https://github.com/langchain-ai/langserve/pull/98).\n\n## Installation​\n\nFor both client and server:\n\n```bash\npip install "langserve[all]"\n```\n\nor `pip install "langserve[client]"` for client code,\nand `pip install "langserve[server]"` for server code.\n\n## LangChain CLI 🛠️​\n\nUse the `LangChain` CLI to bootstrap a `LangServe` project quickly.\n\nTo use the langchain CLI make sure that you have a recent version of `langchain-cli`\ninstalled. You can install it with `pip install -U langchain-cli`.\n\n## Setup​\n\n**Note**: We use `poetry` for dependency management. Please follow poetry [doc](https://python-poetry.org/docs/) to learn more about it.\n\n### 1. Create new app using langchain cli command​\n\n```sh\nlangchain app new my-app\n```\n\n### 2. Define the runnable in add_routes. Go to server.py and edit​\n\n```sh\nadd_routes(app. NotImplemented)\n```\n\n### 3. Use poetry to add 3rd party packages (e.g., langchain-openai, langchain-anthropic, langchain-mistral etc).​</doc>\n<doc id=\'2\'>```shell\nexport LANGCHAIN_TRACING_V2=true\nexport LANGCHAIN_API_KEY=<your-api-key>\nexport LANGCHAIN_PROJECT=<your-project>  # if not specified, defaults to "default"\n```\n\nIf you are inside this directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/rag-chroma-multi-modal-multi-vector/playground](http://127.0.0.1:8000/rag-chroma-multi-modal-multi-vector/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/rag-chroma-multi-modal-multi-vector")\n```\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Input](#input)\n\n- [Storage](#storage)\n\n- [LLM](#llm)\n\n- [Environment Setup](#environment-setup)\n\n- [Usage](#usage)</doc>\n<doc id=\'3\'>```shell\nexport LANGCHAIN_TRACING_V2=true\nexport LANGCHAIN_API_KEY=<your-api-key>\nexport LANGCHAIN_PROJECT=<your-project>  # if not specified, defaults to "default"\n```\n\nIf you are inside this directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/rag-multi-modal-mv-local/playground](http://127.0.0.1:8000/rag-multi-modal-mv-local/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/rag-multi-modal-mv-local")\n```\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Input](#input)\n\n- [Storage](#storage)\n\n- [LLM and Embedding Models](#llm-and-embedding-models)\n\n- [Usage](#usage)</doc>\n<doc id=\'4\'>If you are inside this directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/rag-supabase/playground](http://127.0.0.1:8000/rag-supabase/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/rag-supabase")\n```\n\nTODO: Add details about setting up the Supabase database\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Environment Setup](#environment-setup)\n\n- [Setup Supabase Database](#setup-supabase-database)\n\n- [Setup Environment Variables](#setup-environment-variables)\n\n- [Usage](#usage)</doc>\n<doc id=\'5\'>```shell\nexport LANGCHAIN_TRACING_V2=true\nexport LANGCHAIN_API_KEY=<your-api-key>\nexport LANGCHAIN_PROJECT=<your-project>  # if not specified, defaults to "default"\n```\n\nIf you are inside the top-level project directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/neo4j-semantic-ollama/playground](http://127.0.0.1:8000/neo4j-semantic-ollama/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/neo4j-semantic-ollama")\n```\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Tools](#tools)\n\n- [Environment Setup](#environment-setup)\n\n- [Populating with data](#populating-with-data)\n\n- [Usage](#usage)</doc> \n<context/>\n\nREMEMBER: If there is no relevant information within the context, just say "Hmm, I\'m not sure." Don\'t try to make up an answer. Anything between the preceding \'context\' html blocks is retrieved from a knowledge bank, not part of the conversation with the user.',
                        "type": "system",
                    },
                },
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": "how to I import add_routes from langserve in VS code",
                        "type": "human",
                    },
                },
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "AIMessage"],
                    "kwargs": {
                        "content": "To import `add_routes` from `langserve` in VS Code, you can use the following Python code snippet:\n\n```python\nfrom langserve import add_routes\n```\n\nMake sure you have the `langserve` package installed in your environment. You can then use this import in your FastAPI application to define routes.",
                        "type": "ai",
                        "tool_calls": [],
                        "invalid_tool_calls": [],
                    },
                },
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "HumanMessage"],
                    "kwargs": {"content": "How to install langserve", "type": "human"},
                },
            ]
        },
        "extra": {
            "invocation_params": {
                "model": "accounts/fireworks/models/mixtral-8x7b-instruct",
                "model_name": "accounts/fireworks/models/mixtral-8x7b-instruct",
                "stream": False,
                "n": 1,
                "temperature": 0.0,
                "stop": None,
                "max_tokens": 16384,
                "_type": "fireworks-chat",
            },
            "options": {"stop": None},
            "batch_size": 1,
            "metadata": {
                "name": "how to I import add_...",
                "userId": "****",
                "graph_id": "chat",
                "created_by": "system",
                "run_id": "****",
                "thread_id": "****",
                "x-api-key": "****",
                "model_name": "fireworks_mixtral",
                "x-vercel-id": "****",
                "assistant_id": "****",
                "x-request-id": "****",
                "x-forwarded-for": "****",
                "x-forwarded-proto": "https",
                "x-middleware-subrequest": "****",
                "langgraph_step": 6,
                "langgraph_node": "response_synthesizer",
                "langgraph_triggers": [
                    "branch:retriever_with_chat_history:route_to_response_synthesizer:response_synthesizer"
                ],
                "langgraph_task_idx": 0,
                "checkpoint_id": "****",
                "checkpoint_ns": "response_synthesizer",
                "ls_provider": "fireworks",
                "ls_model_name": "accounts/fireworks/models/mixtral-8x7b-instruct",
                "ls_model_type": "chat",
                "ls_temperature": 0.0,
                "ls_max_tokens": 16384,
                "LANGSMITH_LANGGRAPH_API_VARIANT": "cloud",
                "LANGSMITH_LANGGRAPH_API_REVISION": "****",
                "LANGSMITH_HOST_PROJECT_ID": "****",
                "LANGSMITH_AUTH_VERIFY_TENANT_ID": "True",
                "LANGSMITH_TENANT_ID": "****",
                "LANGSMITH_AUTH_ENDPOINT": "https://api.smith.langchain.com",
            },
            "runtime": {
                "sdk": "langsmith-py",
                "sdk_version": "0.1.100",
                "library": "langsmith",
                "platform": "****",
                "runtime": "python",
                "py_implementation": "CPython",
                "runtime_version": "3.11.9",
                "langchain_version": "0.2.14",
                "langchain_core_version": "0.2.33",
            },
        },
        "outputs": None,
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "session_id": session_id,
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(chat_run_id),
    }


llm_example_input_payload = {"input": "a"}
llm_example_output_payload = {
    "output": ")\n\n        self.__a = a\n        self.__b = b\n        self.__c = c\n\n    @property\n    def a(self):\n        return self.__a\n\n    @a.setter\n    def a(self, a):\n        if not self.__is_valid_triangle_side(a):\n            raise ValueError('Invalid value for side a: {}'.format(a))\n\n        self.__a = a\n\n    @property\n    def b(self):\n        return self.__b\n\n    @b.setter\n    def b(self, b):\n        if not self.__is_valid_triangle_side(b):\n            raise ValueError('Invalid value for side b: {}'.format(b))\n\n        self.__b = b\n\n    @property\n    def c(self):\n        return self.__c\n\n    @c.setter\n    def c(self, c):\n        if not self.__is_valid_triangle_side(c):\n            raise ValueError('Invalid value for side c: {}'.format(c))\n\n        self.__c = c\n\n    def __repr__(self):\n        return 'Triangle(a={}, b={}, c={}, area={})'.format(\n            self.a, self.b, self.c, self.area)\n\n    def __eq__(self, other):\n        return self.a == other.a and self.b == other"
}

llm_example_2_output_payload = {"output": ""}

chat_example_1_input_payload = {
    "input": [
        {
            "data": {
                "type": "human",
                "content": "You are assessing whether a user's input is about LangChain. LangChain is a developer framework for building LLM applications. If the inputs seem to be about a developer framework (or LLMs) then it's likely about LangChain. \n\nHere is the data:\n[BEGIN DATA]\n***\n[User Query]: [{'id': '0.09573682871622125', 'type': 'human', 'content': \"I've asked exact question!\\nHow from this: C:\\\\1og\\\\output_dir\\\\uis_log_audit_full_rules_202408010653\\\\uis_log_audit_full_rules_202408010653\\\\deus-command-control\\\\deus-command-control-1.gz\\\\deus-command-control\\\\deus-command-control-1.gz\\nby using os.path.split()\\nI can make this: C:\\\\1og\\\\output_dir\\\\uis_log_audit_full_rules_202408010653\\\\uis_log_audit_full_rules_202408010653\\\\deus-command-control\\\\deus-command-control-1.gz\\\\deus-command-control\\\\deus-command-control-1\"}]\n***\n[END DATA]",
            },
            "type": "human",
        }
    ],
    "functions": [
        {
            "name": "eval",
            "parameters": {
                "type": "object",
                "required": ["about_langchain"],
                "properties": {
                    "about_langchain": {
                        "type": "boolean",
                        "description": "Is the user input about LangChain? Or is it about other arbitrary information?",
                    }
                },
            },
            "description": "Submit your evaluation for this run.",
        }
    ],
}

chat_example_1_output_payload = {
    "output": {
        "data": {
            "id": "run-*******",
            "type": "ai",
            "content": "",
            "tool_calls": [
                {
                    "id": "*******",
                    "args": {"about_langchain": False},
                    "name": "eval",
                }
            ],
            "usage_metadata": {
                "input_tokens": 298,
                "total_tokens": 305,
                "output_tokens": 7,
            },
            "additional_kwargs": {
                "tool_calls": [
                    {
                        "id": "*******",
                        "type": "function",
                        "function": {
                            "name": "eval",
                            "arguments": '{"about_langchain":False}',
                        },
                    }
                ]
            },
            "response_metadata": {
                "logprobs": None,
                "model_name": "gpt-4o",
                "token_usage": {
                    "total_tokens": 305,
                    "prompt_tokens": 298,
                    "completion_tokens": 7,
                },
                "finish_reason": "stop",
                "system_fingerprint": "*******",
            },
            "invalid_tool_calls": [],
        },
        "type": "ai",
    }
}

chat_example_2_input_payload = {
    "input": [
        {
            "data": {
                "type": "human",
                "content": "Given the following conversation and a follow up question, rephrase the follow up question to be a standalone question.\n\nChat History:\n[{'content': 'Compute embeddings for a given text using openai', 'role': 'human'}, {'content': 'Here is how you can compute embeddings for a given text using OpenAI in LangChain:\\n\\n• First, you need to install the required package:\\n\\n```bash\\npip install langchain-openai\\n```\\n\\n• Then, you need to set the OPENAI_API_KEY environment variable with your OpenAI API key.\\n\\n• Next, you can create an instance of the OpenAIEmbeddings class and use its `embed_query()` and `embed_documents()` methods to compute embeddings for a single text or multiple texts, respectively:\\n\\n```python\\nfrom langchain_openai import OpenAIEmbeddings\\n\\nembeddings = OpenAIEmbeddings()\\n\\n# Compute embedding for a single text\\ntext = \"What is deep learning?\"\\nsingle_vector = embeddings.embed_query(text)\\nprint(single_vector[:3])  # Print the first 3 elements of the embedding vector\\n\\n# Compute embeddings for multiple texts\\ntexts = [\\n    \"Data science involves extracting insights from data.\",\\n    \"Artificial intelligence is transforming various industries.\"\\n]\\nmultiple_vectors = embeddings.embed_documents(texts)\\nfor vector in multiple_vectors:\\n    print(vector[:3])  # Print the first 3 elements of each embedding vector\\n```\\n\\n[0]', 'role': 'ai'}]\nFollow Up Input: I have text . i want to convert into embeddings .\nStandalone Question:",
            },
            "type": "human",
        }
    ]
}

chat_example_2_output_payload = {
    "output": {
        "data": {
            "id": "run-*******",
            "type": "ai",
            "content": "How can I convert a given text into embeddings using OpenAI in LangChain?",
            "tool_calls": [],
            "usage_metadata": {
                "input_tokens": 431,
                "total_tokens": 454,
                "output_tokens": 23,
            },
            "response_metadata": {"stop_reason": "end_turn", "stop_sequence": None},
            "invalid_tool_calls": [],
        },
        "type": "ai",
    }
}

chat_example_3_input_payload = {
    "input": [
        {
            "data": {
                "type": "system",
                "content": 'You are an expert programmer and problem-solver, tasked with answering any question about Langchain.\n\nGenerate a comprehensive and informative answer of 80 words or less for the given question based solely on the provided search results (URL and content). You must only use information from the provided search results. Use an unbiased and journalistic tone. Combine search results together into a coherent answer. Do not repeat text. Cite search results using [${number}] notation. Only cite the most relevant results that answer the question accurately. Place these citations at the end of the sentence or paragraph that reference them - do not put them all at the end. If different results refer to different entities within the same name, write separate answers for each entity.\n\nYou should use bullet points in your answer for readability. Put citations where they apply\nrather than putting them all at the end.\n\nIf there is nothing in the context relevant to the question at hand, just say "Hmm, I\'m not sure." Don\'t try to make up an answer.\n\nAnything between the following `context`  html blocks is retrieved from a knowledge bank, not part of the conversation with the user. \n\n<context>\n    <doc id=\'0\'>If you are inside this directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/rag-redis-multi-modal-multi-vector/playground](http://127.0.0.1:8000/rag-redis-multi-modal-multi-vector/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/rag-redis-multi-modal-multi-vector")\n```\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Input](#input)\n\n- [Storage](#storage)- [Redis](#redis)\n\n- [LLM](#llm)\n\n- [Environment Setup](#environment-setup)\n\n- [Usage](#usage)</doc>\n<doc id=\'1\'>🦜️🏓 LangServe | 🦜️🔗 LangChain\n\n[Skip to main content](#__docusaurus_skipToContent_fallback)LangChain 0.2 is out! Leave feedback on the v0.2 docs [here](https://github.com/langchain-ai/langchain/discussions/21716). You can view the v0.1 docs [here](/v0.1/docs/get_started/introduction/).# 🦜️🏓 LangServe\n\n[](https://github.com/langchain-ai/langserve/releases)\n[](https://pepy.tech/project/langserve)\n[](https://github.com/langchain-ai/langserve/issues)\n[](https://discord.com/channels/1038097195422978059/1170024642245832774)\n\n## Overview​\n\n[LangServe](https://github.com/langchain-ai/langserve) helps developers\ndeploy `LangChain` [runnables and chains](https://python.langchain.com/docs/expression_language/)\nas a REST API.\n\nThis library is integrated with [FastAPI](https://fastapi.tiangolo.com/) and\nuses [pydantic](https://docs.pydantic.dev/latest/) for data validation.\n\nIn addition, it provides a client that can be used to call into runnables deployed on a\nserver.\nA JavaScript client is available\nin [LangChain.js](https://js.langchain.com/docs/ecosystem/langserve).\n\n## Features​\n\n- Input and Output schemas automatically inferred from your LangChain object, and\nenforced on every API call, with rich error messages\n\n- API docs page with JSONSchema and Swagger (insert example link)\n\n- Efficient `/invoke`, `/batch` and `/stream` endpoints with support for many\nconcurrent requests on a single server\n\n- `/stream_log` endpoint for streaming all (or some) intermediate steps from your\nchain/agent\n\n- **new** as of 0.0.40, supports `/stream_events` to make it easier to stream without needing to parse the output of `/stream_log`.\n\n- Playground page at `/playground/` with streaming output and intermediate steps\n\n- Built-in (optional) tracing to [LangSmith](https://www.langchain.com/langsmith), just\nadd your API key (see [Instructions](https://docs.smith.langchain.com/))\n\n- All built with battle-tested open-source Python libraries like FastAPI, Pydantic,\nuvloop and asyncio.\n\n- Use the client SDK to call a LangServe server as if it was a Runnable running\nlocally (or call the HTTP API directly)\n\n- [LangServe Hub](https://github.com/langchain-ai/langchain/blob/master/templates/README.md)\n\n## ⚠️ LangGraph Compatibility​\n\nLangServe is designed to primarily deploy simple Runnables and wok with well-known primitives in langchain-core.\n\nIf you need a deployment option for LangGraph, you should instead be looking at [LangGraph Cloud (beta)](https://langchain-ai.github.io/langgraph/cloud/) which will\nbe better suited for deploying LangGraph applications.\n\n## Limitations​\n\n- Client callbacks are not yet supported for events that originate on the server\n\n- OpenAPI docs will not be generated when using Pydantic V2. Fast API does not\nsupport [mixing pydantic v1 and v2 namespaces](https://github.com/tiangolo/fastapi/issues/10360).\nSee section below for more details.\n\n## Security​\n\n- Vulnerability in Versions 0.0.13 - 0.0.15 -- playground endpoint allows accessing\narbitrary files on\nserver. [Resolved in 0.0.16](https://github.com/langchain-ai/langserve/pull/98).\n\n## Installation​\n\nFor both client and server:\n\n```bash\npip install "langserve[all]"\n```\n\nor `pip install "langserve[client]"` for client code,\nand `pip install "langserve[server]"` for server code.\n\n## LangChain CLI 🛠️​\n\nUse the `LangChain` CLI to bootstrap a `LangServe` project quickly.\n\nTo use the langchain CLI make sure that you have a recent version of `langchain-cli`\ninstalled. You can install it with `pip install -U langchain-cli`.\n\n## Setup​\n\n**Note**: We use `poetry` for dependency management. Please follow poetry [doc](https://python-poetry.org/docs/) to learn more about it.\n\n### 1. Create new app using langchain cli command​\n\n```sh\nlangchain app new my-app\n```\n\n### 2. Define the runnable in add_routes. Go to server.py and edit​\n\n```sh\nadd_routes(app. NotImplemented)\n```\n\n### 3. Use poetry to add 3rd party packages (e.g., langchain-openai, langchain-anthropic, langchain-mistral etc).​</doc>\n<doc id=\'2\'>```shell\nexport LANGCHAIN_TRACING_V2=true\nexport LANGCHAIN_API_KEY=<your-api-key>\nexport LANGCHAIN_PROJECT=<your-project>  # if not specified, defaults to "default"\n```\n\nIf you are inside this directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/rag-chroma-multi-modal-multi-vector/playground](http://127.0.0.1:8000/rag-chroma-multi-modal-multi-vector/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/rag-chroma-multi-modal-multi-vector")\n```\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Input](#input)\n\n- [Storage](#storage)\n\n- [LLM](#llm)\n\n- [Environment Setup](#environment-setup)\n\n- [Usage](#usage)</doc>\n<doc id=\'3\'>```shell\nexport LANGCHAIN_TRACING_V2=true\nexport LANGCHAIN_API_KEY=<your-api-key>\nexport LANGCHAIN_PROJECT=<your-project>  # if not specified, defaults to "default"\n```\n\nIf you are inside this directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/rag-multi-modal-mv-local/playground](http://127.0.0.1:8000/rag-multi-modal-mv-local/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/rag-multi-modal-mv-local")\n```\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Input](#input)\n\n- [Storage](#storage)\n\n- [LLM and Embedding Models](#llm-and-embedding-models)\n\n- [Usage](#usage)</doc>\n<doc id=\'4\'>If you are inside this directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/rag-supabase/playground](http://127.0.0.1:8000/rag-supabase/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/rag-supabase")\n```\n\nTODO: Add details about setting up the Supabase database\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Environment Setup](#environment-setup)\n\n- [Setup Supabase Database](#setup-supabase-database)\n\n- [Setup Environment Variables](#setup-environment-variables)\n\n- [Usage](#usage)</doc>\n<doc id=\'5\'>```shell\nexport LANGCHAIN_TRACING_V2=true\nexport LANGCHAIN_API_KEY=<your-api-key>\nexport LANGCHAIN_PROJECT=<your-project>  # if not specified, defaults to "default"\n```\n\nIf you are inside the top-level project directory, then you can spin up a LangServe instance directly by:\n\n```shell\nlangchain serve\n```\n\nThis will start the FastAPI app with a server is running locally at\n[http://localhost:8000](http://localhost:8000)\n\nWe can see all templates at [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs)\nWe can access the playground at [http://127.0.0.1:8000/neo4j-semantic-ollama/playground](http://127.0.0.1:8000/neo4j-semantic-ollama/playground) \n\nWe can access the template from code with:\n\n```python\nfrom langserve.client import RemoteRunnable\n\nrunnable = RemoteRunnable("http://localhost:8000/neo4j-semantic-ollama")\n```\n\n#### Was this page helpful?\n\n#### You can also leave detailed feedback on GitHub.\n\n- [Tools](#tools)\n\n- [Environment Setup](#environment-setup)\n\n- [Populating with data](#populating-with-data)\n\n- [Usage](#usage)</doc> \n<context/>\n\nREMEMBER: If there is no relevant information within the context, just say "Hmm, I\'m not sure." Don\'t try to make up an answer. Anything between the preceding \'context\' html blocks is retrieved from a knowledge bank, not part of the conversation with the user.',
            },
            "type": "system",
        },
        {
            "data": {
                "type": "human",
                "content": "how to I import add_routes from langserve in VS code",
            },
            "type": "human",
        },
        {
            "data": {
                "type": "ai",
                "content": "To import `add_routes` from `langserve` in VS Code, you can use the following Python code snippet:\n\n```python\nfrom langserve import add_routes\n```\n\nMake sure you have the `langserve` package installed in your environment. You can then use this import in your FastAPI application to define routes.",
                "tool_calls": [],
                "invalid_tool_calls": [],
            },
            "type": "ai",
        },
        {
            "data": {"type": "human", "content": "How to install langserve"},
            "type": "human",
        },
    ]
}
chat_example_3_output_payload = {"output": {"data": {"content": ""}, "type": ""}}


@pytest.mark.flaky
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_use_source_run_io_chat_llm_bulk(
    fresh_tenant: tuple[AsyncClient, AuthInfo],
    use_multipart: bool,
) -> None:
    client, auth = fresh_tenant

    session_id = await create_session(client)
    session_id_2 = await create_session(client)
    session_id_3 = await create_session(client)

    dataset_id = await create_dataset(client, random_lower_string(), "llm")
    dataset_id_2 = await create_dataset(client, random_lower_string(), "chat")

    llm_run_id = uuid4()
    response = await client.post(
        "/runs",
        json=llm_run_payload(session_id, llm_run_id),
    )
    assert response.status_code == 202

    llm_run_id_nested = uuid4()
    response = await client.post(
        "/runs",
        json=llm_run_nested_generations_payload(session_id, llm_run_id_nested),
    )
    assert response.status_code == 202

    llm_run_id_2 = uuid4()
    response = await client.post(
        "/runs",
        json=llm_run_payload_2(session_id, llm_run_id_2),
    )
    assert response.status_code == 202

    chat_run_id_1 = uuid4()
    response = await client.post(
        "/runs",
        json=chat_run_1_payload(session_id_2, chat_run_id_1),
    )
    assert response.status_code == 202

    chat_run_id_nested_1 = uuid4()
    response = await client.post(
        "/runs",
        json=chat_run_1_nested_generations_payload(session_id_2, chat_run_id_nested_1),
    )
    assert response.status_code == 202

    chat_run_id_2 = uuid4()
    response = await client.post(
        "/runs",
        json=chat_run_2_payload(session_id_3, chat_run_id_2),
    )
    assert response.status_code == 202

    chat_run_id_nested_2 = uuid4()
    response = await client.post(
        "/runs",
        json=chat_run_2_nested_generations_payload(session_id_3, chat_run_id_nested_2),
    )
    assert response.status_code == 202

    chat_run_id_3 = uuid4()
    response = await client.post(
        "/runs",
        json=chat_run_3_payload(session_id_3, chat_run_id_3),
    )
    assert response.status_code == 202
    await wait_for_runs_to_end(
        auth,
        llm_run_id,
        llm_run_id_nested,
        llm_run_id_2,
        chat_run_id_1,
        chat_run_id_nested_1,
        chat_run_id_2,
        chat_run_id_nested_2,
        chat_run_id_3,
    )

    examples: list = [
        {
            "source_run_id": str(llm_run_id),
            "dataset_id": str(dataset_id),
            "use_source_run_io": True,
        },
        {
            "source_run_id": str(llm_run_id_2),
            "dataset_id": str(dataset_id),
            "use_source_run_io": True,
        },
    ]
    response = await _post_examples(
        examples,
        client,
        str(dataset_id),
        use_multipart=use_multipart,
    )
    assert response.success()

    example_ids = (
        response.json().get("example_ids")
        if isinstance(response.json(), dict)
        else [e["id"] for e in response.json()]
    )

    response = await client.get(f"/examples?id={example_ids[0]}")
    assert response.status_code == 200

    example = response.json()[0]
    assert example["inputs"] == llm_example_input_payload, f"Example: {example}"
    assert example["outputs"] == llm_example_output_payload, f"Example: {example}"
    assert example["source_run_id"] == str(llm_run_id)

    response = await client.get(f"/examples?id={example_ids[1]}")
    assert response.status_code == 200

    example = response.json()[0]
    assert example["inputs"] == llm_example_input_payload
    assert example["outputs"] == llm_example_2_output_payload
    assert example["source_run_id"] == str(llm_run_id_2)

    examples = [
        {
            "source_run_id": str(chat_run_id_1),
            "dataset_id": str(dataset_id_2),
            "metadata": {"number": "2"},
            "use_source_run_io": True,
        },
        {
            "source_run_id": str(chat_run_id_2),
            "dataset_id": str(dataset_id_2),
            "metadata": {"number": "3"},
            "use_source_run_io": True,
        },
        {
            "source_run_id": str(chat_run_id_3),
            "dataset_id": str(dataset_id_2),
            "metadata": {"number": "4"},
            "use_source_run_io": True,
        },
    ]
    response = await _post_examples(
        examples,
        client,
        str(dataset_id_2),
        use_multipart=use_multipart,
    )
    assert response.success()

    response = await client.get(f"/examples?dataset={dataset_id_2}")
    assert response.status_code == 200
    examples = response.json()
    assert len(examples) == 3

    response = await client.get(f"/examples/count?dataset={dataset_id_2}")
    assert response.status_code == 200
    examples_count = response.json()
    assert examples_count == 3

    example_1 = next(
        example for example in examples if example["metadata"]["number"] == "2"
    )
    example_2 = next(
        example for example in examples if example["metadata"]["number"] == "3"
    )
    example_3 = next(
        example for example in examples if example["metadata"]["number"] == "4"
    )

    assert example_1["inputs"] == chat_example_1_input_payload
    assert example_1["outputs"] == chat_example_1_output_payload
    assert example_1["source_run_id"] == str(chat_run_id_1)

    assert example_2["inputs"] == chat_example_2_input_payload
    assert example_2["outputs"] == chat_example_2_output_payload
    assert example_2["source_run_id"] == str(chat_run_id_2)

    assert example_3["inputs"] == chat_example_3_input_payload
    assert example_3["outputs"] == chat_example_3_output_payload
    assert example_3["source_run_id"] == str(chat_run_id_3)


async def test_use_source_run_io_chat_llm(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    session_id = await create_session(http_tenant_one)
    session_id_2 = await create_session(http_tenant_one)
    session_id_3 = await create_session(http_tenant_one)

    dataset_id = await create_dataset(http_tenant_one, random_lower_string(), "llm")
    dataset_id_2 = await create_dataset(http_tenant_one, random_lower_string(), "chat")

    llm_run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json=llm_run_payload(session_id, llm_run_id),
    )
    assert response.status_code == 202

    llm_run_id_2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json=llm_run_payload_2(session_id, llm_run_id_2),
    )

    chat_run_id_1 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json=chat_run_1_payload(session_id_2, chat_run_id_1),
    )
    assert response.status_code == 202

    chat_run_id_2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json=chat_run_2_payload(session_id_3, chat_run_id_2),
    )
    assert response.status_code == 202

    chat_run_id_3 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json=chat_run_3_payload(session_id_3, chat_run_id_3),
    )
    await wait_for_runs_to_end(
        auth_tenant_one,
        llm_run_id,
        llm_run_id_2,
        chat_run_id_1,
        chat_run_id_2,
        chat_run_id_3,
    )

    response = await http_tenant_one.post(
        "/examples",
        json={
            "source_run_id": str(llm_run_id),
            "dataset_id": str(dataset_id),
            "use_source_run_io": True,
        },
    )
    assert response.status_code == 200
    example_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "source_run_id": str(llm_run_id_2),
            "dataset_id": str(dataset_id),
            "use_source_run_io": True,
        },
    )
    assert response.status_code == 200
    example_id_2 = response.json()["id"]

    response = await http_tenant_one.get(f"/examples?id={example_id}")
    assert response.status_code == 200

    example = response.json()[0]
    assert example["inputs"] == llm_example_input_payload
    assert example["outputs"] == llm_example_output_payload
    assert example["source_run_id"] == str(llm_run_id)

    response = await http_tenant_one.get(f"/examples?id={example_id_2}")
    assert response.status_code == 200

    example = response.json()[0]
    assert example["inputs"] == llm_example_input_payload
    assert example["outputs"] == llm_example_2_output_payload
    assert example["source_run_id"] == str(llm_run_id_2)

    response = await http_tenant_one.post(
        "/examples",
        json={
            "source_run_id": str(chat_run_id_1),
            "dataset_id": str(dataset_id_2),
            "metadata": {"number": "2"},
            "use_source_run_io": True,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/examples",
        json={
            "source_run_id": str(chat_run_id_2),
            "dataset_id": str(dataset_id_2),
            "metadata": {"number": "3"},
            "use_source_run_io": True,
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/examples",
        json={
            "source_run_id": str(chat_run_id_3),
            "dataset_id": str(dataset_id_2),
            "metadata": {"number": "4"},
            "use_source_run_io": True,
        },
    )

    response = await http_tenant_one.get(f"/examples?dataset={dataset_id_2}")
    assert response.status_code == 200
    examples = response.json()
    assert len(examples) == 3

    response = await http_tenant_one.get(f"/examples/count?dataset={dataset_id_2}")
    assert response.status_code == 200
    examples_count = response.json()
    assert examples_count == 3

    example_1 = next(
        example for example in examples if example["metadata"]["number"] == "2"
    )
    example_2 = next(
        example for example in examples if example["metadata"]["number"] == "3"
    )
    example_3 = next(
        example for example in examples if example["metadata"]["number"] == "4"
    )

    assert example_1["inputs"] == chat_example_1_input_payload
    assert example_1["outputs"] == chat_example_1_output_payload
    assert example_1["source_run_id"] == str(chat_run_id_1)

    assert example_2["inputs"] == chat_example_2_input_payload
    assert example_2["outputs"] == chat_example_2_output_payload
    assert example_2["source_run_id"] == str(chat_run_id_2)

    assert example_3["inputs"] == chat_example_3_input_payload
    assert example_3["outputs"] == chat_example_3_output_payload
    assert example_3["source_run_id"] == str(chat_run_id_3)


@asynccontextmanager
async def messages_kv_enabled_tenant_client(
    db_asyncpg: asyncpg.Connection, use_api_key: bool
) -> AsyncGenerator[FreshTenantClient, Any]:
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        await db_asyncpg.execute(
            """
            UPDATE organizations
            SET config = config || '{"kv_dataset_message_support": true}'
            WHERE id = $1
            """,
            authed_client.auth.organization_id,
        )
        yield authed_client


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_chat_input_schema_in_kv_dataset(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with messages_kv_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client

        dataset = await create_chat_preset_dataset(
            "Chat Transform Dataset " + str(uuid4()), langsmith_client
        )
        dataset_id = dataset["id"]

        @tool
        def get_weather(location: str, unit=Literal["celsius", "farenheit"]):
            """Get the current weather for a location"""
            return f"It is sunny in {location} and 35 degrees {unit}"

        # Add a valid example
        valid_example = {
            "inputs": {
                "messages": [
                    dumpd(HumanMessage(content="Hello, AI!")),
                    dumpd(AIMessage(content="Hello! How can I assist you today?")),
                    dumpd(HumanMessage(content="Tell me a joke.")),
                ],
                "tools": [convert_to_openai_tool(get_weather)],
                "temperature": 0.7,
                "max_tokens": 150,
            },
            "outputs": dumpd(
                AIMessage(
                    content="Why don't scientists trust atoms? Because they make up everything!"
                )
            ),
            "dataset_id": dataset_id,
        }
        resp = await langsmith_client.post("/examples", json=valid_example)
        assert resp.status_code == 200, resp.json()

        # Add an example with invalid role
        invalid_role_example = {
            "inputs": {
                "messages": [
                    {"content": "Hi there!", "role": "my-role"},  # Invalid role
                ],
                "temperature": 0.5,
            },
            "outputs": {"content": "Hello! How can I help you?", "role": "ai"},
            "dataset_id": dataset_id,
        }
        resp = await langsmith_client.post("/examples", json=invalid_role_example)
        assert resp.status_code == 400, resp.json()

        # Add an example with invalid tool def
        invalid_temp_example = {
            "inputs": {
                "messages": [
                    {"content": "What's the weather like?", "role": "human"},
                ],
                "tools": [
                    {
                        "name": "get_weather",
                        "definition": "Get the current weather for a location",  # definition instead of description
                        "parameters": {
                            "type": "object",
                            "properties": {"location": {"type": "string"}},
                            "required": ["location"],
                        },
                    }
                ],
            },
            "outputs": {
                "content": "I'm sorry, I don't have real-time weather information.",
                "role": "ai",
            },
            "dataset_id": dataset_id,
        }
        resp = await langsmith_client.post("/examples", json=invalid_temp_example)
        assert resp.status_code == 400, resp.json()

        # Verify the dataset contains only the valid example
        resp = await langsmith_client.get(f"/examples?dataset={dataset_id}")
        assert resp.status_code == 200, resp.json()
        examples = resp.json()
        assert len(examples) == 1, f"Expected 1 example, but found {len(examples)}"

        # Verify the content of the valid example
        assert examples[0]["inputs"] == {
            "tools": [
                {
                    "type": "function",
                    "function": {
                        "name": "get_weather",
                        "parameters": {
                            "type": "object",
                            "required": ["location"],
                            "properties": {"unit": {}, "location": {"type": "string"}},
                        },
                        "description": "Get the current weather for a location",
                    },
                }
            ],
            "messages": [
                {"role": "user", "content": "Hello, AI!"},
                {"role": "assistant", "content": "Hello! How can I assist you today?"},
                {"role": "user", "content": "Tell me a joke."},
            ],
        }

        # Verify example count
        resp = await langsmith_client.get(f"/examples/count?dataset={dataset_id}")
        assert resp.status_code == 200, resp.json()
        count = resp.json()
        assert count == 1, f"Expected 1 example, but count is {count}"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_messages_support_in_kv_dataset(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    use_multipart: bool,
):
    async with messages_kv_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client
        req_json: dict[str, Any] = {
            "name": "test dataset " + str(uuid4()),
            "description": "A test dataset",
            "data_type": "kv",
            "inputs_schema_definition": {
                "type": "object",
                "properties": {
                    "astring": {"type": "string", "description": "a fun string."},
                    "amessage": {"$ref": "/public/schemas/v1/message.json"},
                },
                "additionalProperties": False,
            },
            "transformations": [
                {
                    "path": ["inputs", "amessage"],
                    "transformation_type": "convert_to_openai_message",
                }
            ],
        }
        resp = await langsmith_client.post(
            "/datasets",
            json=req_json,
        )
        assert resp.status_code == 200
        dataset_id = resp.json()["id"]

        examples = [
            {
                "inputs": {
                    "astring": "hello",
                    "amessage": {"content": "hello", "role": "user"},
                },
                "outputs": {"content": "hello", "role": "assistant"},
                "dataset_id": dataset_id,
            }
        ]
        resp = await _post_examples(
            examples,
            langsmith_client,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        expected_status = 201 if use_multipart else 200
        assert resp.status_code == expected_status, resp.json()

        examples = [
            {
                "inputs": {
                    "astring": "hello",
                    "amessage": {
                        "content": "hello",
                        # This is an invalid role and should thus fail validation
                        "role": "jimmy",
                    },
                },
                "outputs": {"content": "hello", "role": "assistant"},
                "dataset_id": dataset_id,
            },
        ]

        resp = await _post_examples(
            examples,
            langsmith_client,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        assert resp.status_code == 422 if use_multipart else 400, resp.json()

        # langchain serialization
        examples = [
            {
                "inputs": {
                    "amessage": {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain_core", "messages", "HumanMessage"],
                        "kwargs": {
                            "content": '\n[AI]: <AGENT name="Upload_File_to_Worksheet" />: The file has been uploaded and processed successfully. The worksheet creation process is complete. If you need further actions or have additional requests, please let me know!\n[AI]: <AGENT name="CSV_Data_Row_Identifier" />:\n[TOOL]: updateFileImportMetadataDataRowIndex',
                            "additional_kwargs": {},
                            "response_metadata": {},
                            "id": "7bad65b1-a33c-4bba-a3de-228af6ae94ae",
                        },
                    }
                },
                "outputs": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain_core", "messages", "AIMessageChunk"],
                    "kwargs": {
                        "content": "[AI]: The file has been uploaded and processed successfully, and the worksheet creation process is complete. If you have further actions or requests, please let me know!",
                        "additional_kwargs": {
                            "agentId": "f3640f8d-35f5-438b-9582-635b8b70ad63"
                        },
                        "tool_call_chunks": [],
                        "id": "chatcmpl-BNT9pSfOU6xkvKLWxCpDB15Vqdhgw",
                        "tool_calls": [],
                        "invalid_tool_calls": [],
                    },
                },
                "dataset_id": dataset_id,
            }
        ]
        resp = await _post_examples(
            examples,
            langsmith_client,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        expected_status = 201 if use_multipart else 200
        assert resp.status_code == expected_status, resp.json()


async def test_fetch_examples_from_non_existent_dataset(
    http_tenant_one: AsyncClient,
) -> None:
    response = await http_tenant_one.get(f"/examples?dataset={uuid4()}")
    assert response.status_code == 404


@pytest.mark.parametrize("use_multipart", [True, False])
async def test_validate_example_no_schema(
    http_tenant_one: AsyncClient, use_multipart: bool
) -> None:
    """Test validating example against dataset with no schema."""
    # Create dataset with no schema
    dataset_response = await http_tenant_one.post(
        "/datasets/",
        json={
            "name": random_lower_string(),
            "description": "Test dataset",
            "data_type": "kv",
        },
    )
    assert dataset_response.status_code == 200
    dataset_id = dataset_response.json()["id"]

    # Test validation with simple key-value example
    response = await http_tenant_one.post(
        "/examples/validate/",
        json={
            "dataset_id": dataset_id,
            "inputs": {"any_key": "any value"},
            "outputs": {"result": "test"},
        },
    )
    assert response.status_code == 200
    # Test bulk validation with simple key-value example
    examples = [
        {
            "dataset_id": dataset_id,
            "inputs": {"any_key": "any value"},
            "outputs": {"result": "test"},
        }
    ]
    response = await _post_examples(
        examples,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )


@pytest.mark.parametrize("use_multipart", [True, False])
async def test_validate_example_array_schema(
    http_tenant_one: AsyncClient, use_multipart: bool
) -> None:
    """Test validating example against dataset with array schema."""
    # Create dataset with array schema
    dataset_response = await http_tenant_one.post(
        "/datasets/",
        json={
            "name": random_lower_string(),
            "description": "Dataset with array schema",
            "data_type": "kv",
            "inputs_schema_definition": {
                "type": "object",
                "properties": {
                    "numbers": {"type": "array", "items": {"type": "number"}}
                },
                "required": ["numbers"],
            },
            "outputs_schema_definition": {
                "type": "object",
                "properties": {"result": {"type": "string"}},
            },
        },
    )
    assert dataset_response.status_code == 200
    dataset_id = dataset_response.json()["id"]

    # Test valid array input
    response = await http_tenant_one.post(
        "/examples/validate/",
        json={
            "dataset_id": dataset_id,
            "inputs": {"numbers": [1, 2, 3.14]},
            "outputs": {"result": "test"},
        },
    )
    assert response.status_code == 200

    # Test invalid array input (string in number array)
    response = await http_tenant_one.post(
        "/examples/validate/",
        json={
            "dataset_id": dataset_id,
            "inputs": {"numbers": [1, "two", 3]},
            "outputs": {"result": "test"},
        },
    )
    assert response.status_code == 400

    # Test bulk
    # Test one invalid array input (string in number array)
    examples = [
        {
            "dataset_id": dataset_id,
            "inputs": {"numbers": [1, 2, 3.14]},
            "outputs": {"result": "test"},
        },
        {
            "dataset_id": dataset_id,
            "inputs": {"numbers": [1, "two", 3]},
            "outputs": {"result": "test"},
        },
    ]
    response = await _post_examples(
        examples,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )
    response = await http_tenant_one.post(
        "/examples/validate/",
        json={
            "dataset_id": dataset_id,
            "inputs": {"numbers": [1, "two", 3]},
            "outputs": {"result": "test"},
        },
    )
    assert response.status_code == 400


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_validate_example_chat_messages(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test validating example against dataset with chat message schema."""
    async with messages_kv_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client

        req_json: dict[str, Any] = {
            "name": "test dataset " + str(uuid4()),
            "description": "A test dataset",
            "data_type": "kv",
            "inputs_schema_definition": {
                "type": "object",
                "properties": {
                    "astring": {"type": "string", "description": "a fun string."},
                    "amessage": {"$ref": "/public/schemas/v1/message.json"},
                },
                "additionalProperties": False,
                "required": ["amessage"],
            },
        }
        resp = await langsmith_client.post(
            "/datasets",
            json=req_json,
        )
        assert resp.status_code == 200, resp.json()
        dataset_id = resp.json()["id"]

        # Test valid chat messages
        response = await langsmith_client.post(
            "/examples/validate/",
            json={
                "dataset_id": dataset_id,
                "inputs": {
                    "amessage": {
                        "role": "system",
                        "content": "You are a helpful assistant",
                    }
                },
                "outputs": {
                    "messages": [
                        {
                            "role": "assistant",
                            "content": "Hi! How can I help you today?",
                        }
                    ]
                },
            },
        )
        assert response.status_code == 200

        # Test invalid role in messages
        response = await langsmith_client.post(
            "/examples/validate/",
            json={
                "dataset_id": dataset_id,
                "inputs": {"messages": [{"role": "invalid_role", "content": "Hello!"}]},
                "outputs": {"messages": [{"role": "assistant", "content": "Hi!"}]},
            },
        )
        assert response.status_code == 400


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_validate_existing_example_chat_messages_with_transformations(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    use_multipart: bool,
) -> None:
    """Test validating example against dataset with chat message schema."""
    async with messages_kv_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client

        req_json: dict[str, Any] = {
            "name": "test dataset " + str(uuid4()),
            "description": "A test dataset",
            "data_type": "kv",
            "transformations": [
                {
                    "path": ["inputs", "amessage"],
                    "transformation_type": "convert_to_openai_message",
                }
            ],
            "inputs_schema_definition": {
                "type": "object",
                "properties": {
                    "astring": {"type": "string", "description": "a fun string."},
                    "amessage": {"$ref": "/public/schemas/v1/message.json"},
                },
                "additionalProperties": False,
                "required": ["amessage"],
            },
        }
        resp = await langsmith_client.post(
            "/datasets",
            json=req_json,
        )
        assert resp.status_code == 200, resp.json()
        dataset_id = resp.json()["id"]

        example = {
            "dataset_id": dataset_id,
            "inputs": {
                "amessage": {"role": "user", "content": "What's the weather?"},
            },
            "outputs": {
                "content": "Let me check the weather for you",
                "role": "assistant",
            },
        }

        resp = await _post_examples(
            [example],
            langsmith_client,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        expected_status_code = 201 if use_multipart else 200
        assert resp.status_code == expected_status_code, resp.json()
        print("example response here", resp.json())
        example_id = (
            resp.json()["example_ids"][0] if use_multipart else resp.json()[0]["id"]
        )
        assert example_id is not None

        # Test valid chat messages
        response = await langsmith_client.post(
            "/examples/validate/",
            json={
                "dataset_id": dataset_id,
                "id": example_id,
                "inputs": {
                    "amessage": {
                        "text": "The human asks how to retrieve multiple meeting minutes at once. The AI responds by outlining a general method: confirming access rights, logging into the system where the minutes are stored, using search functions to find the necessary minutes, utilizing any available bulk download features, and verifying the format of the downloaded files. The AI also offers to provide more specific advice if details about the system or method are given."
                    }
                },
                "outputs": {
                    "messages": [
                        {
                            "role": "assistant",
                            "content": "Hi! How can I help you today?",
                        }
                    ]
                },
            },
        )
        # Should 400 and not 500
        assert response.status_code == 400


async def test_validate_example_nonexistent_dataset(
    http_tenant_one: AsyncClient,
) -> None:
    """Test validating example against non-existent dataset."""
    response = await http_tenant_one.post(
        "/examples/validate/",
        json={
            "dataset_id": str(uuid4()),
            "inputs": {"test": "value"},
            "outputs": {"result": "value"},
        },
    )
    assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_update_example_split_and_metadata(
    http_tenant_one: AsyncClient,
) -> None:
    # Create a dataset with schemas
    input_schema_definition = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "type": "object",
        "properties": {"input": {"type": "string", "description": "The input text"}},
        "required": ["input"],
    }

    output_schema_definition = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "type": "object",
        "properties": {"output": {"type": "string", "description": "The output text"}},
        "required": ["output"],
    }

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
            "inputs_schema_definition": input_schema_definition,
            "outputs_schema_definition": output_schema_definition,
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    # Create an example
    example_data = {
        "inputs": {"input": "test input"},
        "outputs": {"output": "test output"},
        "dataset_id": str(dataset_id),
        "split": "train",
        "metadata": {"tag": "original"},
    }
    response = await http_tenant_one.post("/examples", json=example_data)
    assert response.status_code == 200
    example_id = response.json()["id"]

    # Verify initial state
    response = await http_tenant_one.get(f"/examples/{example_id}")
    assert response.status_code == 200
    assert response.json()["metadata"] == {
        "tag": "original",
        "dataset_split": ["train"],
    }
    assert response.json()["inputs"] == {"input": "test input"}
    assert response.json()["outputs"] == {"output": "test output"}

    # Update only split and metadata
    response = await http_tenant_one.patch(
        f"/examples/{example_id}",
        json={"split": "test", "metadata": {"tag": "updated", "new_field": "value"}},
    )
    assert response.status_code == 200

    # Verify the update
    response = await http_tenant_one.get(f"/examples/{example_id}")
    assert response.status_code == 200
    assert response.json()["metadata"] == {
        "tag": "updated",
        "new_field": "value",
        "dataset_split": ["test"],
    }
    assert response.json()["inputs"] == {"input": "test input"}
    assert response.json()["outputs"] == {"output": "test output"}


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_bulk_update_examples_split_and_metadata(
    http_tenant_one: AsyncClient, use_multipart: bool
) -> None:
    # Create a dataset with schemas
    input_schema_definition = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "type": "object",
        "properties": {"input": {"type": "string", "description": "The input text"}},
        "required": ["input"],
    }

    output_schema_definition = {
        "$schema": "http://json-schema.org/draft-07/schema#",
        "type": "object",
        "properties": {"output": {"type": "string", "description": "The output text"}},
        "required": ["output"],
    }

    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
            "inputs_schema_definition": input_schema_definition,
            "outputs_schema_definition": output_schema_definition,
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    example_ids = sorted([str(uuid4()), str(uuid4())])
    # Create multiple examples
    examples_data = [
        {
            "inputs": {"input": "test input 1"},
            "outputs": {"output": "test output 1"},
            "dataset_id": str(dataset_id),
            "split": "train",
            "metadata": {"tag": "original", "index": 1},
            "id": example_ids[0],
        },
        {
            "inputs": {"input": "test input 2"},
            "outputs": {"output": "test output 2"},
            "dataset_id": str(dataset_id),
            "split": "train",
            "metadata": {"tag": "original", "index": 2},
            "id": example_ids[1],
        },
    ]
    response = await _post_examples(
        examples_data,
        http_tenant_one,
        str(dataset_id),
        use_multipart=use_multipart,
    )
    assert response.success()

    # Verify initial state
    response = await http_tenant_one.get(
        f"/examples?id={example_ids[0]}&id={example_ids[1]}"
    )
    assert response.status_code == 200

    # Update splits and metadata in bulk
    bulk_updates = [
        {
            "id": example_ids[0],
            "split": "test",
            "metadata": {"tag": "updated", "new_field": "value1"},
        },
        {
            "id": example_ids[1],
            "split": "validation",
            "metadata": {"tag": "updated", "new_field": "value2"},
        },
    ]

    response = await _patch_examples(
        bulk_updates, http_tenant_one, str(dataset_id), use_multipart=use_multipart
    )
    assert response.success(), (response.status_code, response.json())
    if not use_multipart:
        assert response.json()["message"] == "2 examples updated"
    else:
        assert response.json()["count"] == 2

    # Verify the updates
    response = await http_tenant_one.get(
        f"/examples?id={example_ids[0]}&id={example_ids[1]}"
    )
    assert response.status_code == 200
    updated_examples = sorted(response.json(), key=lambda x: x["id"])
    expected_examples = [
        {
            "metadata": {
                "tag": "updated",
                "new_field": "value1",
                "dataset_split": ["test"],
            },
            "id": example_ids[0],
            "inputs": {"input": "test input 1"},
            "outputs": {"output": "test output 1"},
        },
        {
            "metadata": {
                "tag": "updated",
                "new_field": "value2",
                "dataset_split": ["validation"],
            },
            "id": example_ids[1],
            "inputs": {"input": "test input 2"},
            "outputs": {"output": "test output 2"},
        },
    ]

    assert len(expected_examples) == len(updated_examples)
    expectations = zip(expected_examples, updated_examples)
    for expected, actual in expectations:
        assert expected["metadata"] == actual["metadata"]
        assert expected["inputs"] == actual["inputs"]
        assert expected["outputs"] == actual["outputs"]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_transform_chat_example(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    use_multipart: bool,
) -> None:
    """Test transforming chat examples with various transformations."""
    async with messages_kv_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client

        dataset_json = await create_chat_preset_dataset(
            "Chat Transform Dataset " + str(uuid4()), langsmith_client
        )
        dataset_id = dataset_json["id"]

        example_with_tools = {
            "dataset_id": dataset_id,
            "inputs": {
                "messages": [
                    {"role": "user", "content": "What's the weather?"},
                ],
                "tools": [
                    {
                        "name": "GetDeliveryDate",
                        "description": "Get the delivery date for a customer's order. Call this whenever you need to know the delivery date, for example when a customer asks 'Where is my package'",
                        "input_schema": {
                            "properties": {
                                "order_id": {
                                    "description": "The customer's order ID.",
                                    "type": "string",
                                }
                            },
                            "required": ["order_id"],
                            "type": "object",
                        },
                    }
                ],
                "model": "gpt-4o",
                "stream": False,
                "tool_choice": None,
                "extra_headers": None,
                "extra_query": None,
                "extra_body": None,
            },
            "outputs": {
                "content": "Let me check the weather for you",
                "role": "assistant",
            },
        }

        resp = await langsmith_client.post(
            "/examples/validate", json=example_with_tools
        )
        assert resp.status_code == 200, resp.json()
        transformed = resp.json()

        resp = await _post_examples(
            [example_with_tools],
            langsmith_client,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        assert resp.status_code == 201 if use_multipart else 200, resp.json()

        assert set(transformed["inputs"].keys()) == set(["tools", "messages"])
        # Verify tool definition structure remains intact
        assert len(transformed["inputs"]["tools"]) == 1
        tool = transformed["inputs"]["tools"][0]
        assert tool == {
            "type": "function",
            "function": {
                "name": "GetDeliveryDate",
                "description": "Get the delivery date for a customer's order. Call this whenever you need to know the delivery date, for example when a customer asks 'Where is my package'",
                "parameters": {
                    "properties": {
                        "order_id": {
                            "description": "The customer's order ID.",
                            "type": "string",
                        }
                    },
                    "required": ["order_id"],
                    "type": "object",
                },
            },
        }

        # Test invalid path for transformation
        invalid_dataset_json = {
            **dataset_json,
            "transformations": [
                {
                    "path": ["inputs", "messages"],
                    "transformation_type": "convert_to_openai_tool",
                }
            ],
        }
        resp = await langsmith_client.post("/datasets", json=invalid_dataset_json)
        assert resp.status_code == 400, resp.json()


_CHAT_MESSAGE_TEST_CASES = [
    {
        "test_name": "langchain openai chatmodel run without tools",
        "inputs": {
            "messages": [
                [
                    {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "schema", "messages", "SystemMessage"],
                        "kwargs": {
                            "content": "You are a helpful assistant. Please respond to the user's request only based on the given context.",
                            "type": "system",
                        },
                    },
                    {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "schema", "messages", "HumanMessage"],
                        "kwargs": {
                            "content": "Question: Can you summarize this morning's meetings?\nContext: During this morning's meeting, we solved all world conflict.",
                            "type": "human",
                        },
                    },
                ]
            ]
        },
        "expected_inputs": {
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Please respond to the user's request only based on the given context.",
                },
                {
                    "role": "user",
                    "content": "Question: Can you summarize this morning's meetings?\nContext: During this morning's meeting, we solved all world conflict.",
                },
            ],
        },
        "outputs": {
            "generations": [
                [
                    {
                        "text": "In this morning's meeting, we successfully resolved all world conflicts.",
                        "generation_info": {"finish_reason": "stop", "logprobs": None},
                        "type": "ChatGeneration",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain", "schema", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "In this morning's meeting, we successfully resolved all world conflicts.",
                                "additional_kwargs": {"refusal": None},
                                "response_metadata": {
                                    "token_usage": {
                                        "completion_tokens": 13,
                                        "prompt_tokens": 54,
                                        "total_tokens": 67,
                                        "completion_tokens_details": {
                                            "reasoning_tokens": 0
                                        },
                                        "prompt_tokens_details": {"cached_tokens": 0},
                                    },
                                    "model_name": "gpt-4o-2024-08-06",
                                    "system_fingerprint": "fp_45cf54deae",
                                    "finish_reason": "stop",
                                    "logprobs": None,
                                },
                                "type": "ai",
                                "id": "run-ea314874-92ff-4eb5-8e18-277b07f4cef9-0",
                                "usage_metadata": {
                                    "input_tokens": 54,
                                    "output_tokens": 13,
                                    "total_tokens": 67,
                                    "input_token_details": {"cache_read": 0},
                                    "output_token_details": {"reasoning": 0},
                                },
                                "tool_calls": [],
                                "invalid_tool_calls": [],
                            },
                        },
                    }
                ]
            ],
            "llm_output": {
                "token_usage": {
                    "completion_tokens": 13,
                    "prompt_tokens": 54,
                    "total_tokens": 67,
                    "completion_tokens_details": {"reasoning_tokens": 0},
                    "prompt_tokens_details": {"cached_tokens": 0},
                },
                "model_name": "gpt-4o-2024-08-06",
                "system_fingerprint": "fp_45cf54deae",
            },
            "run": None,
            "type": "LLMResult",
        },
        "expected_outputs": {
            "message": {
                "content": "In this morning's meeting, we successfully resolved all world conflicts.",
                "role": "assistant",
            }
        },
    },
    {
        "test_name": "wrap openai run without tools",
        "inputs": {
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Please respond to the user's request only based on the given context.",
                },
                {
                    "role": "user",
                    "content": "Question: Can you summarize this morning's meetings?\nContext: During this morning's meeting, we solved all world conflict.",
                },
            ],
            "model": "gpt-4o",
            "stream": False,
            "tool_choice": None,
            "tools": None,
            "extra_headers": None,
            "extra_query": None,
            "extra_body": None,
        },
        "expected_inputs": {
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Please respond to the user's request only based on the given context.",
                },
                {
                    "role": "user",
                    "content": "Question: Can you summarize this morning's meetings?\nContext: During this morning's meeting, we solved all world conflict.",
                },
            ]
        },
        "outputs": {
            "output": {
                "id": "chatcmpl-AODP2zKBV04b2gY9znwk71YOJz7eY",
                "choices": [
                    {
                        "finish_reason": "stop",
                        "index": 0,
                        "message": {
                            "content": "This morning's meeting successfully addressed and resolved all world conflicts.",
                            "role": "assistant",
                        },
                    }
                ],
                "created": 1730334756,
                "model": "gpt-4o-2024-08-06",
                "object": "chat.completion",
                "system_fingerprint": "fp_45cf54deae",
                "usage": {
                    "completion_tokens": 12,
                    "prompt_tokens": 54,
                    "total_tokens": 66,
                    "completion_tokens_details": {"reasoning_tokens": 0},
                    "prompt_tokens_details": {"cached_tokens": 0},
                },
            }
        },
        "expected_outputs": {
            "message": {
                "content": "This morning's meeting successfully addressed and resolved all world conflicts.",
                "role": "assistant",
            }
        },
    },
    {
        "test_name": "langchain openai chat model streaming run from playground",
        "inputs": {
            "messages": [
                [
                    {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "schema", "messages", "SystemMessage"],
                        "kwargs": {"content": "You are a chatbot.", "type": "system"},
                    },
                    {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "schema", "messages", "HumanMessage"],
                        "kwargs": {"content": "who is a good boy?", "type": "human"},
                    },
                ]
            ]
        },
        "expected_inputs": {
            "messages": [
                {"role": "system", "content": "You are a chatbot."},
                {"role": "user", "content": "who is a good boy?"},
            ]
        },
        "outputs": {
            "generations": [
                {
                    "text": "All dogs are good boys!",
                    "generation_info": {
                        "finish_reason": "stop",
                        "model_name": "gpt-3.5-turbo-0125",
                    },
                    "type": "ChatGenerationChunk",
                    "message": {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "schema", "messages", "AIMessageChunk"],
                        "kwargs": {
                            "content": "All dogs are good boys!",
                            "response_metadata": {
                                "finish_reason": "stop",
                                "model_name": "gpt-3.5-turbo-0125",
                            },
                            "type": "AIMessageChunk",
                            "id": "run-0771318d-a768-4e2a-a64f-2d77c8ef5448",
                            "tool_calls": [],
                            "invalid_tool_calls": [],
                        },
                    },
                }
            ],
            "llm_output": None,
            "run": None,
            "type": "LLMResult",
        },
        "expected_outputs": {
            "message": {"content": "All dogs are good boys!", "role": "assistant"}
        },
    },
    {
        "test_name": "direct openai trace from customer",
        "inputs": {
            "messages": [
                {
                    "role": "system",
                    "content": 'You are part of a real-time AI dictation service called Aqua Voice. You will receive input from a dictation service and interpret that input to produce the output that the user wants.\n\nYour primary job is to output exactly what the user intends to commit to writing. Sometimes you should repeat the input verbatim and other times you should condense spoken phrases to written phrases, execute formatting commands, instructions to replace text, or self-corrections etc.\n\nBelow are some rules to guide you. Never break these.\n\nRULES:\n1. Ensure words make sense in context.\n2. If the speaker appears to correct themselves, only include the corrected version. They may not say that are correcting themselves, so be on the lookout for this contextually.\n3. If the speaker explicitly asks you to change something, do so, even if it doesn\'t make sense.\n4. If the speaker adds punctuation, include it.\n5. Be on the lookout for commands like "undo all of it" or "start over," in which case no text should be returned.\n6. NEVER add a period to the end of the transcript unless asked. Most likely, the speaker is not done talking and will continue the sentence.\n7. Make sure to process all of the input every time. When starting a new paragraph, include the previous paragraphs as well.\n8. Make sure to output the whole document every time. You will have to repeat yourself a lot, this is okay. Pay special attention to newlines to maintain consistency.\n9. Unless otherwise asked, format your output to fit the document type. Lists should generally be bullet points, emails should be formatted like emails, and paragraph breaks should usually be added every 1-2 sentences to break up prose. If the user starts with "Hi x" or "Hey x" or "x," assume they are writing an email.\n10. If you change something in the ouput, make sure you change other things too if they are related.\n11. Don\'t complete users sentences, as they are likley still taking.\n12. Always do your best to execute the user\'s commands.\n\nSettings:\nEXECUTE_LLM_PROMPTS = False (Do not attempt to answer questions)\n\nEXAMPLES:\nExplicit correction:\nInput: ```I was going to call him back this morning but I only just heard about the incident actually get rid of that last part```\nOutput: ```I was going to call him back this morning```\nInput: ```You might think, these are rapper boys. Change rapper to wrapper with a w.```\nOutput: ```You might think, these are wrapper boys```\n\nFill in:\nInput: ```Can you put something about how I\'m sorry I missed the party```\nOutput: ```I\'m sorry I wasn\'t able to make it to the party```\n\nImplicit correction:\nInput: ```Hey, Mike. Hey, Mike. This is Finn```\nOutput: ```Hey Mike. This is Finn```\nInput: ```Ladies and gentlemen, comma Welcome to the first welcome to my home```\nOutput: ```Ladies and gentlemen, welcome to my home```\nInput: ```The outcome of the operation was 500 no call it 1000 civilian casualties.```\nOutput: ```The outcome of the operation was 1000 civilian casualties.```\nInput: ```Our dishwasher is broken, our dishwasher stand is broken, and since we moved in our dishwasher upper drawer has been broken, and our washer seems to have an issue that is causing it to take a really long time```\nOutput: ```Since we moved in our dishwasher upper drawer has been broken, and our washer seems to have an issue that is causing it to take a really long time```\nInput: ```Remember the meeting is on Thursday Friday Friday```\nOutput: ```Remember the meeting is on Friday```\n\nCondensing spoken speech to written style:\nInput: ```So what we basically need to do is, we need to get the project done by the end of the week.```\nOutput: ```We need to get the project done by the end of the week```\nInput: ```Yeah, I mean. Let\'s see. We we. we have to make sure that all of the calls are covered. It\'s not going to be good if we have another panama situation. tripple covered.```\nOutput: ```We have to make sure that all of the calls are tripple covered. It\'s not going to be good if we have another Panama situation```\n\nGrammar:\nInput: ```We found this group very effective. Theres is sent a few things still to do on the project"```\nOutput: ```We found this group very effective. There are a few things still to do on the project```\n\nNo action needed\nInput: ```I didn\'t know what to do, so I called my mechanic and he told me to change it```\nOutput: ```I didn\'t know what to do, so I called my mechanic and he told me to change it```\nInput: ```He said, "Call my mom and ask her how she\'s doing"```\nOutput: ```He said, "Call my mom and ask her how she\'s doing"```\n\nPunctuation:\nInput: ```Hey, mark comma```\nOutput:```Hey Mark,```\nInput: ```Cars are being sold for less, comma```\nOutput: ```Cars are being sold for less,```\n\nSpelling out a word (prefer spelled-out characters to words):\nInput: ```Hello, this is Richard Wallenby, spelled Wallenby W-O-L-L-E-N-B-Y. No, Wallenby```\nOutput: ```Hello, this is Richard Wollenby```\nInput: ```My name is Augustus Dorkko, Dorkko spelled D-O-R-I-C-K-O. No change it Dorkko to Dorko. comma son of Julis Dorko and the only other roman fam```\nOutput: ```My name is Augustus Doricko, son of Julius Dorkico and the only other roman fam```\nInput: ```Hey Kathy spelled C-A-TH-I-E```\nOutput ```Hey Cathie```\n\nLists:\nInput: ```Shopping list: colon I need to buy eggs, milk, and cheese. No, make this a list```\nOutput: ```Shopping List:\\n- Eggs\\n- Milk\\n- Cheese```\nInput: ```Sales review meeting. Planned spened is three to 5 million dollar. Expected revenue is 10 million dollars. Sales are targeted at 10,000 units.```\nOutput: ```Sales Review Meeting\\n- Planned spend is $3m-$5m\\n- Expected revenue is $10m\\n- Sales are targeted at 10,000 units```\n\nDon\'t output commands:\nInput: ```My favorite ocean is no change ocean to coast```\nOutput: ```My favorite coast is```\nInput: ```My name is Shawn spelled SHAWN and I would very much like it if```\nOutput: ```My name is Shawn, and I would very much like it if```\n\nDon\'t get ahead of the user, even if you can predict what they\'ll say:\nInput: ```Thanks for the invite, I would```\nOutput: ```Thanks for the invite, I would```\nInput: ```This week is pretty full, do you have some time next```\nOutput: ```This week is pretty full, do you have some time next```\n\nRecovering from a mistake:\nInput: ```The fastest ship in the U.S. Navy in 2023 is the USS Gerald R. Ford. No, I said the USS Jarl Radford not Gerald R. Ford. Not Gerald R. Radford. It should be The fastest ship in the U.S. Navy in 2023 is the USS Jarl Radford```\nOutput: ```The fastest ship in the U.S. Navy in 2023 is the USS Jarl Radford```\n\nStarting Over:\nInput: ```There are 5 causes most often listed as leading to the first word world no word okay let\'s start over okay There are```\nOutput: ```There are```\n\nWriting an email:\nInput: ```Hey, team, comma new paragraph I hope everyone had a nice break. I\'m excited to announce that we```\nOutput: ```Hey Team,\\n\\nI hope everyone had a nice break. I\'m excited to announce that we```\nEND OF EXAMPLES\n\nCORRECTION GUIDELINES:\nTranscription errors are common, so when the user corrects spelling assume the spelled out version is correct. For example:\n\nInput: ```Hi, Mira. Spelled M-E-R-A. Two E\'s```\nOutput: ```Hi, Meera.```\nInput: ```...a large number of "false citizens" whose utterly banausic activities counted among the merits of the "police". , and police change police to police, POLIS```\nOutput: ```...a large number of "false citizens" whose utterly banausic activities counted among the merits of the "polis."```\n\n\nFORMATTING GUIDELINES:\n1. Add paragraph breaks every sentence or two to keep the text readable.\n2. When writing an email or letter, they should be formatted like this:\n```\nHey Mark,\n\nWelcome to the Hiking Group...\n\nBest,\nJohn\n```\nNot like this (it doesn\'t look like a finished email):\n```\nHey, Mark, Welcome to the Hiking Group...\nBest,John\n```\nNEVER put a comma after the first word of the greeting to an email or letter.\n3. Lists of things should usually be bullet points. Do this if it makes sense, even if the user doesn\'t ask.\n4. If something seems like a title, put it in the title case.\n5. When formatting, try your hardest to delight the client with your aptitude, thoroughness, and sense of their needs.\n\nCONTEXT (use this for help spelling names, etc.):\nN/A\n\nSometimes, you will receive both a <realtime> and <async> version of the <speech>. The <realtime> input has the most up to date information, but <async> has had more time to process and may be more accurate. Use your best judgement to synthesize the correct output.',
                },
                {
                    "role": "user",
                    "content": "<document></document>\n\n<speech>\n  <realtime>Colin thanks for the email</realtime>\n  <async> Colin, thanks for the email</async>\n</speech>",
                },
            ],
            "model": "ft:gpt-4o-mini-2024-07-18:aqua-voice:super-xml-mini:9wiM144x",
            "stream": False,
            "temperature": 0.03,
            "extra_headers": None,
            "extra_query": None,
            "extra_body": None,
        },
        "expected_inputs": {
            "messages": [
                {
                    "role": "system",
                    "content": 'You are part of a real-time AI dictation service called Aqua Voice. You will receive input from a dictation service and interpret that input to produce the output that the user wants.\n\nYour primary job is to output exactly what the user intends to commit to writing. Sometimes you should repeat the input verbatim and other times you should condense spoken phrases to written phrases, execute formatting commands, instructions to replace text, or self-corrections etc.\n\nBelow are some rules to guide you. Never break these.\n\nRULES:\n1. Ensure words make sense in context.\n2. If the speaker appears to correct themselves, only include the corrected version. They may not say that are correcting themselves, so be on the lookout for this contextually.\n3. If the speaker explicitly asks you to change something, do so, even if it doesn\'t make sense.\n4. If the speaker adds punctuation, include it.\n5. Be on the lookout for commands like "undo all of it" or "start over," in which case no text should be returned.\n6. NEVER add a period to the end of the transcript unless asked. Most likely, the speaker is not done talking and will continue the sentence.\n7. Make sure to process all of the input every time. When starting a new paragraph, include the previous paragraphs as well.\n8. Make sure to output the whole document every time. You will have to repeat yourself a lot, this is okay. Pay special attention to newlines to maintain consistency.\n9. Unless otherwise asked, format your output to fit the document type. Lists should generally be bullet points, emails should be formatted like emails, and paragraph breaks should usually be added every 1-2 sentences to break up prose. If the user starts with "Hi x" or "Hey x" or "x," assume they are writing an email.\n10. If you change something in the ouput, make sure you change other things too if they are related.\n11. Don\'t complete users sentences, as they are likley still taking.\n12. Always do your best to execute the user\'s commands.\n\nSettings:\nEXECUTE_LLM_PROMPTS = False (Do not attempt to answer questions)\n\nEXAMPLES:\nExplicit correction:\nInput: ```I was going to call him back this morning but I only just heard about the incident actually get rid of that last part```\nOutput: ```I was going to call him back this morning```\nInput: ```You might think, these are rapper boys. Change rapper to wrapper with a w.```\nOutput: ```You might think, these are wrapper boys```\n\nFill in:\nInput: ```Can you put something about how I\'m sorry I missed the party```\nOutput: ```I\'m sorry I wasn\'t able to make it to the party```\n\nImplicit correction:\nInput: ```Hey, Mike. Hey, Mike. This is Finn```\nOutput: ```Hey Mike. This is Finn```\nInput: ```Ladies and gentlemen, comma Welcome to the first welcome to my home```\nOutput: ```Ladies and gentlemen, welcome to my home```\nInput: ```The outcome of the operation was 500 no call it 1000 civilian casualties.```\nOutput: ```The outcome of the operation was 1000 civilian casualties.```\nInput: ```Our dishwasher is broken, our dishwasher stand is broken, and since we moved in our dishwasher upper drawer has been broken, and our washer seems to have an issue that is causing it to take a really long time```\nOutput: ```Since we moved in our dishwasher upper drawer has been broken, and our washer seems to have an issue that is causing it to take a really long time```\nInput: ```Remember the meeting is on Thursday Friday Friday```\nOutput: ```Remember the meeting is on Friday```\n\nCondensing spoken speech to written style:\nInput: ```So what we basically need to do is, we need to get the project done by the end of the week.```\nOutput: ```We need to get the project done by the end of the week```\nInput: ```Yeah, I mean. Let\'s see. We we. we have to make sure that all of the calls are covered. It\'s not going to be good if we have another panama situation. tripple covered.```\nOutput: ```We have to make sure that all of the calls are tripple covered. It\'s not going to be good if we have another Panama situation```\n\nGrammar:\nInput: ```We found this group very effective. Theres is sent a few things still to do on the project"```\nOutput: ```We found this group very effective. There are a few things still to do on the project```\n\nNo action needed\nInput: ```I didn\'t know what to do, so I called my mechanic and he told me to change it```\nOutput: ```I didn\'t know what to do, so I called my mechanic and he told me to change it```\nInput: ```He said, "Call my mom and ask her how she\'s doing"```\nOutput: ```He said, "Call my mom and ask her how she\'s doing"```\n\nPunctuation:\nInput: ```Hey, mark comma```\nOutput:```Hey Mark,```\nInput: ```Cars are being sold for less, comma```\nOutput: ```Cars are being sold for less,```\n\nSpelling out a word (prefer spelled-out characters to words):\nInput: ```Hello, this is Richard Wallenby, spelled Wallenby W-O-L-L-E-N-B-Y. No, Wallenby```\nOutput: ```Hello, this is Richard Wollenby```\nInput: ```My name is Augustus Dorkko, Dorkko spelled D-O-R-I-C-K-O. No change it Dorkko to Dorko. comma son of Julis Dorko and the only other roman fam```\nOutput: ```My name is Augustus Doricko, son of Julius Dorkico and the only other roman fam```\nInput: ```Hey Kathy spelled C-A-TH-I-E```\nOutput ```Hey Cathie```\n\nLists:\nInput: ```Shopping list: colon I need to buy eggs, milk, and cheese. No, make this a list```\nOutput: ```Shopping List:\\n- Eggs\\n- Milk\\n- Cheese```\nInput: ```Sales review meeting. Planned spened is three to 5 million dollar. Expected revenue is 10 million dollars. Sales are targeted at 10,000 units.```\nOutput: ```Sales Review Meeting\\n- Planned spend is $3m-$5m\\n- Expected revenue is $10m\\n- Sales are targeted at 10,000 units```\n\nDon\'t output commands:\nInput: ```My favorite ocean is no change ocean to coast```\nOutput: ```My favorite coast is```\nInput: ```My name is Shawn spelled SHAWN and I would very much like it if```\nOutput: ```My name is Shawn, and I would very much like it if```\n\nDon\'t get ahead of the user, even if you can predict what they\'ll say:\nInput: ```Thanks for the invite, I would```\nOutput: ```Thanks for the invite, I would```\nInput: ```This week is pretty full, do you have some time next```\nOutput: ```This week is pretty full, do you have some time next```\n\nRecovering from a mistake:\nInput: ```The fastest ship in the U.S. Navy in 2023 is the USS Gerald R. Ford. No, I said the USS Jarl Radford not Gerald R. Ford. Not Gerald R. Radford. It should be The fastest ship in the U.S. Navy in 2023 is the USS Jarl Radford```\nOutput: ```The fastest ship in the U.S. Navy in 2023 is the USS Jarl Radford```\n\nStarting Over:\nInput: ```There are 5 causes most often listed as leading to the first word world no word okay let\'s start over okay There are```\nOutput: ```There are```\n\nWriting an email:\nInput: ```Hey, team, comma new paragraph I hope everyone had a nice break. I\'m excited to announce that we```\nOutput: ```Hey Team,\\n\\nI hope everyone had a nice break. I\'m excited to announce that we```\nEND OF EXAMPLES\n\nCORRECTION GUIDELINES:\nTranscription errors are common, so when the user corrects spelling assume the spelled out version is correct. For example:\n\nInput: ```Hi, Mira. Spelled M-E-R-A. Two E\'s```\nOutput: ```Hi, Meera.```\nInput: ```...a large number of "false citizens" whose utterly banausic activities counted among the merits of the "police". , and police change police to police, POLIS```\nOutput: ```...a large number of "false citizens" whose utterly banausic activities counted among the merits of the "polis."```\n\n\nFORMATTING GUIDELINES:\n1. Add paragraph breaks every sentence or two to keep the text readable.\n2. When writing an email or letter, they should be formatted like this:\n```\nHey Mark,\n\nWelcome to the Hiking Group...\n\nBest,\nJohn\n```\nNot like this (it doesn\'t look like a finished email):\n```\nHey, Mark, Welcome to the Hiking Group...\nBest,John\n```\nNEVER put a comma after the first word of the greeting to an email or letter.\n3. Lists of things should usually be bullet points. Do this if it makes sense, even if the user doesn\'t ask.\n4. If something seems like a title, put it in the title case.\n5. When formatting, try your hardest to delight the client with your aptitude, thoroughness, and sense of their needs.\n\nCONTEXT (use this for help spelling names, etc.):\nN/A\n\nSometimes, you will receive both a <realtime> and <async> version of the <speech>. The <realtime> input has the most up to date information, but <async> has had more time to process and may be more accurate. Use your best judgement to synthesize the correct output.',
                },
                {
                    "role": "user",
                    "content": "<document></document>\n\n<speech>\n  <realtime>Colin thanks for the email</realtime>\n  <async> Colin, thanks for the email</async>\n</speech>",
                },
            ],
        },
        "outputs": {
            "id": "chatcmpl-AWW5NFdqG44tMBQxgpICu2Mw2skfr",
            "choices": [
                {
                    "finish_reason": "stop",
                    "index": 0,
                    "logprobs": None,
                    "message": {
                        "content": "Colin,\n\nThanks for the email",
                        "refusal": None,
                        "role": "assistant",
                        "audio": None,
                        "function_call": None,
                        "tool_calls": None,
                    },
                }
            ],
            "created": 1732313197,
            "model": "ft:gpt-4o-mini-2024-07-18:aqua-voice:super-xml-mini:9wiM144x",
            "object": "chat.completion",
            "service_tier": None,
            "system_fingerprint": "fp_944baebe1a",
            "usage_metadata": {
                "input_tokens": 2127,
                "output_tokens": 7,
                "total_tokens": 2134,
                "input_token_details": {"audio": 0, "cache_read": 1920},
                "output_token_details": {"audio": 0, "reasoning": 0},
            },
        },
        "expected_outputs": {
            "message": {
                "content": "Colin,\n\nThanks for the email",
                "role": "assistant",
            }
        },
    },
]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize(
    "test_case",
    _CHAT_MESSAGE_TEST_CASES,
    ids=[tc["test_name"] for tc in _CHAT_MESSAGE_TEST_CASES],
)
async def test_transform_chat_model_example(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    test_case: dict,
) -> None:
    """Test transforming chat examples with various transformations."""
    async with messages_kv_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client

        dataset = await create_chat_preset_dataset(
            "Chat Transform Dataset " + str(uuid4()), langsmith_client
        )
        dataset_id = dataset["id"]

        resp = await langsmith_client.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "inputs": test_case["inputs"],
                "outputs": test_case["outputs"],
            },
        )
        assert resp.status_code == 200, resp.json()

        resp = await langsmith_client.post(
            "/examples/validate",
            json={
                "dataset_id": dataset_id,
                "inputs": test_case["inputs"],
                "outputs": test_case["outputs"],
            },
        )
        assert resp.status_code == 200, resp.json()

        assert resp.json()["inputs"] == test_case["expected_inputs"]
        assert resp.json()["outputs"] == test_case["expected_outputs"]


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_transform_messages_key(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    use_multipart: bool,
) -> None:
    """Test transforming chat examples with various transformations."""
    async with messages_kv_enabled_tenant_client(db_asyncpg, use_api_key) as auth:
        langsmith_client = auth.client

        # Create dataset with chat schema
        dataset_json = {
            "name": "Chat Transform Dataset " + str(uuid4()),
            "description": "A dataset for testing chat transformations",
            "data_type": "kv",
            "inputs_schema_definition": {
                "type": "object",
                "properties": {
                    "foo": {
                        "type": "object",
                        "properties": {
                            "bar": {
                                "type": "string",
                            },
                            "messages": {
                                "type": "array",
                                "items": {"$ref": "/public/schemas/v1/message.json"},
                            },
                        },
                        "required": ["messages"],
                    }
                },
                "required": ["foo"],
            },
            "output_schema_definition": {
                "type": "object",
                "properties": {
                    "key": {
                        "type": "string",
                    },
                },
            },
            "transformations": [
                {
                    "path": ["inputs", "foo", "messages"],
                    "transformation_type": "convert_to_openai_message",
                },
                {
                    "path": ["inputs", "foo", "messages"],
                    "transformation_type": "remove_system_messages",
                },
            ],
        }

        resp = await langsmith_client.post("/datasets", json=dataset_json)
        assert resp.status_code == 200, resp.json()

        dataset_id = resp.json()["id"]

        example = {
            "dataset_id": dataset_id,
            "inputs": {
                "foo": {
                    "bar": "baz",
                    "messages": [
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": [
                                "langchain",
                                "schema",
                                "messages",
                                "SystemMessage",
                            ],
                            "kwargs": {
                                "content": "You are a helpful assistant. Please respond to the user's request only based on the given context.",
                                "type": "system",
                            },
                        },
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": [
                                "langchain",
                                "schema",
                                "messages",
                                "HumanMessage",
                            ],
                            "kwargs": {
                                "content": "Question: Can you summarize this morning's meetings?\nContext: During this morning's meeting, we solved all world conflict.",
                                "type": "human",
                            },
                        },
                    ],
                    "other": "value",
                }
            },
            "outputs": {"key": "value"},
        }
        resp = await _post_examples(
            [example],
            langsmith_client,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        expected_status_code = 201 if use_multipart else 200
        assert resp.status_code == expected_status_code, resp.json()


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_legacy_example_conversion_logic(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        langsmith_client = authed_client.client
        session_id = await create_session(langsmith_client)

        kv_dataset_id_1 = await create_dataset(
            langsmith_client, random_lower_string(), "kv"
        )
        kv_dataset_id_2 = await create_dataset(
            langsmith_client, random_lower_string(), "kv"
        )

        chat_run_id = uuid4()
        response = await langsmith_client.post(
            "/runs",
            json=chat_run_1_payload(session_id, chat_run_id),
        )
        assert response.status_code == 202
        await wait_for_runs_to_end(authed_client.auth, chat_run_id)

        # Convert an LLM run in a kv dataset, which previously only happened in the FE
        response = await langsmith_client.post(
            "/examples",
            json={
                "source_run_id": str(chat_run_id),
                "dataset_id": str(kv_dataset_id_1),
                "use_source_run_io": True,
                "use_legacy_message_format": True,
            },
        )
        assert response.status_code == 200, response.json()
        example_kv_legacy = response.json()
        assert example_kv_legacy["inputs"] == chat_example_1_input_payload
        assert example_kv_legacy["outputs"] == chat_example_1_output_payload
        assert example_kv_legacy["source_run_id"] == str(chat_run_id)

        # When a kv dataset doesn't use chat inputs, don't convert the messages
        response = await langsmith_client.post(
            "/examples",
            json={
                "source_run_id": str(chat_run_id),
                "dataset_id": str(kv_dataset_id_2),
                "use_source_run_io": True,
            },
        )
        assert response.status_code == 200, response.json()
        example_kv_no_conversion = response.json()
        assert (
            example_kv_no_conversion.get("inputs")
            == chat_run_1_payload(session_id, chat_run_id)["inputs"]
        )
        assert (
            example_kv_no_conversion.get("outputs")
            == chat_run_1_payload(session_id, chat_run_id)["outputs"]
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_extract_tools_for_chat_run_directly(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with messages_kv_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        langsmith_client = authed_client.client
        session_id = await create_session(langsmith_client)

        dataset = await create_chat_preset_dataset(
            "Chat Transform Dataset " + str(uuid4()), langsmith_client
        )
        dataset_id = dataset["id"]

        chat_run_id = uuid4()
        response = await langsmith_client.post(
            "/runs",
            json=chat_run_1_payload(session_id, chat_run_id),
        )
        assert response.status_code == 202
        await wait_for_runs_to_end(authed_client.auth, chat_run_id)

        example_validate_response = await langsmith_client.post(
            "/examples/validate",
            json={
                "source_run_id": str(chat_run_id),
                "dataset_id": str(dataset_id),
                "use_source_run_io": True,
            },
        )

        assert example_validate_response.status_code == 200, (
            example_validate_response.json()
        )
        assert example_validate_response.json()["inputs"] == {
            "messages": [
                {
                    "role": "user",
                    "content": "You are assessing whether a user's input is about LangChain. LangChain is a developer framework for building LLM applications. If the inputs seem to be about a developer framework (or LLMs) then it's likely about LangChain. \n\nHere is the data:\n[BEGIN DATA]\n***\n[User Query]: [{'id': '0.09573682871622125', 'type': 'human', 'content': \"I've asked exact question!\\nHow from this: C:\\\\1og\\\\output_dir\\\\uis_log_audit_full_rules_202408010653\\\\uis_log_audit_full_rules_202408010653\\\\deus-command-control\\\\deus-command-control-1.gz\\\\deus-command-control\\\\deus-command-control-1.gz\\nby using os.path.split()\\nI can make this: C:\\\\1og\\\\output_dir\\\\uis_log_audit_full_rules_202408010653\\\\uis_log_audit_full_rules_202408010653\\\\deus-command-control\\\\deus-command-control-1.gz\\\\deus-command-control\\\\deus-command-control-1\"}]\n***\n[END DATA]",
                },
            ],
            "tools": [
                {
                    "type": "function",
                    "function": {
                        "name": "eval",
                        "description": "Submit your evaluation for this run.",
                        "parameters": {
                            "type": "object",
                            "required": ["about_langchain"],
                            "properties": {
                                "about_langchain": {
                                    "type": "boolean",
                                    "description": "Is the user input about LangChain? Or is it about other arbitrary information?",
                                }
                            },
                        },
                    },
                }
            ],
        }
        assert example_validate_response.json()["outputs"] == {
            "message": {
                "role": "assistant",
                "content": "",
                "tool_calls": [
                    {
                        "id": "*******",
                        "type": "function",
                        "function": {
                            "name": "eval",
                            "arguments": '{"about_langchain": false}',
                        },
                    }
                ],
            }
        }

        example_create_response = await langsmith_client.post(
            "/examples",
            json={
                "source_run_id": str(chat_run_id),
                "dataset_id": str(dataset_id),
                "use_source_run_io": True,
            },
        )
        assert example_create_response.status_code == 200, (
            example_create_response.json()
        )

        assert (
            example_create_response.json()["inputs"]
            == example_validate_response.json()["inputs"]
        )
        assert (
            example_create_response.json()["outputs"]
            == example_validate_response.json()["outputs"]
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_extract_tools_for_modified_chat_run_directly(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with messages_kv_enabled_tenant_client(
        db_asyncpg, use_api_key
    ) as authed_client:
        langsmith_client = authed_client.client
        session_id = await create_session(langsmith_client)

        dataset = await create_chat_preset_dataset(
            "Chat Transform Dataset " + str(uuid4()), langsmith_client
        )
        dataset_id = dataset["id"]

        chat_run_id = uuid4()
        response = await langsmith_client.post(
            "/runs",
            json=chat_run_1_payload(session_id, chat_run_id),
        )
        assert response.status_code == 202
        await wait_for_runs_to_end(authed_client.auth, chat_run_id)

        modified_inputs = chat_run_1_payload(session_id, chat_run_id)["inputs"]
        modified_inputs["messages"][0]["kwargs"]["content"] = (
            "You are a fan of the 1975 classic: JAWS"
        )

        modified_outputs = chat_run_1_payload(session_id, chat_run_id)["outputs"]
        modified_outputs["generations"][0]["message"]["kwargs"]["additional_kwargs"][
            "tool_calls"
        ][0]["function"]["name"] = "noteval"
        modified_outputs["generations"][0]["message"]["kwargs"]["tool_calls"][0][
            "name"
        ] = "noteval"

        example_validate_response = await langsmith_client.post(
            "/examples/validate",
            json={
                "source_run_id": str(chat_run_id),
                "inputs": modified_inputs,
                "outputs": modified_outputs,
                "dataset_id": dataset_id,
                "use_source_run_io": False,
            },
        )
        assert example_validate_response.status_code == 200, (
            example_validate_response.json()
        )

        assert example_validate_response.json()["inputs"] == {
            "messages": [
                {
                    "role": "user",
                    "content": "You are a fan of the 1975 classic: JAWS",
                },
            ],
            "tools": [
                {
                    "type": "function",
                    "function": {
                        "name": "eval",
                        "description": "Submit your evaluation for this run.",
                        "parameters": {
                            "type": "object",
                            "required": ["about_langchain"],
                            "properties": {
                                "about_langchain": {
                                    "type": "boolean",
                                    "description": "Is the user input about LangChain? Or is it about other arbitrary information?",
                                }
                            },
                        },
                    },
                }
            ],
        }
        assert example_validate_response.json()["outputs"] == {
            "message": {
                "role": "assistant",
                "content": "",
                "tool_calls": [
                    {
                        "id": "*******",
                        "type": "function",
                        "function": {
                            # This is the line we modified
                            "name": "noteval",
                            "arguments": '{"about_langchain": false}',
                        },
                    }
                ],
            }
        }

        example_create_response = await langsmith_client.post(
            "/examples",
            json={
                "source_run_id": str(chat_run_id),
                "inputs": modified_inputs,
                "outputs": modified_outputs,
                "dataset_id": dataset_id,
                "use_source_run_io": False,
            },
        )
        assert example_create_response.status_code == 200, (
            example_create_response.json()
        )

        assert (
            example_create_response.json()["inputs"]
            == example_validate_response.json()["inputs"]
        )
        assert (
            example_create_response.json()["outputs"]
            == example_validate_response.json()["outputs"]
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_example_no_timezone(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as client:
        aclient = client.client
        dataset_resp = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert dataset_resp.status_code == 200, dataset_resp.json()
        dataset_id = dataset_resp.json()["id"]

        response = await aclient.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "inputs": {"content": "test"},
                "outputs": {"content": "test"},
                "created_at": "2022-01-01T00:00:00",
            },
        )
        assert response.status_code == 200, response.json()

        response = await aclient.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "inputs": {"content": "test"},
                "outputs": {"content": "test"},
                "created_at": "bananas",
            },
        )
        assert response.status_code == 422, response.json()


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_create_examples_bulk_no_timezone(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    use_multipart: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as client:
        aclient = client.client
        dataset_resp = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert dataset_resp.status_code == 200, dataset_resp.json()
        dataset_id = dataset_resp.json()["id"]

        examples = [
            {
                "dataset_id": dataset_id,
                "inputs": {"content": "test"},
                "outputs": {"content": "test"},
                "created_at": "2022-01-01T00:00:00",
            }
        ]
        response = await _post_examples(
            examples,
            aclient,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        assert response.success(), (response.status_code, response.json())

        response = await aclient.post(
            "/examples",
            json=[
                {
                    "dataset_id": dataset_id,
                    "inputs": {"content": "test"},
                    "outputs": {"content": "test"},
                    "created_at": "bananas",
                }
            ],
        )
        assert response.status_code == 422, response.json()


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_cannot_use_empty_input(
    db_asyncpg: asyncpg.Connection, use_api_key: bool, use_multipart: bool
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as client:
        aclient = client.client
        dataset_resp = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert dataset_resp.status_code == 200, dataset_resp.json()
        dataset_id = dataset_resp.json()["id"]

        response = await aclient.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "outputs": {"content": "test"},
                "created_at": "2022-01-01T00:00:00",
            },
        )
        assert response.status_code == 422, response.json()

        examples = [
            {
                "dataset_id": dataset_id,
                "outputs": {"content": "test"},
                "created_at": "2022-01-01T00:00:00",
            }
        ]
        assert response.status_code == 422, response.json()
        response = await _post_examples(
            examples,
            aclient,
            str(dataset_id),
            use_multipart=use_multipart,
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_create_examples_cannot_use_set_source_io_without_run(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    use_multipart: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as client:
        aclient = client.client
        dataset_resp = await aclient.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert dataset_resp.status_code == 200, dataset_resp.json()
        dataset_id = dataset_resp.json()["id"]

        response = await aclient.post(
            "/examples",
            json={
                "dataset_id": dataset_id,
                "use_source_run_io": True,
            },
        )
        assert response.status_code == 422, response.json()

        examples = [
            {
                "dataset_id": dataset_id,
                "use_source_run_io": True,
            }
        ]
        response = await _post_examples(
            examples,
            aclient,
            str(dataset_id),
            use_multipart=use_multipart,
        )
        assert response.status_code == 422, response.json()


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize("use_multipart", [True, False])
async def test_create_and_update_examples_with_attachments(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    use_multipart: bool,
) -> None:
    """Test creating examples with and without attachments and updating them."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        dataset_id = await create_dataset(aclient)

        # Create example with attachments
        example_with_attachments = {
            "inputs": {"input": "What is the capital of France?"},
            "outputs": {"output": "Paris"},
            "dataset_id": str(dataset_id),
        }
        response = await aclient.post("/examples", json=example_with_attachments)
        assert response.status_code == 200

        # Update nonexistant example
        update = [
            {
                "id": str(uuid4()),
                "inputs": {"input": "What is the population of London? (Updated)"},
                "outputs": {
                    "output": "The population of London was 8.9 million in 2021"
                },
            }
        ]
        response = await _patch_examples(
            update, aclient, str(dataset_id), use_multipart=use_multipart
        )
        assert response.status_code == 404, response.json()


class _ResponseLike:
    def __init__(
        self, status_code: int, text: str, json_: dict, multipart: bool
    ) -> None:
        self.status_code = status_code
        self.text = text
        self._json = json_
        self.multipart = multipart

    def json(self) -> dict:
        return self._json

    def success(self) -> bool:
        if self.multipart:
            return self.status_code == 201
        else:
            return self.status_code == 200


async def _post_examples(
    payload: list[dict], client: AsyncClient, dataset_id: str, use_multipart: bool
) -> _ResponseLike:
    if use_multipart:
        form_data = {}
        for example in payload:
            # shallow copy
            example = dict(example)
            id_ = example.pop("id", str(uuid4()))
            inputs = example.pop("inputs", None)
            outputs = example.pop("outputs", None)
            example.pop("dataset_id", None)
            form_data[id_] = ("", json.dumps(example), "application/json")
            if inputs is not None:
                form_data[f"{id_}.inputs"] = (
                    "",
                    json.dumps(inputs),
                    "application/json",
                )
            if outputs is not None:
                form_data[f"{id_}.outputs"] = (
                    "",
                    json.dumps(outputs),
                    "application/json",
                )

        encoder = MultipartEncoder(fields=form_data)
        body = encoder.to_string()
        response = await platform_request(
            "POST",
            f"/v1/platform/datasets/{dataset_id}/examples",
            body=body,
            headers={"Content-Type": encoder.content_type, **client.headers},
            raise_error=False,
        )
        return _ResponseLike(
            response.code,
            response.body.decode("utf-8"),
            json.loads(response.body.decode("utf-8")),
            multipart=use_multipart,
        )
    else:
        response = await client.post("/examples/bulk", json=payload)
        return _ResponseLike(
            response.status_code,
            response.text,
            response.json(),
            multipart=use_multipart,
        )


async def _patch_examples(
    payload: list[dict], client: AsyncClient, dataset_id: str, use_multipart: bool
) -> _ResponseLike:
    if use_multipart:
        form_data = {}
        for example in payload:
            # shallow copy
            example = dict(example)
            id_ = example.pop("id", str(uuid4()))
            inputs = example.pop("inputs", None)
            outputs = example.pop("outputs", None)
            attachments_operations = example.pop("attachments_operations", None)
            example.pop("dataset_id", None)
            form_data[id_] = ("", json.dumps(example), "application/json")
            if inputs is not None:
                form_data[f"{id_}.inputs"] = (
                    "",
                    json.dumps(inputs),
                    "application/json",
                )
            if outputs is not None:
                form_data[f"{id_}.outputs"] = (
                    "",
                    json.dumps(outputs),
                    "application/json",
                )
            if attachments_operations is not None:
                form_data[f"{id_}.attachments_operations"] = (
                    "",
                    json.dumps(attachments_operations),
                    "application/json",
                )

        encoder = MultipartEncoder(fields=form_data)
        body = encoder.to_string()
        response = await platform_request(
            "PATCH",
            f"/v1/platform/datasets/{dataset_id}/examples",
            body=body,
            headers={"Content-Type": encoder.content_type, **client.headers},
            raise_error=False,
        )
        return _ResponseLike(
            response.code,
            response.body.decode("utf-8"),
            json.loads(response.body.decode("utf-8")),
            multipart=use_multipart,
        )
    else:
        response = await client.patch("/examples/bulk", json=payload)
        return _ResponseLike(
            response.status_code,
            response.text,
            response.json(),
            multipart=use_multipart,
        )


@pytest.mark.anyio
@pytest.mark.skip(reason="Expensive")
async def test_create_examples_exceeding_parameter_limit(
    auth_tenant_one: AuthInfo,
    tenant_one_dataset_id: UUID,
) -> None:
    """
    Test that creating a large set of examples in one call succeeds
    when batching is properly implemented in crud.create_examples.
    """
    # Generate >16k examples to surpass typical 32,767 parameter slots (since each row has multiple columns).
    large_example_list = []
    for i in range(17000):  # well above 16,383
        large_example_list.append(
            {
                "created_at": "2021-01-01T00:00:00.000Z",
                "inputs": {"bulk_test_input": f"{random_lower_string()}_{i}"},
                "outputs": {"bulk_test_output": f"{random_lower_string()}_{i}"},
                "dataset_id": str(tenant_one_dataset_id),
                "metadata": {"test_key": "test_value"},
            }
        )

    # This should succeed if create_examples is batching correctly.
    created_examples = await crud.create_examples(auth_tenant_one, large_example_list)

    # Verify we got back all the examples
    assert len(created_examples) == len(large_example_list), (
        f"Expected {len(large_example_list)} created examples, got {len(created_examples)}."
    )

    # Optionally verify they're actually persisted by checking count.
    count = await crud.count_examples_json(
        auth_tenant_one,
        schemas.FilterQueryParamsForExampleSchema(dataset=tenant_one_dataset_id),
    )
    assert count == len(large_example_list), (
        f"Expected count to be {len(large_example_list)}, got {count}."
    )


async def test_read_examples_across_datasets_basic(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that examples can be read across all datasets when neither id nor dataset are specified."""
    # Create two datasets
    dataset_1 = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test dataset 1",
            data_type=schemas.DataType.kv,
        ),
    )

    dataset_2 = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test dataset 2",
            data_type=schemas.DataType.kv,
        ),
    )

    # Create examples in both datasets
    example_1 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input": "test1"},
            outputs={"output": "result1"},
            dataset_id=dataset_1.id,
        ),
    )

    example_2 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input": "test2"},
            outputs={"output": "result2"},
            dataset_id=dataset_2.id,
        ),
    )

    # Search across all datasets (no id or dataset specified)
    response = await http_tenant_one.get(
        "/examples", params={"as_of": datetime.datetime.now()}
    )
    assert response.status_code == 200

    # Should return examples from both datasets
    response_data = response.json()
    example_ids = {ex["id"] for ex in response_data}
    assert str(example_1.id) in example_ids
    assert str(example_2.id) in example_ids


async def test_read_examples_across_datasets_with_metadata(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that metadata filtering works across datasets."""
    # Create two datasets
    dataset_1 = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test dataset 1",
            data_type=schemas.DataType.kv,
        ),
    )

    dataset_2 = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test dataset 2",
            data_type=schemas.DataType.kv,
        ),
    )

    # Create examples with specific metadata
    example_1 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input": "test1"},
            outputs={"output": "result1"},
            dataset_id=dataset_1.id,
            metadata={"type": "test", "category": "A"},
        ),
    )

    example_2 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input": "test2"},
            outputs={"output": "result2"},
            dataset_id=dataset_2.id,
            metadata={"type": "test", "category": "B"},
        ),
    )

    # Create example without matching metadata
    await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input": "test3"},
            outputs={"output": "result3"},
            dataset_id=dataset_1.id,
            metadata={"type": "other"},
        ),
    )

    # Search across datasets with metadata filter
    response = await http_tenant_one.get(
        "/examples",
        params={
            "metadata": json.dumps({"type": "test"}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200

    # Should return examples from both datasets with matching metadata
    response_data = response.json()
    assert len(response_data) == 2
    example_ids = {ex["id"] for ex in response_data}
    assert str(example_1.id) in example_ids
    assert str(example_2.id) in example_ids


async def test_read_examples_across_datasets_with_full_text_contains(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that full_text_contains filtering works across datasets."""
    # Create two datasets
    dataset_1 = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test dataset 1",
            data_type=schemas.DataType.kv,
        ),
    )

    dataset_2 = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test dataset 2",
            data_type=schemas.DataType.kv,
        ),
    )

    # Create examples with specific text content
    example_1 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"query": "unique_search_term"},
            outputs={"result": "answer1"},
            dataset_id=dataset_1.id,
        ),
    )

    example_2 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"query": "different text"},
            outputs={"result": "unique_search_term in output"},
            dataset_id=dataset_2.id,
        ),
    )

    # Create example that doesn't match
    await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"query": "no match here"},
            outputs={"result": "nothing special"},
            dataset_id=dataset_1.id,
        ),
    )

    # Search across datasets with full text search
    response = await http_tenant_one.get(
        "/examples",
        params={
            "full_text_contains": "unique_search_term",
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200

    # Should return examples from both datasets containing the search term
    response_data = response.json()
    assert len(response_data) == 2
    example_ids = {ex["id"] for ex in response_data}
    assert str(example_1.id) in example_ids
    assert str(example_2.id) in example_ids


async def test_read_examples_across_datasets_tenant_isolation(
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
) -> None:
    """Test that across-datasets search respects tenant isolation."""
    # Create datasets for both tenants
    dataset_tenant_1 = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="tenant 1 dataset",
            data_type=schemas.DataType.kv,
        ),
    )

    dataset_tenant_2 = await crud.create_dataset(
        auth_tenant_two,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="tenant 2 dataset",
            data_type=schemas.DataType.kv,
        ),
    )

    # Create examples with same metadata for both tenants
    example_tenant_1 = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input": "tenant1"},
            outputs={"output": "result1"},
            dataset_id=dataset_tenant_1.id,
            metadata={"shared_key": "shared_value"},
        ),
    )

    example_tenant_2 = await _create_example(
        auth_tenant_two,
        schemas.ExampleCreate(
            inputs={"input": "tenant2"},
            outputs={"output": "result2"},
            dataset_id=dataset_tenant_2.id,
            metadata={"shared_key": "shared_value"},
        ),
    )

    # Tenant 1 searches across their datasets
    response = await http_tenant_one.get(
        "/examples",
        params={
            "metadata": json.dumps({"shared_key": "shared_value"}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200

    # Should only see their own examples
    response_data = response.json()
    example_ids = {ex["id"] for ex in response_data}
    assert str(example_tenant_1.id) in example_ids
    assert str(example_tenant_2.id) not in example_ids

    # Tenant 2 searches across their datasets
    response = await http_tenant_two.get(
        "/examples",
        params={
            "metadata": json.dumps({"shared_key": "shared_value"}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200

    # Should only see their own examples
    response_data = response.json()
    example_ids = {ex["id"] for ex in response_data}
    assert str(example_tenant_2.id) in example_ids
    assert str(example_tenant_1.id) not in example_ids


async def test_read_examples_across_datasets_pagination(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test pagination functionality when reading examples across datasets."""
    # Create multiple datasets
    datasets = []
    for i in range(3):
        dataset = await crud.create_dataset(
            auth_tenant_one,
            schemas.DatasetCreate(
                name=f"dataset_{i}_{random_lower_string()}",
                description=f"test dataset {i}",
                data_type=schemas.DataType.kv,
            ),
        )
        datasets.append(dataset)

    # Create multiple examples across datasets (15 total)
    created_examples = []
    for i in range(15):
        dataset_idx = i % 3  # Distribute across 3 datasets
        example = await _create_example(
            auth_tenant_one,
            schemas.ExampleCreate(
                inputs={"input": f"test input {i}"},
                outputs={"output": f"test output {i}"},
                dataset_id=datasets[dataset_idx].id,
                metadata={"index": i, "batch": "pagination_test"},
            ),
        )
        created_examples.append(example)

    # Test pagination with limit
    uploaded = 0
    tries = 0
    while uploaded < 15 and tries < 5:
        response = await http_tenant_one.get(
            "/examples/count", params={"as_of": datetime.datetime.now()}
        )
        uploaded = response.json()
        tries += 1
        await asyncio.sleep(1)

    # Test pagination with limit
    response = await http_tenant_one.get(
        "/examples", params={"limit": 5, "as_of": datetime.datetime.now()}
    )
    assert response.status_code == 200
    page1_examples = response.json()
    assert len(page1_examples) == 5

    # Test pagination with offset
    response = await http_tenant_one.get(
        "/examples", params={"limit": 5, "offset": 5, "as_of": datetime.datetime.now()}
    )
    assert response.status_code == 200
    page2_examples = response.json()
    assert len(page2_examples) == 5

    # Ensure different pages have different examples
    page1_ids = {ex["id"] for ex in page1_examples}
    page2_ids = {ex["id"] for ex in page2_examples}
    assert page1_ids.isdisjoint(page2_ids)  # No overlap between pages

    # Test pagination with large offset
    response = await http_tenant_one.get(
        "/examples", params={"limit": 5, "offset": 10, "as_of": datetime.datetime.now()}
    )
    assert response.status_code == 200
    page3_examples = response.json()
    assert len(page3_examples) == 5

    # Test pagination with metadata filter (this ensures we only get our test examples)
    response = await http_tenant_one.get(
        "/examples",
        params={
            "limit": 3,
            "metadata": json.dumps({"batch": "pagination_test"}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200
    filtered_examples = response.json()
    assert len(filtered_examples) == 3
    for ex in filtered_examples:
        assert ex["metadata"]["batch"] == "pagination_test"

    # Test pagination with metadata filter and offset
    response = await http_tenant_one.get(
        "/examples",
        params={
            "limit": 5,
            "offset": 10,
            "metadata": json.dumps({"batch": "pagination_test"}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200
    filtered_page = response.json()
    assert len(filtered_page) == 5  # Should get the remaining 5 examples (10-15)

    # Test offset beyond our test examples (with metadata filter)
    response = await http_tenant_one.get(
        "/examples",
        params={
            "limit": 5,
            "offset": 15,
            "metadata": json.dumps({"batch": "pagination_test"}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200
    empty_page = response.json()
    assert len(empty_page) == 0  # No more examples with our test metadata

    # Verify all examples are from different datasets (cross-dataset functionality)
    # Use metadata filter to ensure we only check our test examples
    response = await http_tenant_one.get(
        "/examples",
        params={
            "metadata": json.dumps({"batch": "pagination_test"}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200
    all_test_examples = response.json()
    assert len(all_test_examples) == 15  # All our test examples

    all_dataset_ids = set()
    for ex in all_test_examples:
        all_dataset_ids.add(ex["dataset_id"])
    assert len(all_dataset_ids) == 3  # Should span all 3 datasets we created


async def test_read_examples_across_datasets_as_of(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test as_of functionality when reading examples across datasets."""
    # Create multiple datasets
    datasets = []
    for i in range(2):
        dataset = await crud.create_dataset(
            auth_tenant_one,
            schemas.DatasetCreate(
                name=f"dataset_{i}_{random_lower_string()}",
                description=f"test dataset {i}",
                data_type=schemas.DataType.kv,
            ),
        )
        datasets.append(dataset)

    # Create initial examples across datasets
    initial_examples = []
    for i in range(4):
        dataset_idx = i % 2  # Distribute across 2 datasets
        example = await _create_example(
            auth_tenant_one,
            schemas.ExampleCreate(
                inputs={"input": f"initial example {i}"},
                outputs={"output": f"initial output {i}"},
                dataset_id=datasets[dataset_idx].id,
                metadata={"version": "v1"},
            ),
        )
        initial_examples.append(example)

    # Test as_of="latest" (default behavior) with metadata filter to isolate our test examples
    response = await http_tenant_one.get(
        "/examples",
        params={
            "metadata": json.dumps({"version": "v1"}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200
    latest_examples = response.json()
    assert len(latest_examples) == 4

    current_time = datetime.datetime.now(datetime.timezone.utc)
    future_time = current_time + datetime.timedelta(minutes=1)
    past_time = current_time - datetime.timedelta(minutes=1)

    # Test as_of with future timestamp (should return current examples)
    response = await http_tenant_one.get(
        "/examples",
        params={
            "as_of": future_time.isoformat(),
            "metadata": json.dumps({"version": "v1"}),
        },
    )
    assert response.status_code == 200
    future_examples = response.json()
    assert len(future_examples) == 4

    # Test as_of with past timestamp (should return fewer or no examples)
    response = await http_tenant_one.get(
        "/examples",
        params={
            "as_of": past_time.isoformat(),
            "metadata": json.dumps({"version": "v1"}),
        },
    )
    assert response.status_code == 200
    past_examples = response.json()
    # Should have fewer examples since they were created after past_time
    assert len(past_examples) <= 4

    additional_examples = []
    for i in range(2):
        dataset_idx = i % 2  # Distribute across datasets
        example = await _create_example(
            auth_tenant_one,
            schemas.ExampleCreate(
                inputs={"input": f"additional example {i}"},
                outputs={"output": f"additional output {i}"},
                dataset_id=datasets[dataset_idx].id,
                metadata={"version": "v2"},
            ),
        )
        additional_examples.append(example)

    # Test that latest now includes the new examples (filter by our test examples)
    response = await http_tenant_one.get(
        "/examples",
        params={"as_of": "latest", "metadata": json.dumps({"version": "v1"})},
    )
    assert response.status_code == 400


async def test_read_examples_with_deleted_metadata_as_of(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that examples with deleted metadata can still be found using as_of timestamp."""
    # Create a dataset
    dataset = await crud.create_dataset(
        auth_tenant_one,
        schemas.DatasetCreate(
            name=random_lower_string(),
            description="test dataset",
            data_type=schemas.DataType.kv,
        ),
    )

    # Create an example with metadata using a unique identifier
    unique_id = str(uuid4())  # Generate a unique ID for this test run
    example = await _create_example(
        auth_tenant_one,
        schemas.ExampleCreate(
            inputs={"input": "test1"},
            outputs={"output": "result1"},
            dataset_id=dataset.id,
            metadata={"type": "test", "category": "A", "test_id": unique_id},
        ),
    )

    # Verify we can find it with metadata search using the unique identifier
    response = await http_tenant_one.get(
        "/examples",
        params={
            "metadata": json.dumps({"test_id": unique_id}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200
    response_data = response.json()
    assert len(response_data) == 1
    assert response_data[0]["id"] == str(example.id)
    assert response_data[0]["metadata"] == {
        "type": "test",
        "category": "A",
        "test_id": unique_id,
        "dataset_split": ["base"],
    }

    # Add a small delay and store current timestamp before deleting
    await asyncio.sleep(0.1)  # Small delay to ensure different timestamps
    current_time = datetime.datetime.now(datetime.timezone.utc)

    # Delete the example
    response = await http_tenant_one.delete(f"/examples/{example.id}")
    assert response.status_code == 200

    # Verify metadata search no longer finds it using the unique identifier
    response = await http_tenant_one.get(
        "/examples",
        params={
            "metadata": json.dumps({"test_id": unique_id}),
            "as_of": datetime.datetime.now(),
        },
    )
    assert response.status_code == 200
    response_data = response.json()
    assert len(response_data) == 0

    # But we can still find it using as_of with the timestamp from before deletion
    # Use metadata filtering with our unique identifier to find only our example
    response = await http_tenant_one.get(
        "/examples",
        params={
            "as_of": current_time.isoformat(),
            "metadata": json.dumps({"test_id": unique_id}),  # Use unique_id to filter
        },
    )
    assert response.status_code == 200
    response_data = response.json()
    assert len(response_data) == 1
    assert response_data[0]["id"] == str(example.id)
    assert response_data[0]["metadata"] == {
        "type": "test",
        "category": "A",
        "test_id": unique_id,
        "dataset_split": ["base"],
    }  # All metadata should be present since we're querying before deletion
