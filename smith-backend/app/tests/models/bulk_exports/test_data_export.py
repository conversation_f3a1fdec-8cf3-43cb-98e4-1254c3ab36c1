"""Test correct behavior of the runs endpoints."""

import datetime
import os
import uuid
from typing import Any, Awaitable, Callable, cast
from unittest.mock import AsyncMock, patch
from uuid import UUID, uuid4

import aiochclient
import orjson
import pytest
from httpx import AsyncClient
from lc_config.settings import shared_settings as settings

from app import config, crud, schemas
from app.api.auth import AuthInfo
from app.models.bulk_exports.export import (
    _calculate_partitions_for_data_counts,
    calculate_partitions,
    export_partition,
)
from app.tests.api.test_runs import post_runs
from app.tests.conftest import get_minio_server_url

pytestmark = [
    pytest.mark.anyio,
    pytest.mark.skipif(
        config.settings.AUTH_TYPE == "oauth",
        reason="oauth not support in queue",
    ),
]


async def _construct_runs(
    http_tenant_one: AsyncClient,
    tracer_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    num_root_runs: int = 10,
    num_child_runs: int = 10,
    dates: list[str] | None = None,
) -> list[dict]:
    default_dates = [
        "2023-05-01T05:13:24.000000Z",
        "2023-05-02T05:13:24.000000Z",
        "2023-05-03T05:13:24.000000Z",
        "2023-05-04T05:13:24.000000Z",
        "2023-05-05T05:13:24.000000Z",
    ]
    if not dates:
        dates = default_dates

    new_runs = []
    for i in range(num_root_runs):
        trace_id = uuid4()
        date = dates[i % len(dates)]
        date_dotted = date.replace("-", "").replace(":", "").replace(".", "")
        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ]
        serialized = {"name": "AgentExecutor"}
        error = "an error"
        extra = {"foo": "bar"}

        parent_run = {
            "name": "AgentExecutor",
            "start_time": date,
            "end_time": date,
            "extra": extra,
            "error": error,
            "events": events,
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "session_id": str(tracer_session_id),
            "run_type": "chain",
            "id": str(trace_id),
            "parent_run_id": None,
            "trace_id": str(trace_id),
            "dotted_order": date_dotted + str(trace_id),
        }
        new_runs.append(parent_run)
        for _ in range(num_child_runs):
            run_id = uuid4()
            new_runs.append(
                {
                    "name": "AgentExecutor",
                    "start_time": date,
                    "end_time": date,
                    "extra": extra,
                    "error": None,
                    "events": events,
                    "execution_order": 1,
                    "serialized": serialized,
                    "inputs": inputs,
                    "outputs": outputs,
                    "session_id": str(tracer_session_id),
                    "run_type": "chain",
                    "id": str(run_id),
                    "parent_run_id": str(trace_id),
                    "trace_id": str(trace_id),
                    "dotted_order": (
                        cast(str, parent_run["dotted_order"])
                        + "."
                        + date_dotted
                        + str(run_id)
                    ),
                },
            )

        # should succeed
        await post_runs(
            "/runs/batch",
            http_tenant_one,
            new_runs,
        )

    await wait_until_task_queue_empty()
    return new_runs


@pytest.mark.parametrize("is_s3_payload", [True, False])
async def test_create_run(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    is_s3_payload: bool,
) -> None:
    """Test that a run can be created."""
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )
    tenant_one_tracer_session_id = session.id
    run_id = uuid4()
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]
    serialized = {"name": "AgentExecutor"}
    error = "an error"

    if is_s3_payload:
        large_data = "x" * (settings.MIN_BLOB_STORAGE_SIZE_KB * 1024)
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        events[0]["data"] = large_data
        error = "e" * (settings.MIN_BLOB_STORAGE_SIZE_KB * 1024)

    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar", "metadata": {"conversation_id": "112233"}},
        "error": error,
        "execution_order": 1,
        "serialized": serialized,
        "inputs": inputs,
        "outputs": outputs,
        "events": events,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
        "tags": ["tag1", "tag2"],
        "dotted_order": f"20230505T051324571809Z{run_id}",
        "trace_id": str(run_id),
    }

    response = await http_tenant_one.post("/runs/batch", json={"post": [run_data]})

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id

    # construct data export
    export_start_time = datetime.datetime.fromisoformat(
        "2023-05-05T05:13:24.571809"
    ) - datetime.timedelta(minutes=5)
    export_end_time = datetime.datetime.fromisoformat(
        "2023-05-05T05:13:32.022361"
    ) + datetime.timedelta(minutes=5)

    s3_config = schemas.BulkExportDestinationS3Config(
        bucket_name=settings.S3_BUCKET_NAME,
        prefix="ls_exports",
        endpoint_url=settings.S3_API_URL,
        region="us-east-1",
    )
    creds = schemas.BulkExportDestinationS3Credentials(
        access_key_id=settings.S3_ACCESS_KEY,
        secret_access_key=settings.S3_ACCESS_KEY_SECRET,
    )
    result = await export_partition(
        auth_tenant_one,
        s3_config,
        creds,
        uuid.uuid4(),
        uuid.uuid4(),
        auth_tenant_one.tenant_id,
        run.session_id,
        export_start_time,
        export_end_time,
        None,
    )

    # Check that the run was exported
    exported_run_rows = await ch_client.fetch(
        "select * from s3({path}, {key}, {secret}, 'Parquet')",
        params={
            "path": f"{get_minio_server_url()}/{settings.S3_BUCKET_NAME}/{result.export_path}/**",
            "key": settings.S3_ACCESS_KEY,
            "secret": settings.S3_ACCESS_KEY_SECRET,
        },
    )

    exported_runs = [dict(row) for row in exported_run_rows]
    assert len(exported_runs) == 1
    new_run = exported_runs[0]

    # Check that the exported run matches the original run
    assert new_run["id"] == str(run.id)
    assert new_run["tenant_id"] == str(auth_tenant_one.tenant_id)
    assert new_run["name"] == run.name
    assert new_run["start_time"] == run.start_time
    assert new_run["end_time"] == run.end_time
    assert orjson.loads(new_run["extra"]) == run.extra
    assert new_run["error"] == run.error
    assert new_run["is_root"]
    assert new_run["run_type"] == run.run_type
    assert orjson.loads(new_run["inputs"]) == run.inputs
    assert orjson.loads(new_run["outputs"]) == run.outputs
    assert new_run["session_id"] == str(run.session_id)
    assert new_run["parent_run_id"] == (
        str(run.parent_run_id) if run.parent_run_id else None
    )
    if run.reference_example_id:
        assert new_run["reference_example_id"] == (
            str(run.reference_example_id) if run.reference_example_id else None
        )
    assert orjson.loads(new_run["events"]) == run.events
    assert orjson.loads(new_run["tags"]) == run.tags
    assert new_run["status"] == run.status
    assert new_run["trace_id"] == str(run.trace_id)
    assert new_run["dotted_order"] == run.dotted_order
    assert new_run["prompt_tokens"] == run.prompt_tokens
    assert new_run["completion_tokens"] == run.completion_tokens
    assert new_run["total_tokens"] == run.total_tokens
    assert new_run["first_token_time"] == run.first_token_time
    assert (
        orjson.loads(new_run["feedback_stats"]) if new_run["feedback_stats"] else None
    ) == run.feedback_stats
    assert new_run["total_cost"] == run.total_cost
    assert new_run["prompt_cost"] == run.prompt_cost
    assert new_run["completion_cost"] == run.completion_cost
    assert new_run["trace_tier"] == run.trace_tier
    assert (
        orjson.loads(new_run["parent_run_ids"]) if new_run["parent_run_ids"] else None
    ) == run.parent_run_ids

    # Export with filter
    run_id = uuid4()
    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar", "metadata": {"conversation_id": "123456"}},
        "error": error,
        "execution_order": 1,
        "serialized": serialized,
        "inputs": inputs,
        "outputs": outputs,
        "events": events,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
        "tags": ["tag1", "tag2"],
        "dotted_order": f"20230505T051324571809Z{run_id}",
        "trace_id": str(run_id),
    }

    response = await http_tenant_one.post("/runs/batch", json={"post": [run_data]})

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id

    # construct data export
    export_start_time = datetime.datetime.fromisoformat(
        "2023-05-05T05:13:24.571809"
    ) - datetime.timedelta(minutes=5)
    export_end_time = datetime.datetime.fromisoformat(
        "2023-05-05T05:13:32.022361"
    ) + datetime.timedelta(minutes=5)

    s3_config = schemas.BulkExportDestinationS3Config(
        bucket_name=settings.S3_BUCKET_NAME,
        prefix="ls_exports",
        endpoint_url=settings.S3_API_URL,
        region="us-east-1",
    )
    creds = schemas.BulkExportDestinationS3Credentials(
        access_key_id=settings.S3_ACCESS_KEY,
        secret_access_key=settings.S3_ACCESS_KEY_SECRET,
    )
    result = await export_partition(
        auth_tenant_one,
        s3_config,
        creds,
        uuid.uuid4(),
        uuid.uuid4(),
        auth_tenant_one.tenant_id,
        run.session_id,
        export_start_time,
        export_end_time,
        "and(eq(run_type, 'chain'), eq(metadata_key, 'conversation_id'), eq(metadata_value, '123456'))",
    )

    # Check that the run was exported
    exported_run_rows = await ch_client.fetch(
        "select * from s3({path}, {key}, {secret}, 'Parquet')",
        params={
            "path": f"{get_minio_server_url()}/{settings.S3_BUCKET_NAME}/{result.export_path}/**",
            "key": settings.S3_ACCESS_KEY,
            "secret": settings.S3_ACCESS_KEY_SECRET,
        },
    )

    exported_runs = [dict(row) for row in exported_run_rows]
    assert len(exported_runs) == 1
    new_run = exported_runs[0]

    # Check that the exported run matches the original run
    assert new_run["id"] == str(run.id)
    assert new_run["tenant_id"] == str(auth_tenant_one.tenant_id)
    assert new_run["name"] == run.name
    assert (
        new_run["extra"]
        == '{"foo":"bar","metadata":{"conversation_id":"123456","ls_run_depth":0}}'
    )


@pytest.mark.parametrize(
    "data_export_batch_size,data_export_run_limit,data_export_target_size_kb,expected_parquet_files",
    [
        (
            settings.DATA_EXPORT_START_BATCH_SIZE,
            settings.DATA_EXPORT_RUN_LIMIT,
            settings.DATA_EXPORT_TARGET_SIZE_KB,
            (5, 5),
        ),
        (8, 5, 5, (15, 25)),
    ],
)
async def test_create_run_batch_across_dates(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    data_export_batch_size: int,
    data_export_run_limit: int,
    data_export_target_size_kb: int,
    expected_parquet_files: tuple[int, int],
) -> None:
    """Test that a run can be created."""

    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )

    new_runs = await _construct_runs(
        http_tenant_one=http_tenant_one,
        tracer_session_id=session.id,
        wait_until_task_queue_empty=wait_until_task_queue_empty,
    )

    # construct data export
    export_start_time = datetime.datetime.fromisoformat(
        "2023-05-01T05:13:24.571809"
    ) - datetime.timedelta(minutes=5)
    export_end_time = datetime.datetime.fromisoformat(
        "2023-05-10T05:13:32.022361"
    ) + datetime.timedelta(minutes=5)

    s3_config = schemas.BulkExportDestinationS3Config(
        bucket_name=settings.S3_BUCKET_NAME,
        prefix="ls_exports",
        endpoint_url=settings.S3_API_URL,
        region="us-east-1",
    )
    creds = schemas.BulkExportDestinationS3Credentials(
        access_key_id=settings.S3_ACCESS_KEY,
        secret_access_key=settings.S3_ACCESS_KEY_SECRET,
    )
    with (
        patch(
            "app.models.bulk_exports.export.settings.DATA_EXPORT_START_BATCH_SIZE",
            data_export_batch_size,
        ),
        patch(
            "app.models.bulk_exports.export.settings.DATA_EXPORT_RUN_LIMIT",
            data_export_run_limit,
        ),
        patch(
            "app.models.bulk_exports.export.settings.DATA_EXPORT_TARGET_SIZE_KB",
            data_export_target_size_kb,
        ),
    ):
        export_result = await export_partition(
            auth_tenant_one,
            s3_config,
            creds,
            uuid.uuid4(),
            uuid.uuid4(),
            auth_tenant_one.tenant_id,
            session.id,
            export_start_time,
            export_end_time,
            None,
        )

    # Check that the run was exported
    export_run_count = await ch_client.fetchrow(
        "select count(1) as count from s3({path}, {key}, {secret}, 'Parquet')",
        params={
            "path": f"{get_minio_server_url()}/{settings.S3_BUCKET_NAME}/{export_result.export_path}/**",
            "key": settings.S3_ACCESS_KEY,
            "secret": settings.S3_ACCESS_KEY_SECRET,
        },
    )

    assert export_run_count["count"] == len(new_runs)
    # use range due to variability with parquet files, compression, etc
    assert len(export_result.files) >= expected_parquet_files[0]
    assert len(export_result.files) <= expected_parquet_files[1]


@pytest.mark.parametrize(
    "data_counts, expected, description",
    [
        # Test case where data counts are below the partition target size.
        (
            [
                (
                    datetime.datetime(2021, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2021, 1, 2, 0, 0, tzinfo=datetime.timezone.utc),
                    settings.BULK_EXPORT_PARTITION_TARGET_SIZE - 1,
                )
            ],
            [
                (
                    datetime.datetime(2021, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2021, 1, 2, 0, 0, tzinfo=datetime.timezone.utc),
                )
            ],
            "Data counts below partition target size",
        ),
        # Test case where data counts are above the partition target size, splitting into hours.
        (
            [
                (
                    datetime.datetime(2024, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 3, 12, 0, tzinfo=datetime.timezone.utc),
                    settings.BULK_EXPORT_PARTITION_TARGET_SIZE + 1,
                )
            ],
            [
                *[
                    (
                        datetime.datetime(
                            2024, 1, 1, i, 0, tzinfo=datetime.timezone.utc
                        ),
                        datetime.datetime(
                            2024, 1, 1, i + 1, 0, tzinfo=datetime.timezone.utc
                        ),
                    )
                    for i in range(23)
                ],
                (
                    datetime.datetime(2024, 1, 1, 23, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 2, 0, 0, tzinfo=datetime.timezone.utc),
                ),
                *[
                    (
                        datetime.datetime(
                            2024, 1, 2, i, 0, tzinfo=datetime.timezone.utc
                        ),
                        datetime.datetime(
                            2024, 1, 2, i + 1, 0, tzinfo=datetime.timezone.utc
                        ),
                    )
                    for i in range(23)
                ],
                (
                    datetime.datetime(2024, 1, 2, 23, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 3, 0, 0, tzinfo=datetime.timezone.utc),
                ),
                *[
                    (
                        datetime.datetime(
                            2024, 1, 3, i, 0, tzinfo=datetime.timezone.utc
                        ),
                        datetime.datetime(
                            2024, 1, 3, i + 1, 0, tzinfo=datetime.timezone.utc
                        ),
                    )
                    for i in range(12)
                ],
            ],
            "Data counts above partition target size, split into hours",
        ),
        # Test case with mixed data counts, splitting some days into hours.
        (
            [
                (
                    datetime.datetime(2024, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 2, 0, 0, tzinfo=datetime.timezone.utc),
                    settings.BULK_EXPORT_PARTITION_TARGET_SIZE + 1,
                ),
                (
                    datetime.datetime(2024, 1, 2, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 3, 0, 0, tzinfo=datetime.timezone.utc),
                    settings.BULK_EXPORT_PARTITION_TARGET_SIZE - 10,
                ),
                (
                    datetime.datetime(2024, 1, 3, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 4, 0, 0, tzinfo=datetime.timezone.utc),
                    settings.BULK_EXPORT_PARTITION_TARGET_SIZE + 50,
                ),
                (
                    datetime.datetime(2024, 1, 4, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 4, 12, 0, tzinfo=datetime.timezone.utc),
                    settings.BULK_EXPORT_PARTITION_TARGET_SIZE,
                ),
            ],
            [
                *[
                    (
                        datetime.datetime(
                            2024, 1, 1, i, 0, tzinfo=datetime.timezone.utc
                        ),
                        datetime.datetime(
                            2024, 1, 1, i + 1, 0, tzinfo=datetime.timezone.utc
                        ),
                    )
                    for i in range(23)
                ],
                (
                    datetime.datetime(2024, 1, 1, 23, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 2, 0, 0, tzinfo=datetime.timezone.utc),
                ),
                (
                    datetime.datetime(2024, 1, 2, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 3, 0, 0, tzinfo=datetime.timezone.utc),
                ),
                *[
                    (
                        datetime.datetime(
                            2024, 1, 3, i, 0, tzinfo=datetime.timezone.utc
                        ),
                        datetime.datetime(
                            2024, 1, 3, i + 1, 0, tzinfo=datetime.timezone.utc
                        ),
                    )
                    for i in range(23)
                ],
                (
                    datetime.datetime(2024, 1, 3, 23, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 4, 0, 0, tzinfo=datetime.timezone.utc),
                ),
                (
                    datetime.datetime(2024, 1, 4, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 4, 12, 0, tzinfo=datetime.timezone.utc),
                ),
            ],
            "Mixed data counts, some days split into hours",
        ),
        # Test case with data counts above the medium partition target size, splitting into minutes.
        (
            [
                (
                    datetime.datetime(2024, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                    datetime.datetime(2024, 1, 2, 0, 0, tzinfo=datetime.timezone.utc),
                    settings.BULK_EXPORT_MEDIUM_PARTITION_TARGET_SIZE + 1,
                ),
            ],
            [
                (
                    (
                        start := datetime.datetime(
                            2024, 1, 1, h, m, tzinfo=datetime.timezone.utc
                        )
                    ),
                    start + datetime.timedelta(minutes=5),
                )
                for h in range(24)
                for m in range(0, 60, 5)
            ],
            "Data counts above medium partition target size, splitting into minutes",
        ),
    ],
)
async def test_calculate_partitions_for_data_counts(data_counts, expected, description):
    result = await _calculate_partitions_for_data_counts(data_counts)
    assert result == expected, description


@pytest.mark.parametrize(
    "bulk_export_partition_target_size",
    [
        1,
        100,
    ],
)
async def test_run_partitions(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    bulk_export_partition_target_size: int,
) -> None:
    """Test that a run can be created."""

    with (
        patch(
            "app.models.bulk_exports.export.settings.BULK_EXPORT_PARTITION_TARGET_SIZE",
            bulk_export_partition_target_size,
        ),
    ):
        session = await crud.create_tracer_session(
            auth_tenant_one,
            schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
        )

        await _construct_runs(
            http_tenant_one=http_tenant_one,
            tracer_session_id=session.id,
            wait_until_task_queue_empty=wait_until_task_queue_empty,
        )

        export_start_time = datetime.datetime.fromisoformat(
            "2023-05-01T01:00:24.571809Z"
        ) - datetime.timedelta(minutes=5)
        export_end_time = datetime.datetime.fromisoformat(
            "2023-05-05T23:00:00.022361Z"
        ) + datetime.timedelta(minutes=5)

        partitions = await calculate_partitions(
            auth_tenant_one.tenant_id,
            session.id,
            export_start_time,
            export_end_time,
            None,
        )

        if bulk_export_partition_target_size == 1:
            # should use hourly partitions due to smaller target size
            assert len(partitions) == 24 * 5
        else:
            assert len(partitions) == 5
            assert partitions[0][0] == export_start_time
            assert partitions[-1][-1] == export_end_time


@pytest.mark.parametrize(
    "data_export_batch_size,data_export_run_limit,data_export_target_size_kb",
    [
        (
            settings.DATA_EXPORT_START_BATCH_SIZE,
            settings.DATA_EXPORT_RUN_LIMIT,
            settings.DATA_EXPORT_TARGET_SIZE_KB,
        ),
        (3, 3, 0.001),
    ],
)
async def test_export_continue_cursor(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    data_export_batch_size: int,
    data_export_run_limit: int,
    data_export_target_size_kb: int,
) -> None:
    """Test that can continue a single export where left off via cursor without duplicates
    This tests both cases with smaller sizes and cursor, or with ones that are larger and finish the whole batch
    """
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )

    # dates for single partition
    dates = [
        "2023-05-01T01:00:00.000000Z",
        "2023-05-01T02:00:00.000000Z",
        "2023-05-01T03:00:00.000000Z",
        "2023-05-01T04:00:00.000000Z",
        "2023-05-01T05:00:00.000000Z",
    ]

    new_runs = await _construct_runs(
        http_tenant_one=http_tenant_one,
        tracer_session_id=session.id,
        wait_until_task_queue_empty=wait_until_task_queue_empty,
        dates=dates,
    )

    with (
        patch(
            "app.models.bulk_exports.export.settings.DATA_EXPORT_START_BATCH_SIZE",
            data_export_batch_size,
        ),
        patch(
            "app.models.bulk_exports.export.settings.DATA_EXPORT_RUN_LIMIT",
            data_export_run_limit,
        ),
        patch(
            "app.models.bulk_exports.export.settings.DATA_EXPORT_TARGET_SIZE_KB",
            data_export_target_size_kb,
        ),
    ):
        # construct data export
        export_start_time = datetime.datetime.fromisoformat(
            "2023-05-01T01:00:00.000000"
        ) - datetime.timedelta(minutes=5)
        export_end_time = datetime.datetime.fromisoformat(
            "2023-05-01T05:30:00.000000"
        ) + datetime.timedelta(minutes=5)

        s3_config = schemas.BulkExportDestinationS3Config(
            bucket_name=settings.S3_BUCKET_NAME,
            prefix="ls_exports",
            endpoint_url=settings.S3_API_URL,
            region="us-east-1",
        )
        creds = schemas.BulkExportDestinationS3Credentials(
            access_key_id=settings.S3_ACCESS_KEY,
            secret_access_key=settings.S3_ACCESS_KEY_SECRET,
        )

        # Create an AsyncMock for report_progress_fn
        mock_report_progress = AsyncMock()
        mock_should_update_status_fn = AsyncMock()
        # stop the execution midway for the cursor based approach
        mock_should_update_status_fn.side_effect = [
            None,
            None,
            None,
            schemas.BulkExportRunStatus.CANCELLED,
        ]

        # rerun again - it should not duplicate and pick up where it left off
        export_id = uuid.uuid4()
        export_run_id = uuid.uuid4()
        export_result = await export_partition(
            auth_tenant_one,
            s3_config,
            creds,
            export_id,
            export_run_id,
            auth_tenant_one.tenant_id,
            session.id,
            export_start_time,
            export_end_time,
            None,
            should_update_status_fn=mock_should_update_status_fn
            if data_export_batch_size == 3
            else None,
            report_progress_fn=mock_report_progress,
        )

        # Assert that mock_report_progress was called
        mock_report_progress.assert_called()

        # Get the latest cursor from the last call to mock_report_progress.
        # -1 is the last call, 0 is the positional args, -1 is the last positional arg.
        latest_progress = mock_report_progress.call_args_list[-1][0][-1]

        # export an overlapping partition, should not be duplicated
        export_start_time = datetime.datetime.fromisoformat(
            "2023-05-01T01:00:00.000000"
        ) - datetime.timedelta(minutes=5)
        export_end_time = datetime.datetime.fromisoformat(
            "2023-05-01T05:30:00.000000"
        ) + datetime.timedelta(minutes=5)

        export_result = await export_partition(
            auth_tenant_one,
            s3_config,
            creds,
            export_id,
            export_run_id,
            auth_tenant_one.tenant_id,
            session.id,
            export_start_time,
            export_end_time,
            None,
            latest_progress=latest_progress,
            report_progress_fn=mock_report_progress,
        )

        # Check that the run was exported
        exported_count = await ch_client.fetchrow(
            "select count(1) as count from s3({path}, {key}, {secret}, 'Parquet')",
            params={
                "path": f"{get_minio_server_url()}/{settings.S3_BUCKET_NAME}/{export_result.export_path}/**",
                "key": settings.S3_ACCESS_KEY,
                "secret": settings.S3_ACCESS_KEY_SECRET,
            },
        )
        assert exported_count["count"] == len(new_runs)


async def test_export_with_env_vars(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )

    dates = [
        "2023-05-01T01:00:00.000000Z",
        "2023-05-01T02:00:00.000000Z",
        "2023-05-01T03:00:00.000000Z",
        "2023-05-01T04:00:00.000000Z",
        "2023-05-01T05:00:00.000000Z",
    ]

    new_runs = await _construct_runs(
        http_tenant_one=http_tenant_one,
        tracer_session_id=session.id,
        wait_until_task_queue_empty=wait_until_task_queue_empty,
        dates=dates,
    )
    # construct data export
    export_start_time = datetime.datetime.fromisoformat(
        "2023-05-01T01:00:00.000000"
    ) - datetime.timedelta(minutes=5)
    export_end_time = datetime.datetime.fromisoformat(
        "2023-05-01T05:30:00.000000"
    ) + datetime.timedelta(minutes=5)
    # Create s3 config without explicit credentials
    env_var_s3_config = schemas.BulkExportDestinationS3Config(
        bucket_name=settings.S3_BUCKET_NAME,
        prefix="test-export-env-vars",
        endpoint_url=settings.S3_API_URL,
        region="us-east-1",
    )

    # Test export with credentials from environment variables
    with patch.dict(
        os.environ,
        {
            "AWS_ACCESS_KEY_ID": settings.S3_ACCESS_KEY,
            "AWS_SECRET_ACCESS_KEY": settings.S3_ACCESS_KEY_SECRET,
            "IS_SELF_HOSTED": "true",
        },
    ):
        export_id = uuid.uuid4()
        export_run_id = uuid.uuid4()
        mock_report_progress = AsyncMock()

        export_result = await export_partition(
            auth_tenant_one,
            env_var_s3_config,
            None,  # No explicit credentials
            export_id,
            export_run_id,
            auth_tenant_one.tenant_id,
            session.id,
            export_start_time,
            export_end_time,
            None,
            report_progress_fn=mock_report_progress,
        )

        # Verify the export worked by checking count in exported files
        exported_count = await ch_client.fetchrow(
            "select count(1) as count from s3({path}, {key}, {secret}, 'Parquet')",
            params={
                "path": f"{get_minio_server_url()}/{settings.S3_BUCKET_NAME}/{export_result.export_path}/**",
                "key": settings.S3_ACCESS_KEY,
                "secret": settings.S3_ACCESS_KEY_SECRET,
            },
        )
        assert exported_count["count"] == len(new_runs)
