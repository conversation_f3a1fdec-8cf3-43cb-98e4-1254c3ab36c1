from typing import Optional, Type

import pytest
from fastapi import HTTPException
from langchain_core.callbacks import (
    CallbackManagerForToolRun,
)
from langchain_core.load import dumpd
from langchain_core.messages import (
    AIMessage,
    AIMessageChunk,
    HumanMessage,
    HumanMessageChunk,
    SystemMessage,
    SystemMessageChunk,
    ToolCallChunk,
    ToolMessage,
    ToolMessageChunk,
)
from langchain_core.tools import BaseTool, StructuredTool, tool
from langchain_core.utils.function_calling import convert_to_openai_tool
from pydantic import BaseModel, Field

import app.schemas as schemas
from app.models.examples.sourcerun import _get_message_as_stored_message
from app.models.examples.validate import (
    validate_and_transform_example_json,
)
from app.models.runs.preview import StoredMessage

EXPECTED_OUTPUT = StoredMessage(
    type="ai",
    data={
        "content": "Using weather tool",
        "additional_kwargs": {
            "tool_calls": [
                {
                    "id": "call_123",
                    "type": "function",
                    "function": {
                        "name": "get_weather",
                        "arguments": '{"location": "London"}',
                    },
                }
            ]
        },
    },
)


@pytest.mark.parametrize(
    "input_message",
    [
        # StoredMessage input
        StoredMessage(
            type="ai",
            data={
                "content": "Using weather tool",
                "additional_kwargs": {
                    "tool_calls": [
                        {
                            "id": "call_123",
                            "type": "function",
                            "function": {
                                "name": "get_weather",
                                "arguments": '{"location": "London"}',
                            },
                        }
                    ]
                },
            },
        ),
        # SerializedMessage input
        {
            "lc": 1,
            "type": "AIMessage",
            "id": ["langchain", "schema", "messages", "AIMessage"],
            "kwargs": {
                "content": "Using weather tool",
                "additional_kwargs": {
                    "tool_calls": [
                        {
                            "id": "call_123",
                            "type": "function",
                            "function": {
                                "name": "get_weather",
                                "arguments": '{"location": "London"}',
                            },
                        }
                    ]
                },
            },
        },
        # MessageFields input (raw dict)
        {
            "type": "ai",
            "data": {
                "content": "Using weather tool",
                "additional_kwargs": {
                    "tool_calls": [
                        {
                            "id": "call_123",
                            "type": "function",
                            "function": {
                                "name": "get_weather",
                                "arguments": '{"location": "London"}',
                            },
                        }
                    ]
                },
            },
        },
        # OpenAI Message input
        {
            "role": "assistant",
            "content": "Using weather tool",
            "tool_calls": [
                {
                    "id": "call_123",
                    "type": "function",
                    "function": {
                        "name": "get_weather",
                        "arguments": '{"location": "London"}',
                    },
                }
            ],
        },
    ],
)
def test_convert_message_to_stored_message_format(input_message):
    result = _get_message_as_stored_message(input_message)
    assert result == EXPECTED_OUTPUT


@tool
def my_tool(a: int, b: str):
    """My favorite tool!"""
    return b * a


def structured_func(a: int, b: str):
    """My structured tool!"""
    return b * a


my_structured_tool = StructuredTool.from_function(structured_func)


class CustomArgsSchema(BaseModel):
    a: int = Field(..., description="The first argument")
    b: int = Field(..., description="The second argument")


class CustomBaseTool(BaseTool):
    name: str = "my_base_tool"
    description: str = "My custom base tool"
    args_schema: Type[BaseModel] = CustomArgsSchema
    return_direct: bool = True

    def _run(
        self, a: int, b: int, run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> int:
        """Use the tool."""
        return a * b


async def test_single_tool():
    schema = {
        "type": "object",
        "properties": {"tool": {"$ref": "/public/schemas/v1/tooldef.json"}},
    }

    single_tool = {"tool": convert_to_openai_tool(my_tool)}

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "tool"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_tool,
        )
    ]

    response, _ = validate_and_transform_example_json(
        field="inputs",
        example=single_tool,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert response is not None
    assert "tool" in response
    assert response["tool"]["type"] == "function"
    assert response["tool"]["function"]["name"] == "my_tool"
    assert response["tool"]["function"]["description"] == "My favorite tool!"
    assert "parameters" in response["tool"]["function"]


async def test_list_of_tools():
    schema = {
        "type": "object",
        "properties": {
            "tools": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/tooldef.json"},
            }
        },
    }

    tools = {
        "tools": [
            convert_to_openai_tool(my_tool),
            convert_to_openai_tool(my_structured_tool),
            convert_to_openai_tool(CustomBaseTool()),
        ]
    }

    response, _ = validate_and_transform_example_json(
        field="inputs",
        example=tools,
        schema=schema,
        ff_w_chat_support=True,
    )

    assert "tools" in response
    assert isinstance(response["tools"], list)
    assert len(response["tools"]) == 3

    # Check the first tool (my_tool)
    assert response["tools"][0]["type"] == "function"
    assert response["tools"][0]["function"]["name"] == "my_tool"
    assert response["tools"][0]["function"]["description"] == "My favorite tool!"
    assert response["tools"][0]["function"]["parameters"]["type"] == "object"
    assert set(response["tools"][0]["function"]["parameters"]["properties"]) == {
        "a",
        "b",
    }
    assert (
        response["tools"][0]["function"]["parameters"]["properties"]["a"]["type"]
        == "integer"
    )
    assert (
        response["tools"][0]["function"]["parameters"]["properties"]["b"]["type"]
        == "string"
    )
    assert set(response["tools"][0]["function"]["parameters"]["required"]) == {"a", "b"}

    # Check the second tool (structured_func)
    assert response["tools"][1]["type"] == "function"
    assert response["tools"][1]["function"]["name"] == "structured_func"
    assert response["tools"][1]["function"]["description"] == "My structured tool!"
    assert response["tools"][1]["function"]["parameters"]["type"] == "object"
    assert set(response["tools"][1]["function"]["parameters"]["properties"]) == {
        "a",
        "b",
    }
    assert (
        response["tools"][1]["function"]["parameters"]["properties"]["a"]["type"]
        == "integer"
    )
    assert (
        response["tools"][1]["function"]["parameters"]["properties"]["b"]["type"]
        == "string"
    )
    assert set(response["tools"][1]["function"]["parameters"]["required"]) == {"a", "b"}

    # Check the third tool (CustomBaseTool)
    assert response["tools"][2]["type"] == "function"
    assert response["tools"][2]["function"]["name"] == "my_base_tool"
    assert response["tools"][2]["function"]["description"] == "My custom base tool"
    assert response["tools"][2]["function"]["parameters"]["type"] == "object"
    assert set(response["tools"][2]["function"]["parameters"]["properties"]) == {
        "a",
        "b",
    }
    assert (
        response["tools"][2]["function"]["parameters"]["properties"]["a"]["type"]
        == "integer"
    )
    assert (
        response["tools"][2]["function"]["parameters"]["properties"]["b"]["type"]
        == "integer"
    )
    assert set(response["tools"][2]["function"]["parameters"]["required"]) == {"a", "b"}


async def test_long_list_of_tools():
    schema = {
        "type": "object",
        "properties": {
            "tools": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/tooldef.json"},
            }
        },
    }

    # this is to check that the recursion depth isn't individually checking errors but rather fixing the whole list at once
    tools = {
        "tools": [
            convert_to_openai_tool(my_tool),
            convert_to_openai_tool(my_structured_tool),
            convert_to_openai_tool(CustomBaseTool()),
            convert_to_openai_tool(my_tool),
            convert_to_openai_tool(my_structured_tool),
            convert_to_openai_tool(CustomBaseTool()),
            convert_to_openai_tool(my_tool),
            convert_to_openai_tool(my_structured_tool),
            convert_to_openai_tool(CustomBaseTool()),
        ]
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "tools"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_tool,
        )
    ]

    response, _ = validate_and_transform_example_json(
        field="inputs",
        example=tools,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert "tools" in response
    assert len(response["tools"]) == 9


async def test_remove_system_messages_transformation_nested():
    schema = {
        "type": "object",
        "properties": {
            "a": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "b": {
                            "type": "object",
                            "properties": {
                                "messages": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "/public/schemas/v1/message.json"
                                    },
                                }
                            },
                        }
                    },
                },
            }
        },
    }

    example = {
        "a": [
            {
                "b": {
                    "messages": [
                        {"role": "system", "content": "You are a helpful assistant."},
                        {"role": "user", "content": "Hello, AI!"},
                        {"role": "assistant", "content": "Hello! How can I help you?"},
                    ],
                }
            },
            {
                "b": {
                    "messages": [
                        {"role": "system", "content": "Remember to be polite."},
                        {"role": "user", "content": "What's the weather like?"},
                        {
                            "role": "assistant",
                            "content": "I'm sorry, I don't have real-time weather information.",
                        },
                    ],
                }
            },
        ]
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "a", "*", "b", "messages"],
            transformation_type=schemas.DatasetTransformationType.remove_system_messages,
        )
    ]

    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert result is not None
    assert len(result["a"]) == 2
    assert len(result["a"][0]["b"]["messages"]) == 2
    assert len(result["a"][1]["b"]["messages"]) == 2
    assert all(msg["role"] != "system" for msg in result["a"][0]["b"]["messages"])
    assert all(msg["role"] != "system" for msg in result["a"][1]["b"]["messages"])
    assert [msg["role"] for msg in result["a"][0]["b"]["messages"]] == [
        "user",
        "assistant",
    ]
    assert [msg["role"] for msg in result["a"][1]["b"]["messages"]] == [
        "user",
        "assistant",
    ]


async def test_invalid_chat_input():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
                "description": "A list of messages in the chat history.",
            },
            "tools": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/tooldef.json"},
                "description": "A list of available tools or functions.",
            },
        },
        "required": ["messages"],
        "additionalProperties": True,
    }
    # Test with invalid input (missing 'messages' key)
    invalid_chat_input = {
        "tools": [
            {
                "name": "get_weather",
                "description": "Get the current weather for a location",
                "parameters": {
                    "type": "object",
                    "properties": {"location": {"type": "string"}},
                    "required": ["location"],
                },
            }
        ]
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "messages"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        ),
        schemas.DatasetTransformation(
            path=["inputs", "tools"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_tool,
        ),
    ]

    validation_errors = []
    validate_and_transform_example_json(
        field="inputs",
        example=invalid_chat_input,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
        validation_errors=validation_errors,
    )
    assert len(validation_errors) == 1


async def test_developer_message():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            }
        },
    }

    example = {
        "messages": [
            {"role": "developer", "content": "You are a helpful assistant."},
        ]
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "messages"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert result is not None
    assert len(result["messages"]) == 1
    assert result["messages"][0]["role"] == "developer"
    assert result["messages"][0]["content"] == "You are a helpful assistant."


async def test_remove_system_messages_transformation_wrong_path():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            }
        },
    }

    example = {
        "not_messages": [
            "hi",
            "hello",
        ],
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, AI!"},
            {"role": "assistant", "content": "Hello! How can I help you?"},
            {"role": "system", "content": "Remember to be polite."},
            {"role": "user", "content": "What's the weather like?"},
        ],
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "not_messages"],
            transformation_type=schemas.DatasetTransformationType.remove_system_messages,
        )
    ]

    with pytest.raises(HTTPException):
        validate_and_transform_example_json(
            field="inputs",
            example=example,
            schema=schema,
            ff_w_chat_support=True,
            transformations=transformations,
        )


async def test_remove_system_messages_transformation():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            }
        },
    }

    example = {
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, AI!"},
            {"role": "assistant", "content": "Hello! How can I help you?"},
            {"role": "system", "content": "Remember to be polite."},
            {"role": "user", "content": "What's the weather like?"},
        ],
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "messages"],
            transformation_type=schemas.DatasetTransformationType.remove_system_messages,
        )
    ]

    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert result is not None
    assert len(result["messages"]) == 3
    assert all(msg["role"] != "system" for msg in result["messages"])
    assert [msg["role"] for msg in result["messages"]] == ["user", "assistant", "user"]


async def test_coerce_langchain_messages_to_openai_format():
    schema = {
        "type": "object",
        "properties": {
            "first": {
                "type": "object",
                "properties": {
                    "messages": {
                        "type": "array",
                        "items": {"$ref": "/public/schemas/v1/message.json"},
                    }
                },
            }
        },
    }

    example = {
        "first": {
            "messages": dumpd(
                [
                    HumanMessage(content="Hello, AI!"),
                    AIMessage(
                        content="Hello, human! How can I assist you today?",
                        tool_calls=[
                            {
                                "name": "multiply",
                                "args": {"a": 3, "b": 12},
                                "id": "tool-call-id",
                                "type": "function",
                            }
                        ],
                    ),
                    ToolMessage(content="36", tool_call_id="tool-call-id"),
                    SystemMessage(content="You are a helpful AI assistant."),
                ]
            )
        }
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "first", "messages"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert result is not None
    assert result["first"]["messages"] == [
        {"role": "user", "content": "Hello, AI!"},
        {
            "role": "assistant",
            "tool_calls": [
                {
                    "type": "function",
                    "id": "tool-call-id",
                    "function": {"name": "multiply", "arguments": '{"a": 3, "b": 12}'},
                }
            ],
            "content": "Hello, human! How can I assist you today?",
        },
        {"role": "tool", "tool_call_id": "tool-call-id", "content": "36"},
        {"role": "system", "content": "You are a helpful AI assistant."},
    ]


async def test_invalid_message_type():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            }
        },
    }

    invalid_example = {
        "messages": [{"role": "invalid", "content": "This is an invalid message type"}]
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "messages"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    validation_errors = []
    validate_and_transform_example_json(
        field="inputs",
        example=invalid_example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
        validation_errors=validation_errors,
    )
    assert len(validation_errors) == 1


async def test_coerce_stored_message_to_openai_format():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            },
            "tools": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/tooldef.json"},
            },
        },
        "required": ["messages"],
    }

    example = {
        "messages": [
            [
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "AIMessage"],
                    "kwargs": {
                        "content": [
                            {
                                "text": "I see that Harrison actually has a Team Stand-Up at 11am this Friday. Let me check if we can schedule it for 10:30am instead, right after his Design Review.",
                                "type": "text",
                            },
                            {
                                "id": "toolu_01MgoQvH8tY2Qsa5hdQPfuzP",
                                "input": {
                                    "date": "2024-11-08",
                                    "start_time": "10:30",
                                    "end_time": "11:00",
                                    "title": "LangSmith Support Call with Sam",
                                },
                                "name": "schedule_cal",
                                "type": "tool_use",
                            },
                        ],
                        "response_metadata": {
                            "id": "msg_017Un7RmUzmvKkNrVGpBVGPy",
                            "model": "claude-3-5-sonnet-20241022",
                            "stop_reason": "tool_use",
                            "stop_sequence": None,
                            "usage": {
                                "input_tokens": 1317,
                                "output_tokens": 166,
                                "cache_creation_input_tokens": 0,
                                "cache_read_input_tokens": 0,
                            },
                        },
                        "type": "ai",
                        "id": "run-1fa2ff64-3f52-4274-bf0d-5876113a6354-0",
                        "tool_calls": [
                            {
                                "name": "schedule_cal",
                                "args": {
                                    "date": "2024-11-08",
                                    "start_time": "10:30",
                                    "end_time": "11:00",
                                    "title": "LangSmith Support Call with Sam",
                                },
                                "id": "toolu_01MgoQvH8tY2Qsa5hdQPfuzP",
                                "type": "tool_call",
                            }
                        ],
                        "usage_metadata": {
                            "input_tokens": 1317,
                            "output_tokens": 166,
                            "total_tokens": 1483,
                            "input_token_details": {
                                "cache_read": 0,
                                "cache_creation": 0,
                            },
                        },
                        "invalid_tool_calls": [],
                    },
                },
                {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "ToolMessage"],
                    "kwargs": {
                        "content": "event scheduled!",
                        "type": "tool",
                        "name": "schedule_cal",
                        "id": "a211693b-e1e7-470c-bc34-7027bf1162f2",
                        "tool_call_id": "toolu_01MgoQvH8tY2Qsa5hdQPfuzP",
                        "status": "success",
                    },
                },
            ]
        ]
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "messages"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert result is not None
    assert result["messages"] == [
        {
            "role": "assistant",
            "tool_calls": [
                {
                    "type": "function",
                    "id": "toolu_01MgoQvH8tY2Qsa5hdQPfuzP",
                    "function": {
                        "name": "schedule_cal",
                        "arguments": '{"date": "2024-11-08", "start_time": "10:30", "end_time": "11:00", "title": "LangSmith Support Call with Sam"}',
                    },
                }
            ],
            "content": "I see that Harrison actually has a Team Stand-Up at 11am this Friday. Let me check if we can schedule it for 10:30am instead, right after his Design Review.",
        },
        {
            "role": "tool",
            "name": "schedule_cal",
            "tool_call_id": "toolu_01MgoQvH8tY2Qsa5hdQPfuzP",
            "content": "event scheduled!",
        },
    ]


async def test_coerce_single_langchain_message_to_openai_format():
    schema = {
        "type": "object",
        "properties": {"message": {"$ref": "/public/schemas/v1/message.json"}},
    }
    example = {"message": dumpd(HumanMessage(content="Hello, AI!"))}

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "message"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert result["message"] == {"role": "user", "content": "Hello, AI!"}


async def test_coerce_single_langchain_message_to_openai_format_no_path():
    schema = {"$ref": "/public/schemas/v1/message.json"}
    example = dumpd(HumanMessage(content="Hello, AI!"))

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert result == {"role": "user", "content": "Hello, AI!"}


async def test_message_without_content():
    schema = {
        "type": "object",
        "properties": {"message": {"$ref": "/public/schemas/v1/message.json"}},
    }
    example = {"message": {"role": "user"}}  # Message without content

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "message"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    validation_errors = []
    validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
        validation_errors=validation_errors,
    )
    assert len(validation_errors) == 1


async def test_coerce_message_chunks_to_openai_format():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            }
        },
    }

    example = {
        "messages": dumpd(
            [
                HumanMessageChunk(content="Hello"),
                AIMessageChunk(
                    content="Hi there",
                    tool_call_chunks=[
                        ToolCallChunk(
                            name="foo", args='{"a":', index=0, id="tool-call-id"
                        )
                    ],
                ),
                ToolMessageChunk(content="Tool info", tool_call_id="tool_1"),
                SystemMessageChunk(content="System info"),
            ]
        )
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "messages"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert result is not None
    assert result["messages"] == [
        {"role": "user", "content": "Hello"},
        {
            "role": "assistant",
            "tool_calls": [
                {
                    "type": "function",
                    "id": "tool-call-id",
                    "function": {"name": "foo", "arguments": "{}"},
                }
            ],
            "content": "Hi there",
        },
        {"role": "tool", "tool_call_id": "tool_1", "content": "Tool info"},
        {"role": "system", "content": "System info"},
    ]


async def test_coerce_with_correct_transformation():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            },
            "tools": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/tooldef.json"},
            },
        },
        "required": ["messages"],
    }

    example = {
        "messages": dumpd(
            [
                HumanMessage(content="Hello, AI!"),
                AIMessage(
                    content="Hello! How can I help?",
                    tool_calls=[
                        {
                            "name": "GetDeliveryDate",
                            "args": {"order_id": "foo"},
                            "id": "call_12345",
                        }
                    ],
                ),
                ToolMessage(
                    content="The total price including tax is $107.",
                    tool_call_id="call_12345",
                ),
            ]
        ),
        "tools": [
            {
                "name": "GetDeliveryDate",
                "description": "Get the delivery date for a customer's order. Call this whenever you need to know the delivery date, for example when a customer asks 'Where is my package'",
                "input_schema": {
                    "properties": {
                        "order_id": {
                            "description": "The customer's order ID.",
                            "type": "string",
                        }
                    },
                    "required": ["order_id"],
                    "type": "object",
                },
            }
        ],
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "messages"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        ),
        schemas.DatasetTransformation(
            path=["inputs", "tools"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_tool,
        ),
    ]

    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )

    assert result is not None
    # Verify messages are in OpenAI format
    assert result["messages"] == [
        {"role": "user", "content": "Hello, AI!"},
        {
            "role": "assistant",
            "content": "Hello! How can I help?",
            "tool_calls": [
                {
                    "type": "function",
                    "id": "call_12345",
                    "function": {
                        "name": "GetDeliveryDate",
                        "arguments": '{"order_id": "foo"}',
                    },
                }
            ],
        },
        {
            "role": "tool",
            "tool_call_id": "call_12345",
            "content": "The total price including tax is $107.",
        },
    ]
    # Verify tools are in OpenAI format
    assert len(result["tools"]) == 1
    assert result["tools"][0]["type"] == "function"
    assert result["tools"][0]["function"]["name"] == "GetDeliveryDate"


async def test_no_coercion_without_transformation():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            },
            "tools": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/tooldef.json"},
            },
        },
    }

    example = {
        "messages": dumpd(
            [
                HumanMessage(content="Hello, AI!"),
                AIMessage(content="Hello! How can I help?"),
            ]
        ),
        "tools": dumpd([my_tool, my_structured_tool]),
    }

    validation_errors = []
    validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        validation_errors=validation_errors,
    )
    assert len(validation_errors) == 1


async def test_partial_coercion_with_specific_transformation():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            },
            "tools": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/tooldef.json"},
            },
        },
    }

    example = {
        "messages": dumpd(
            [
                HumanMessage(content="Hello, AI!"),
                AIMessage(content="Hello! How can I help?"),
            ]
        ),
        "tools": dumpd([my_tool, my_structured_tool]),
    }

    # Only transform messages, not tools
    transformations = [
        schemas.DatasetTransformation(
            path=["inputs", "messages"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    validation_errors = []
    validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
        validation_errors=validation_errors,
    )
    assert len(validation_errors) == 1


CHAT_OUTPUT_TRANSFORMATION_CASES = [
    {
        "output": {
            "id": "chatcmpl-AODP0z7Wy9ReGIIIQ7NlNPRDeBl48",
            "choices": [
                {
                    "finish_reason": "tool_calls",
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "tool_calls": [
                            {
                                "id": "call_EhSViLNoZIteisVrLCqL0f9H",
                                "function": {
                                    "arguments": '{"location":"Boston","unit":"c"}',
                                    "name": "get_weather",
                                },
                                "type": "function",
                            }
                        ],
                    },
                }
            ],
            "created": 1730334754,
            "model": "gpt-4o-2024-08-06",
            "object": "chat.completion",
            "system_fingerprint": "fp_159d8341cc",
            "usage": {
                "completion_tokens": 18,
                "prompt_tokens": 70,
                "total_tokens": 88,
                "completion_tokens_details": {"reasoning_tokens": 0},
                "prompt_tokens_details": {"cached_tokens": 0},
            },
        }
    },
    {
        "output": {
            "content": "",
            "additional_kwargs": {
                "tool_calls": [
                    {
                        "id": "call_lvQr2CiUaC6DPAwoe1MpMFMJ",
                        "function": {
                            "arguments": '{"location":"Boston","unit":"c"}',
                            "name": "GetWeather",
                        },
                        "type": "function",
                    }
                ],
                "refusal": None,
            },
            "response_metadata": {
                "token_usage": {
                    "completion_tokens": 18,
                    "prompt_tokens": 70,
                    "total_tokens": 88,
                    "completion_tokens_details": {"reasoning_tokens": 0},
                    "prompt_tokens_details": {"cached_tokens": 0},
                },
                "model_name": "gpt-4o-2024-08-06",
                "system_fingerprint": "fp_45cf54deae",
                "finish_reason": "tool_calls",
                "logprobs": None,
            },
            "type": "ai",
            "id": "run-ae521978-510c-4221-9520-b48108d9faf9-0",
            "example": False,
            "tool_calls": [
                {
                    "name": "GetWeather",
                    "args": {"location": "Boston", "unit": "c"},
                    "id": "call_lvQr2CiUaC6DPAwoe1MpMFMJ",
                    "type": "tool_call",
                }
            ],
            "invalid_tool_calls": [],
            "usage_metadata": {
                "input_tokens": 70,
                "output_tokens": 18,
                "total_tokens": 88,
                "input_token_details": {"cache_read": 0},
                "output_token_details": {"reasoning": 0},
            },
        }
    },
    {
        "generations": [
            [
                {
                    "text": "",
                    "generation_info": None,
                    "type": "ChatGeneration",
                    "message": {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "schema", "messages", "AIMessage"],
                        "kwargs": {
                            "content": [
                                {
                                    "id": "toolu_015HAJpTdRYNCCrcUa3wEWua",
                                    "input": {
                                        "setup": "I'm sorry, but I don't have access to real-time weather information. However, I can tell you a weather-related joke about Boston. Why did the Bostonian bring an umbrella to the party?",
                                        "punchline": "Because they heard there was going to be a wicked storm!",
                                    },
                                    "name": "Joke",
                                    "type": "tool_use",
                                }
                            ],
                            "response_metadata": {
                                "id": "msg_01HMiFybab4myCSApj3QZBtr",
                                "model": "claude-3-5-sonnet-20240620",
                                "stop_reason": "tool_use",
                                "stop_sequence": None,
                                "usage": {"input_tokens": 377, "output_tokens": 109},
                            },
                            "type": "ai",
                            "id": "run-6d0ac1d5-0611-466b-9a53-1b43f8783878-0",
                            "tool_calls": [
                                {
                                    "name": "Joke",
                                    "args": {
                                        "setup": "I'm sorry, but I don't have access to real-time weather information. However, I can tell you a weather-related joke about Boston. Why did the Bostonian bring an umbrella to the party?",
                                        "punchline": "Because they heard there was going to be a wicked storm!",
                                    },
                                    "id": "toolu_015HAJpTdRYNCCrcUa3wEWua",
                                    "type": "tool_call",
                                }
                            ],
                            "usage_metadata": {
                                "input_tokens": 377,
                                "output_tokens": 109,
                                "total_tokens": 486,
                                "input_token_details": {},
                            },
                            "invalid_tool_calls": [],
                        },
                    },
                }
            ]
        ],
        "llm_output": {
            "id": "msg_01HMiFybab4myCSApj3QZBtr",
            "model": "claude-3-5-sonnet-20240620",
            "stop_reason": "tool_use",
            "stop_sequence": None,
            "usage": {"input_tokens": 377, "output_tokens": 109},
        },
        "run": None,
        "type": "LLMResult",
    },
]


@pytest.mark.xfail(reason="Known issue with oss message conversion")
@pytest.mark.parametrize("example", CHAT_OUTPUT_TRANSFORMATION_CASES)
async def test_chat_output_transformation(example):
    schema = {
        "type": "object",
        "properties": {
            "message": {"$ref": "/public/schemas/v1/message.json"},
        },
    }

    transformations = [
        schemas.DatasetTransformation(
            path=["outputs", "message"],
            transformation_type=schemas.DatasetTransformationType.convert_to_openai_message,
        )
    ]

    response, _ = validate_and_transform_example_json(
        field="outputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
        transformations=transformations,
    )
    assert response is not None


async def test_validate_schema_with_image_url():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            },
        },
    }
    example = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "<prompt>"},
                    {"type": "image_url", "image_url": {"url": "<base64>"}},
                ],
            }
        ]
    }
    result, _ = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
    )

    assert result is not None


async def test_validate_schema_failure():
    schema = {
        "type": "object",
        "properties": {
            "messages": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/message.json"},
            },
            "tools": {
                "type": "array",
                "items": {"$ref": "/public/schemas/v1/tooldef.json"},
            },
        },
    }
    example = {
        "messages": [
            {
                "role": "user",
                "continent": "North America",
            }
        ]
    }
    _, errors = validate_and_transform_example_json(
        field="inputs",
        example=example,
        schema=schema,
        ff_w_chat_support=True,
    )

    assert len(errors) > 0
