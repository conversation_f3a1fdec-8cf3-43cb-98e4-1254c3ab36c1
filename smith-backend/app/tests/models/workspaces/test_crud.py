import datetime
import uuid
from unittest.mock import AsyncMock, patch

import asyncpg
import pytest
from fastapi import HTTPException
from host.models import host_metadata_crud
from lc_config.tenant_config import OrganizationConfig

from app import config, crud, schemas
from app.api.auth.schemas import OrganizationRoles, TenantlessAuthInfo
from app.hub import schema as hub_schema
from app.hub.crud import commits, repos
from app.models.api_keys import crud as api_keys_crud
from app.models.bulk_exports import crud as bulk_exports_crud
from app.models.charts import create as charts_crud
from app.models.datasets import comparative as comparative_crud
from app.models.feedback import tokens as feedback_tokens_crud
from app.models.identities import crud as identities_crud
from app.models.model_price_map import fetch as model_price_map_fetch
from app.models.model_price_map import ingest as model_price_map_create
from app.models.organizations.payment import auth_has_reached_max_workspaces
from app.models.organizations.roles import list_roles
from app.models.prompt_optimization_jobs import crud as prompt_optimization_jobs_crud
from app.models.service_accounts import crud as service_accounts_crud
from app.models.service_accounts import schemas as service_accounts_schemas
from app.models.tenants import tags as tags_crud
from app.models.tracer_sessions import create as tracer_sessions_crud
from app.models.ttl_settings import crud as ttl_settings_crud
from app.models.usage_limits import user_defined_limits
from app.models.workspaces import crud as workspaces_crud
from app.tests.ensure import DecodedUserInfo, DecodedUserMetadata, ensure_user
from app.tests.utils import fresh_tenant_client, org_auth_from_auth


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
async def test_has_reached_max_workspaces(
    db_asyncpg: asyncpg.Connection, use_api_key: bool
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        org_auth = org_auth_from_auth(
            authed_client.auth, OrganizationConfig(max_workspaces=2)
        )
        assert await auth_has_reached_max_workspaces(db_asyncpg, org_auth) is False

        org_auth = org_auth_from_auth(
            authed_client.auth, OrganizationConfig(max_workspaces=-1)
        )
        assert await auth_has_reached_max_workspaces(db_asyncpg, org_auth) is False


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_delete_workspace_without_traces(
    mock_validate_bulk_export_destination_connection: AsyncMock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    manifest_one: dict,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        org_auth = org_auth_from_auth(
            authed_client.auth,
            OrganizationConfig(max_workspaces=2, can_use_bulk_export=True),
        )
        auth.tenant_config.organization_config = org_auth.org_config

        # create resources & invite users
        await api_keys_crud.create_api_key(
            auth, schemas.APIKeyCreateRequest(description="test key")
        )
        model_price_map = await model_price_map_create.create_model_price_map(
            schemas.ModelPriceMapUpdateSchema(
                name="cascade_test",
                match_pattern="^cascade_test$",
                prompt_cost=1,
                completion_cost=1,
            ),
            auth.tenant_id,
        )
        integration = await host_metadata_crud.create_integration(
            tenant_id=auth.tenant_id,
            req=host_metadata_crud.CreateHostIntegrationRequest(
                namespace_id="cascade_test",
                provider=host_metadata_crud.HostIntegrationProvider.GITHUB,
            ),
        )
        role_id = next(
            x.id
            for x in await list_roles(org_auth)
            if x.name == OrganizationRoles.ADMIN.value
        )

        await identities_crud.invite_user_to_org(
            org_auth,
            schemas.PendingIdentityCreateInternal(
                email="<EMAIL>",
                access_scope=schemas.AccessScope.organization,
                role_id=role_id,
            ),
        )

        # leave one member as pending
        await identities_crud.invite_user_to_org(
            org_auth,
            schemas.PendingIdentityCreateInternal(
                email="<EMAIL>",
                access_scope=schemas.AccessScope.organization,
                role_id=role_id,
            ),
        )

        new_user_id = uuid.uuid4()
        user = await ensure_user(
            DecodedUserInfo(
                sub="nbd",
                id=str(new_user_id),
                email="<EMAIL>",
                full_name="Test User",
                user_metadata=DecodedUserMetadata(
                    avatar_url="https://example.com/avatar.png"
                ),
            )
        )

        await identities_crud.claim_pending_org_identity(
            TenantlessAuthInfo(
                available_tenants=[auth.tenant_id],
                available_organizations=[auth.organization_id],
                ls_user_id=user["ls_user_id"],
                user_id=new_user_id,
                user_email="<EMAIL>",
            ),
            auth.organization_id,
        )

        ws_members = await identities_crud.get_tenant_members(auth)
        assert len(ws_members.members) == 2
        assert len(ws_members.pending) == 1

        # Datasets
        dataset = await crud.create_dataset(
            auth,
            schemas.DatasetCreate(
                name="cascade_test",
                description="cascade_test",
            ),
        )
        assert dataset is not None
        datasets = await db_asyncpg.fetch(
            "SELECT FROM dataset WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(datasets) == 1

        # Tracer sessions (tracer_session)
        tracer_session = await tracer_sessions_crud.create_tracer_session(
            db_asyncpg,
            auth,
            schemas.TracerSessionCreate(
                name="cascade_test",
                description="cascade_test",
                trace_tier=schemas.TraceTier.longlived,
                reference_dataset_id=dataset.id,
            ),
            upsert=True,
        )
        assert tracer_session is not None
        sessions = await db_asyncpg.fetch(
            "SELECT FROM tracer_session WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(sessions) == 1

        # Hub repos (hub_repos)
        repo = await repos.create_repo(
            db_asyncpg,
            auth,
            hub_schema.CreateRepoRequest(
                repo_handle="cascade_test",
                description="cascade_test",
                is_public=True,
            ),
        )
        repos_list = await db_asyncpg.fetch(
            "SELECT FROM hub_repos WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(repos_list) == 1

        # Hub commits (hub_commits)
        commit = await commits.create_commit(
            db_asyncpg,
            auth,
            commits.CreateCommitRequest(
                owner=None,
                repo=repo.repo_handle,
                manifest=manifest_one,
                example_run_ids=[],
            ),
        )

        hub_commits = await db_asyncpg.fetch(
            "SELECT FROM hub_commits WHERE repo_id = $1",
            repo.id,
        )
        assert len(hub_commits) == 1

        # Hub commit downloads (hub_commit_downloads)
        # Fetching commit manifest creates a download record
        await commits.get_commit_manifest(
            db_asyncpg,
            auth,
            owner=None,
            repo=repo.repo_handle,
            commit_hash_or_tag=commit.commit_hash,
            get_examples=True,
            is_view=False,
        )

        hub_commit_downloads = await db_asyncpg.fetch(
            "SELECT FROM hub_commit_downloads WHERE downloaded_by = $1",
            auth.tenant_id,
        )
        assert len(hub_commit_downloads) == 1

        # Annotation queues (annotation_queues)
        anno_queue = await crud.create_annotation_queue(
            db_asyncpg,
            auth,
            queue=schemas.AnnotationQueueCreateSchema(
                name="cascade_test",
                description="cascade_test",
            ),
        )

        # Per customer trace tier settings (per_customer_trace_tier_settings)
        await ttl_settings_crud.upsert_ttl_settings(
            auth,
            upsertReq=ttl_settings_crud.UpsertTTLSettingsRequest(
                tenant_id=auth.tenant_id,
                default_trace_tier=schemas.TraceTier.longlived,
            ),
        )

        tier_for_tenant = await db_asyncpg.fetch(
            "SELECT FROM per_customer_trace_tier_settings WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(tier_for_tenant) == 1

        # Playground settings (playground_settings)
        await crud.create_playground_settings(
            db_asyncpg,
            auth,
            settings=schemas.PlaygroundSettingsCreateRequest(
                name="cascade_test",
                description="cascade_test",
                settings={},
            ),
        )

        playground_settings = await db_asyncpg.fetch(
            "SELECT FROM playground_settings WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(playground_settings) == 1

        # Run manifests (run_manifests)
        await db_asyncpg.fetch(
            "INSERT INTO run_manifests (tenant_id, manifest, display_name) VALUES ($1, $2, $3)",
            auth.tenant_id,
            {"a": 1},
            "cascade_test",
        )

        # Charts (custom_chart), chart sections (custom_chart_section), chart series (custom_chart_series)
        section = await charts_crud.create_custom_chart_section(
            auth=auth,
            section=schemas.CustomChartsSectionCreateInternal(
                title="Dashboard 1",
                index=0,
                access_scope=schemas.AccessScope.workspace,
            ),
        )

        create_by_ws_chart_req = schemas.CustomChartCreateInternal(
            title="cascade_test",
            description="cascade_test",
            chart_type="line",
            section_id=section.id,
            access_scope=schemas.AccessScope.workspace,
            series=[
                schemas.CustomChartSeriesCreate(
                    name="cascade_test",
                    filters=schemas.CustomChartSeriesFilters(
                        filter="eq(is_root, true)",
                        session=[str(tracer_session.id)],
                    ),
                    metric=schemas.CustomChartMetric.run_count,
                )
            ],
        )

        chart = await charts_crud.create_custom_chart(
            auth,
            chart=create_by_ws_chart_req,
        )
        assert chart is not None

        custom_charts = await db_asyncpg.fetch(
            "SELECT FROM custom_chart WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(custom_charts) == 1

        custom_chart_sections = await db_asyncpg.fetch(
            "SELECT FROM custom_chart_section WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(custom_chart_sections) == 1

        custom_chart_series = await db_asyncpg.fetch(
            "SELECT FROM custom_chart_series WHERE custom_chart_id = $1",
            chart.id,
        )
        assert len(custom_chart_series) == 1

        # Per tenant usage limits (per_tenant_usage_limits)
        limit = await user_defined_limits.upsert_user_defined_usage_limit(
            auth,
            usage_limit=user_defined_limits.UpsertUsageLimit(
                limit_type=user_defined_limits.UsageLimitType.MONTHLY_LONGLIVED_TRACES,
                limit_value=100,
            ),
        )
        assert limit is not None
        per_tenant_usage_limits = await db_asyncpg.fetch(
            "SELECT FROM per_tenant_usage_limits WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(per_tenant_usage_limits) == 1

        # Feedback ingest tokens (feedback_ingest_tokens)
        ingest_tokens = await feedback_tokens_crud.create_ingest_tokens(
            auth,
            [
                schemas.FeedbackIngestTokenCreateSchema(
                    run_id=uuid.uuid4(),
                    feedback_key="cascade_test",
                    expires_at=datetime.date.today(),
                )
            ],
        )
        assert len(ingest_tokens) == 1
        ingest_tokens = await db_asyncpg.fetch(
            "SELECT FROM feedback_ingest_tokens WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(ingest_tokens) == 1

        # Comparative experiments (comparative_experiment)
        comparative_experiment = await comparative_crud.create_comparative_experiment(
            auth,
            schemas.ComparativeExperimentCreate(
                name="cascade_test",
                description="cascade_test",
                experiment_ids=[tracer_session.id],
                reference_dataset_id=tracer_session.reference_dataset_id,
            ),
        )
        assert comparative_experiment is not None
        comparative_experiments = await db_asyncpg.fetch(
            "SELECT FROM comparative_experiment WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(comparative_experiments) == 1

        # Tag keys (tag_keys)
        tag_key = await tags_crud.create_tag_key(
            auth,
            schemas.TagKeyCreate(
                key="cascade_test",
                description="cascade_test",
            ),
        )
        assert tag_key is not None
        tag_keys = await db_asyncpg.fetch(
            "SELECT FROM tag_keys WHERE key = 'cascade_test' AND tenant_id = $1",
            auth.tenant_id,
        )
        assert len(tag_keys) == 1

        # Prompt optimization jobs (prompt_optimization_jobs)
        prompt_optim_job = (
            await prompt_optimization_jobs_crud.create_prompt_optimization_job(
                job=schemas.OptimizePromptJobRequest(
                    algorithm=schemas.EPromptOptimizationAlgorithm.demo,
                    config=schemas.DemoConfig(
                        message_index=0,
                        metaprompt={},
                        examples=[{}],
                        overall_feedback="cascade_test",
                    ),
                    prompt_name="cascade_test",
                ),
                auth=auth,
            )
        )
        assert prompt_optim_job is not None
        jobs = await db_asyncpg.fetch(
            "SELECT FROM prompt_optimization_jobs WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(jobs) == 1

        # Bulk export destinations (bulk_export_destinations)
        bulk_export_destination = (
            await bulk_exports_crud.create_bulk_export_destination(
                auth,
                schemas.BulkExportDestinationCreate(
                    display_name="cascade_test",
                    config=schemas.BulkExportDestinationS3Config(
                        endpoint_url="cascade_test",
                        prefix="cascade_test",
                        bucket_name="cascade_test",
                        region="cascade_test",
                    ),
                    credentials=schemas.BulkExportDestinationS3Credentials(
                        access_key_id="cascade_test",
                        secret_access_key="cascade_test",
                    ),
                ),
            )
        )
        assert bulk_export_destination is not None
        bulk_export_destinations = await db_asyncpg.fetch(
            "SELECT FROM bulk_export_destinations WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(bulk_export_destinations) == 1

        bulk_export = await bulk_exports_crud.create_bulk_export(
            auth,
            schemas.BulkExportCreate(
                bulk_export_destination_id=bulk_export_destination.id,
                session_id=tracer_session.id,
                start_time=datetime.datetime.now() - datetime.timedelta(days=1),
                end_time=datetime.datetime.now(),
            ),
        )
        assert bulk_export is not None
        bulk_exports = await db_asyncpg.fetch(
            "SELECT FROM bulk_exports WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(bulk_exports) == 1

        assert mock_validate_bulk_export_destination_connection.called

        # Quick actions (quick_actions) -- using raw SQL for now - crud is in smith-go/prompt-canvas
        await db_asyncpg.execute(
            "INSERT INTO quick_actions (tenant_id, name, prompt, description, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, $6)",
            auth.tenant_id,
            "cascade_test",
            "cascade_test",
            "cascade_test",
            datetime.datetime.now(),
            datetime.datetime.now(),
        )
        quick_actions = await db_asyncpg.fetch(
            "SELECT FROM quick_actions WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(quick_actions) == 1

        # Run rules (run_rules) -- using raw SQL for now to bypass validation in crud
        await db_asyncpg.execute(
            "INSERT INTO run_rules (tenant_id, display_name, sampling_rate, created_at, updated_at) VALUES ($1, $2, $3, $4, $5)",
            auth.tenant_id,
            "cascade_test",
            1.0,
            datetime.datetime.now(),
            datetime.datetime.now(),
        )
        run_rules = await db_asyncpg.fetch(
            "SELECT FROM run_rules WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(run_rules) == 1

        # Service accounts
        sa = await service_accounts_crud.create_service_account(
            auth,
            payload=service_accounts_schemas.ServiceAccountCreateRequest(
                name="cascade_test"
            ),
        )
        await db_asyncpg.fetch(
            "UPDATE service_accounts SET default_workspace_id = $1 WHERE id = $2",
            auth.tenant_id,
            sa.id,
        )

        # Host projects (host_projects)
        await db_asyncpg.execute(
            "INSERT INTO host_projects (tenant_id, name, description, knative, tracer_session_id, created_at, updated_at) "
            "VALUES ($1, $2, $3, $4, $5, $6, $7)",
            auth.tenant_id,
            "cascade_test",
            "cascade_test",
            "'{}'::jsonb",
            tracer_session.id,
            datetime.datetime.now(),
            datetime.datetime.now(),
        )
        host_projects = await db_asyncpg.fetch(
            "SELECT FROM host_projects WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(host_projects) == 1

        # Tasks (tasks)
        await db_asyncpg.execute(
            "INSERT INTO tasks (id, tenant_id, args, status, target) VALUES ($1, $2, $3, $4, $5)",
            str(uuid.uuid4()),
            auth.tenant_id,
            "'{}'::jsonb",
            "cascade_test",
            "cascade_test",
        )
        tasks = await db_asyncpg.fetch(
            "SELECT FROM tasks WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(tasks) == 1

        await workspaces_crud.delete_workspace(
            db_asyncpg,
            auth,
            auth.tenant_id,
        )

        with pytest.raises(HTTPException) as exc:
            ws_members = await identities_crud.get_tenant_members(auth)
        assert exc.value.status_code == 404

        api_keys = await api_keys_crud.list_api_keys(auth)
        assert len(api_keys) == 0

        model_price_maps = await model_price_map_fetch.fetch_model_price_map(
            auth.tenant_id
        )
        assert model_price_map.id not in [x.id for x in model_price_maps]

        with pytest.raises(HTTPException) as exc:
            await host_metadata_crud.get_integration(auth.tenant_id, integration.id)
        assert exc.value.status_code == 400

        # Test other cascade delete relationships

        # Tracer sessions (tracer_session)
        tracer_sessions = await db_asyncpg.fetch(
            "SELECT FROM tracer_session WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(tracer_sessions) == 0

        # Hub repos (hub_repos), hub commits (hub_commits), hub commit downloads (hub_commit_downloads)
        repos_list = await db_asyncpg.fetch(
            "SELECT FROM hub_repos WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(repos_list) == 0

        hub_commits = await db_asyncpg.fetch(
            "SELECT FROM hub_commits WHERE repo_id = $1",
            repo.id,
        )
        assert len(hub_commits) == 0

        hub_commit_downloads = await db_asyncpg.fetch(
            "SELECT FROM hub_commit_downloads WHERE downloaded_by = $1",
            auth.tenant_id,
        )
        assert len(hub_commit_downloads) == 0

        # Annotation queues (annotation_queues)
        queues = await crud.get_annotation_queues(
            db_asyncpg,
            auth,
            query_params=schemas.QueryParamsForAnnotationQueueSchema(
                ids=[anno_queue.id]
            ),
        )
        assert len(queues[0]) == 0

        # Per customer trace tier settings (per_customer_trace_tier_settings)
        tier_for_tenant = await db_asyncpg.fetch(
            "SELECT FROM per_customer_trace_tier_settings WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(tier_for_tenant) == 0

        # Playground settings (playground_settings)
        playground_settings = await db_asyncpg.fetch(
            "SELECT FROM playground_settings WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(playground_settings) == 0

        # Run manifests (run_manifests)
        run_manifests = await db_asyncpg.fetch(
            "SELECT FROM run_manifests WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(run_manifests) == 0

        # Charts (custom_chart), chart sections (custom_chart_section), chart series (custom_chart_series)
        custom_charts = await db_asyncpg.fetch(
            "SELECT FROM custom_chart WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(custom_charts) == 0

        custom_chart_sections = await db_asyncpg.fetch(
            "SELECT FROM custom_chart_section WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(custom_chart_sections) == 0

        custom_chart_series = await db_asyncpg.fetch(
            "SELECT FROM custom_chart_series WHERE custom_chart_id = $1",
            chart.id,
        )
        assert len(custom_chart_series) == 0

        # Per tenant usage limits (per_tenant_usage_limits)
        limits = await db_asyncpg.fetch(
            "SELECT FROM per_tenant_usage_limits WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(limits) == 0

        # Feedback ingest tokens (feedback_ingest_tokens)
        feedback_ingest_tokens = await db_asyncpg.fetch(
            "SELECT FROM feedback_ingest_tokens WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(feedback_ingest_tokens) == 0

        # Comparative experiments (comparative_experiment)
        comparative_experiment = await db_asyncpg.fetch(
            "SELECT FROM comparative_experiment WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(comparative_experiment) == 0

        # Tag keys (tag_keys)
        tag_keys = await db_asyncpg.fetch(
            "SELECT FROM tag_keys WHERE key = 'cascade_test' AND tenant_id = $1",
            auth.tenant_id,
        )
        assert len(tag_keys) == 0

        # Prompt optimization jobs (prompt_optimization_jobs)
        prompt_optim_jobs = await db_asyncpg.fetch(
            "SELECT FROM prompt_optimization_jobs WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(prompt_optim_jobs) == 0

        # Bulk export_destinations (bulk_export_destinations)
        bulk_export_destinations = await db_asyncpg.fetch(
            "SELECT FROM bulk_export_destinations WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(bulk_export_destinations) == 0

        # Bulk exports (bulk_exports)
        bulk_exports = await db_asyncpg.fetch(
            "SELECT FROM bulk_exports WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(bulk_exports) == 0

        # Quick actions (quick_actions)
        quick_actions = await db_asyncpg.fetch(
            "SELECT FROM quick_actions WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(quick_actions) == 0

        # Run rules (run_rules)
        run_rules = await db_asyncpg.fetch(
            "SELECT FROM run_rules WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(run_rules) == 0

        # Service accounts (service_accounts)
        service_accounts_for_tier = await db_asyncpg.fetch(
            "SELECT FROM service_accounts WHERE default_workspace_id = $1",
            auth.tenant_id,
        )
        assert len(service_accounts_for_tier) == 0

        # Host projects (host_projects)
        host_projects = await db_asyncpg.fetch(
            "SELECT FROM host_projects WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(host_projects) == 0

        # Tasks (tasks)
        tasks = await db_asyncpg.fetch(
            "SELECT FROM tasks WHERE tenant_id = $1",
            auth.tenant_id,
        )
        assert len(tasks) == 0


@pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)
async def test_workspace_foreign_keys_cascade(
    db_asyncpg: asyncpg.Connection,
):
    # Assert on presence of foreign keys and CASCADE behavior
    foreign_keys = await db_asyncpg.fetch(
        """
        SELECT conname, pg_get_constraintdef(oid, true) as condef
        FROM pg_constraint
        WHERE contype = 'f' AND confrelid = 'tenants'::regclass
        ORDER BY conname
        """
    )

    foreign_key_names = [x["conname"] for x in foreign_keys]
    foreign_key_defs = [x["condef"] for x in foreign_keys]
    for fkd in foreign_key_defs:
        assert fkd.endswith("ON DELETE CASCADE") or fkd.endswith("ON DELETE SET NULL")

    assert foreign_key_names == [
        "alert_actions_tenant_id_fkey",
        "alert_rules_tenant_id_fkey",
        "annotation_queues_tenant_id_fkey",
        "api_keys_tenant_id_fkey",
        "bulk_export_destinations_tenant_id_fkey",
        "bulk_exports_tenant_id_fkey",
        "comparative_experiment_tenant_id_fkey",
        "custom_chart_section_tenant_id_fkey",
        "custom_chart_series_workspace_id_fkey",
        "custom_chart_tenant_id_fkey",
        "dataset_tenant_id_fkey",
        "feedback_configs_tenant_id_fkey",
        "feedback_ingest_tokens_tenant_id_fkey",
        "feedbacks_tenant_id_fkey",
        "host_integration_namespace_ids_tenant_id_fkey",
        "host_projects_tenant_id_fkey",
        "hub_commit_downloads_downloaded_by_fkey",
        "hub_repo_likes_liked_by_fkey",
        "hub_repos_tenant_id_fkey",
        "identities_tenant_id_fkey",
        "model_price_map_tenant_id_fkey",
        "pending_identities_tenant_id_fkey",
        "per_customer_trace_tier_settings_tenant_id_fkey",
        "per_tenant_usage_limits_tenant_id_fkey",
        "playground_settings_tenant_id_fkey",
        "prompt_optimization_jobs_tenant_id_fkey",
        "prompt_webhooks_tenant_id_fkey",
        "quick_actions_tenant_id_fkey",
        "run_manifests_tenant_id_fkey",
        "run_rules_tenant_id_fkey",
        "scim_groups_workspace_id_fkey",
        "service_accounts_default_workspace_id_fkey",
        "tag_keys_tenant_id_fkey",
        "tasks_tenant_id_fkey",
        "tracer_session_tenant_id_fkey",
    ]
