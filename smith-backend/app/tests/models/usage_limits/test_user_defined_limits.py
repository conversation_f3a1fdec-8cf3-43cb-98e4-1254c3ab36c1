import uuid

import asyncpg
import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from pydantic import ValidationError

from app import config
from app.models.usage_limits import user_defined_limits
from app.tests.utils import (
    fresh_tenant_client,
)


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_upsert_usage_limit(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        id = uuid.uuid4()
        limit = await user_defined_limits.upsert_user_defined_usage_limit(
            auth,
            user_defined_limits.UpsertUsageLimit(
                id=id,
                limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
                limit_value=100,
            ),
        )

        assert limit.id == id
        assert limit.tenant_id == auth.tenant_id
        assert limit.limit_type == user_defined_limits.UsageLimitType.MONTHLY_TRACES
        assert limit.limit_value == 100
        assert limit.created_at == limit.updated_at

        updated_limit = await user_defined_limits.upsert_user_defined_usage_limit(
            auth,
            user_defined_limits.UpsertUsageLimit(
                limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
                limit_value=200,
            ),
        )

        assert updated_limit.tenant_id == auth.tenant_id
        assert (
            updated_limit.limit_type
            == user_defined_limits.UsageLimitType.MONTHLY_TRACES
        )
        assert updated_limit.limit_value == 200
        assert updated_limit.id == limit.id
        assert updated_limit.created_at == limit.created_at
        assert updated_limit.updated_at > limit.updated_at

        negative_limit = await user_defined_limits.upsert_user_defined_usage_limit(
            auth,
            user_defined_limits.UpsertUsageLimit(
                limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
                limit_value=-1,
            ),
        )

        assert negative_limit.tenant_id == auth.tenant_id
        assert (
            negative_limit.limit_type
            == user_defined_limits.UsageLimitType.MONTHLY_TRACES
        )
        assert negative_limit.limit_value == -1
        assert negative_limit.id == limit.id
        assert negative_limit.created_at == limit.created_at
        assert negative_limit.updated_at > limit.updated_at


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_non_existent_limit(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        with pytest.raises(ValidationError):
            await user_defined_limits.upsert_user_defined_usage_limit(
                auth,
                user_defined_limits.UpsertUsageLimit(
                    limit_type="some_fake_limit",
                    limit_value=100,
                ),
            )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_list_usage_limits(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth

        limits = await user_defined_limits.list_user_defined_usage_limits(auth)

        assert len(limits) == 0

        await user_defined_limits.upsert_user_defined_usage_limit(
            auth,
            user_defined_limits.UpsertUsageLimit(
                limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
                limit_value=100,
            ),
        )

        cached_limits_after_upsert = (
            await user_defined_limits.list_user_defined_usage_limits(auth)
        )
        assert len(cached_limits_after_upsert) == 0

        uncached_limits_after_upsert = (
            await user_defined_limits.list_user_defined_usage_limits_uncached(auth)
        )

        assert len(uncached_limits_after_upsert) == 1
        assert uncached_limits_after_upsert[0].tenant_id == auth.tenant_id
        assert (
            uncached_limits_after_upsert[0].limit_type
            == user_defined_limits.UsageLimitType.MONTHLY_TRACES
        )
        assert uncached_limits_after_upsert[0].limit_value == 100


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_list_org_usage_limits(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        org_auth = authed_client.org_auth

        limits = await user_defined_limits.list_org_user_defined_usage_limits_uncached(
            org_auth
        )

        assert len(limits) == 0

        await user_defined_limits.upsert_user_defined_usage_limit(
            auth,
            user_defined_limits.UpsertUsageLimit(
                limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
                limit_value=100,
            ),
        )

        uncached_limits_after_upsert = (
            await user_defined_limits.list_org_user_defined_usage_limits_uncached(
                org_auth
            )
        )

        assert len(uncached_limits_after_upsert) == 1

        async with fresh_tenant_client(
            db_asyncpg, use_api_key, org_id=auth.organization_id, user_id=auth.user_id
        ) as authed_client2:
            tenant_two_auth = authed_client2.auth

            await user_defined_limits.upsert_user_defined_usage_limit(
                tenant_two_auth,
                user_defined_limits.UpsertUsageLimit(
                    limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
                    limit_value=100,
                ),
            )

        uncached_limits_after_upsert = (
            await user_defined_limits.list_org_user_defined_usage_limits_uncached(
                org_auth
            )
        )

        assert len(uncached_limits_after_upsert) == 2
        assert uncached_limits_after_upsert[0].tenant_id == auth.tenant_id
        assert uncached_limits_after_upsert[1].tenant_id == tenant_two_auth.tenant_id
        assert (
            uncached_limits_after_upsert[0].limit_type
            == user_defined_limits.UsageLimitType.MONTHLY_TRACES
        )
        assert uncached_limits_after_upsert[0].limit_value == 100


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_cannot_list_other_tenant_usage_limits(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client_tenant_one:
        tenant_one_auth = authed_client_tenant_one.auth
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        org_id=tenant_one_auth.organization_id,
        user_id=tenant_one_auth.user_id,
    ) as authed_client_tenant_two:
        tenant_two_auth = authed_client_tenant_two.auth

    await user_defined_limits.upsert_user_defined_usage_limit(
        tenant_one_auth,
        user_defined_limits.UpsertUsageLimit(
            limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
            limit_value=100,
        ),
    )

    limits = await user_defined_limits.list_user_defined_usage_limits(tenant_two_auth)

    assert len(limits) == 0


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_delete_usage_limit(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    id = uuid.uuid4()
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
    limit = await user_defined_limits.upsert_user_defined_usage_limit(
        auth,
        user_defined_limits.UpsertUsageLimit(
            id=id,
            limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
            limit_value=100,
        ),
    )

    limits = await user_defined_limits.list_user_defined_usage_limits_uncached(auth)
    assert len(limits) == 1

    await user_defined_limits.delete_user_defined_usage_limit(auth, limit.id)

    limits = await user_defined_limits.list_user_defined_usage_limits_uncached(auth)

    assert len(limits) == 0


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_cannot_delete_non_existent_usage_limit(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth

    limits = await user_defined_limits.list_user_defined_usage_limits_uncached(auth)
    assert len(limits) == 0

    non_existent_uuid = uuid.UUID("e8bcbf74-fea2-4c35-a753-0df3266e28f0")

    with pytest.raises(HTTPException) as e:
        await user_defined_limits.delete_user_defined_usage_limit(
            auth, non_existent_uuid
        )

    assert e.value.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_delete_does_not_delete_other_limits(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client_tenant_one:
        tenant_one_auth = authed_client_tenant_one.auth
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        org_id=tenant_one_auth.organization_id,
        user_id=tenant_one_auth.user_id,
    ) as authed_client_tenant_two:
        tenant_two_auth = authed_client_tenant_two.auth

    limit_one = await user_defined_limits.upsert_user_defined_usage_limit(
        tenant_one_auth,
        user_defined_limits.UpsertUsageLimit(
            limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
            limit_value=100,
        ),
    )

    limit_two = await user_defined_limits.upsert_user_defined_usage_limit(
        tenant_two_auth,
        user_defined_limits.UpsertUsageLimit(
            limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
            limit_value=100,
        ),
    )

    limits = await user_defined_limits.list_user_defined_usage_limits_uncached(
        tenant_one_auth
    )
    assert len(limits) == 1

    await user_defined_limits.delete_user_defined_usage_limit(
        tenant_one_auth, limit_one.id
    )

    limits = await user_defined_limits.list_user_defined_usage_limits_uncached(
        tenant_one_auth
    )
    assert len(limits) == 0

    limits = await user_defined_limits.list_user_defined_usage_limits_uncached(
        tenant_two_auth
    )
    assert len(limits) == 1
    assert limits[0] == limit_two
