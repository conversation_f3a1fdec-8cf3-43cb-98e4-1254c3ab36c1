from collections import defaultdict
from datetime import datetime, timezone

from app import schemas
from app.models.charts.stream import _unpack_feedback_datapoints


def make_feedback_series_obj(
    series: dict, feedback_key: str = "key1"
) -> schemas.CustomChartSeries:
    return schemas.CustomChartSeries(
        id=series["id"],
        name=f"series-{series['id']}",
        metric=series.get("metric", schemas.CustomChartMetric.feedback_values),
        feedback_key=feedback_key,
        filters=None,
        workspace_id=None,
        group_by=None,
    )


def test__unpack_categorical_feedback_datapoints() -> None:
    series_id = "series1"
    chart_id = "chart1"
    ts = datetime(2023, 1, 1, tzinfo=timezone.utc)
    val = {"key1": {"values": {"A": 2, "B": 3}, "avg": None}}
    dp = schemas.CustomChartsDataPoint(
        series_id=series_id, timestamp=ts, value=val, group=None
    )
    all_series_data = [dp]

    chart: dict = {"series": [{"id": series_id}], "id": chart_id}
    chart_id_to_chart = {chart_id: chart}
    series_id_to_chart_id = {series_id: chart_id}
    series_id_to_series_obj = {
        s["id"]: make_feedback_series_obj(s) for s in chart["series"]
    }
    categorical_feedback_keys_to_categories: dict[str, set[str]] = defaultdict(set)

    result = _unpack_feedback_datapoints(
        all_series_data,
        chart_id_to_chart,
        series_id_to_chart_id,
        series_id_to_series_obj,
        categorical_feedback_keys_to_categories,
    )

    assert len(result) == 2
    got_ids = {r.series_id for r in result}
    assert got_ids == {f"{series_id}:A", f"{series_id}:B"}

    by_id = {r.series_id: r.value for r in result}
    assert by_id[f"{series_id}:A"] == 2
    assert by_id[f"{series_id}:B"] == 3

    series_ids_in_chart = {s["id"] for s in chart["series"]}
    assert series_ids_in_chart == {f"{series_id}:A", f"{series_id}:B"}

    assert series_id_to_chart_id[f"{series_id}:A"] == chart_id
    assert series_id_to_chart_id[f"{series_id}:B"] == chart_id
    assert f"{series_id}:A" in series_id_to_series_obj
    assert f"{series_id}:B" in series_id_to_series_obj


def test__unpack_categorical_feedback_datapoints_no_categoricals() -> None:
    series_id = "s2"
    chart_id = "c2"
    ts = datetime(2023, 2, 2, tzinfo=timezone.utc)
    dp = schemas.CustomChartsDataPoint(
        series_id=series_id,
        timestamp=ts,
        value={"key1": {"values": {}, "avg": 5}},
        group=None,
    )
    all_series_data = [dp]

    chart: dict = {"series": [{"id": series_id}], "id": chart_id}
    chart_id_to_chart = {chart_id: chart}
    series_id_to_chart_id = {series_id: chart_id}
    series_id_to_series_obj = {
        s["id"]: make_feedback_series_obj(s) for s in chart["series"]
    }
    categorical_feedback_keys_to_categories: dict[str, set[str]] = defaultdict(set)

    result = _unpack_feedback_datapoints(
        all_series_data,
        chart_id_to_chart,
        series_id_to_chart_id,
        series_id_to_series_obj,
        categorical_feedback_keys_to_categories,
    )

    assert len(result) == 1
    out = result[0]
    assert out.series_id == series_id
    assert out.value == 5

    assert chart["series"][0]["id"] == series_id


def test__unpack_categorical_feedback_datapoints_no_feedback():
    series_id = "s3"
    chart_id = "c3"
    ts = datetime(2023, 3, 3, tzinfo=timezone.utc)
    dp = schemas.CustomChartsDataPoint(
        series_id=series_id, timestamp=ts, value=42, group=None
    )
    all_series_data = [dp]

    chart = {"series": [{"id": series_id}], "id": chart_id}
    chart_id_to_chart = {chart_id: chart}
    series_id_to_chart_id = {series_id: chart_id}
    series_id_to_series_obj = {}
    categorical_feedback_keys_to_categories: dict[str, set[str]] = defaultdict(set)

    result = _unpack_feedback_datapoints(
        all_series_data,
        chart_id_to_chart,
        series_id_to_chart_id,
        series_id_to_series_obj,
        categorical_feedback_keys_to_categories,
    )

    assert result == [dp]


def test__unpack_categorical_feedback_datapoints_wrong_metric() -> None:
    series_id = "series1"
    chart_id = "chart1"
    ts = datetime(2023, 1, 1, tzinfo=timezone.utc)
    val = {"key1": {"values": {"A": 2, "B": 3}, "avg": None}}
    dp = schemas.CustomChartsDataPoint(
        series_id=series_id, timestamp=ts, value=val, group=None
    )
    all_series_data = [dp]

    chart: dict = {
        "series": [{"id": series_id, "metric": "feedback_score_avg"}],
        "id": chart_id,
    }
    chart_id_to_chart = {chart_id: chart}
    series_id_to_chart_id = {series_id: chart_id}
    series_id_to_series_obj = {
        s["id"]: make_feedback_series_obj(s) for s in chart["series"]
    }
    categorical_feedback_keys_to_categories: dict[str, set[str]] = defaultdict(set)

    result = _unpack_feedback_datapoints(
        all_series_data,
        chart_id_to_chart,
        series_id_to_chart_id,
        series_id_to_series_obj,
        categorical_feedback_keys_to_categories,
    )

    assert len(result) == 1
    got_ids = {r.series_id for r in result}
    assert got_ids == {series_id}

    by_id = {r.series_id: r.value for r in result}
    assert by_id[series_id] is None
