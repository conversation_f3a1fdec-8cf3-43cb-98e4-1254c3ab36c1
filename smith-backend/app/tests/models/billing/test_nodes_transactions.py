import datetime
import uuid
from typing import Callable, List, NamedTuple
from unittest.mock import patch

import pytest
from lc_database import metronome
from lc_database.database import asyncpg_conn

from app import config
from app.models.billing.usagereporting.nodes import (
    NodesTransactionReporter,
    NodesTransactionWithOrg,
)
from app.models.billing.usagereporting.transactions import (
    UsageReportingStatus,
)
from app.schemas import TraceTransactionSource
from app.tests.models.billing.test_transactions import (
    MockMetronomeClient,
    _create_tenant,
    _execute_with_db,
)

pytestmark = pytest.mark.serial


class NodesTransactionTestCase(NamedTuple):
    name: str
    existing_transactions: List[NodesTransactionWithOrg]
    sent_events: List[dict]
    transactions_after_reporting: List[NodesTransactionWithOrg]
    metronome_client: Callable[..., metronome.JsonHttpClient] = MockMetronomeClient


# Create test cases for nodes transactions
NODES_TRANSACTION_TEST_CASES = [
    NodesTransactionTestCase(
        name="Basic nodes transaction reporting",
        existing_transactions=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000001"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000001"),
                project_id=uuid.UUID("*************-0000-0000-000000000001"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.PENDING.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000001"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
        sent_events=[
            {
                "transaction_id": "00000000-0000-0000-0000-000000000001",
                "customer_id": "*************-0000-0000-000000000001",
                "event_type": "nodes_executed",
                "timestamp": "2025-04-23T00:00:00+00:00",
                "properties": {
                    "tenant_id": "10000000-0000-0000-0000-000000000001",
                    "count": 10,
                    "project_id": "*************-0000-0000-000000000001",
                    "standby_minutes": 0,
                    "deployment_type": "prod",
                    "plan": "cloud",
                },
            }
        ],
        transactions_after_reporting=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000001"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000001"),
                project_id=uuid.UUID("*************-0000-0000-000000000001"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000001"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
    ),
    NodesTransactionTestCase(
        name="Basic nodes transaction reporting, but with nodes_count=0",
        existing_transactions=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000002"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000002"),
                project_id=uuid.UUID("*************-0000-0000-000000000002"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=0,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.PENDING.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000002"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
        sent_events=[
            {
                "transaction_id": "00000000-0000-0000-0000-000000000002",
                "customer_id": "*************-0000-0000-000000000002",
                "event_type": "nodes_executed",
                "timestamp": "2025-04-23T00:00:00+00:00",
                "properties": {
                    "tenant_id": "10000000-0000-0000-0000-000000000002",
                    "count": 0,
                    "project_id": "*************-0000-0000-000000000002",
                    "standby_minutes": 0,
                    "deployment_type": "prod",
                    "plan": "cloud",
                },
            }
        ],
        transactions_after_reporting=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000002"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000002"),
                project_id=uuid.UUID("*************-0000-0000-000000000002"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=0,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000002"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
    ),
    NodesTransactionTestCase(
        name="Basic nodes transaction reporting, but project was created before May 14, 2025",
        existing_transactions=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000005"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000005"),
                project_id=uuid.UUID("*************-0000-0000-000000000005"),
                project_created_at=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.PENDING.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000005"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
        sent_events=[
            {
                "transaction_id": "00000000-0000-0000-0000-000000000005",
                "customer_id": "*************-0000-0000-000000000005",
                "event_type": "nodes_executed",
                "timestamp": "2025-04-23T00:00:00+00:00",
                "properties": {
                    "tenant_id": "10000000-0000-0000-0000-000000000005",
                    # No usage is reported for projects created before May 14, 2025
                    "count": 0
                    if datetime.datetime.now(datetime.timezone.utc)
                    < datetime.datetime(
                        2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                    )
                    else 10,
                    "project_id": "*************-0000-0000-000000000005",
                    "standby_minutes": 0,
                    "deployment_type": "prod",
                    "plan": "cloud",
                },
            }
        ],
        transactions_after_reporting=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000005"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000005"),
                project_id=uuid.UUID("*************-0000-0000-000000000005"),
                project_created_at=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000005"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
    ),
    NodesTransactionTestCase(
        name="Basic nodes transaction reporting, but project is remote reconciled",
        existing_transactions=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000006"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000006"),
                project_id=uuid.UUID("*************-0000-0000-000000000006"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=True,
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.PENDING.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000006"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
        sent_events=[
            {
                "transaction_id": "00000000-0000-0000-0000-000000000006",
                "customer_id": "*************-0000-0000-000000000006",
                "event_type": "nodes_executed",
                "timestamp": "2025-04-23T00:00:00+00:00",
                "properties": {
                    "tenant_id": "10000000-0000-0000-0000-000000000006",
                    "count": 10,
                    "project_id": "*************-0000-0000-000000000006",
                    "standby_minutes": 0,
                    "deployment_type": "prod",
                    "plan": "self_hosted_data_plane",
                },
            }
        ],
        transactions_after_reporting=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000006"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000006"),
                project_id=uuid.UUID("*************-0000-0000-000000000006"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=True,
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000006"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
    ),
    NodesTransactionTestCase(
        name="Failed nodes transaction reporting",
        existing_transactions=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000003"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000003"),
                project_id=uuid.UUID("*************-0000-0000-000000000003"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=20,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.PENDING.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000003"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
        sent_events=[],  # No events sent due to failure
        transactions_after_reporting=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000003"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000003"),
                project_id=uuid.UUID("*************-0000-0000-000000000003"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=20,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=1,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000003"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
        metronome_client=lambda: MockMetronomeClient(
            orgs_to_throw_500_for=["*************-0000-0000-000000000003"]
        ),
    ),
    NodesTransactionTestCase(
        name="Multiple nodes transactions reporting",
        existing_transactions=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000004"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000004"),
                project_id=uuid.UUID("*************-0000-0000-000000000004"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=5,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.PENDING.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000004"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            ),
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000005"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000004"),
                project_id=uuid.UUID("*************-0000-0000-000000000004"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 1, 0, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 1, 0, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.PENDING.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000004"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            ),
        ],
        sent_events=[
            {
                "transaction_id": "00000000-0000-0000-0000-000000000004",
                "customer_id": "*************-0000-0000-000000000004",
                "event_type": "nodes_executed",
                "timestamp": "2025-04-23T00:00:00+00:00",
                "properties": {
                    "tenant_id": "10000000-0000-0000-0000-000000000004",
                    "count": 5,
                    "project_id": "*************-0000-0000-000000000004",
                    "standby_minutes": 0,
                    "deployment_type": "prod",
                    "plan": "cloud",
                },
            },
            {
                "transaction_id": "00000000-0000-0000-0000-000000000005",
                "customer_id": "*************-0000-0000-000000000004",
                "event_type": "nodes_executed",
                "timestamp": "2025-04-23T00:00:30+00:00",
                "properties": {
                    "tenant_id": "10000000-0000-0000-0000-000000000004",
                    "count": 10,
                    "project_id": "*************-0000-0000-000000000004",
                    "standby_minutes": 0,
                    "deployment_type": "prod",
                    "plan": "cloud",
                },
            },
        ],
        transactions_after_reporting=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000004"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000004"),
                project_id=uuid.UUID("*************-0000-0000-000000000004"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=5,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000004"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            ),
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000005"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000004"),
                project_id=uuid.UUID("*************-0000-0000-000000000004"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 1, 0, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 1, 0, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000004"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            ),
        ],
    ),
    NodesTransactionTestCase(
        name="Basic nodes transaction reporting self hosted",
        existing_transactions=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000011"),
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.PENDING.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=uuid.UUID(
                    "*************-0000-0000-000000000001"
                ),
            )
        ],
        sent_events=[
            {
                "transaction_id": "00000000-0000-0000-0000-000000000011",
                "customer_id": "*************-0000-0000-000000000001",
                "event_type": "nodes_executed",
                "timestamp": "2025-04-23T00:00:00+00:00",
                "properties": {
                    "count": 10,
                    "tenant_id": "self-hosted",
                    "project_id": "",
                    "standby_minutes": 0,
                    "deployment_type": "prod",
                    "plan": "self_hosted",
                },
            }
        ],
        transactions_after_reporting=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000011"),
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=uuid.UUID(
                    "*************-0000-0000-000000000001"
                ),
            )
        ],
    ),
    NodesTransactionTestCase(
        name="Edge case: remote_metrics.tags->>langgraph.platform.project_id is an invalid UUID",
        existing_transactions=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000012"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000012"),
                project_id=uuid.UUID("*************-0000-0000-000000000012"),
                project_created_at=datetime.datetime(
                    2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                api_key="abc123",
                remote_reconciled=False,
                nodes_count=10,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.PENDING.value,
                num_failed_send_attempts=0,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000012"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
        sent_events=[],
        transactions_after_reporting=[],
    ),
]


# Decorator that patches NODES_REPORTING_LIMIT to the input of a pytest parameter
def patch_nodes_reporting_limit(func):
    @pytest.mark.parametrize("nodes_reporting_limit", [1, 10000])
    async def wrapper(nodes_reporting_limit, test_case, *args, **kwargs):
        with patch(
            "app.models.billing.usagereporting.nodes.shared_settings.NODES_REPORTING_LIMIT",
            new=nodes_reporting_limit,
        ):
            return await func(test_case, *args, **kwargs)

    return wrapper


async def _insert_nodes_transactions(
    transactions: List[NodesTransactionWithOrg], should_replace=False
):
    # SETUP TEST DATA IN POSTGRES
    if should_replace:
        await _execute_with_db("DELETE FROM remote_metrics")

    async with asyncpg_conn() as db:
        for txn in transactions:
            # Use consistent API key format - extract the last digit from tenant_id
            tenant_id_str = str(txn.tenant_id)
            tenant_suffix = tenant_id_str[-1]  # Get the last character
            api_key: str | None = f"test_api_key_{tenant_suffix}"
            project_id: uuid.UUID | str | None = txn.project_id

            if txn.id == uuid.UUID("00000000-0000-0000-0000-000000000011"):
                # set the API key to null for self-hosted metrics
                api_key = None

            if txn.id == uuid.UUID("00000000-0000-0000-0000-000000000012"):
                # set the project_id to an invalid UUID
                project_id = "invalid_uuid"

            await db.execute(
                """
                INSERT INTO remote_metrics (
                    id,
                    api_key,
                    measures,
                    tags,
                    from_timestamp,
                    to_timestamp,
                    received_at,
                    reported_status,
                    num_failed_metronome_send_attempts,
                    tenant_id,
                    self_hosted_customer_id
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
                )
                """,
                txn.id,
                api_key,  # Use the consistent API key format
                {"langgraph.platform.nodes": txn.nodes_count},
                {"langgraph.platform.project_id": project_id},
                txn.start_interval_time,
                txn.end_interval_time,
                txn.start_insertion_time,
                txn.status,
                txn.num_failed_send_attempts,
                txn.tenant_id,
                txn.self_hosted_customer_id,
            )


async def _list_all_nodes_transactions():
    async with asyncpg_conn() as db:
        rows = await db.fetch(
            """
            SELECT
                r.id as transaction_id,
                r.api_key,
                r.measures,
                r.from_timestamp as start_interval_time,
                r.to_timestamp as end_interval_time,
                r.received_at as start_insertion_time,
                r.received_at as end_insertion_time,
                r.reported_status,
                r.num_failed_metronome_send_attempts,
                r.tenant_id,
                ak.organization_id,
                r.self_hosted_customer_id
            FROM
                remote_metrics r
            LEFT JOIN api_keys ak ON ak.api_key = r.api_key
            """
        )
        return [
            NodesTransactionWithOrg(
                id=row["transaction_id"],
                tenant_id=row["tenant_id"],
                nodes_count=row["measures"]["langgraph.platform.nodes"],
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=row["start_insertion_time"],
                end_insertion_time=row["end_insertion_time"],
                start_interval_time=row["start_interval_time"],
                end_interval_time=row["end_interval_time"],
                status=row["reported_status"],
                num_failed_send_attempts=row["num_failed_metronome_send_attempts"],
                transaction_type="nodes_executed",
                organization_id=row["organization_id"],
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=row["self_hosted_customer_id"],
            )
            for row in rows
        ]


@pytest.fixture(autouse=True)
async def setup():
    # Create tenants and organizations for testing
    await _execute_with_db(
        "DELETE FROM identities where organization_id in ('*************-0000-0000-000000000001', '*************-0000-0000-000000000002', '*************-0000-0000-000000000003', '*************-0000-0000-000000000004', '*************-0000-0000-000000000005', '*************-0000-0000-000000000006', '*************-0000-0000-000000000007')"
    )
    await _execute_with_db(
        "DELETE FROM organizations where id in ('*************-0000-0000-000000000001', '*************-0000-0000-000000000002', '*************-0000-0000-000000000003', '*************-0000-0000-000000000004', '*************-0000-0000-000000000005', '*************-0000-0000-000000000006', '*************-0000-0000-000000000007')"
    )
    await _execute_with_db("DELETE FROM host_projects")
    await _execute_with_db("DELETE FROM tracer_session")

    for i in range(1, 8):
        tenant_id = uuid.UUID(f"10000000-0000-0000-0000-00000000000{i}")
        org_id = uuid.UUID(f"*************-0000-0000-00000000000{i}")
        metronome_customer_id = org_id

        await _create_tenant(
            tenant_id=tenant_id,
            org_id=org_id,
            metronome_customer_id=metronome_customer_id,
            config=None,
        )

    # Create API keys for testing
    async with asyncpg_conn() as db:
        for i in range(1, 8):
            await db.execute(
                """
                INSERT INTO api_keys (
                    id,
                    tenant_id,
                    api_key,
                    short_key,
                    read_only,
                    description,
                    organization_id,
                    created_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8
                )
                """,
                uuid.uuid4(),
                uuid.UUID(f"10000000-0000-0000-0000-00000000000{i}"),
                f"test_api_key_{i}",
                f"test_{i}",
                False,
                f"Test API Key {i}",
                uuid.UUID(f"*************-0000-0000-00000000000{i}"),
                datetime.datetime.now(),
            )

            # creating tracer session is needed to create a project
            tracer_session_row = await db.fetchrow(
                """
                INSERT INTO tracer_session (id, name, tenant_id)
                VALUES ($1, $2, $3)
                RETURNING id
                """,
                uuid.uuid4(),
                f"Tracer session {i}",
                uuid.UUID(f"10000000-0000-0000-0000-00000000000{i}"),
            )

            # Set all projects to be created after August 14, 2025.
            project_created_at = datetime.datetime(
                2025, 8, 14, 0, 0, 0, tzinfo=datetime.timezone.utc
            )
            if i == 5:
                # For project ID *************-0000-0000-000000000005, set the project to be created
                # before May 14, 2025.
                project_created_at = datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                )

            remote_reconciled = False
            if i == 6:
                # For project ID *************-0000-0000-000000000006, set the project to be
                # remote reconciled.
                remote_reconciled = True

            # create project
            await db.execute(
                """
                INSERT INTO host_projects (id, tenant_id, name, tracer_session_id, knative, created_at, remote_reconciled)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                uuid.UUID(f"*************-0000-0000-00000000000{i}"),
                uuid.UUID(f"10000000-0000-0000-0000-00000000000{i}"),
                f"Project {i}",
                tracer_session_row["id"],
                {},
                project_created_at,
                remote_reconciled,
            )

    yield


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in NODES_TRANSACTION_TEST_CASES],
    ids=lambda x: x.name,
)
async def test_fetch_reportable_transactions(test_case: NodesTransactionTestCase):
    # Insert test transactions
    await _insert_nodes_transactions(
        test_case.existing_transactions, should_replace=True
    )

    # call method / assertions
    txns, _ = await NodesTransactionReporter._fetch_reportable_transactions(
        UsageReportingStatus.PENDING, None
    )

    assert len(txns) == len(test_case.transactions_after_reporting)


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in NODES_TRANSACTION_TEST_CASES],
    ids=lambda x: x.name,
)
@patch_nodes_reporting_limit
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_metronome_nodes_reporting(test_case: NodesTransactionTestCase):
    # Insert test transactions
    await _insert_nodes_transactions(
        test_case.existing_transactions, should_replace=True
    )

    # Mock the metronome client
    with pytest.MonkeyPatch.context() as mp:
        # Create a mock metronome client
        mock_client = test_case.metronome_client()

        # Patch the metronome client
        mp.setattr(metronome, "metronome_client", lambda: mock_client)

        # Run the reporter
        await NodesTransactionReporter.report_usage()

        # Check that the expected events were sent
        assert sorted(mock_client.ingested_data, key=lambda x: str(x)) == sorted(
            test_case.sent_events, key=lambda x: str(x)
        )

        # Check that the transactions were updated correctly
        transactions_after = await _list_all_nodes_transactions()

        # Sort both lists by ID for comparison
        expected_transactions = sorted(
            test_case.transactions_after_reporting, key=lambda x: x.id
        )
        actual_transactions = sorted(transactions_after, key=lambda x: x.id)

        if len(actual_transactions) == len(expected_transactions):
            for expected, actual in zip(expected_transactions, actual_transactions):
                assert expected.id == actual.id
                assert expected.tenant_id == actual.tenant_id
                assert expected.nodes_count == actual.nodes_count
                assert expected.status == actual.status
                assert (
                    expected.num_failed_send_attempts == actual.num_failed_send_attempts
                )
                assert expected.transaction_type == actual.transaction_type
                assert expected.organization_id == actual.organization_id


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in NODES_TRANSACTION_TEST_CASES],
    ids=lambda x: x.name,
)
@patch_nodes_reporting_limit
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_retry_failed_nodes_transactions(test_case: NodesTransactionTestCase):
    # Only test cases with failed transactions
    if all(
        txn.status != UsageReportingStatus.SHOULD_RETRY.value
        for txn in test_case.existing_transactions
    ):
        return

    # Insert test transactions
    await _insert_nodes_transactions(
        [
            NodesTransactionWithOrg(
                id=txn.id,
                tenant_id=txn.tenant_id,
                api_key=txn.api_key,
                remote_reconciled=txn.remote_reconciled,
                nodes_count=txn.nodes_count,
                deduped_standby_minutes=txn.deduped_standby_minutes,
                deployment_type=txn.deployment_type,
                start_insertion_time=txn.start_insertion_time,
                end_insertion_time=txn.end_insertion_time,
                start_interval_time=txn.start_interval_time,
                end_interval_time=txn.end_interval_time,
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=1,
                transaction_type=txn.transaction_type,
                organization_id=txn.organization_id,
                source=txn.source,
                self_hosted_customer_id=txn.self_hosted_customer_id,
            )
            for txn in test_case.existing_transactions
        ],
        should_replace=True,
    )

    # Mock the metronome client
    with pytest.MonkeyPatch.context() as mp:
        # Create a mock metronome client - but this time without failures
        mock_client = MockMetronomeClient()

        # Patch the metronome client
        mp.setattr(metronome, "metronome_client", lambda: mock_client)

        # Run the retry reporter
        await NodesTransactionReporter.retry_report_failed_transactions()

        # Check that the transactions were updated correctly
        transactions_after = await _list_all_nodes_transactions()

        # All transactions should now be SENT
        for txn in transactions_after:
            assert txn.status == UsageReportingStatus.SENT.value


class NodesTransactionRetryTestCase(NamedTuple):
    name: str
    existing_transactions: List[NodesTransactionWithOrg]
    transactions_state_updated_midway: List[NodesTransactionWithOrg]
    sent_events: List[dict]
    transactions_after_reporting: List[NodesTransactionWithOrg]
    metronome_client: Callable[..., metronome.JsonHttpClient] = MockMetronomeClient


# Create test cases for concurrent retry scenarios
CONCURRENT_NODES_RETRY_TEST_CASES = [
    NodesTransactionRetryTestCase(
        name="Concurrent retry - transaction already sent",
        existing_transactions=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000008"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000001"),
                nodes_count=40,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=1,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000001"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
        transactions_state_updated_midway=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000008"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000001"),
                nodes_count=40,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SENT.value,  # Already sent by another process
                num_failed_send_attempts=1,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000001"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
        sent_events=[],  # No events should be sent since it's already marked as sent
        transactions_after_reporting=[
            NodesTransactionWithOrg(
                id=uuid.UUID("00000000-0000-0000-0000-000000000008"),
                tenant_id=uuid.UUID("10000000-0000-0000-0000-000000000001"),
                nodes_count=40,
                deduped_standby_minutes=0,
                deployment_type="prod",
                start_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_insertion_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                start_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                end_interval_time=datetime.datetime(
                    2025, 4, 23, 0, 0, 30, tzinfo=datetime.timezone.utc
                ),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=1,
                transaction_type="nodes_executed",
                organization_id=uuid.UUID("*************-0000-0000-000000000001"),
                source=TraceTransactionSource.local.value,
                self_hosted_customer_id=None,
            )
        ],
    ),
]


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in CONCURRENT_NODES_RETRY_TEST_CASES],
    ids=lambda x: x.name,
)
@patch_nodes_reporting_limit
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_concurrent_nodes_retry_updates(
    test_case: NodesTransactionRetryTestCase,
):
    # Insert test transactions
    await _insert_nodes_transactions(
        test_case.existing_transactions, should_replace=True
    )

    # Mock the metronome client
    with pytest.MonkeyPatch.context() as mp:
        # Create a mock metronome client
        mock_client = test_case.metronome_client()

        # Patch the metronome client
        mp.setattr(metronome, "metronome_client", lambda: mock_client)

        # Define a side effect that updates the transaction state midway
        async def side_effect():
            async with asyncpg_conn() as db:
                # Update the transaction state to simulate another process changing it
                for txn in test_case.transactions_state_updated_midway:
                    await db.execute(
                        """
                        UPDATE remote_metrics
                        SET reported_status = $1
                        WHERE id = $2
                        """,
                        txn.status,
                        txn.id,
                    )

        # Patch the _send_transaction_batch method to trigger the side effect
        original_send_batch = NodesTransactionReporter._send_transaction_batch

        async def patched_send_batch(cls, txns):
            # Trigger the side effect before sending
            await side_effect()
            # Call the original method
            return await original_send_batch.__func__(cls, txns)

        mp.setattr(
            NodesTransactionReporter,
            "_send_transaction_batch",
            classmethod(patched_send_batch),
        )

        # Run the retry reporter
        await NodesTransactionReporter.retry_report_failed_transactions()

        # Check that the transactions were updated correctly
        transactions_after = await _list_all_nodes_transactions()

        # Sort both lists by ID for comparison
        expected_transactions = sorted(
            test_case.transactions_after_reporting, key=lambda x: x.id
        )
        actual_transactions = sorted(transactions_after, key=lambda x: x.id)

        assert len(actual_transactions) == len(expected_transactions)

        for expected, actual in zip(expected_transactions, actual_transactions):
            assert expected.id == actual.id
            assert expected.tenant_id == actual.tenant_id
            assert expected.nodes_count == actual.nodes_count
            assert expected.status == actual.status
            assert expected.num_failed_send_attempts == actual.num_failed_send_attempts
            assert expected.transaction_type == actual.transaction_type
            assert expected.organization_id == actual.organization_id
