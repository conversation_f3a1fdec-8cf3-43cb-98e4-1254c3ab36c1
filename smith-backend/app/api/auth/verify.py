import logging
from typing import List, Literal, NotRequired, <PERSON><PERSON>, cast
from uuid import UUI<PERSON>

import or<PERSON>son
import structlog
from ddtrace import tracer
from fastapi import HTTP<PERSON>xception, status
from lc_config.tenant_config import (
    OrganizationConfig,
    TenantConfig,
)
from lc_database.curl import (
    HTTPClientError,
    internal_platform_request,
    platform_request,
)
from opentelemetry import trace as otel_tracer
from starlette_context import context
from typing_extensions import TypedDict

from app.api.auth.schemas import (
    AuthInfo,
    OrgAuthInfo,
    ServiceIdentity,
    TenantlessAuthInfo,
    UnverifiedAuthInfo,
)
from app.config import settings
from app.models.organizations.config import (
    get_org_config_cached,
    get_org_tenant_config_cached,
)
from app.retry import retry_asyncpg

logger = logging.getLogger(__name__)


class AuthDict(TypedDict):
    organization_id: str
    organization_is_personal: bool
    organization_metronome_customer_id: NotRequired[str]
    organization_stripe_customer_id: NotRequired[str]
    organization_permissions: list[str]
    organization_config: NotRequired[dict]
    organization_disabled: bool
    public_sharing_disabled: NotRequired[bool]
    sso_only: NotRequired[bool]

    tenant_id: str
    tenant_handle: NotRequired[str]
    tenant_config: dict
    tenant_is_deleted: NotRequired[bool]

    identity_id: str
    identity_read_only: bool
    identity_permissions: list[str]

    user_id: NotRequired[str]
    ls_user_id: NotRequired[str]
    user_email: NotRequired[str]
    user_full_name: NotRequired[str]
    is_sso_user: NotRequired[bool]
    service_identity: NotRequired[str]


class OrgAuthDict(TypedDict):
    organization_id: str
    organization_is_personal: bool
    organization_metronome_customer_id: NotRequired[str]
    organization_stripe_customer_id: NotRequired[str]
    organization_config: dict
    organization_disabled: bool
    public_sharing_disabled: NotRequired[bool]
    sso_only: NotRequired[bool]

    identity_id: dict[str, str]
    identity_read_only: bool
    organization_permissions: list[str]

    user_id: NotRequired[str]
    ls_user_id: NotRequired[str]
    user_email: NotRequired[str]
    user_full_name: NotRequired[str]
    is_sso_user: NotRequired[bool]


def auth_info_context(auth_info: AuthInfo | OrgAuthInfo | TenantlessAuthInfo) -> None:
    """
    Set the auth info in the context regardless of a cache hit or miss.
    These context vars are also used for audit logging.
    """
    if context.exists():
        context["auth_info"] = auth_info.model_dump()
        if auth_info:
            bind_vars = {}
            if auth_info.user_id:
                bind_vars["user_id"] = str(auth_info.user_id)
            if auth_info.ls_user_id:
                bind_vars["ls_user_id"] = str(auth_info.ls_user_id)
            # api_key_short (unverified) is set in the exception logger b/c it's not set for all auth info types
            if isinstance(auth_info, AuthInfo):
                if auth_info.tenant_id:
                    bind_vars["tenant_id"] = str(auth_info.tenant_id)
            if isinstance(auth_info, (AuthInfo, OrgAuthInfo)):
                if auth_info.organization_id:
                    bind_vars["organization_id"] = str(auth_info.organization_id)
            structlog.contextvars.bind_contextvars(**bind_vars)
            if settings.DATADOG_ENABLED and (span := tracer.current_span()):
                span.set_tags(bind_vars)
            elif settings.OTEL_TRACING_ENABLED and (
                span := otel_tracer.get_current_span()
            ):
                span.set_attributes(bind_vars)


async def _retrieve_auth_dict(
    info: UnverifiedAuthInfo,
    endpoint: str,
    additional_headers: List[Tuple[str, str | None]],
) -> dict:
    headers = [
        (
            "Authorization",
            f"{info.authorization.scheme} {info.authorization.credentials}"
            if info.authorization
            else None,
        ),
        ("X-Api-Key", info.x_api_key),
        ("X-Service-Key", str(info.x_service_key) if info.x_service_key else None),
        ("Cookie", info.cookies),
    ] + additional_headers
    try:
        res = await platform_request(
            "GET",
            endpoint,
            headers={k: v for k, v in headers if v is not None},
        )
        return orjson.loads(res.body)
    except HTTPClientError as exc:
        if exc.code == 401:
            if info.x_api_key and info.x_api_key.startswith("ls__"):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Using legacy API key. Please generate a new API key.",
                )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
            )
        elif exc.code == 403:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Forbidden",
            )
        else:
            raise


def _auth_info_from_auth_dict(
    auth_dict: AuthDict,
    tenant_config: TenantConfig,
    authorization_scheme: Literal["API_KEY", "BEARER"] | None = None,
    unverified: UnverifiedAuthInfo | None = None,
) -> AuthInfo:
    fields = ["authorization_scheme", "unverified"]
    auth_dict["tenant_config"] = tenant_config.model_dump()
    info = AuthInfo.model_validate(
        {k: v for k, v in auth_dict.items() if k not in fields and v is not None},
    )
    info.authorization_scheme = authorization_scheme
    info.unverified = unverified
    return info


def _org_auth_info_from_auth_dict(
    auth_dict: OrgAuthDict, org_config: OrganizationConfig
) -> OrgAuthInfo:
    fields = ["identity_id", "identity_permissions", "organization_config"]
    base_auth_dict = {
        k: v for k, v in auth_dict.items() if k not in fields and v is not None
    }
    base_auth_dict["org_config"] = org_config.model_dump()
    base_auth_dict["identity_id"] = (
        auth_dict.get("identity_id", {}).get("String") or None
    )
    base_auth_dict["identity_permissions"] = auth_dict.get(
        "organization_permissions", []
    )
    info = OrgAuthInfo.model_validate(base_auth_dict)
    return info


def _tenant_config_from_dict(
    tenant_config: dict,
) -> TenantConfig:
    """Safely constructs TenantConfig by overriding from the inputted dict only if keys are present"""
    config = TenantConfig()

    return config.model_copy(
        update={k: v for k, v in tenant_config.items() if v is not None},
        deep=True,
    )


def _org_config_from_dict(
    org_config: dict,
) -> OrganizationConfig:
    """Safely constructs OrganizationConfig by overriding from the inputted dict only if keys are present"""
    config = OrganizationConfig()

    return config.model_copy(
        update={k: v for k, v in org_config.items() if v is not None},
        deep=True,
    )


def tenant_config_with_nested_org_config(auth_dict: AuthDict) -> TenantConfig:
    # Nest organization config in tenant config if present.
    # This is ejected from verify_auth_info for easier testing.
    # TODO: remove this once config has a more unified strategy.
    if "tenant_config" not in auth_dict or not auth_dict["tenant_config"]:
        raise ValueError(f"Missing tenant_config in auth_dict: {auth_dict}")
    tenant_config = _tenant_config_from_dict(auth_dict["tenant_config"])
    if "organization_config" in auth_dict:
        tenant_config.organization_config = _org_config_from_dict(
            auth_dict["organization_config"]
        )
    return tenant_config


async def verify_auth_info(
    info: UnverifiedAuthInfo,
    permission: list[str] | str | None,
    org_permission: str | None = None,
    allow_disabled: bool = False,
    allow_deleted: bool = False,
    allowed_services: list[ServiceIdentity] | None = None,
) -> AuthInfo:
    if allowed_services is None:
        allowed_services = []
    auth_headers = [
        ("X-Tenant-Id", str(info.x_tenant_id) if info.x_tenant_id else None),
        ("X-Service-Key", str(info.x_service_key) if info.x_service_key else None),
        ("X-User-Id", str(info.x_user_id) if info.x_user_id else None),
    ]
    auth_dict = cast(AuthDict, await _retrieve_auth_dict(info, "/auth", auth_headers))

    service_identity = auth_dict.get("service_identity")
    if service_identity:
        if service_identity not in [s.value for s in allowed_services]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied, service {service_identity} does not have access to this endpoint",
            )
    else:
        if not permission:
            # This implies you are trying to call a service endpoint with a user token
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied",
            )
        if not isinstance(permission, list):
            permission = [permission]
        for perm in permission:
            if perm not in auth_dict["identity_permissions"]:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission denied, you do not have the required permission {perm}",
                )
        # Additionally check for org permission if provided
        if (
            org_permission
            and org_permission not in auth_dict["organization_permissions"]
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied, you do not have the required permission {org_permission}",
            )

    if not allow_disabled and auth_dict.get("organization_disabled", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Organization is disabled",
        )

    if not allow_deleted and auth_dict.get("tenant_is_deleted", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Workspace is deleted",
        )

    if (
        info.x_user_id
        and auth_dict.get("user_id")
        and auth_dict.get("user_id") != info.x_user_id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Forbidden",
        )

    tenant_config_source = tenant_config_with_nested_org_config(auth_dict)
    tenant_config, has_plan = await get_org_tenant_config_cached(
        tenant_config_source,
        auth_dict["organization_id"],
        auth_dict["organization_is_personal"],
        auth_dict.get("organization_metronome_customer_id"),
    )
    if (
        has_plan is False
        and not allow_disabled
        # TODO: decide whether to allow service auth here
        and not auth_dict["organization_is_personal"]
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant not allowed",
        )
    info = _auth_info_from_auth_dict(
        auth_dict,
        tenant_config,
        authorization_scheme=info.authorization.scheme.upper()
        if info.authorization
        else "API_KEY"
        if info.x_api_key
        else None,
        unverified=info,
    )
    auth_info_context(info)
    return info


async def verify_org_auth_info(
    info: UnverifiedAuthInfo,
    permission: str | None,
    allow_disabled: bool = False,
    allowed_services: list[ServiceIdentity] | None = None,
) -> OrgAuthInfo:
    # TODO: support x-service-auth at org level
    if allowed_services is None:
        allowed_services = []
    auth_headers = [
        (
            "X-Organization-Id",
            str(info.x_organization_id) if info.x_organization_id else None,
        ),
        ("X-Service-Key", str(info.x_service_key) if info.x_service_key else None),
        ("X-User-Id", str(info.x_user_id) if info.x_user_id else None),
    ]
    auth_dict = cast(
        OrgAuthDict, await _retrieve_auth_dict(info, "/orgs/auth", auth_headers)
    )

    service_identity = auth_dict.get("service_identity")
    if service_identity:
        if service_identity not in [s.value for s in allowed_services]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied, service {service_identity} does not have access to this endpoint",
            )
    else:
        if not permission:
            # This implies you are trying to call a service endpoint with a user token
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied",
            )
        if permission not in auth_dict["organization_permissions"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied, you do not have the required permission {permission}",
            )

        if not auth_dict.get("identity_id", {}).get("Valid") or not auth_dict.get(
            "identity_id", {}
        ).get("String"):
            logger.error(f"Missing identity_id in org auth info: {auth_dict}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Forbidden",
            )

    if not allow_disabled and auth_dict["organization_disabled"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant is disabled",
        )

    if (
        info.x_user_id
        and auth_dict.get("user_id")
        and auth_dict.get("user_id") != info.x_user_id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Forbidden",
        )

    org_config, has_plan = await get_org_config_cached(
        _org_config_from_dict(auth_dict["organization_config"]),
        auth_dict["organization_id"],
        auth_dict["organization_is_personal"],
        auth_dict.get("organization_metronome_customer_id"),
    )
    if (
        has_plan is False
        and not allow_disabled
        and not auth_dict["organization_is_personal"]
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant not allowed",
        )

    info = _org_auth_info_from_auth_dict(auth_dict, org_config)
    auth_info_context(info)
    return info


class TenantlessAuthDict(TypedDict):
    user_id: NotRequired[str]
    ls_user_id: NotRequired[str]
    user_email: NotRequired[str]
    user_full_name: NotRequired[str]
    is_sso_user: NotRequired[bool]
    tenant_ids: list[str]
    # TODO: update this to be required once rolled out
    organization_ids: NotRequired[list[str]]


@retry_asyncpg
async def verify_tenantless_auth_info(
    info: UnverifiedAuthInfo,
) -> TenantlessAuthInfo:
    """Get tenant-less auth info."""
    headers = [
        (
            "Authorization",
            f"{info.authorization.scheme} {info.authorization.credentials}"
            if info.authorization
            else None,
        ),
        ("Cookie", info.cookies),
    ]
    try:
        res = await platform_request(
            "GET",
            "/current/tenants",
            headers={k: v for k, v in headers if v is not None},
        )
        auth_dict: TenantlessAuthDict = orjson.loads(res.body)
    except HTTPClientError as exc:
        if exc.code == 401:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
            )
        elif exc.code == 403:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Forbidden",
            )
        else:
            raise
    if not auth_dict.get("user_id"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Forbidden",
        )
    auth_info = TenantlessAuthInfo(
        user_id=cast(UUID, auth_dict.get("user_id")),
        ls_user_id=auth_dict.get("ls_user_id"),
        user_email=auth_dict.get("user_email"),
        user_full_name=auth_dict.get("user_full_name"),
        is_sso_user=auth_dict.get("is_sso_user", False),
        available_tenants=[UUID(t) for t in auth_dict["tenant_ids"]],
        # TODO: update this to be required once rolled out
        available_organizations=[
            UUID(o) for o in auth_dict.get("organization_ids", [])
        ],
    )
    auth_info_context(auth_info)
    return auth_info


async def internal_auth_request(
    tenant_id: UUID,
) -> AuthInfo:
    auth_resp = await internal_platform_request(
        "GET",
        "/internal/auth",
        jwt_payload={"tenant_id": str(tenant_id)},
    )
    auth_dict = cast(AuthDict, orjson.loads(auth_resp.body))
    tenant_config_source = tenant_config_with_nested_org_config(auth_dict)
    tenant_config, _ = await get_org_tenant_config_cached(
        tenant_config_source,
        auth_dict["organization_id"],
        auth_dict["organization_is_personal"],
        auth_dict.get("organization_metronome_customer_id"),
    )
    return _auth_info_from_auth_dict(auth_dict, tenant_config)


async def internal_org_auth_request(
    organization_id: UUID,
) -> OrgAuthInfo:
    auth_resp = await internal_platform_request(
        "GET",
        "/internal/org-auth",
        jwt_payload={"organization_id": str(organization_id)},
    )
    auth_dict = cast(OrgAuthDict, orjson.loads(auth_resp.body))
    org_config_source = _org_config_from_dict(auth_dict["organization_config"])
    org_config, _ = await get_org_config_cached(
        org_config_source,
        auth_dict["organization_id"],
        auth_dict["organization_is_personal"],
        auth_dict.get("organization_metronome_customer_id"),
    )

    return _org_auth_info_from_auth_dict(auth_dict, org_config)
