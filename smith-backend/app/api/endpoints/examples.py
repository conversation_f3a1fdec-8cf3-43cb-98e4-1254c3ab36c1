"""Endpoints for examples."""

from typing import Annotated, List, cast
from uuid import UUID

import orjson
from fastapi import Depends, Form, Request, Response, UploadFile
from fastapi.param_functions import Query
from lc_database import api_router
from lc_logging.audit_logs import audit_operation_name

from app import crud, schemas
from app.api import deps
from app.api.auth.schemas import Permissions
from app.models.examples import validate, validate_schema
from app.receive import consume_body
from app.utils import arun_in_executor

router = api_router.TrailingSlashRouter()


@router.get("/count", response_model=int)
async def count_examples(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
    id: Annotated[list[UUID] | None, Query()] = None,
    as_of: schemas.AsOfType = "latest",
    metadata: str | None = None,
    full_text_contains: Annotated[list[str] | None, Query()] = None,
    splits: Annotated[list[str] | None, Query()] = None,
    dataset: UUID | None = None,
    filter: str | None = None,
) -> int:
    """Count all examples by query params"""
    count = await crud.count_examples_json(
        auth,
        schemas.FilterQueryParamsForCountExampleSchema(
            id=id,
            dataset=dataset,
            as_of=as_of,
            metadata=metadata,
            full_text_contains=full_text_contains,
            splits=splits,
            filter=filter,
        ),
    )
    return Response(
        orjson.dumps(count),
        media_type="application/json",
    )


@router.get("/{example_id}", response_model=schemas.Example)
@audit_operation_name("read_example")
async def read_example(
    example_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
    as_of: schemas.AsOfType = "latest",
) -> schemas.Example:
    """Get a specific example."""
    return await crud.get_example(auth, example_id, as_of=as_of)


@router.get("", response_model=List[schemas.Example])
@audit_operation_name("read_examples")
async def read_examples(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
    id: Annotated[list[UUID] | None, Query()] = None,
    as_of: schemas.AsOfType = "latest",
    metadata: str | None = None,
    full_text_contains: Annotated[list[str] | None, Query()] = None,
    splits: Annotated[list[str] | None, Query()] = None,
    dataset: UUID | None = None,
    offset: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    order: schemas.ExampleListOrder = schemas.ExampleListOrder.recent,
    random_seed: float | None = None,
    # if attachment_urls is in the select by default, performance suffers
    select: Annotated[tuple[schemas.ExampleSelect, ...], Query()] = tuple(
        e for e in schemas.ExampleSelect if e != schemas.ExampleSelect.attachment_urls
    ),
    filter: str | None = None,
) -> List[schemas.Example]:
    """Get all examples by query params"""

    always_select = [
        schemas.ExampleSelect.id,
        schemas.ExampleSelect.created_at,
        schemas.ExampleSelect.modified_at,
        schemas.ExampleSelect.inputs,
        schemas.ExampleSelect.name_,
        schemas.ExampleSelect.dataset_id,
    ]

    select = (*always_select, *[e for e in select if e not in always_select])

    query_params = schemas.FilterQueryParamsForExampleSchema.model_construct(
        id=id,
        dataset=dataset,
        as_of=as_of,
        metadata=metadata,
        full_text_contains=full_text_contains,
        offset=offset,
        limit=limit,
        splits=splits,
        order=order,
        random_seed=random_seed,
        select=select,
        filter=filter,
    )

    if schemas.ExampleSelect.attachment_urls not in select:
        # return directly as json, best performance
        rows, total = await crud.get_examples_json(
            auth,
            query_params,
        )

        return Response(
            orjson.dumps([orjson.Fragment(row) for row in rows]),
            media_type="application/json",
            headers={"X-Pagination-Total": str(total)},
        )
    else:
        # return as objects, slower performance
        rows, total = await crud.get_examples(auth, query_params)
        rows_dict = [row.dict() for row in rows]
        return Response(
            orjson.dumps(rows_dict),
            media_type="application/json",
            headers={"X-Pagination-Total": str(total)},
        )


@router.post("")
@audit_operation_name("create_example")
async def create_example(
    request: Request,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
) -> schemas.Example:
    """Create a new example."""
    raw = await consume_body(request)
    example_dict = await arun_in_executor(validate_schema.validate_create, raw)

    example_json_strs = await crud.create_examples(
        auth, [example_dict], ignore_conflicts=False
    )
    if len(example_json_strs) > 1:
        raise ValueError("Expected only one example to be created")

    return Response(
        content=example_json_strs[0],
        media_type="application/json",
    )


@router.patch("/bulk")
@audit_operation_name("update_examples")
async def legacy_update_examples(
    example_updates: List[schemas.ExampleUpdateWithID],
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
):
    """Legacy update examples in bulk. For update involving attachments, use PATCH /v1/platform/datasets/{dataset_id}/examples instead."""
    updated_examples = await crud.update_examples(auth, example_updates)
    return {"message": f"{len(updated_examples)} examples updated"}


@router.post("/bulk")
@audit_operation_name("create_examples")
async def create_examples(
    request: Request,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
):
    """Create a new example."""
    raw = await consume_body(request)
    examples_list = await arun_in_executor(validate_schema.validate_create_bulk, raw)

    examples = await crud.create_examples(auth, examples_list)
    return Response(
        orjson.dumps([orjson.Fragment(row) for row in examples]),
        media_type="application/json",
    )


@router.post("/upload/{dataset_id}", response_model=List[schemas.Example])
@audit_operation_name("create_examples")
async def upload_examples_from_csv(
    dataset_id: UUID,
    file: UploadFile,
    input_keys: List[str] = Form(...),
    output_keys: List[str] = Form(default_factory=list),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
) -> List[schemas.Example]:
    """Upload examples from a CSV file.

    Note: For non-csv upload, please use
    the POST /v1/platform/datasets/{dataset_id}/examples endpoint which provides more efficient upload.
    """
    uploads = await crud.create_examples_from_csv(
        auth, dataset_id, file.file, input_keys, output_keys
    )
    examples, _ = await crud.get_examples_json(
        auth,
        schemas.FilterQueryParamsForExampleSchema(
            id=[e["id"] for e in uploads], dataset=dataset_id
        ),
    )
    return Response(
        orjson.dumps([orjson.Fragment(row) for row in examples]),
        media_type="application/json",
    )


@router.patch("/{example_id}")
@audit_operation_name("update_example")
async def update_example(
    example_id: UUID,
    example: schemas.ExampleUpdate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
):
    """Update a specific example."""
    await crud.update_example(auth, example_id, example)
    return {"message": "Example updated"}


@router.delete("/{example_id}")
@audit_operation_name("delete_example")
async def delete_example(
    example_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
) -> None:
    """Soft delete an example. Only deletes the example in the 'latest' version of the dataset."""
    await crud.delete_example(auth, example_id)


@router.delete("")
@audit_operation_name("delete_examples")
async def delete_examples(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
    example_ids: List[UUID] = Query(...),
) -> None:
    """Soft delete examples. Only deletes the examples in the 'latest' version of the dataset."""
    await crud.delete_examples(auth, example_ids)


@router.post("/validate", response_model=schemas.ExampleValidationResult)
async def validate_example(
    request: Request,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> schemas.Example:
    """Validate an example."""
    raw = await consume_body(request)
    example = await arun_in_executor(validate_schema.validate_create, raw)

    resp = await validate.validate_example_from_api(auth, example)
    return cast(schemas.ExampleValidationResult, resp)


@router.post("/validate/bulk", response_model=list[schemas.ExampleValidationResult])
async def validate_examples(
    request: Request,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> list[schemas.Example]:
    """Validate an example."""
    raw = await consume_body(request)
    examples = await arun_in_executor(validate_schema.validate_create_bulk, raw)

    resp = await validate.validate_examples_from_api(auth, examples)
    return cast(list[schemas.ExampleValidationResult], resp)
