"""Endpoints for datasets."""

import logging
import math
from datetime import datetime
from typing import Annotated, List, Optional
from uuid import UUID

import anyio
from fastapi import Body, Depends, Form, HTTPException, Response, UploadFile
from fastapi.params import Query
from fastapi.responses import ORJ<PERSON><PERSON>esponse, StreamingResponse
from langchain_core.runnables.utils import Output
from lc_database import api_router
from lc_logging.audit_logs import audit_operation_name
from sse_starlette import EventSourceResponse
from starlette.background import BackgroundTask

from app import config, crud, models, schemas
from app.api import deps
from app.api.auth.schemas import Permissions
from app.models.datasets.run_over_dataset import (
    create_playground_experiment,
    fetch_and_execute_examples,
    stream_dataset_response,
)
from app.models.datasets.schema import (
    PlaygroundRunOverDatasetBatchRequestSchema,
    PlaygroundRunOverDatasetRequestSchema,
    StudioRunOverDatasetRequestSchema,
)
from app.stream import jsonpatch_sse_stream

router = api_router.TrailingSlashRouter()
logger = logging.getLogger(__name__)


@router.get("/{dataset_id}", response_model=schemas.Dataset)
@audit_operation_name("read_dataset")
async def read_dataset(
    dataset_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> schemas.Dataset:
    """Get a specific dataset."""
    return await crud.get_dataset(auth, dataset_id)


@router.get("", response_model=List[schemas.Dataset])
@audit_operation_name("read_datasets")
async def read_datasets(
    response: Response,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
    id: Annotated[list[UUID] | None, Query()] = None,
    data_type: Annotated[
        list[schemas.DataType] | schemas.DataType | None, Query()
    ] = None,
    name: str | None = None,
    name_contains: str | None = None,
    metadata: str | None = None,
    offset: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    sort_by: schemas.SortByDatasetColumn = schemas.SortByDatasetColumn.last_session_start_time,
    sort_by_desc: bool = True,
    tag_value_id: Annotated[list[UUID] | None, Query()] = None,
) -> List[schemas.Dataset]:
    """Get all datasets by query params and owner."""
    rows, total = await crud.get_datasets(
        auth,
        schemas.FilterQueryParamsForDatasetSchema(
            id=id,
            data_type=data_type,
            name=name,
            name_contains=name_contains,
            metadata=metadata,
            offset=offset,
            limit=limit,
            sort_by=sort_by,
            sort_by_desc=sort_by_desc,
            tag_value_id=tag_value_id,
        ),
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.post("", response_model=schemas.Dataset)
@audit_operation_name("create_dataset")
async def create_dataset(
    dataset: schemas.DatasetCreate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_CREATE)),
) -> schemas.Dataset:
    """Create a new dataset."""
    return await crud.create_dataset(auth, dataset)


EMPTY_LIST: list[str] = []


@router.post("/upload", response_model=schemas.Dataset)
@audit_operation_name("create_csv_dataset")
async def upload_csv_dataset(
    file: UploadFile,
    input_keys: Annotated[List[str], Form()],
    name: Annotated[Optional[str], Form()] = None,
    data_type: schemas.DataType = Form(schemas.DataType.kv),
    output_keys: Annotated[List[str], Form()] = EMPTY_LIST,
    description: Annotated[Optional[str], Form()] = None,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_CREATE)),
) -> schemas.Dataset:
    """Create a new dataset from a CSV file."""
    assert file.filename
    # Check the file extension
    if not file.filename.endswith(".csv"):
        raise HTTPException(status_code=400, detail="Only CSV files are allowed.")

    try:
        return await crud.create_dataset_from_csv(
            auth,
            file.file,
            name or file.filename,
            input_keys,
            list(output_keys),  # copy list to avoid mutation of default value
            description,
            data_type,
        )
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        logger.exception("Error while uploading CSV dataset.")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/upload-experiment", response_model=schemas.ExperimentResultsUploadResult)
@audit_operation_name("create_experiment_via_upload")
async def upload_experiment(
    existing_experiment: schemas.ExperimentResultsUpload,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            [
                Permissions.DATASETS_CREATE,
                Permissions.DATASETS_UPDATE,
                Permissions.PROJECTS_CREATE,
                Permissions.RUNS_CREATE,
            ]
        )
    ),
) -> schemas.ExperimentResultsUploadResult:
    """Upload an experiment that has already been run."""
    results = await models.datasets.upload_existing.upload_existing_experiment_results(
        existing_experiment, auth
    )
    return schemas.ExperimentResultsUploadResult(
        dataset=results[0], experiment=results[1]
    )


@router.delete("/{dataset_id}")
@audit_operation_name("delete_dataset")
async def delete_dataset(
    dataset_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_DELETE)),
) -> None:
    """Delete a specific dataset."""
    return await crud.delete_dataset(auth, dataset_id)


@router.patch(
    "/{dataset_id}",
    response_model=schemas.DatasetSchemaForUpdate,
    responses={
        200: {
            "description": "Dataset updated successfully",
            "headers": {
                "X-Updated-Examples-Count": {
                    "description": "Number of examples updated",
                    "schema": {"type": "integer"},
                }
            },
        }
    },
)
@audit_operation_name("update_dataset")
async def update_dataset(
    response: Response,
    dataset_id: UUID,
    dataset: schemas.DatasetUpdate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
) -> schemas.DatasetSchemaForUpdate:
    """Update a specific dataset."""
    dataset, updated_examples_count = await crud.update_dataset(
        auth, dataset_id, dataset
    )
    response.headers["X-Updated-Examples-Count"] = str(updated_examples_count)
    return dataset


async def _download_dataset_generic(
    dataset_id: UUID,
    as_of: Optional[datetime],
    auth: deps.AuthInfo,
    crud_function,
    media_type: str,
    file_extension: str,
) -> Response:
    # Create a read/write stream to stream the CSV data
    # This can be streamed to the client at the same time as it is being written to
    write_stream, read_stream = anyio.create_memory_object_stream(
        math.inf, item_type=bytes
    )
    # Create a task group to run the CSV writing in the background
    # create_task_group is meant to be used as an async context manager, but
    # here we need to return the response to fastapi before closing it
    task_group = await anyio.create_task_group().__aenter__()
    # We await get_dataset_as_csv to signal it is done checking invariants
    # before we send StreamingResponse to the client
    await task_group.start(crud_function, auth, dataset_id, write_stream, as_of)

    # Close the task group when the response is done
    async def close():
        await task_group.__aexit__(None, None, None)

    return StreamingResponse(
        content=read_stream,
        media_type=media_type,
        headers={
            "Content-Disposition": f"attachment; filename=dataset_{dataset_id}.{file_extension}"
        },
        background=BackgroundTask(close),
    )


@router.get("/{dataset_id}/versions", response_model=List[schemas.DatasetVersion])
async def get_dataset_versions(
    response: Response,
    dataset_id: UUID,
    query_params: schemas.QueryParamsForDatasetVersionsSchema = Depends(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> List[schemas.DatasetVersion]:
    """Get dataset versions."""
    versions, total = await models.datasets.versions.get_dataset_versions(
        query_params, auth, dataset_id
    )
    response.headers["X-Pagination-Total"] = str(total)
    return versions


@router.get("/{dataset_id}/versions/diff", response_model=schemas.DatasetDiffInfo)
async def diff_dataset_versions(
    dataset_id: UUID,
    query_params: schemas.QueryParamsForDatasetVersionsDiffSchema = Depends(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> schemas.DatasetDiffInfo:
    """Get diff between two dataset versions."""
    return await models.datasets.versions.get_dataset_versions_diff(
        query_params, auth, dataset_id
    )


@router.get("/{dataset_id}/version", response_model=schemas.DatasetVersion)
async def get_dataset_version(
    dataset_id: UUID,
    query_params: schemas.QueryParamsForSingleDatasetVersionSchema = Depends(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> schemas.DatasetVersion:
    """Get dataset version by as_of or exact tag."""
    if (query_params.as_of and query_params.tag) or (
        query_params.as_of is None and query_params.tag is None
    ):
        raise HTTPException(
            status_code=400,
            detail="Exactly one of as_of and tag must be specified.",
        )
    version = await models.datasets.versions.get_dataset_version_by_as_of_or_tag(
        query_params, auth, dataset_id
    )
    return version


@router.put("/{dataset_id}/tags", response_model=schemas.DatasetVersion)
@audit_operation_name("update_dataset_version")
async def update_dataset_version(
    dataset_id: UUID,
    schema: schemas.PutDatasetVersionsSchema = Body(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
) -> schemas.DatasetVersion:
    """Set a tag on a dataset version."""
    if schema.tag == "latest":
        raise HTTPException(
            status_code=400,
            detail="The tag 'latest' is reserved and cannot be used as a tag.",
        )
    return await models.datasets.versions.update_dataset_version(
        auth, dataset_id, schema
    )


@router.get("/{dataset_id}/openai")
@audit_operation_name("read_dataset")
async def download_dataset_openai(
    dataset_id: UUID,
    as_of: Annotated[Optional[datetime], schemas.AsOfDescription] = None,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> Response:
    """Download a dataset as OpenAI Evals Jsonl format."""
    return await _download_dataset_generic(
        dataset_id,
        as_of,
        auth,
        crud.get_dataset_as_openai_evals,
        "application/octet-stream",
        "jsonl",
    )


@router.get("/{dataset_id}/openai_ft")
@audit_operation_name("read_dataset")
async def download_dataset_openai_ft(
    dataset_id: UUID,
    as_of: Annotated[Optional[datetime], schemas.AsOfDescription] = None,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> Response:
    """Download a dataset as OpenAI Jsonl format."""
    return await _download_dataset_generic(
        dataset_id,
        as_of,
        auth,
        crud.get_dataset_as_openai_ft,
        "application/octet-stream",
        "jsonl",
    )


@router.get("/{dataset_id}/csv")
@audit_operation_name("read_dataset")
async def download_dataset_csv(
    dataset_id: UUID,
    as_of: Annotated[Optional[datetime], schemas.AsOfDescription] = None,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> Response:
    """Download a dataset as CSV format."""
    return await _download_dataset_generic(
        dataset_id,
        as_of,
        auth,
        crud.get_dataset_as_csv,
        "text/csv",
        "csv",
    )


@router.get("/{dataset_id}/jsonl")
@audit_operation_name("read_dataset")
async def download_dataset_jsonl(
    dataset_id: UUID,
    as_of: Annotated[Optional[datetime], schemas.AsOfDescription] = None,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> Response:
    """Download a dataset as CSV format."""
    return await _download_dataset_generic(
        dataset_id,
        as_of,
        auth,
        crud.get_dataset_as_jsonl,
        "application/jsonl",
        "jsonl",
    )


@router.post(
    "/{dataset_id}/runs",
    response_model=List[schemas.ExampleWithRuns]
    | List[schemas.ExampleWithRunsCH]
    | None,
)
@audit_operation_name("read_examples")
async def read_examples_with_runs(
    response: Response,
    dataset_id: UUID,
    schema: schemas.QueryExampleSchemaWithRuns,
    format: Optional[str] = Query(None, description="Response format, e.g., 'csv'"),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> (
    List[schemas.ExampleWithRuns] | List[schemas.ExampleWithRunsCH] | StreamingResponse
):
    """Fetch examples for a dataset, and fetch the runs for each example if they are associated with the given session_ids."""
    if format == "csv":
        return await models.runs.fetch_ch.fetch_runs_comparison_view_csv(
            auth, dataset_id, schema
        )
    else:
        rows, total = await crud.get_example_runs_from_session_ch(
            auth, dataset_id, schema
        )
        response.headers["X-Pagination-Total"] = str(total)
        return rows


@router.post(
    "/{dataset_id}/group/runs",
    response_model=schemas.GroupedExamplesWithRunsResponse,
)
@audit_operation_name("read_examples")
async def read_examples_with_runs_grouped(
    response: Response,
    dataset_id: UUID,
    schema: schemas.QueryGroupedExamplesWithRuns,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> schemas.GroupedExamplesWithRunsResponse:
    """Fetch examples for a dataset, and fetch the runs for each example if they are associated with the given session_ids."""
    rows, total = await models.runs.fetch_ch.fetch_groups_comparison_view(
        auth, dataset_id, schema
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.post(
    "/{dataset_id}/runs/delta",
    response_model=schemas.SessionFeedbackDelta,
)
async def read_delta(
    response: Response,
    dataset_id: UUID,
    schema: schemas.QueryFeedbackDelta,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> schemas.SessionFeedbackDelta:
    """Fetch the number of regressions/improvements for each example in a dataset, between sessions[0] and sessions[1]."""
    rows, total = await models.runs.fetch_ch.fetch_feedback_delta(
        auth, dataset_id, schema
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.get("/{dataset_id}/share", response_model=schemas.DatasetShareSchema | None)
async def read_dataset_share_state(
    dataset_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
):
    """Get the state of sharing a dataset"""
    return await crud.get_dataset_share_key_by_dataset_id(auth, dataset_id)


@router.put("/{dataset_id}/share", response_model=schemas.DatasetShareSchema)
@audit_operation_name("share_dataset")
async def share_dataset(
    dataset_id: UUID,
    query_params: schemas.QueryParamsForShareDatasetSchema = Depends(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_SHARE)),
):
    """Share a dataset."""
    return await crud.share_dataset(auth, dataset_id, query_params)


@router.delete("/{dataset_id}/share")
@audit_operation_name("unshare_dataset")
async def unshare_dataset(
    dataset_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_SHARE)),
):
    """Unshare a dataset."""
    await crud.unshare_dataset(auth, dataset_id)
    return ORJSONResponse({"message": "Dataset unshared"})


@router.get(
    "/{dataset_id}/comparative", response_model=List[schemas.ComparativeExperiment]
)
@audit_operation_name("read_comparative_experiments")
async def read_comparative_experiments(
    response: Response,
    dataset_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_READ)),
    name: str | None = None,
    name_contains: str
    | None = None,  # search by comparative experiment or tracer session name
    id: Annotated[list[UUID] | None, Query()] = None,
    offset: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    sort_by: schemas.SortByComparativeExperimentColumn = schemas.SortByComparativeExperimentColumn.created_at,
    sort_by_desc: bool = True,
) -> List[schemas.ComparativeExperiment]:
    """Get all comparative experiments for a given dataset."""
    query_params = schemas.QueryParamsForComparativeExperimentsSchema(
        limit=limit,
        offset=offset,
        name=name,
        name_contains=name_contains,
        sort_by=sort_by,
        sort_by_desc=sort_by_desc,
        reference_dataset_id=dataset_id,
        id=id,
    )

    rows, total = await models.datasets.comparative.get_comparative_experiments(
        auth, query_params
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.post("/comparative", response_model=schemas.ComparativeExperimentBase)
@audit_operation_name("create_comparative_experiment")
async def create_comparative_experiment(
    comparative_experiment: schemas.ComparativeExperimentCreate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_CREATE)),
) -> schemas.ComparativeExperimentBase:
    """Create a comparative experiment."""

    return await models.datasets.comparative.create_comparative_experiment(
        auth, comparative_experiment
    )


@router.delete("/comparative/{comparative_experiment_id}")
@audit_operation_name("delete_comparative_experiment")
async def delete_comparative_experiment(
    comparative_experiment_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.PROJECTS_DELETE)),
) -> None:
    """Delete a specific comparative experiment."""
    await models.datasets.comparative.delete_comparative_experiment(
        auth, comparative_experiment_id
    )


@router.post("/clone")
@audit_operation_name("clone_dataset")
async def clone_dataset(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
    target_dataset_id: UUID = Body(),
    source_dataset_id: UUID = Body(),
    as_of: Optional[schemas.AsOfType] = Body(None),
    examples: List[UUID] = Body([]),
) -> List[schemas.Example]:
    """Clone a dataset."""
    return await models.examples.create.clone_examples(
        target_dataset_id, source_dataset_id, examples, auth, as_of
    )


@router.get("/{dataset_id}/splits")
@audit_operation_name("read_dataset_splits")
async def get_dataset_splits(
    dataset_id: UUID,
    as_of: schemas.AsOfType = "latest",
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> List[str]:
    return await models.datasets.splits.get_splits_for_dataset(auth, dataset_id, as_of)


@router.put("/{dataset_id}/splits")
@audit_operation_name("update_dataset_splits")
async def update_dataset_splits(
    dataset_id: UUID,
    split_name: str = Body(...),
    examples: List[UUID] = Body(...),
    remove: bool = Body(False),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> List[UUID]:
    return await models.datasets.splits.update_splits_for_dataset(
        auth, dataset_id, split_name, examples, remove
    )


@router.post("/{dataset_id}/index")
async def index(
    dataset_id: UUID,
    index_req: schemas.DatasetIndexRequest = Body(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
) -> None:
    """Index a dataset."""
    if not config.settings.SERVED_DATASET_ENABLED:
        raise HTTPException(
            status_code=403,
            detail="Served datasets are disabled.",
        )

    await models.datasets.indexed.index.index_dataset(auth, dataset_id, index_req)


@router.post("/{dataset_id}/index/sync")
async def sync_index(
    dataset_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
) -> None:
    """Sync an index for a dataset."""
    if not config.settings.SERVED_DATASET_ENABLED:
        raise HTTPException(
            status_code=403,
            detail="Served datasets are disabled.",
        )

    await models.datasets.indexed.index.sync_dataset_if_exists(auth, dataset_id)


@router.delete("/{dataset_id}/index")
async def remove_index(
    dataset_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
) -> None:
    """Remove an index for a dataset."""
    if not config.settings.SERVED_DATASET_ENABLED:
        raise HTTPException(
            status_code=403,
            detail="Served datasets are disabled.",
        )

    await models.datasets.indexed.index.remove_index_for_dataset(auth, dataset_id)


@router.get("/{dataset_id}/index")
async def get_index_info(
    dataset_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
) -> schemas.DatasetIndexInfo:
    """Get index info."""
    if not config.settings.SERVED_DATASET_ENABLED:
        raise HTTPException(
            status_code=403,
            detail="Served datasets are disabled.",
        )

    return await models.datasets.indexed.index.get_dataset_index(auth, dataset_id)


@router.post("/{dataset_id}/search")
async def search(
    dataset_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_READ)),
    query: schemas.SearchDatasetRequest = Body(...),
) -> schemas.SearchDatasetResponse:
    """Search a dataset."""

    # NOTE: THIS WILL BE REMOVED AND MOVED TO GO SOON

    if not config.settings.SERVED_DATASET_ENABLED:
        raise HTTPException(
            status_code=403,
            detail="Served datasets are disabled.",
        )

    return await models.datasets.indexed.index.search(auth, dataset_id, query)


@router.post("/{dataset_id}/generate")
async def generate(
    dataset_id: UUID,
    payload: schemas.GenerateSyntheticExamplesBody,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.DATASETS_UPDATE)),
):
    """Generate synthetic examples for a dataset."""
    return EventSourceResponse(
        jsonpatch_sse_stream(
            models.examples.create.generate_synthetic_examples(
                auth, dataset_id, payload.example_ids, payload.num_examples
            )
        )
    )


@router.post("/playground_experiment/batch")
@audit_operation_name("create_playground_experiment")
async def dataset_handler(
    request: PlaygroundRunOverDatasetBatchRequestSchema,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            [
                Permissions.PROMPTS_READ,
                Permissions.DATASETS_READ,
                Permissions.PROJECTS_CREATE,
            ],
        )
    ),
) -> Output:
    session = await create_playground_experiment(auth, request)
    return await fetch_and_execute_examples(request, auth, session, 0, True)


@router.post("/playground_experiment/stream")
async def stream_dataset_handler(
    request: PlaygroundRunOverDatasetRequestSchema,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            [
                Permissions.PROMPTS_READ,
                Permissions.DATASETS_READ,
                Permissions.PROJECTS_CREATE,
            ],
        )
    ),
) -> StreamingResponse:
    session = await create_playground_experiment(auth, request)
    return await stream_dataset_response(request, auth, session)


@router.post("/studio_experiment")
async def studio_experiment(
    request: StudioRunOverDatasetRequestSchema,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            [
                Permissions.DATASETS_READ,
                Permissions.PROJECTS_CREATE,
            ],
        )
    ),
) -> Output:
    session = await create_playground_experiment(auth, request, "lg_studio")
    return session
