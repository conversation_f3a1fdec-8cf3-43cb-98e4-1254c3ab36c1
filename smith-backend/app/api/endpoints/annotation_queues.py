"""Endpoints for annotation queues."""

from datetime import datetime
from typing import Annotated, List, Optional
from uuid import UUID

import asyncpg
from fastapi import Depends, HTTPException, Query, Response
from fastapi.responses import OR<PERSON><PERSON>NResponse
from lc_database import api_router, database

from app import crud, schemas
from app.api import deps
from app.api.auth.schemas import Permissions
from app.stream import streaming_file_response

router = api_router.TrailingSlashRouter()


@router.get("")
async def get_annotation_queues(
    response: Response,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_READ)),
    ids: Annotated[list[UUID] | None, Query()] = None,
    name: str | None = None,
    name_contains: str | None = None,
    offset: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    tag_value_id: Annotated[list[UUID] | None, Query()] = None,
    dataset_id: Annotated[UUID | None, Query()] = None,
) -> List[schemas.AnnotationQueueSchemaWithSize]:
    async with database.asyncpg_conn() as db:
        rows, total = await crud.get_annotation_queues(
            db,
            auth,
            schemas.QueryParamsForAnnotationQueueSchema(
                ids=ids,
                name=name,
                name_contains=name_contains,
                limit=limit,
                offset=offset,
                tag_value_id=tag_value_id,
                dataset_id=dataset_id,
            ),
        )

    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.post("")
async def create_annotation_queue(
    queue: schemas.AnnotationQueueCreateSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_CREATE)),
) -> schemas.AnnotationQueueSchema:
    try:
        async with database.asyncpg_conn() as db:
            return await crud.create_annotation_queue(db, auth, queue)
    except asyncpg.exceptions.UniqueViolationError:
        raise HTTPException(
            status_code=400, detail="Annotation Queue with this name already exists."
        )


@router.post("/populate")
async def populate_annotation_queue(
    request: schemas.PopulateAnnotationQueueSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_UPDATE)),
):
    """Populate annotation queue with runs from an experiment."""
    async with database.asyncpg_conn() as db:
        await crud.populate_annotation_queue(db, auth, request)

    return ORJSONResponse({"message": "Annotation Queue populated successfully"})


@router.delete("/{queue_id}")
async def delete_annotation_queue(
    queue_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_DELETE)),
):
    async with database.asyncpg_conn() as db:
        await crud.delete_annotation_queue(db, auth, queue_id)

    return ORJSONResponse({"message": "Annotation Queue deleted"})


@router.patch("/{queue_id}")
async def update_annotation_queue(
    queue_id: UUID,
    queue: schemas.AnnotationQueueUpdateSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_UPDATE)),
):
    try:
        async with database.asyncpg_conn() as db:
            await crud.update_annotation_queue(db, auth, queue_id, queue)

        return ORJSONResponse({"message": "Annotation Queue updated"})
    except asyncpg.exceptions.UniqueViolationError:
        raise HTTPException(
            status_code=400, detail="Annotation Queue with this name already exists."
        )


@router.post("/{queue_id}/runs")
async def add_runs_to_annotation_queue(
    queue_id: UUID,
    run_ids: List[UUID],
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_UPDATE)),
) -> List[schemas.AnnotationQueueRunSchema]:
    async with database.asyncpg_conn() as db:
        return await crud.add_runs_to_annotation_queue(db, auth, queue_id, run_ids)


@router.get("/{queue_id}/runs")
async def get_runs_from_annotation_queue(
    queue_id: UUID,
    response: Response,
    query_params: schemas.QueryParamsForAnnotationQueueRunSchema = Depends(),
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_READ)),
) -> list[schemas.RunSchemaWithAnnotationQueueInfo]:
    async with database.asyncpg_conn() as db:
        fetched_runs, total = await crud.get_runs_from_annotation_queue(
            db, auth, queue_id, query_params
        )
    response.headers["X-Pagination-Total"] = str(total)
    return fetched_runs


@router.post("/{queue_id}/export")
async def export_annotation_queue_archived_runs(
    queue_id: UUID,
    request: schemas.ExportAnnotationQueueRunsRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_READ)),
):
    return await streaming_file_response(
        lambda write_stream, task_status: crud.export_annotation_queue_archived_runs(
            auth, queue_id, request, write_stream, task_status
        ),
        "text/csv",
        f"annotation_queue_{queue_id}.csv",
    )


@router.get("/{queue_id}/run/{index}")
async def get_run_from_annotation_queue(
    queue_id: UUID,
    index: int,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_READ)),
    include_extra: bool = False,
) -> schemas.RunSchemaWithAnnotationQueueInfo:
    """Get a run from an annotation queue"""
    async with database.asyncpg_conn() as db:
        return await crud.get_run_from_annotation_queue(
            db, auth, queue_id, index, include_extra
        )


@router.get("/{run_id}/queues")
async def get_annotation_queues_for_run(
    run_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_READ)),
) -> List[schemas.AnnotationQueueSchema]:
    async with database.asyncpg_conn() as db:
        return await crud.get_annotation_queues_for_run(db, auth, run_id)


@router.patch("/{queue_id}/runs/{queue_run_id}")
async def update_run_in_annotation_queue(
    queue_id: UUID,
    queue_run_id: UUID,
    run: schemas.AnnotationQueueRunUpdateSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_UPDATE)),
):
    async with database.asyncpg_conn() as db:
        await crud.update_run_in_queue(db, auth, queue_id, queue_run_id, run)
    return ORJSONResponse({"message": "Run updated"})


@router.delete("/{queue_id}/runs/{queue_run_id}")
async def delete_run_from_annotation_queue(
    queue_id: UUID,
    queue_run_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_UPDATE)),
):
    async with database.asyncpg_conn() as db:
        await crud.delete_run_from_queue(db, auth, queue_id, queue_run_id)
    return ORJSONResponse({"message": "Run deleted"})


@router.post("/{queue_id}/runs/delete")
async def delete_runs_from_annotation_queue(
    queue_id: UUID,
    request: schemas.AnnotationQueueBulkDeleteRunsRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_UPDATE)),
):
    async with database.asyncpg_conn() as db:
        await crud.delete_runs_from_queue(db, auth, queue_id, request)
    return ORJSONResponse({"message": "Runs deleted"})


@router.get("/{queue_id}/total_size")
async def get_total_size_from_annotation_queue(
    queue_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_READ)),
) -> schemas.AnnotationQueueSizeSchema:
    size = await crud.get_total_size_from_annotation_queue(auth, queue_id)
    return schemas.AnnotationQueueSizeSchema(size=size)


@router.get("/{queue_id}/total_archived")
async def get_total_archived_from_annotation_queue(
    queue_id: UUID,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_READ)),
) -> schemas.AnnotationQueueSizeSchema:
    size = await crud.get_total_archived_size_from_annotation_queue(
        auth, queue_id, start_time, end_time
    )
    return schemas.AnnotationQueueSizeSchema(size=size)


@router.get("/{queue_id}/size")
async def get_size_from_annotation_queue(
    queue_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_READ)),
) -> schemas.AnnotationQueueSizeSchema:
    async with database.asyncpg_conn() as db:
        size = await crud.get_size_from_annotation_queue(db, auth, queue_id)
        return schemas.AnnotationQueueSizeSchema(size=size)


@router.post("/status/{annotation_queue_run_id}")
async def create_identity_annotation_queue_run_status(
    annotation_queue_run_id: UUID,
    log_info: schemas.IdentityAnnotationQueueRunStatusCreateSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_UPDATE)),
):
    await crud.create_identity_annotation_queue_run_status(
        auth, annotation_queue_run_id, log_info
    )
    return ORJSONResponse({"message": "Status logged"})


@router.get("/{queue_id}")
async def get_annotation_queue(
    queue_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.ANNOTATION_QUEUES_READ)),
) -> schemas.AnnotationQueueSchemaWithRubric:
    async with database.asyncpg_conn() as db:
        row = await crud.get_annotation_queue(
            db,
            auth,
            queue_id,
        )

    if not row:
        raise HTTPException(status_code=404, detail="Annotation Queue not found")
    return row
