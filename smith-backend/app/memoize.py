import asyncio
import copyreg
import logging
import pickle
import re
import time
from functools import wraps
from typing import (
    Any,
    Awaitable,
    Callable,
    Coroutine,
    Optional,
    ParamSpec,
    TypeVar,
    cast,
)

import orjson
from asyncpg.pgproto import pgproto
from fastapi import BackgroundTasks, HTTPException
from lc_database.queue.serializer import _option
from lc_database.redis import aredis_caching_pool

from app.api.auth.schemas import AuthInfo, BaseAuthInfo
from app.utils import arun_in_executor

# Import tracing for cache telemetry
try:
    from lc_logging.trace import set_tag
except ImportError:
    # Fallback if tracing is not available
    def set_tag(tag_name: str, tag_value: Any) -> None:
        pass


logger = logging.getLogger(__name__)
_orjson_option = _option | orjson.OPT_SORT_KEYS
Param = ParamSpec("Param")
RetType = TypeVar("RetType")


# Simple stub functions to replace removed cache span tagging
def tag_cache_event(
    hit: bool,
    operation: str = "get",
    cache_key: Optional[str] = "",
    duration_ms: float = 0.0,
    endpoint: str = "",
) -> None:
    """Tag cache events using span attributes (cost-effective telemetry)."""
    try:
        # Extract repo info from cache key for hub commits
        repo_info = _extract_repo_info_from_cache_key(cache_key)

        # Use the conditional wrapper to set tags/attributes based on active tracing backend
        set_tag("cache.hit", hit)
        set_tag("cache.operation", operation)
        set_tag("cache.endpoint", endpoint)
        set_tag("cache.duration_ms", duration_ms)

        if repo_info:
            set_tag("cache.repo_owner", repo_info.get("owner", "unknown"))
            set_tag("cache.repo_name", repo_info.get("repo", "unknown"))

    except Exception:
        # Don't break functionality if telemetry fails
        pass


def tag_cache_hit(
    operation: str = "get",
    cache_key: Optional[str] = "",
    duration_ms: float = 0.0,
    endpoint: str = "",
) -> None:
    """Tag cache hit events using span attributes (cost-effective telemetry)."""
    tag_cache_event(True, operation, cache_key, duration_ms, endpoint)


def tag_cache_miss(
    operation: str = "get",
    cache_key: Optional[str] = "",
    duration_ms: float = 0.0,
    endpoint: str = "",
) -> None:
    """Tag cache miss events using span attributes (cost-effective telemetry)."""
    tag_cache_event(False, operation, cache_key, duration_ms, endpoint)


def _extract_repo_info_from_cache_key(
    cache_key: Optional[str],
) -> dict[str, str] | None:
    """
    Extract repository information from cache keys for telemetry.

    Parses cache keys to identify repository owner and name for enhanced
    monitoring and debugging. This enables repo-specific cache analytics.

    Args:
        cache_key: Redis cache key string, typically containing serialized function parameters

    Returns:
        Dictionary with 'repo_owner' and 'repo_name' keys if found, None otherwise.

    Example:
        >>> key = '["smith:cache:", "app.hub.crud.commits", "get_commits", [], {"owner": "langchain-ai", "repo": "langchain"}]'
        >>> _extract_repo_info_from_cache_key(key)
        {'repo_owner': 'langchain-ai', 'repo_name': 'langchain'}
    """
    if not cache_key or "hub.crud.commits" not in cache_key:
        return None

    try:
        # Cache keys contain serialized arguments, try to extract owner/repo
        # Format: ['smith:cache:', 'app.hub.crud.commits', 'get_commits_public', args, kwargs, version]

        # Parse the cache key to extract arguments
        if cache_key.startswith("b'"):
            # Handle byte string representation
            cache_key = cache_key[2:-1]  # Remove b' and '

        # Look for patterns like owner and repo in the key
        if "owner" in cache_key and "repo" in cache_key:
            # Simple regex to extract owner/repo from serialized data
            owner_match = re.search(r'"owner":\s*"([^"]+)"', cache_key)
            repo_match = re.search(r'"repo":\s*"([^"]+)"', cache_key)

            if owner_match and repo_match:
                return {
                    "owner": owner_match.group(1)[:10],  # Truncate to avoid cardinality
                    "repo": repo_match.group(1)[:20],  # Truncate to avoid cardinality
                }

    except Exception:
        # If parsing fails, just return None
        pass

    return None


def orjson_default(obj):
    if isinstance(obj, AuthInfo):
        return (obj.tenant_id, obj.user_id)
    if isinstance(obj, BaseAuthInfo):
        return (obj.tenant_id,)
    if hasattr(obj, "model_dump"):
        return obj.model_dump()
    if hasattr(obj, "dict"):
        return obj.dict()
    if isinstance(obj, BackgroundTasks):
        return None
    if type(obj) is pgproto.UUID:
        return str(obj)
    # Handle database connections and other non-serializable objects
    if hasattr(obj, "__class__") and "Connection" in obj.__class__.__name__:
        return f"<{obj.__class__.__name__}>"
    if hasattr(obj, "__class__") and "Pool" in obj.__class__.__name__:
        return f"<{obj.__class__.__name__}>"
    else:
        raise TypeError(
            f"Object of type {obj.__class__.__name__} is not JSON serializable"
        )


def reduce_http_exception(e: HTTPException) -> Any:
    return HTTPException, (e.status_code, e.detail, e.headers)


copyreg.pickle(HTTPException, reduce_http_exception)


GET_TASKS: dict[bytes, asyncio.Task] = {}
INFLIGHT_TASKS: dict[bytes, asyncio.Task] = {}


async def _compute_version_suffix(
    version_func: Callable[..., str] | Callable[..., Awaitable[str]] | None,
    *args,
    **kwargs,
) -> str:
    """Helper function to compute version suffix from version function."""
    if not version_func:
        return ""

    try:
        version_result = version_func(*args, **kwargs)
        # Check if the result is a coroutine (async function)
        if asyncio.iscoroutine(version_result):
            return await cast(Awaitable[str], version_result)
        else:
            return cast(str, version_result)
    except Exception:
        logger.warning("Failed to generate version key", exc_info=True)
        return ""


async def clear_redis_cache(
    func: Callable[Param, Awaitable[RetType]],
    *args: Param.args,
    **kwargs: Param.kwargs,
) -> None:
    """
    Clear the cache for a function and call params.
    Make sure to match the exclude_kwargs and pass the correct args to match the original method.
    """
    exclude_kwargs = cast(Optional[list[str]], kwargs.pop("exclude_kwargs"))

    # Build cache key components as a list to avoid type issues
    cache_key_components: list[Any] = [
        "smith:cache:",
        func.__module__,
        func.__name__,
        args,
        kwargs
        if not exclude_kwargs
        else {k: v for k, v in kwargs.items() if k not in exclude_kwargs},
    ]

    key = orjson.dumps(
        tuple(cache_key_components),
        option=_orjson_option,
        default=orjson_default,
    )
    async with aredis_caching_pool() as redis:
        if key is not None:
            await redis.delete(key)


async def update_redis_cache_version_key(
    func: Callable[Param, Awaitable[RetType]],
    version_func: Callable[..., str] | Callable[..., Awaitable[str]] | None,
    exclude_kwargs: list[str] | None = None,
    *args: Param.args,
    **kwargs: Param.kwargs,
) -> None:
    """
    Clear the cache for a function with version key support.

    Args:
        func: The function to clear cache for
        version_func: Function to generate version from args/kwargs
        exclude_kwargs: List of kwargs to exclude from cache key
        *args: Function arguments
        **kwargs: Function keyword arguments
    """
    # Compute version using helper function
    version = await _compute_version_suffix(version_func, *args, **kwargs)

    # Build cache key components as a list to avoid type issues
    cache_key_components: list[Any] = [
        "smith:cache:",
        func.__module__,
        func.__name__,
        args,
        kwargs
        if not exclude_kwargs
        else {k: v for k, v in kwargs.items() if k not in exclude_kwargs},
    ]

    # Add version if provided
    if version:
        cache_key_components.append(version)

    key = orjson.dumps(
        tuple(cache_key_components),
        option=_orjson_option,
        default=orjson_default,
    )
    async with aredis_caching_pool() as redis:
        if key is not None:
            await redis.delete(key)


def redis_cache(
    ttl: int | None,
    exclude_kwargs: list[str] | None = None,
    success_callback: Callable[[Any], None] | None = None,
    log_set: bool = False,
    cache_empty: bool = True,
    enabled: bool | Callable[[], bool] = True,
    version_func: Callable[..., str] | Callable[..., Awaitable[str]] | None = None,
):
    """Cache the result of a function in Redis.

    Args:
        ttl (int): Time to live in seconds.
        cache_empty (bool): Cache empty or null results, defaults to true.
        enabled (bool | Callable[[], bool]): Whether caching is enabled.
        version_func (Callable[..., str] | Callable[..., Awaitable[str]] | None): Optional function to generate version from args/kwargs. Can be sync or async.
    """

    def decorator(
        func: Callable[Param, Awaitable[RetType]],
    ) -> Callable[Param, Awaitable[RetType]]:
        @wraps(func)
        async def wrapper(*args: Param.args, **kwargs: Param.kwargs) -> RetType:
            # Check if caching is enabled
            cache_enabled = enabled() if callable(enabled) else enabled
            if not cache_enabled:
                return await func(*args, **kwargs)
            cache_ttl_value = kwargs.pop("cache_ttl", None)
            if cache_ttl_value is not None:
                ttl_override = cast(int, cache_ttl_value)
            elif ttl is not None:
                ttl_override = ttl
            else:
                ttl_override = 0

            # Skip caching if ttl is 0
            if ttl_override == 0:
                return await func(*args, **kwargs)
            try:
                # Build cache key components as a list to avoid type issues
                cache_key_components: list[Any] = [
                    "smith:cache:",
                    func.__module__,
                    func.__name__,
                    args,
                    kwargs
                    if not exclude_kwargs
                    else {k: v for k, v in kwargs.items() if k not in exclude_kwargs},
                ]

                # Add version if provided
                if version_func:
                    # Compute version using helper function
                    version = await _compute_version_suffix(
                        version_func, *args, **kwargs
                    )
                    if version:
                        cache_key_components.append(version)

                key = await arun_in_executor(
                    orjson.dumps,
                    tuple(cache_key_components),
                    option=_orjson_option,
                    default=orjson_default,
                )
            except Exception:
                logger.warning("Failed to serialize cache key", exc_info=True)
                key = None
            result = await execute_with_cache(
                key=key,
                func=func,
                args=args,
                kwargs=kwargs,
                ttl=ttl_override,
                cache_empty=cache_empty,
                log_set=log_set,
                success_callback=success_callback,
            )
            return result

        return wrapper

    return decorator


async def execute_with_cache(
    key: bytes | None,
    func: Callable[Param, Awaitable[RetType]],
    args: tuple,
    kwargs: dict,
    ttl: int,
    success_callback: Callable[[Any], None] | None = None,
    log_set: bool = True,
    cache_empty: bool = False,
) -> Any:
    cache_start_time = time.perf_counter()
    saved = None  # Initialize saved to avoid UnboundLocalError

    async with aredis_caching_pool() as redis:
        if key is not None:
            try:
                if inflight := GET_TASKS.get(key):
                    saved = await inflight
                else:
                    task = asyncio.create_task(redis.get(key))
                    GET_TASKS[key] = task
                    saved = await task
            except Exception:
                logger.warning("Failed to get cached result", exc_info=True)
                saved = None
            finally:
                GET_TASKS.pop(key, None)

        # Determine if we have a cache hit or miss
        cache_hit = False
        result = None

        if saved is not None:
            try:
                result = pickle.loads(saved)
                cache_hit = True
            except Exception:
                logger.warning("Failed to deserialize cached result", exc_info=True)
                cache_hit = False
                saved = None  # Treat as cache miss
        else:
            cache_hit = False

        # Tag cache hit or miss based on the actual outcome
        cache_duration_ms = (time.perf_counter() - cache_start_time) * 1000
        cache_key_str = key.decode() if isinstance(key, bytes) else key

        if cache_hit:
            tag_cache_hit(
                operation="get",
                cache_key=cache_key_str,
                duration_ms=cache_duration_ms,
                endpoint=func.__name__,
            )
        else:
            tag_cache_miss(
                operation="get",
                cache_key=cache_key_str,
                duration_ms=cache_duration_ms,
                endpoint=func.__name__,
            )

        # If cache miss, execute the function and cache the result
        if not cache_hit:
            try:
                try:
                    if key is not None and (inflight := INFLIGHT_TASKS.get(key)):
                        result = await inflight
                    else:
                        task = asyncio.create_task(
                            cast(
                                Coroutine[None, None, RetType],
                                func(*args, **kwargs),
                            )
                        )
                        if key is not None:
                            INFLIGHT_TASKS[key] = task
                        result = await task
                except HTTPException as e:
                    result = e
                try:
                    if cache_empty or (
                        result is not None
                        and (not isinstance(result, list) or len(result) > 0)
                    ):
                        await redis.set(key, pickle.dumps(result), ex=ttl)
                        set_duration_ms = (
                            time.perf_counter() - cache_start_time
                        ) * 1000

                        # Tag cache set operation (lightweight span tagging only)
                        cache_key_str = key.decode() if isinstance(key, bytes) else key
                        tag_cache_hit(
                            operation="set",
                            cache_key=cache_key_str,
                            duration_ms=set_duration_ms,
                            endpoint=func.__name__,
                        )

                        if log_set:
                            logger.info("Set cache key: %s\nto %s", key, result)
                except Exception:
                    logger.warning("Failed to set cached result", exc_info=True)
            finally:
                if key is not None:
                    INFLIGHT_TASKS.pop(key, None)

        if isinstance(result, HTTPException):
            raise result
        else:
            if success_callback:
                success_callback(result)
            return result
