# K6 Load Testing

## Overview

This directory contains K6 load testing scripts for LangSmith APIs and PostgreSQL database performance testing. The framework supports testing at multiple scale levels to prepare for 100B traces (20x current volume).

## Test Types

### API Load Tests
- **run-by-id-test.js** - Tests GET /runs/<id> endpoint performance
- **run-by-id-dynamic-test.js** - Tests GET /runs/<id> with dynamic run IDs from /runs/query
- **run-by-trace.js** - Tests runs by trace ID queries  
- **multipart.js** - Tests multipart upload performance

### Database Load Tests 
- **large_batch_size.js** - Tests database write performance via /runs/batch
- **data-export-test.js** - Tests database read performance for large data exports
- **postgres-load-test.js** - Direct PostgreSQL performance testing

📖 **For comprehensive PostgreSQL load testing documentation, see [README-postgres-load-testing.md](./README-postgres-load-testing.md)**

## Prerequisites

1. Install K6: https://grafana.com/docs/k6/latest/set-up/install-k6
2. For PostgreSQL direct testing: Install K6 with SQL extension
   ```bash
   go install go.k6.io/xk6/cmd/xk6@latest
   xk6 build --with github.com/grafana/xk6-sql
   ```

## Quick Start

### Via GitHub Actions (Recommended)
1. Go to Actions → "K6 Load Test"
2. Choose test type and configuration:
   - **run-by-id**: Set `test_config` to a single run ID
   - **run-by-id-dynamic**: Set `test_config` to `runsLimit,projectName,sessionId` (e.g., `1000,my-project,session-123`)
   - **run-by-trace**: Set `test_config` to `traceId,sessionId` (e.g., `trace-123,session-456`)
   - **multipart**: Use batch_size and payload_size fields
3. Configure scale level (baseline/4x/20x)

### Manual Script Execution

Set your environment variables:

```bash
export LANGCHAIN_API_KEY='your_api_key'
export LANGCHAIN_ENDPOINT='https://domain.langchain.dev/api/v1'
export SCALE_LEVEL='baseline'  # baseline, 4x, or 20x
```

Run a test:
```bash
k6 run smith-backend/scripts/k6/large_batch_size.js        # Database write test
k6 run smith-backend/scripts/k6/data-export-test.js        # Database read test
k6 run smith-backend/scripts/k6/run-by-id-test.js          # API performance test (single run ID)
k6 run smith-backend/scripts/k6/run-by-id-dynamic-test.js  # API performance test (dynamic run IDs)
```

## Scale Levels

| Level | Description | Target Volume |
|-------|-------------|---------------|
| `baseline` | Current production load | 5B traces |
| `4x` | Intermediate scale target | 20B traces |
| `20x` | Final scale target | 100B traces |

## Database Testing

⚠️ **Important**: Use database clones for load testing, never production databases.

**Available Clone**: `langchainplus-db-clone` (Google Cloud SQL)

### Common Database Issues
- **asyncpg + PGBouncer prepared statement conflicts** - See PostgreSQL documentation for solutions
- **Connection pool exhaustion under high load**
- **Memory issues during bulk operations**

## OS Tuning

For larger tests, it's recommended to avoid running Docker desktop at the same time (or other memory-intensive applications).

More details from K6 on tuning the OS where tests are running: https://grafana.com/docs/k6/latest/set-up/fine-tune-os.

## Monitoring & Results

Tests output JSON results with detailed metrics:
- Response time percentiles (p50, p95, p99)
- Error rates and categorization  
- Throughput metrics
- Database-specific metrics (for DB tests)

Results are automatically processed and uploaded as GitHub Actions artifacts.

## References

- [PostgreSQL Load Testing Guide](./README-postgres-load-testing.md) - Comprehensive database testing documentation
- [K6 Documentation](https://grafana.com/docs/k6/) - Official K6 docs
- [LangSmith Load Testing Notion](https://www.notion.so/Load-Testing-f2c36b983224481aa51c52636c862664) - Historical performance data
