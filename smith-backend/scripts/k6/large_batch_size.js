import http from 'k6/http';
import { sleep } from 'k6';

import { uuidv4 } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

/*
 * Large Batch Size Load Test for LangSmith
 *
 * This script tests database write performance via the /runs/batch endpoint with large payloads.
 *
 * Configuration Options (via environment variables):
 * - RPS_PER_VU_RATIO: How many RPS per VU (default: 5). Lower = more concurrent connections.
 * - BASELINE_RPS/VUS: Override baseline scale values (defaults: 20 RPS, 10 VUs)
 * - SCALE_4X_RPS/VUS: Override 4x scale values (defaults: 80 RPS, 40 VUs)
 * - SCALE_20X_RPS/VUS: Override 20x scale values (defaults: 400 RPS, 200 VUs)
 * - VU_SCALE_MULTIPLIER: Max VU scaling factor (default: 2)
 *
 * Examples:
 *   RPS_PER_VU_RATIO=3 k6 run large_batch_size.js   # More aggressive VU scaling
 *   BASELINE_RPS=50 k6 run large_batch_size.js      # Higher baseline load
 */

const BASE_URL = __ENV.LANGCHAIN_ENDPOINT;
const SESSION_NAME = __ENV.SESSION_NAME || 'k6-batch-test';

// ===== CONFIGURATION SECTION =====
// These values can be tweaked to adjust load testing behavior
const LOAD_TEST_CONFIG = {
  // VU calculation: How many RPS per VU for batch operations
  // Lower ratio = more VUs for given RPS (batch operations are write-heavy)
  RPS_PER_VU_RATIO: parseInt(__ENV.RPS_PER_VU_RATIO || '5'),

  // Default scale levels - can be overridden via environment
  SCALE_DEFAULTS: {
    baseline: {
      vus: parseInt(__ENV.BASELINE_VUS || '10'),     // Batch operations need more VUs
      rps: parseInt(__ENV.BASELINE_RPS || '20')      // Conservative baseline for batches
    },
    '4x': {
      vus: parseInt(__ENV.SCALE_4X_VUS || '40'),     // 4x scale
      rps: parseInt(__ENV.SCALE_4X_RPS || '80')      // 4x RPS
    },
    '20x': {
      vus: parseInt(__ENV.SCALE_20X_VUS || '200'),   // 20x scale
      rps: parseInt(__ENV.SCALE_20X_RPS || '400')    // 20x RPS
    }
  },

  // VU scaling multiplier - how much to allow K6 to scale up if needed
  VU_SCALE_MULTIPLIER: parseInt(__ENV.VU_SCALE_MULTIPLIER || '2')
};
// ===== END CONFIGURATION SECTION =====

// Parse scale configuration from environment (format: "rps" or "vus,rps" or "baseline,rps_ratio=5,vu_mult=3")
function parseScaleConfig() {
  const scaleLevel = __ENV.SCALE_LEVEL;

  // Parse extended format with key=value parameters
  if (scaleLevel && scaleLevel.includes(',')) {
    const parts = scaleLevel.split(',');
    const basePart = parts[0];

    // Initialize with default config values
    let currentConfig = { ...LOAD_TEST_CONFIG };

    // Parse key=value parameters
    parts.slice(1).forEach(part => {
      if (part.includes('=')) {
        const [key, value] = part.split('=');
        const numValue = parseInt(value);

        switch (key.trim()) {
          case 'rps_ratio':
            currentConfig.RPS_PER_VU_RATIO = numValue;
            break;
          case 'vu_mult':
            currentConfig.VU_SCALE_MULTIPLIER = numValue;
            break;
          default:
            console.warn(`Unknown parameter: ${key}=${value}`);
        }
      }
    });

    // Check if basePart is VUs,RPS format (legacy)
    if (!isNaN(parseInt(basePart))) {
      // Could be "50,200,rps_ratio=3" or just "50,200"
      const secondPart = parts[1];
      if (secondPart && !secondPart.includes('=') && !isNaN(parseInt(secondPart))) {
        // Legacy VUs,RPS format: "50,200,rps_ratio=3"
        const vus = parseInt(basePart);
        const rps = parseInt(secondPart);
        return { vus, rps };
      } else {
        // RPS with parameters: "100,rps_ratio=3"
        const rps = parseInt(basePart);
        const vus = Math.max(1, Math.ceil(rps / currentConfig.RPS_PER_VU_RATIO));
        return { vus, rps };
      }
    }

    // Named scale level with parameters: "baseline,rps_ratio=5,vu_mult=3"
    const config = currentConfig.SCALE_DEFAULTS[basePart];
    if (config) {
      return {
        vus: config.vus,
        rps: config.rps,
        vuMultiplier: currentConfig.VU_SCALE_MULTIPLIER
      };
    }
  }

  // If SCALE_LEVEL is just a number (RPS only)
  if (scaleLevel && !isNaN(parseInt(scaleLevel))) {
    const rps = parseInt(scaleLevel);
    const vus = Math.max(1, Math.ceil(rps / LOAD_TEST_CONFIG.RPS_PER_VU_RATIO));
    return { vus, rps };
  }

  // Fallback to configurable scale configurations
  const scaleLevelKey = scaleLevel || 'baseline';
  const config = LOAD_TEST_CONFIG.SCALE_DEFAULTS[scaleLevelKey];

  if (!config) {
    throw new Error(`Invalid scale level: ${scaleLevelKey}. Must be one of: ${Object.keys(LOAD_TEST_CONFIG.SCALE_DEFAULTS).join(', ')}, "rps" format (e.g. "50"), "vus,rps" format, or extended format (e.g. "baseline,rps_ratio=5,vu_mult=3")`);
  }

  return config;
}

// Get scale configuration
const CONFIG = parseScaleConfig();

export const options = {
  scenarios: {
    batch_test: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      stages: [
        { duration: '1m', target: CONFIG.rps },   // Ramp up to target RPS
        { duration: '3m', target: CONFIG.rps },   // Maintain target RPS
        { duration: '1m', target: 0 }             // Ramp down to 0
      ],
      preAllocatedVUs: CONFIG.vus,
      maxVUs: CONFIG.vus * (CONFIG.vuMultiplier || LOAD_TEST_CONFIG.VU_SCALE_MULTIPLIER), // Use dynamic or default multiplier
    },
  },
};

export const customOptions = {
  session_name: SESSION_NAME,
  batchSize: parseInt(__ENV.BATCH_SIZE || 99),
  apikey: __ENV.LANGCHAIN_API_KEY,
  baseurl: BASE_URL,
  payloadSize: parseInt(__ENV.DATA_SIZE || 25000)
}

const largePayload = "a".repeat(customOptions.payloadSize);


function formatCurrOrder(runId) {
  const now = new Date();
  return now.toISOString().replace(/[-:.]/g, '') + runId;
}

function createParentRun(runId) {
  const now = new Date();
  const endTime = new Date(now.getTime() + 1000);
  return {
    name: "LLM",
    start_time: now.toISOString(),
    end_time: endTime.toISOString(),
    extra: { foo: "bar" },
    error: null,
    execution_order: 1,
    serialized: { name: "AgentExecutor" },
    inputs: { input: largePayload },
    outputs: { output: "39,566,248" },
    session_name: customOptions.session_name,
    parent_run_id: null,
    trace_id: runId.toString(),
    dotted_order: formatCurrOrder(runId),
    run_type: "chain",
    id: runId.toString()
  };
}

function createChildRun(parentRun) {
  const runId = uuidv4();
  const now = new Date();
  const endTime = new Date(now.getTime() + 1000);
  return {
    name: "LLM",
    start_time: now.toISOString(),
    end_time: endTime.toISOString(),
    extra: { foo: "bar" },
    error: null,
    execution_order: 1,
    serialized: { name: "AgentExecutor" },
    inputs: { input: "How many people live in canada as of 2023?" },
    outputs: { output: "39,566,248" },
    session_name: parentRun.session_name,
    parent_run_id: parentRun.id,
    trace_id: parentRun.id,
    dotted_order: parentRun.dotted_order + "." + formatCurrOrder(runId),
    run_type: "chain",
    id: runId.toString()
  };
}

// The function that defines VU logic.
//
// See https://grafana.com/docs/k6/latest/examples/get-started-with-k6/ to learn more
// about authoring k6 scripts.
//
export default function () {
  const requestOptions = {
    headers: {
      "X-API-Key": customOptions.apikey,
      'Content-Type': 'application/json'
    }
  };
  const runId = uuidv4();
  const parentRun = createParentRun(runId);
  const childRuns = Array.from({ length: customOptions.batchSize }, () => createChildRun(parentRun));
  const req = { post: [parentRun, ...childRuns] };
  const response = http.post(customOptions.baseurl + '/runs/batch', JSON.stringify(req), requestOptions);
  console.log(`Request duration: ${response.timings.duration} ms`);
  console.log(`Response status: ${response.status}`);
}
