{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/datasets/{dataset_id}/experiment-view-overrides": {"get": {"description": "Retrieves the experiment view override configuration for a specific dataset.\nThis endpoint returns column display overrides including color gradients,\nprecision settings, and column visibility configurations that customize how\nexperiment results are displayed in the UI.\n\nThe response includes all column overrides with their display settings:\n- Column identifiers (must start with inputs, outputs, reference_outputs, feedback, metrics, attachments, or metadata)\n- Color gradients for numeric data visualization\n- Precision settings for numeric columns (1-6 decimal places)\n- Hide flags to control column visibility", "produces": ["application/json"], "tags": ["experiment-view-overrides"], "summary": "Get experiment view override configuration for a dataset", "parameters": [{"type": "string", "format": "uuid", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Dataset ID", "name": "dataset_id", "in": "path", "required": true}], "responses": {"200": {"description": "Successfully retrieved experiment view override configuration", "schema": {"$ref": "#/definitions/experiment_view_overrides.ExperimentViewOverride"}}, "400": {"description": "Invalid dataset ID format\" example({\"error\":\"invalid dataset ID format\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized access\" example({\"error\":\"Unauthorized\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Experiment view override not found\" example({\"error\":\"experiment view override not found\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal server error\" example({\"error\":\"internal server error\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "Creates a new experiment view override configuration for a dataset with column display settings.\nThis endpoint allows you to customize how experiment results are displayed by configuring\ncolumn-specific overrides including colors, precision, and visibility.\n\nThe request must include a 'column_overrides' array with at least one override configuration.\nEach column override can specify:\n- column: Required field name (must start with inputs, outputs, reference_outputs, feedback, metrics, attachments, or metadata)\n- color_gradient: Optional array of [number, color] tuples for numeric data visualization\n- precision: Optional number (1-6) for decimal places in numeric columns\n- hide: Optional boolean to control column visibility\n\nExample request body:\n{\n\"column_overrides\": [\n{\n\"column\": \"outputs.accuracy\",\n\"color_gradient\": [[0.0, \"#ff0000\"], [0.5, \"#ffff00\"], [1.0, \"#00ff00\"]],\n\"precision\": 3\n},\n{\n\"column\": \"inputs.model_type\",\n\"hide\": false\n}\n]\n}\n\nThis operation fails if an override already exists for the dataset (use PATCH to update).", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["experiment-view-overrides"], "summary": "Create new experiment view override configuration for a dataset", "parameters": [{"type": "string", "format": "uuid", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Dataset ID", "name": "dataset_id", "in": "path", "required": true}, {"description": "Column overrides configuration to create", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/experiment_view_overrides.ExperimentViewOverridePostRequest"}}], "responses": {"201": {"description": "Successfully created experiment view override", "schema": {"$ref": "#/definitions/experiment_view_overrides.ExperimentViewOverride"}}, "400": {"description": "Invalid request data\" example({\"error\":\"column_overrides field is required\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized access\" example({\"error\":\"Unauthorized\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Dataset not found\" example({\"error\":\"dataset not found or not accessible\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "409": {"description": "Override already exists\" example({\"error\":\"experiment view override already exists\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "422": {"description": "Validation error\" example({\"error\":\"column name at index 0 must start with one of: inputs, outputs, reference_outputs, feedback, metrics, attachments, metadata\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal server error\" example({\"error\":\"internal server error\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/datasets/{dataset_id}/experiment-view-overrides/{id}": {"get": {"description": "Retrieves a specific experiment view override configuration using both dataset ID and override ID.\nThis endpoint provides more precise access to experiment view overrides when you have\nthe specific override ID, useful for direct links or cached references.\n\nThe response includes the same column override information as the dataset-level endpoint:\n- Column identifiers with validation prefixes\n- Color gradient settings for numeric data visualization\n- Numeric precision configurations\n- Column visibility controls\n\nBoth the dataset and override must exist and be accessible by the authenticated user.", "produces": ["application/json"], "tags": ["experiment-view-overrides"], "summary": "Get experiment view override configuration by specific ID", "parameters": [{"type": "string", "format": "uuid", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Dataset ID", "name": "dataset_id", "in": "path", "required": true}, {"type": "string", "format": "uuid", "example": "\"123e4567-e89b-12d3-a456-************\"", "description": "Experiment view override ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Successfully retrieved experiment view override configuration", "schema": {"$ref": "#/definitions/experiment_view_overrides.ExperimentViewOverride"}}, "400": {"description": "Invalid ID format\" example({\"error\":\"invalid experiment view override ID format\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized access\" example({\"error\":\"Unauthorized\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Override not found\" example({\"error\":\"experiment view override not found\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal server error\" example({\"error\":\"internal server error\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"description": "Permanently deletes an experiment view override configuration for a dataset.\nThis operation removes all column override settings including color gradients,\nprecision configurations, and visibility settings.\n\nAfter deletion, the experiment view will revert to default column display settings.\nThis action cannot be undone - you will need to recreate the override configuration\nif you want to restore custom column settings.\n\nBoth the dataset and override must exist and be accessible by the authenticated user.\nThe operation will fail if the override doesn't exist or if the user doesn't have\nappropriate permissions for the dataset.", "tags": ["experiment-view-overrides"], "summary": "Delete experiment view override configuration", "parameters": [{"type": "string", "format": "uuid", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Dataset ID", "name": "dataset_id", "in": "path", "required": true}, {"type": "string", "format": "uuid", "example": "\"123e4567-e89b-12d3-a456-************\"", "description": "Experiment view override ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "Successfully deleted experiment view override (no content returned)"}, "400": {"description": "Invalid ID format\" example({\"error\":\"invalid experiment view override ID format\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized access\" example({\"error\":\"Unauthorized\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Override not found\" example({\"error\":\"experiment view override not found\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal server error\" example({\"error\":\"internal server error\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "patch": {"description": "Updates an existing experiment view override configuration by completely replacing\nthe column overrides for the specified dataset and override ID.\n\nThis endpoint performs a complete replacement of the column overrides configuration.\nAll existing column overrides will be replaced with the new configuration provided\nin the request body. To add or modify individual columns, include the complete\ndesired configuration in the request.\n\nThe request format is identical to the create endpoint:\n- column_overrides: Required array with at least one override configuration\n- Each override can specify color gradients, precision, and visibility\n\nExample request body:\n{\n\"column_overrides\": [\n{\n\"column\": \"metrics.f1_score\",\n\"color_gradient\": [[0.0, \"#ff4444\"], [0.8, \"#44ff44\"]],\n\"precision\": 4\n},\n{\n\"column\": \"feedback.rating\",\n\"hide\": false\n}\n]\n}\n\nBoth the dataset and override must exist and be accessible by the authenticated user.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["experiment-view-overrides"], "summary": "Update existing experiment view override configuration", "parameters": [{"type": "string", "format": "uuid", "example": "\"550e8400-e29b-41d4-a716-************\"", "description": "Dataset ID", "name": "dataset_id", "in": "path", "required": true}, {"type": "string", "format": "uuid", "example": "\"123e4567-e89b-12d3-a456-************\"", "description": "Experiment view override ID", "name": "id", "in": "path", "required": true}, {"description": "Updated column overrides configuration", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/experiment_view_overrides.ExperimentViewOverridePatchRequest"}}], "responses": {"200": {"description": "Successfully updated experiment view override", "schema": {"$ref": "#/definitions/experiment_view_overrides.ExperimentViewOverride"}}, "400": {"description": "Invalid request data\" example({\"error\":\"invalid experiment view override ID format\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized access\" example({\"error\":\"Unauthorized\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Override not found\" example({\"error\":\"experiment view override not found\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "422": {"description": "Validation error\" example({\"error\":\"'precision' must be between 1 and 6 for column at index 0\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal server error\" example({\"error\":\"internal server error\"})", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/feedback/batch": {"post": {"description": "Ingests a batch of feedback objects in a single JSON array payload.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["feedback"], "summary": "<PERSON><PERSON><PERSON>back (Batch JSON)", "parameters": [{"description": "Array of feedback objects", "name": "body", "in": "body", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/feedback.FeedbackCreateSchema"}}}], "responses": {"202": {"description": "Feedback batch ingested", "schema": {"type": "object", "additionalProperties": {"allOf": [{"type": "string"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "422": {"description": "Unprocessable Entity", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "429": {"description": "Too Many Requests", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}}}}, "/runs": {"post": {"description": "Queues a single run for ingestion. The request body must be a JSON-encoded run object that follows the Run schema.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["runs"], "summary": "Create a Run", "parameters": [{"description": "Run object", "name": "run", "in": "body", "required": true, "schema": {"$ref": "#/definitions/runs.Run"}}], "responses": {"202": {"description": "Run created", "schema": {"type": "object", "additionalProperties": {"allOf": [{"type": "string"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "422": {"description": "Unprocessable Entity", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "429": {"description": "Too Many Requests", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}}}}, "/runs/batch": {"post": {"description": "Ingests a batch of runs in a single JSON payload. The payload must have `post` and/or `patch` arrays containing run objects.\nPrefer this endpoint over single‑run ingestion when submitting hundreds of runs, but `/runs/multipart` offers better handling for very large fields and attachments.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["runs"], "summary": "Ingest Runs (Batch JSON)", "parameters": [{"description": "Batch payload with 'post' and 'patch' arrays", "name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"patch": {"type": "array", "items": {"$ref": "#/definitions/runs.Run"}}, "post": {"type": "array", "items": {"$ref": "#/definitions/runs.Run"}}}}}], "responses": {"202": {"description": "Runs batch ingested", "schema": {"type": "object", "additionalProperties": {"allOf": [{"type": "string"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "422": {"description": "Unprocessable Entity", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "429": {"description": "Too Many Requests", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}}}}, "/runs/multipart": {"post": {"description": "Ingests multiple runs, feedback objects, and binary attachments in a single `multipart/form-data` request.\n**Part‑name pattern**: `<event>.<run_id>[.<field>]` where `event` ∈ {`post`, `patch`, `feedback`, `attachment`}.\n* `post|patch.<run_id>` – JSON run payload.\n* `post|patch.<run_id>.<field>` – out‑of‑band run data (`inputs`, `outputs`, `events`, `error`, `extra`, `serialized`).\n* `feedback.<run_id>` – JSON feedback payload (must include `trace_id`).\n* `attachment.<run_id>.<filename>` – arbitrary binary attachment stored in S3.\n**Headers**: every part must set `Content-Type` **and** either a `Content-Length` header or `length` parameter. Per‑part `Content-Encoding` is **not** allowed; the top‑level request may be `Content-Encoding: zstd`.\n**Best performance** for high‑volume ingestion.", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["runs"], "summary": "Ingest Runs (Multipart)", "parameters": [{"type": "string", "format": "binary", "description": "Run to create (JSON)", "name": "post.{run_id}", "in": "formData"}, {"type": "string", "format": "binary", "description": "Run to update (JSON)", "name": "patch.{run_id}", "in": "formData"}, {"type": "string", "format": "binary", "description": "Large inputs object (JSON) stored out‑of‑band", "name": "post.{run_id}.inputs", "in": "formData"}, {"type": "string", "format": "binary", "description": "Large outputs object (JSON) stored out‑of‑band", "name": "patch.{run_id}.outputs", "in": "formData"}, {"type": "string", "format": "binary", "description": "Feedback object (JSON) – must include trace_id", "name": "feedback.{run_id}", "in": "formData"}, {"type": "file", "format": "binary", "description": "Binary attachment linked to run {run_id}", "name": "attachment.{run_id}.{filename}", "in": "formData"}], "responses": {"202": {"description": "Accepted", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "422": {"description": "Unprocessable Entity", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "429": {"description": "Too Many Requests", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}}}}, "/runs/{run_id}": {"patch": {"description": "Updates a run identified by its ID. The body should contain only the fields to be changed; unknown fields are ignored.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["runs"], "summary": "Update a Run", "parameters": [{"type": "string", "format": "uuid", "description": "Run ID", "name": "run_id", "in": "path", "required": true}, {"description": "Run update", "name": "run", "in": "body", "required": true, "schema": {"$ref": "#/definitions/runs.Run"}}], "responses": {"202": {"description": "Run updated", "schema": {"type": "object", "additionalProperties": {"allOf": [{"type": "string"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "422": {"description": "Unprocessable Entity", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}, "429": {"description": "Too Many Requests", "schema": {"$ref": "#/definitions/runs.ErrorResponse"}}}}}, "/v1/platform/alerts/{session_id}": {"post": {"description": "Creates a new alert rule. The request body must be a JSON-encoded alert rule object that follows the CreateAlertRuleRequest schema.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["alert_rules"], "summary": "Create an alert rule", "parameters": [{"type": "string", "description": "LangSmith API Key", "name": "X-API-Key", "in": "header", "required": true}, {"type": "string", "description": "Tenant ID", "name": "X-Tenant-ID", "in": "header", "required": true}, {"type": "string", "description": "Session ID", "name": "session_id", "in": "path", "required": true}, {"description": "Alert rule request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/alerts.CreateAlertRuleRequest"}}], "responses": {"201": {"description": "Alert rule created", "schema": {"$ref": "#/definitions/alerts.AlertRuleResponse"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "429": {"description": "Alert <PERSON> Limit Reached", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "503": {"description": "Service unavailable", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}}}}, "/v1/platform/alerts/{session_id}/test": {"post": {"description": "Tests an alert action which will fire a notification to all configured recipients if the configuration is valid.", "produces": ["application/json"], "tags": ["alert_rules"], "summary": "Test an alert action to determine if configuration is valid", "parameters": [{"type": "string", "description": "LangSmith API Key", "name": "X-API-Key", "in": "header", "required": true}, {"type": "string", "description": "Tenant ID", "name": "X-Tenant-ID", "in": "header", "required": true}, {"type": "string", "description": "Session ID", "name": "session_id", "in": "path", "required": true}, {"type": "string", "description": "Alert rule ID", "name": "alert_rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "Alert action fired successfully", "schema": {"type": "object", "additionalProperties": {"allOf": [{"type": "string"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "503": {"description": "Service unavailable", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}}}}, "/v1/platform/alerts/{session_id}/{alert_rule_id}": {"get": {"description": "Gets an alert rule.", "produces": ["application/json"], "tags": ["alert_rules"], "summary": "Get an alert rule", "parameters": [{"type": "string", "description": "LangSmith API Key", "name": "X-API-Key", "in": "header", "required": true}, {"type": "string", "description": "Tenant ID", "name": "X-Tenant-ID", "in": "header", "required": true}, {"type": "string", "description": "Session ID", "name": "session_id", "in": "path", "required": true}, {"type": "string", "description": "Alert rule ID", "name": "alert_rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "Alert rule", "schema": {"$ref": "#/definitions/alerts.AlertRuleResponse"}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "404": {"description": "Not found", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "503": {"description": "Service unavailable", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}}}, "delete": {"description": "Deletes an alert rule", "produces": ["application/json"], "tags": ["alert_rules"], "summary": "Delete an alert rule", "parameters": [{"type": "string", "description": "LangSmith API Key", "name": "X-API-Key", "in": "header", "required": true}, {"type": "string", "description": "Tenant ID", "name": "X-Tenant-ID", "in": "header", "required": true}, {"type": "string", "description": "Session ID", "name": "session_id", "in": "path", "required": true}, {"type": "string", "description": "Alert rule ID", "name": "alert_rule_id", "in": "path", "required": true}], "responses": {"200": {"description": "Alert rule deleted", "schema": {"type": "object", "additionalProperties": {"allOf": [{"type": "string"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "404": {"description": "Not found", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "503": {"description": "Service unavailable", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}}}, "patch": {"description": "Updates an alert rule.", "produces": ["application/json"], "tags": ["alert_rules"], "summary": "Update an alert rule", "parameters": [{"type": "string", "description": "LangSmith API Key", "name": "X-API-Key", "in": "header", "required": true}, {"type": "string", "description": "Tenant ID", "name": "X-Tenant-ID", "in": "header", "required": true}, {"type": "string", "description": "Session ID", "name": "session_id", "in": "path", "required": true}, {"type": "string", "description": "Alert rule ID", "name": "alert_rule_id", "in": "path", "required": true}, {"description": "Alert rule request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/alerts.UpdateAlertRuleRequest"}}], "responses": {"200": {"description": "Alert rule updated", "schema": {"type": "object", "additionalProperties": {"allOf": [{"type": "string"}, {"type": "object", "properties": {"message": {"type": "string"}}}]}}}, "400": {"description": "Bad request", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "404": {"description": "Not found", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}, "503": {"description": "Service unavailable", "schema": {"$ref": "#/definitions/alerts.ErrorResponse"}}}}}, "/v1/platform/datasets/examples/delete": {"post": {"description": "This endpoint hard deletes *all* versions of a dataset example(s).\nDeletion is performed by setting inputs, outputs, and metadata to null and deleting attachment files while keeping the example ID, dataset ID, and creation timestamp.\nIMPORTANT: attachment files can take up to 7 days to be deleted. inputs, outputs and metadata are nullified immediately.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["examples"], "summary": "Hard Delete Examples", "parameters": [{"description": "Example IDs to delete. Max 1000.", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/examples.DeleteExamplesRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/examples.ExamplesDeletedResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "422": {"description": "Unprocessable Entity", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}}}}, "/v1/platform/datasets/{dataset_id}/examples": {"post": {"description": "This endpoint allows clients to upload examples to a specified dataset by sending a multipart/form-data POST request.\nEach form part contains either JSON-encoded data or binary attachment files associated with an example.", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["examples"], "summary": "Upload Examples", "parameters": [{"type": "string", "format": "uuid", "description": "Dataset ID", "name": "dataset_id", "in": "path", "required": true}, {"type": "string", "format": "binary", "description": "The Example info as JSON. Can have fields 'metadata', 'split', 'use_source_run_io', 'source_run_id', 'created_at', 'modified_at'", "name": "{example_id}", "in": "formData", "required": true}, {"type": "string", "format": "binary", "description": "The Example inputs as JSON", "name": "{example_id}.inputs", "in": "formData", "required": true}, {"type": "string", "format": "binary", "description": "THe Example outputs as JSON", "name": "{example_id}.outputs", "in": "formData"}, {"type": "string", "format": "binary", "description": "File attachment named {name}", "name": "{example_id}.attachments.{name}", "in": "formData"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/examples.ExamplesCreatedResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "422": {"description": "Unprocessable Entity", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}}}, "patch": {"description": "This endpoint allows clients to update existing examples in a specified dataset by sending a multipart/form-data PATCH request.\nEach form part contains either JSON-encoded data or binary attachment files to update an example.", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["examples"], "summary": "Update Examples", "parameters": [{"type": "string", "format": "uuid", "description": "Dataset ID", "name": "dataset_id", "in": "path", "required": true}, {"type": "string", "format": "binary", "description": "The Example update info as JSON. Can have fields 'metadata', 'split'", "name": "{example_id}", "in": "formData", "required": true}, {"type": "string", "format": "binary", "description": "The updated Example inputs as JSON", "name": "{example_id}.inputs", "in": "formData"}, {"type": "string", "format": "binary", "description": "The updated Example outputs as JSON", "name": "{example_id}.outputs", "in": "formData"}, {"type": "string", "format": "binary", "description": "JSON describing attachment operations (retain, rename)", "name": "{example_id}.attachments_operations", "in": "formData"}, {"type": "string", "format": "binary", "description": "New file attachment named {name}", "name": "{example_id}.attachment.{name}", "in": "formData"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/examples.ExamplesUpdatedResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}, "422": {"description": "Unprocessable Entity", "schema": {"$ref": "#/definitions/examples.ErrorResponse"}}}}}}, "definitions": {"alerts.AlertAction": {"type": "object", "required": ["config", "target"], "properties": {"alert_rule_id": {"type": "string"}, "config": {"type": "object"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "target": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>", "webhook"]}, "updated_at": {"type": "string"}}}, "alerts.AlertActionBase": {"type": "object", "required": ["config", "target"], "properties": {"alert_rule_id": {"type": "string"}, "config": {"type": "object"}, "id": {"type": "string"}, "target": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>", "webhook"]}}}, "alerts.AlertRule": {"type": "object", "required": ["aggregation", "attribute", "description", "name", "operator", "type", "window_minutes"], "properties": {"aggregation": {"type": "string", "enum": ["avg", "sum", "pct"]}, "attribute": {"type": "string", "enum": ["latency", "error_count", "feedback_score", "run_latency", "run_count"]}, "created_at": {"type": "string"}, "denominator_filter": {"type": "string"}, "description": {"type": "string"}, "filter": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "operator": {"type": "string", "enum": ["gte", "lte"]}, "threshold": {"type": "number"}, "threshold_multiplier": {"type": "number"}, "threshold_window_minutes": {"type": "integer", "maximum": 60}, "type": {"type": "string", "enum": ["threshold", "change"]}, "updated_at": {"type": "string"}, "window_minutes": {"description": "max 15 minutes for alert rule", "type": "integer", "maximum": 15}}}, "alerts.AlertRuleBase": {"type": "object", "required": ["aggregation", "attribute", "description", "name", "operator", "type", "window_minutes"], "properties": {"aggregation": {"type": "string", "enum": ["avg", "sum", "pct"]}, "attribute": {"type": "string", "enum": ["latency", "error_count", "feedback_score", "run_latency", "run_count"]}, "denominator_filter": {"type": "string"}, "description": {"type": "string"}, "filter": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "operator": {"type": "string", "enum": ["gte", "lte"]}, "threshold": {"type": "number"}, "threshold_multiplier": {"type": "number"}, "threshold_window_minutes": {"type": "integer", "maximum": 60}, "type": {"type": "string", "enum": ["threshold", "change"]}, "window_minutes": {"description": "max 15 minutes for alert rule", "type": "integer", "maximum": 15}}}, "alerts.AlertRuleResponse": {"type": "object", "properties": {"actions": {"type": "array", "items": {"$ref": "#/definitions/alerts.AlertAction"}}, "rule": {"$ref": "#/definitions/alerts.AlertRule"}}}, "alerts.CreateAlertRuleRequest": {"type": "object", "required": ["actions", "rule"], "properties": {"actions": {"type": "array", "items": {"$ref": "#/definitions/alerts.AlertActionBase"}}, "rule": {"$ref": "#/definitions/alerts.AlertRuleBase"}}}, "alerts.ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request: missing required fields"}}}, "alerts.UpdateAlertRuleRequest": {"type": "object", "required": ["actions", "rule"], "properties": {"actions": {"type": "array", "items": {"$ref": "#/definitions/alerts.AlertActionBase"}}, "rule": {"$ref": "#/definitions/alerts.AlertRuleBase"}}}, "examples.DeleteExamplesRequest": {"type": "object", "required": ["example_ids", "hard_delete"], "properties": {"example_ids": {"description": "ExampleIDs is a list of UUIDs identifying the examples to delete.", "type": "array", "maxItems": 1000, "minItems": 1, "items": {"type": "string"}}, "hard_delete": {"description": "HardDelete indicates whether to perform a hard delete.\nCurrently only True is supported.", "type": "boolean"}}}, "examples.ErrorResponse": {"type": "object", "properties": {"details": {"description": "Optional error details as JSON string", "type": "string", "example": "{\"field\":\"dataset_id\",\"reason\":\"required\"}"}, "error": {"description": "Error message", "type": "string", "example": "Invalid request: missing required fields"}}}, "examples.ExamplesCreatedResponse": {"type": "object", "properties": {"count": {"type": "integer", "example": 1}, "example_ids": {"type": "array", "items": {"type": "string"}, "example": ["[\"123e4567-e89b-12d3-a456-************\"]"]}}}, "examples.ExamplesDeletedResponse": {"type": "object", "properties": {"count": {"type": "integer", "example": 1}, "example_ids": {"type": "array", "items": {"type": "string"}, "example": ["[\"123e4567-e89b-12d3-a456-************\"]"]}}}, "examples.ExamplesUpdatedResponse": {"type": "object", "properties": {"count": {"type": "integer", "example": 1}, "example_ids": {"type": "array", "items": {"type": "string"}, "example": ["[\"123e4567-e89b-12d3-a456-************\"]"]}}}, "experiment_view_overrides.ColumnOverride": {"type": "object", "required": ["column"], "properties": {"color_gradient": {"type": "array", "maxItems": 20, "items": {"type": "array", "items": {}}}, "color_map": {"type": "object", "additionalProperties": true}, "column": {"type": "string", "maxLength": 200}, "hide": {"type": "boolean"}, "precision": {"type": "integer", "maximum": 6, "minimum": 1}}}, "experiment_view_overrides.ExperimentViewOverride": {"type": "object", "properties": {"column_overrides": {"type": "array", "items": {"$ref": "#/definitions/experiment_view_overrides.ColumnOverride"}}, "created_at": {"type": "string"}, "dataset_id": {"type": "string"}, "id": {"type": "string"}, "modified_at": {"type": "string"}}}, "experiment_view_overrides.ExperimentViewOverridePatchRequest": {"type": "object", "required": ["column_overrides"], "properties": {"column_overrides": {"type": "array", "maxItems": 50, "minItems": 1, "items": {"$ref": "#/definitions/experiment_view_overrides.ColumnOverride"}}}}, "experiment_view_overrides.ExperimentViewOverridePostRequest": {"type": "object", "required": ["column_overrides"], "properties": {"column_overrides": {"type": "array", "maxItems": 50, "minItems": 1, "items": {"$ref": "#/definitions/experiment_view_overrides.ColumnOverride"}}}}, "feedback.FeedbackCategory": {"type": "object", "properties": {"label": {"type": "string", "minLength": 1}, "value": {"type": "number"}}}, "feedback.FeedbackConfig": {"type": "object", "properties": {"categories": {"type": "array", "items": {"$ref": "#/definitions/feedback.FeedbackCategory"}}, "max": {"type": "number"}, "min": {"type": "number"}, "type": {"$ref": "#/definitions/feedback.FeedbackType"}}}, "feedback.FeedbackCreateSchema": {"type": "object", "properties": {"comment": {"type": "string"}, "comparative_experiment_id": {"type": "string"}, "correction": {}, "created_at": {"type": "string"}, "error": {"type": "boolean"}, "feedback_config": {"$ref": "#/definitions/feedback.FeedbackConfig"}, "feedback_group_id": {"type": "string"}, "feedback_source": {"$ref": "#/definitions/feedback.FeedbackSource"}, "id": {"type": "string"}, "key": {"type": "string"}, "modified_at": {"type": "string"}, "run_id": {"type": "string"}, "score": {}, "session_id": {"type": "string"}, "trace_id": {"type": "string"}, "value": {}}}, "feedback.FeedbackSource": {"type": "object", "properties": {"metadata": {"type": "object", "additionalProperties": true}, "type": {"type": "string"}}}, "feedback.FeedbackType": {"type": "string", "enum": ["continuous", "categorical", "freeform"], "x-enum-varnames": ["FeedbackTypeContinuous", "FeedbackTypeCategorical", "FeedbackTypeFreeform"]}, "runs.ErrorResponse": {"type": "object", "properties": {"details": {"description": "Optional error details as JSON string", "type": "string", "example": "{\"field\":\"dataset_id\",\"reason\":\"required\"}"}, "error": {"description": "Error message", "type": "string", "example": "Invalid request: missing required fields"}}}, "runs.Run": {"type": "object", "properties": {"dotted_order": {"type": "string"}, "end_time": {"type": "string"}, "error": {"type": "string"}, "events": {"type": "array", "items": {"type": "object", "additionalProperties": true}}, "extra": {"type": "object", "additionalProperties": true}, "id": {"type": "string"}, "input_attachments": {"type": "object", "additionalProperties": true}, "inputs": {"type": "object", "additionalProperties": true}, "name": {"type": "string"}, "output_attachments": {"type": "object", "additionalProperties": true}, "outputs": {"type": "object", "additionalProperties": true}, "parent_run_id": {"type": "string"}, "reference_example_id": {"type": "string"}, "run_type": {"type": "string", "enum": ["tool", "chain", "llm", "retriever", "embedding", "prompt", "parser"]}, "serialized": {"type": "object", "additionalProperties": true}, "session_id": {"type": "string"}, "session_name": {"type": "string"}, "start_time": {"type": "string"}, "status": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "trace_id": {"type": "string"}}}}}